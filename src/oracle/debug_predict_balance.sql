-- Debug script for predict balance investigation
-- This script helps investigate predict balance calculation issues

-- 1. First, clear the debug log
DELETE FROM DEBUG_LOG WHERE DBG_TEXT LIKE '%fn_get_predict_balance%' OR DBG_TEXT LIKE '%fn_get_preadvice_amounts%' OR DBG_TEXT LIKE '%sp_populate_data%';
COMMIT;

-- 2. Create a procedure to enable debugging for specific accounts
CREATE OR REPLACE PROCEDURE DEBUG_SPECIFIC_ACCOUNT(
   p_host_id     VARCHAR2,
   p_entity_id   VARCHAR2,
   p_account_id  VARCHAR2,
   p_value_date  DATE DEFAULT SYSDATE
) IS
   v_debug_text VARCHAR2(4000);
BEGIN
   v_debug_text := '=== STARTING DEBUG FOR SPECIFIC ACCOUNT === ' ||
                  'Host:' || p_host_id || ', Entity:' || p_entity_id || 
                  ', Account:' || p_account_id || ', Date:' || TO_CHAR(p_value_date, 'DD-MON-YYYY');
   PKG_DEBUG.LOG(v_debug_text);
   
   -- Call the debug procedure directly
   PKG_PREDICT_JOBS.debug_balance_calculation(
      p_host_id, p_entity_id, 'USD', p_account_id, p_value_date, SYSDATE
   );
   
   v_debug_text := '=== COMPLETED DEBUG FOR SPECIFIC ACCOUNT === ' || p_account_id;
   PKG_DEBUG.LOG(v_debug_text);
END;
/

-- 3. Query to check current movements for an account
CREATE OR REPLACE FUNCTION GET_MOVEMENT_SUMMARY(
   p_host_id     VARCHAR2,
   p_entity_id   VARCHAR2,
   p_account_id  VARCHAR2,
   p_value_date  DATE DEFAULT SYSDATE
) RETURN VARCHAR2 IS
   v_result VARCHAR2(4000) := '';
   v_count  NUMBER := 0;
BEGIN
   FOR rec IN (
      SELECT movement_id, amount, sign, match_status, predict_status, 
             position_level, input_source, ext_bal_status,
             CASE sign WHEN 'C' THEN amount WHEN 'D' THEN -amount END as calc_amt
      FROM p_movement
      WHERE host_id = p_host_id
        AND entity_id = p_entity_id
        AND account_id = p_account_id
        AND value_date = p_value_date
      ORDER BY movement_id
   ) LOOP
      v_count := v_count + 1;
      v_result := v_result || 'Mvt' || v_count || ':' || rec.movement_id || 
                 '(Amt:' || rec.amount || ',Sign:' || rec.sign || 
                 ',Match:' || rec.match_status || ',Pred:' || rec.predict_status || 
                 ',Pos:' || rec.position_level || ',Src:' || rec.input_source || ') ';
   END LOOP;
   
   RETURN 'Total:' || v_count || ' movements - ' || v_result;
END;
/

-- 4. Query to see debug log results
CREATE OR REPLACE VIEW V_DEBUG_PREDICT_BALANCE AS
SELECT DBG_TIME, 
       SUBSTR(DBG_TEXT, 1, 200) as DEBUG_MESSAGE,
       DBG_TEXT as FULL_MESSAGE
FROM DEBUG_LOG 
WHERE DBG_TEXT LIKE '%fn_get_predict_balance%' 
   OR DBG_TEXT LIKE '%fn_get_preadvice_amounts%' 
   OR DBG_TEXT LIKE '%sp_populate_data%'
   OR DBG_TEXT LIKE '%BALANCE CALCULATION SUMMARY%'
ORDER BY DBG_TIME DESC;

-- 5. Instructions for customer
/*
USAGE INSTRUCTIONS:
==================

1. To debug a specific account:
   EXEC DEBUG_SPECIFIC_ACCOUNT('HOST_ID', 'ENTITY_ID', 'ACCOUNT_ID', DATE'2024-01-15');

2. To see movement summary for an account:
   SELECT GET_MOVEMENT_SUMMARY('HOST_ID', 'ENTITY_ID', 'ACCOUNT_ID', DATE'2024-01-15') FROM DUAL;

3. To run the populate job and see debug output:
   EXEC PKG_PREDICT_JOBS.sp_populate_data;

4. To view debug results:
   SELECT * FROM V_DEBUG_PREDICT_BALANCE WHERE DBG_TIME > SYSDATE - 1;

5. To see specific account balance calculation:
   SELECT * FROM V_DEBUG_PREDICT_BALANCE 
   WHERE FULL_MESSAGE LIKE '%ACCOUNT_ID_HERE%' 
   ORDER BY DBG_TIME;

6. To clear debug log:
   DELETE FROM DEBUG_LOG; COMMIT;

EXAMPLE USAGE:
==============
-- Debug account 'TEST123' for today
EXEC DEBUG_SPECIFIC_ACCOUNT('HOST1', 'ENTITY1', 'TEST123');

-- See the results
SELECT DBG_TIME, DEBUG_MESSAGE FROM V_DEBUG_PREDICT_BALANCE 
WHERE FULL_MESSAGE LIKE '%TEST123%' 
ORDER BY DBG_TIME;

WHAT TO LOOK FOR:
=================
1. Check if authorized movements (match_status='A') are being included in predict balance
2. Check if preadvice amounts are being added to predict balance (they shouldn't be)
3. Look for the "BALANCE CALCULATION SUMMARY" entries to see the final calculation
4. Compare the individual function results with the final balance

KEY DEBUG MESSAGES:
===================
- "fn_get_predict_balance ENTRY" - Shows parameters passed to predict balance function
- "INCLUDING Movement" - Shows which movements are included in predict balance
- "fn_get_preadvice_amounts" - Shows preadvice movements (stored separately)
- "BALANCE CALCULATION SUMMARY" - Shows final calculation breakdown
- "AUTHORIZED PREADVICE (NOT INCLUDED)" - Shows authorized preadvice movements that are excluded

*/
