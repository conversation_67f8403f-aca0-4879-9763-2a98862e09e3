<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.apache.commons.lang.StringEscapeUtils" %>

<html>
<head>
<title><fmt:message key="correspondentaccountmaintenance.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">

<script type="text/javascript">
    // Global variables for Angular
    var appName = "<%=StringEscapeUtils.escapeJavaScript(SwtUtil.appName)%>";
    var baseURL = new String('<%=StringEscapeUtils.escapeJavaScript(request.getRequestURL().toString())%>');
    var screenRoute = "CorrespondentAcctMaintenance";

    // Initial parameters from JSP request scope, escaped for JavaScript
    var initialParams = {
        menuEntityCurrGrpAccess: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("menuEntityCurrGrpAccess")))%>",
        // Button states (ensure these attributes are set in the Java action's initial JSP-serving method)
        swt_add_btn_sts: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(SwtConstants.STR_TRUE.equals(request.getAttribute(SwtConstants.ADD_BUT_STS))))%>",
        swt_chg_btn_sts: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(SwtConstants.STR_TRUE.equals(request.getAttribute(SwtConstants.CHG_BUT_STS))))%>",
        swt_del_btn_sts: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(SwtConstants.STR_TRUE.equals(request.getAttribute(SwtConstants.DEL_BUT_STS))))%>",

        // Default/initial filter values if available from backend for the first load
        defaultEntityId: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("defaultEntityId")))%>", // Example: Set by backend
        defaultCurrencyCode: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("defaultCurrencyCode")))%>", // Example
        defaultSearchMessageType: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("defaultSearchMessageType")))%>", // 'P' or 'M'
        defaultMessageType: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("defaultMessageType")))%>", // Predefined value
        defaultOtherMessageType: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("defaultOtherMessageType")))%>", // Manual value

        actionError: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("actionError")))%>" // Pass action errors
    };

    // JavaScript functions to be called by Angular via ExternalInterface
    function openLegacyWindow(url, windowName, features, isCascade) {
        // This openWindow function should be available from your included /angularJSUtils.jsp or similar global utility
        return openWindow(url, windowName, features, isCascade);
    }

    function openHelpWindow(printKey, screenTitle) {
        var helpUrl = buildPrintURL(printKey, screenTitle); // From /angularJSUtils.jsp
        if (helpUrl) {
            return openLegacyWindow(helpUrl, 'sectionprintdwindow', 'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no', 'true');
        }
        return null;
    }

    function jsPrintPage() { // Renamed to avoid conflict if 'printPage' is a global var
        // Logic for printing the current view, might involve calling a backend print method
        // For now, placeholder. Could use a generic print utility if available.
        alert("Print function to be implemented via ExternalInterface or specific backend call.");
    }

    function getLocalizedMessage(key) {
        var messages = {
            "confirm.delete": "<fmt:message key="confirm.delete"/>",
            "party.alert.pagination": "<fmt:message key="party.alert.pagination"/>" // Example, add more as needed
            // Add other keys required by Angular's SwtAlert here
        };
        return messages[key] || key;
    }

    // Callback from legacy popups to refresh Angular grid
    function refreshParentGrid() {
        if (window.CorrespondentAcctMaintenanceComponent && typeof window.CorrespondentAcctMaintenanceComponent.refreshGridData === 'function') {
            window.CorrespondentAcctMaintenanceComponent.refreshGridData();
        } else {
            console.warn("CorrespondentAcctMaintenanceComponent or refreshGridData method not found on window object.");
        }
    }
</script>
</head>

<body>
    <%@ include file="/angularJSUtils.jsp"%>
    <%@ include file="/angularscripts.jsp"%>

    <app-root></app-root> <%-- Angular app mounts here --%>
</body>
</html>
