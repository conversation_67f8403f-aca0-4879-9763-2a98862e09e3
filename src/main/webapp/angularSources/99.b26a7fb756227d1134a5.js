(window.webpackJsonp=window.webpackJsonp||[]).push([[99],{POaQ:function(t,e,i){"use strict";i.r(e);var s=i("CcnG"),n=i("mrSG"),a=i("447K"),o=i("ZYCi"),l=i("R1Kr"),r=function(t){function e(e,i){var s=t.call(this,i,e)||this;return s.commonService=e,s.element=i,s.moduleId="Predict",s.jsonReader=new a.L,s.inputData=new a.G(s.commonService),s.messageRPC=new a.G(s.commonService),s.rowsRPC=new a.G(s.commonService),s.reprocessRPC=new a.G(s.commonService),s.deleteRPC=new a.G(s.commonService),s.colWidth=new a.G(s.commonService),s.requestParams=[],s.actionMethod="",s.actionPath="",s.params=[],s.currentPage=1,s.maxPage=1,s.extraPages=0,s.selectedMessageSeqId="",s.seqIdArray=null,s.descending="false",s.baseURL=a.Wb.getBaseURL(),s.gridData=null,s.prevRawData=null,s.columnData=null,s.isFiltered=!1,s.filteredColumns=[],s.filteredColumn="",s.filteredValue="",s.selectedFilteredColumns=[],s.requested=!1,s.sendRequest=!0,s.fromPCM=null,s.versionNumber="1.0.0001",s.screenName="Interface Exceptions",s.releaseDate="17 March 2020",s.screenVersion=new a.V(s.commonService),s.logger=new a.R("Interface Monitor Screen",s.commonService.httpclient),s.swtAlert=new a.bb(e),s}return n.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){var t=this;instanceElement=this,this.inputExceptionsGrid=this.gridContainer.addChild(a.hb),this.inputExceptionsGrid.lockedColumnCount=1,this.inputExceptionsGrid.paginationComponent=this.pagination,this.inputExceptionsGrid.onPaginationChanged=this.paginationChanged.bind(this),this.inputExceptionsGrid.onSortChanged=this.globalSort.bind(this),this.inputExceptionsGrid.columnWidthChanged.subscribe(function(e){t.updateWidths(e)}),this.messageTypelbl.text="Interface:",this.messageStatuslbl.text="Message Status:",this.closeButton.label=a.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=a.Wb.getPredictMessage("tooltip.close",null)},e.prototype.onLoad=function(){var t,e=this;switch(this.createAboutMenu(),this.inputExceptionsGrid.onRowClick=function(t){e.obtainCell(t)},this.fromPCM=a.x.call("eval","fromPCM"),this.dictionary=this.getUrlParams(),this.messageType=this.dictionary.type,this.totalAvailableMessages=parseInt(this.dictionary.m,10),parseInt(this.dictionary.status,10)){case 1:t="FILTERED";break;case 2:t="BAD"}this.startDate=this.dictionary.fromDate,this.endDate=this.dictionary.toDate,this.currentPage=this.dictionary.p,this.messagesPerPage=this.dictionary.n,this.status=this.dictionary.status;var i=this.totalAvailableMessages/100;this.extraPages=this.totalAvailableMessages%100,this.extraPages>0&&(i=Math.ceil(i)),this.maxPage=i,i>1?(this.pageBox.visible=!0,this.pagination.maximum=Number(this.maxPage),this.pagination.value=Number(this.currentPage)):this.pageBox.visible=!1,this.messageTypelbl.text="Interface:",this.messageTypeValue.text=this.messageType,this.messageStatuslbl.text="Message Status:",this.messageStatusValue.text=t,this.params={fromPCM:this.fromPCM,fromDate:this.startDate,toDate:this.endDate,status:this.status,type:this.messageType,p:this.currentPage,n:this.messagesPerPage,m:this.totalAvailableMessages.toString()},this.actionPath="interfaceexceptions.do?",this.actionMethod="method=messagesData",this.rowsRPC.cbStart=this.startLoader.bind(this),this.rowsRPC.cbStop=this.stopLoader.bind(this),this.rowsRPC.cbResult=function(t){e.rowsRPCResult(t)},this.rowsRPC.cbFault=this.inputDataFault.bind(this),this.rowsRPC.encodeURL=!1,this.rowsRPC.url=this.baseURL+this.actionPath+this.actionMethod,this.sendRequest&&this.rowsRPC.send(this.params)},e.prototype.createAboutMenu=function(){this.screenVersion.loadScreenVersion(this,"Interface Exception Screen",this.versionNumber,this.releaseDate);var t=new a.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.height="400",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},e.prototype.rowsRPCResult=function(t){if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)){this.selectedMessageSeqId="",this.columnData=this.jsonReader.getColumnData(),this.gridData=this.jsonReader.getGridData(),this.totalAvailableMessages=Number(t.interfaceexceptions.grid.metadata.datapage.records_total);var e={columns:this.jsonReader.getColumnData()};this.inputExceptionsGrid.CustomGrid(e),this.jsonReader.getGridData().size>0?(this.inputExceptionsGrid.dataProvider=null,this.inputExceptionsGrid.gridData=this.jsonReader.getGridData(),this.inputExceptionsGrid.setRowSize=this.jsonReader.getRowSize(),this.inputExceptionsGrid.selectable=!0,this.inputExceptionsGrid.allowMultipleSelection=!0):(this.inputExceptionsGrid.dataProvider=[],this.inputExceptionsGrid.selectedIndex=-1),this.prevRecievedJSON=this.lastRecievedJSON}}else this.swtAlert.warning(this.jsonReader.getRequestReplyMessage(),"Error")},e.prototype.updateWidths=function(t){t.currentTarget.columns[t.columnIndex].width<115&&(t.currentTarget.columns[t.columnIndex].width=115),this.updateColumnWidths()},e.prototype.updateColumnWidths=function(){for(var t=this,e=[],i=this.inputExceptionsGrid.columnDefinitions,s=0;s<this.inputExceptionsGrid.columnDefinitions.length-1;s++)null!=i[s].field&&e.push(i[s].field+"="+i[s].width);this.colWidth.isBusy()&&this.colWidth.cancel(),this.actionPath="interfaceexceptions.do?",this.actionMethod="method=deleteRequest&amp;",this.colWidth.cbStart=this.startLoader.bind(this),this.colWidth.cbStop=this.stopLoader.bind(this),this.colWidth.cbResult=function(e){t.reprocessRPCResult(e)},this.colWidth.cbFault=this.inputDataFault.bind(this),this.colWidth.encodeURL=!1,this.colWidth.url=this.baseURL+this.actionPath+this.actionMethod;var n=new Object;n.width=e.join(","),n.fromPCM=this.fromPCM,this.sendRequest&&this.colWidth.send(n)},e.prototype.checkLegalDrag=function(t){if(t.newIndex<1){var e,i=[];e=t.currentTarget.columns;for(var s=0;s<e.length;s++)s!=t.newIndex&&i.push(e[s]),s==t.oldIndex&&i.push(e[t.newIndex]);t.currentTarget.columns=i}},e.prototype.filterCall=function(t){var e=[];if("--ALL--"!=t.target.selectedLabel)this.filteredValue=t.target.selectedLabel,this.filteredColumn=this.inputExceptionsGrid.filteredGridColumns,e[this.filteredColumn]=this.filteredValue,this.filteredColumns.push(e),this.selectedFilteredColumns[this.filteredColumn]=!0,this.isFiltered=!0;else{this.filteredColumn=this.inputExceptionsGrid.filteredGridColumns,this.filteredValue="",this.selectedFilteredColumns[this.filteredColumn]=!1;for(var i=0;i<this.filteredColumns.length;i++)if(this.filteredColumns[i].hasOwnProperty(this.filteredColumn)){this.filteredColumns.splice(i,1);break}this.filteredColumns.length<1&&(this.isFiltered=!1)}this.inputExceptionsGrid.dataProvider=this.filterData(this.gridData.children())},e.prototype.filtering=function(t){return t[this.filteredColumn]==this.filteredValue},e.prototype.filterData=function(t){},e.prototype.htmlEntities=function(t){try{return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/ /g,"&nbsp;")}catch(e){console.log("error",e,t)}},e.prototype.messageRPCResult=function(t){var e;this.messageForm.text="",e=(e=(e=String(t.interfaceexceptions.message)).split("$#$").join("\n")).split("&@&").join("&nbsp;");var i=l.pd.xml(e.split("&nbsp;").join(" "));i=this.htmlEntities(i),this.messageForm.htmlText=i},e.prototype.close=function(){a.x.call("close")},e.prototype.resizing=function(t){console.log("event :",t)},e.prototype.getUrlParams=function(){var t={},e=a.x.call("document_location_href").split("?")[1];if(e)for(var i=(e=e.split("#")[0]).split("&"),s=0;s<i.length;s++){var n=i[s].split("="),o=n[0],l=void 0===n[1]||n[1];if(o.match(/\[(\d+)?\]$/)){var r=o.replace(/\[(\d+)?\]/,"");if(t[r]||(t[r]=[]),o.match(/\[\d+\]$/)){var h=/\[(\d+)\]/.exec(o)[1];t[r][h]=l}else t[r].push(l)}else t[o]?t[o]&&"string"==typeof t[o]?(t[o]=[t[o]],t[o].push(l)):t[o].push(l):t[o]=l}return t},e.prototype.setRefresh=function(){a.x.call("refreshParent")},e.prototype.obtainCell=function(t){var e=this;if(this.seqIdArray=this.inputExceptionsGrid.selectedItems,this.actionPath="interfaceexceptions.do?",this.actionMethod="method=messageData",this.messageRPC.cbStart=this.startLoader.bind(this),this.messageRPC.cbStop=this.stopLoader.bind(this),this.messageRPC.cbResult=function(t){e.messageRPCResult(t)},this.messageRPC.cbFault=this.inputDataFault.bind(this),this.messageRPC.encodeURL=!1,this.messageRPC.url=this.baseURL+this.actionPath+this.actionMethod,this.seqIdArray.length>0)if(1==this.seqIdArray.length){var i=[];i.seqid=this.seqIdArray[0].msgid.content,i.fromPCM=this.fromPCM,this.sendRequest&&this.messageRPC.send(i)}else{for(var s="Multiple Messages Selected: ",n=0;n<this.seqIdArray.length;n++)s+="\n Message ID: "+String(this.seqIdArray[n].msgid.content);this.messageForm.htmlText=s.replace("\n","<br />")}else this.messageForm.text=""},e.prototype.keyDownPager=function(t){t.charCode==a.N.ENTER&&(this.currentPage=this.pagination.value,this.dictionary=this.getUrlParams(),this.params={fromPCM:this.fromPCM,fromDate:this.startDate,toDate:this.endDate,status:this.status,type:this.messageType,p:this.currentPage,n:this.messagesPerPage,m:this.totalAvailableMessages,desc:this.descending,order:this.sortingColumn},this.requested=!0,this.sendRequest&&this.rowsRPC.send(this.params))},e.prototype.paginationChanged=function(t){this.next()},e.prototype.next=function(){this.currentPage=this.pagination.value,this.dictionary=this.getUrlParams(),this.params={fromPCM:this.fromPCM,fromDate:this.startDate,toDate:this.endDate,status:this.status,type:this.messageType,p:this.currentPage,n:this.messagesPerPage,m:this.totalAvailableMessages,desc:this.descending,order:this.sortingColumn},this.requested=!0,this.sendRequest&&this.rowsRPC.send(this.params)},e.prototype.getParamsFromParent=function(){var t="";return this.inputExceptionsGrid.selectedIndex>-1&&(t=this.inputExceptionsGrid.selectedItem.spreadId.content),[{screenName:this.screenName,spreadId:t}]},e.prototype.reprocessMessages=function(){var t=this,e=[];if(this.actionPath="interfaceexceptions.do?",this.actionMethod="method=reprocessRequest&amp;",this.reprocessRPC.cbStart=this.startLoader.bind(this),this.reprocessRPC.cbStop=this.stopLoader.bind(this),this.reprocessRPC.cbResult=function(e){t.reprocessRPCResult(e)},this.reprocessRPC.cbFault=this.inputDataFault.bind(this),this.reprocessRPC.encodeURL=!1,this.reprocessRPC.url=this.baseURL+this.actionPath+this.actionMethod,0!=this.seqIdArray.length){for(var i=0;i<this.seqIdArray.length;i++)e.push(this.seqIdArray[i].msgid.content);this.sendRequest&&this.reprocessRPC.arraySend(e,"seqid",!1)}else this.swtAlert.show(a.Wb.getPredictMessage("label_noMessag",null),"Error")},e.prototype.globalSort=function(t){if(this.sendRequest){t.preventDefault();for(var e=0;e<this.inputExceptionsGrid.sorters.length;e++)this.sortingColumn=this.inputExceptionsGrid.sorters[e].columnId,this.descending=this.inputExceptionsGrid.sorters[e].direction?"true":"false";this.params={fromPCM:this.fromPCM,fromDate:this.startDate,toDate:this.endDate,status:this.status,type:this.messageType,p:this.currentPage,n:this.messagesPerPage,m:this.totalAvailableMessages.toString(),desc:this.descending,order:this.sortingColumn},this.requested=!0,this.rowsRPC.send(this.params)}},e.prototype.reprocessRPCResult=function(t){var e=t,i=new a.L;i.setInputJSON(e),this.messageForm.text="Update Response\n",this.messageForm.text+="--------------------\n\n",this.messageForm.text+=i.getRequestReplyMessage(),this.setRefresh(),this.totalAvailableMessages-=this.seqIdArray.length;var s=this.totalAvailableMessages/50;this.extraPages=this.totalAvailableMessages%50,this.extraPages>0&&s++,s<this.maxPage&&(this.maxPage=s,this.pagination.maximum=this.maxPage),this.currentPage>this.maxPage&&(this.currentPage--,this.pagination.value=this.currentPage),this.params={fromPCM:this.fromPCM,fromDate:this.startDate,toDate:this.endDate,status:this.status,type:this.messageType,p:this.currentPage,n:this.messagesPerPage,m:this.totalAvailableMessages.toString(),desc:this.descending,order:this.sortingColumn},this.requested=!0,this.sendRequest&&this.rowsRPC.send(this.params)},e.prototype.openDeleteAlert=function(){this.swtAlert.show(a.Wb.getPredictMessage("alert.interfaceExceptions.sure",null),"Delete Messages",a.c.OK|a.c.CANCEL,this,this.alertListener.bind(this),null,a.c.CANCEL)},e.prototype.alertListener=function(t){t.detail==a.c.OK&&this.deleteMessages()},e.prototype.deleteMessages=function(){var t=this,e=[];if(this.actionPath="interfaceexceptions.do?",this.actionMethod="method=deleteRequest&amp;",this.deleteRPC.cbStart=this.startLoader.bind(this),this.deleteRPC.cbStop=this.stopLoader.bind(this),this.deleteRPC.cbResult=function(e){t.reprocessRPCResult(e)},this.deleteRPC.cbFault=this.inputDataFault.bind(this),this.deleteRPC.encodeURL=!1,this.deleteRPC.url=this.baseURL+this.actionPath+this.actionMethod,0!=this.seqIdArray.length){for(var i=0;i<this.seqIdArray.length;i++)e.push(this.seqIdArray[i].msgid.content);this.sendRequest&&this.deleteRPC.arraySend(e,"seqid",!1)}else this.swtAlert.show(a.Wb.getPredictMessage("alert.interfaceExceptions.noMessage",null),a.Wb.getPredictMessage("label-error",null))},e.prototype.updatesResult=function(t){var e=t,i=new a.L;i.setInputJSON(e),i.getRequestReplyMessage()||this.swtAlert.show(""+i.getRequestReplyMessage(),a.Wb.getPredictMessage("alert-error",null))},e.prototype.startLoader=function(){this.loadingImage.visible=!0,this.pagination.enabled=!1,this.sendRequest=!1},e.prototype.stopLoader=function(){this.rowsRPC.isBusy()||this.messageRPC.isBusy()||this.reprocessRPC.isBusy()||this.deleteRPC.isBusy()||this.colWidth.isBusy()||(this.loadingImage.visible=!1,this.pagination.enabled=!0,this.sendRequest=!0)},e.prototype.keyDownEventHandler=function(t){var e=Object(a.ic.getFocus()).id;t.keyCode==a.N.ENTER&&("reprocessButton"==e?this.reprocessMessages():"closeButton"==e&&close())},e.prototype.inputDataFault=function(t){this.sendRequest=!0,this.pagination.enabled=!0},e.prototype.howVScrollToolTip=function(t,e){return"No of Records:"+this.totalAvailableMessages},e}(a.yb),h=[{path:"",component:r}],d=(o.l.forChild(h),function(){return function(){}}()),u=i("pMnS"),c=i("RChO"),p=i("t6HQ"),g=i("WFGK"),b=i("5FqG"),m=i("Ip0R"),R=i("gIcY"),f=i("t/Na"),C=i("sE5F"),P=i("OzfB"),w=i("T7CS"),y=i("S7LP"),x=i("6aHO"),v=i("WzUx"),S=i("A7o+"),I=i("zCE2"),M=i("Jg5P"),D=i("3R0m"),L=i("hhbb"),T=i("5rxC"),E=i("Fzqc"),A=i("21Lb"),q=i("hUWP"),G=i("3pJQ"),J=i("V9q+"),N=i("VDKW"),B=i("kXfT"),W=i("BGbe");i.d(e,"InterfaceExceptionModuleNgFactory",function(){return F}),i.d(e,"RenderType_InterfaceException",function(){return k}),i.d(e,"View_InterfaceException_0",function(){return _}),i.d(e,"View_InterfaceException_Host_0",function(){return V}),i.d(e,"InterfaceExceptionNgFactory",function(){return z});var F=s.Gb(d,[],function(t){return s.Qb([s.Rb(512,s.n,s.vb,[[8,[u.a,c.a,p.a,g.a,b.Cb,b.Pb,b.r,b.rc,b.s,b.Ab,b.Bb,b.Db,b.qd,b.Hb,b.k,b.Ib,b.Nb,b.Ub,b.yb,b.Jb,b.v,b.A,b.e,b.c,b.g,b.d,b.Kb,b.f,b.ec,b.Wb,b.bc,b.ac,b.sc,b.fc,b.lc,b.jc,b.Eb,b.Fb,b.mc,b.Lb,b.nc,b.Mb,b.dc,b.Rb,b.b,b.ic,b.Yb,b.Sb,b.kc,b.y,b.Qb,b.cc,b.hc,b.pc,b.oc,b.xb,b.p,b.q,b.o,b.h,b.j,b.w,b.Zb,b.i,b.m,b.Vb,b.Ob,b.Gb,b.Xb,b.t,b.tc,b.zb,b.n,b.qc,b.a,b.z,b.rd,b.sd,b.x,b.td,b.gc,b.l,b.u,b.ud,b.Tb,z]],[3,s.n],s.J]),s.Rb(4608,m.m,m.l,[s.F,[2,m.u]]),s.Rb(4608,R.c,R.c,[]),s.Rb(4608,R.p,R.p,[]),s.Rb(4608,f.j,f.p,[m.c,s.O,f.n]),s.Rb(4608,f.q,f.q,[f.j,f.o]),s.Rb(5120,f.a,function(t){return[t,new a.tb]},[f.q]),s.Rb(4608,f.m,f.m,[]),s.Rb(6144,f.k,null,[f.m]),s.Rb(4608,f.i,f.i,[f.k]),s.Rb(6144,f.b,null,[f.i]),s.Rb(4608,f.f,f.l,[f.b,s.B]),s.Rb(4608,f.c,f.c,[f.f]),s.Rb(4608,C.c,C.c,[]),s.Rb(4608,C.g,C.b,[]),s.Rb(5120,C.i,C.j,[]),s.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),s.Rb(4608,C.f,C.a,[]),s.Rb(5120,C.d,C.k,[C.h,C.f]),s.Rb(5120,s.b,function(t,e){return[P.j(t,e)]},[m.c,s.O]),s.Rb(4608,w.a,w.a,[]),s.Rb(4608,y.a,y.a,[]),s.Rb(4608,x.a,x.a,[s.n,s.L,s.B,y.a,s.g]),s.Rb(4608,v.c,v.c,[s.n,s.g,s.B]),s.Rb(4608,v.e,v.e,[v.c]),s.Rb(4608,S.l,S.l,[]),s.Rb(4608,S.h,S.g,[]),s.Rb(4608,S.c,S.f,[]),s.Rb(4608,S.j,S.d,[]),s.Rb(4608,S.b,S.a,[]),s.Rb(4608,S.k,S.k,[S.l,S.h,S.c,S.j,S.b,S.m,S.n]),s.Rb(4608,v.i,v.i,[[2,S.k]]),s.Rb(4608,v.r,v.r,[v.L,[2,S.k],v.i]),s.Rb(4608,v.t,v.t,[]),s.Rb(4608,v.w,v.w,[]),s.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),s.Rb(1073742336,m.b,m.b,[]),s.Rb(1073742336,R.n,R.n,[]),s.Rb(1073742336,R.l,R.l,[]),s.Rb(1073742336,I.a,I.a,[]),s.Rb(1073742336,M.a,M.a,[]),s.Rb(1073742336,R.e,R.e,[]),s.Rb(1073742336,D.a,D.a,[]),s.Rb(1073742336,S.i,S.i,[]),s.Rb(1073742336,v.b,v.b,[]),s.Rb(1073742336,f.e,f.e,[]),s.Rb(1073742336,f.d,f.d,[]),s.Rb(1073742336,C.e,C.e,[]),s.Rb(1073742336,L.b,L.b,[]),s.Rb(1073742336,T.b,T.b,[]),s.Rb(1073742336,P.c,P.c,[]),s.Rb(1073742336,E.a,E.a,[]),s.Rb(1073742336,A.d,A.d,[]),s.Rb(1073742336,q.c,q.c,[]),s.Rb(1073742336,G.a,G.a,[]),s.Rb(1073742336,J.a,J.a,[[2,P.g],s.O]),s.Rb(1073742336,N.b,N.b,[]),s.Rb(1073742336,B.a,B.a,[]),s.Rb(1073742336,W.b,W.b,[]),s.Rb(1073742336,a.Tb,a.Tb,[]),s.Rb(1073742336,d,d,[]),s.Rb(256,f.n,"XSRF-TOKEN",[]),s.Rb(256,f.o,"X-XSRF-TOKEN",[]),s.Rb(256,"config",{},[]),s.Rb(256,S.m,void 0,[]),s.Rb(256,S.n,void 0,[]),s.Rb(256,"popperDefaults",{},[]),s.Rb(1024,o.i,function(){return[[{path:"",component:r}]]},[])])}),O=[[""]],k=s.Hb({encapsulation:0,styles:O,data:{}});function _(t){return s.dc(0,[s.Zb(402653184,1,{_container:0}),s.Zb(402653184,2,{gridContainer:0}),s.Zb(402653184,3,{messageDetails:0}),s.Zb(402653184,4,{loadingImage:0}),s.Zb(402653184,5,{closeButton:0}),s.Zb(402653184,6,{helpIcon:0}),s.Zb(402653184,7,{messageTypelbl:0}),s.Zb(402653184,8,{messageTypeValue:0}),s.Zb(402653184,9,{messageStatuslbl:0}),s.Zb(402653184,10,{messageStatusValue:0}),s.Zb(402653184,11,{pageBox:0}),s.Zb(402653184,12,{pagination:0}),s.Zb(402653184,13,{messageForm:0}),(t()(),s.Jb(13,0,null,null,45,"SwtModule",[["height","700"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var s=!0,n=t.component;"creationComplete"===e&&(s=!1!==n.onLoad()&&s);return s},b.ad,b.hb)),s.Ib(14,4440064,null,0,a.yb,[s.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),s.Jb(15,0,null,0,43,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,b.od,b.vb)),s.Ib(16,4440064,null,0,a.ec,[s.r,a.i,s.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),s.Jb(17,0,null,0,15,"SwtCanvas",[["height","5%"],["width","100%"]],null,null,null,b.Nc,b.U)),s.Ib(18,4440064,null,0,a.db,[s.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),s.Jb(19,0,null,0,9,"HBox",[["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(20,4440064,null,0,a.C,[s.r,a.i],{width:[0,"width"]},null),(t()(),s.Jb(21,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","messageTypelbl"],["text","Interface:"]],null,null,null,b.Yc,b.fb)),s.Ib(22,4440064,[[7,4],["messageTypelbl",4]],0,a.vb,[s.r,a.i],{id:[0,"id"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),s.Jb(23,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","messageTypeValue"],["text",""]],null,null,null,b.Yc,b.fb)),s.Ib(24,4440064,[[8,4],["messageTypeValue",4]],0,a.vb,[s.r,a.i],{id:[0,"id"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),s.Jb(25,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","messageStatuslbl"],["text","Message Status:"]],null,null,null,b.Yc,b.fb)),s.Ib(26,4440064,[[9,4],["messageStatuslbl",4]],0,a.vb,[s.r,a.i],{id:[0,"id"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),s.Jb(27,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","messageStatusValue"]],null,null,null,b.Yc,b.fb)),s.Ib(28,4440064,[[10,4],["messageStatusValue",4]],0,a.vb,[s.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),s.Jb(29,0,null,0,3,"HBox",[["horizontalAlign","right"],["visible","false"]],null,null,null,b.Dc,b.K)),s.Ib(30,4440064,[[11,4],["pageBox",4]],0,a.C,[s.r,a.i],{horizontalAlign:[0,"horizontalAlign"],visible:[1,"visible"]},null),(t()(),s.Jb(31,0,null,0,1,"SwtCommonGridPagination",[],null,null,null,b.Qc,b.Y)),s.Ib(32,2211840,[[12,4],["pagination",4]],0,a.ib,[f.c,s.r],null,null),(t()(),s.Jb(33,0,null,0,9,"VBox",[["height","90%"],["width","100%"]],null,null,null,b.od,b.vb)),s.Ib(34,4440064,null,0,a.ec,[s.r,a.i,s.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),s.Jb(35,0,null,0,7,"VDividedBox",[["height","100%"],["id","vSplit"],["width","100%"]],null,[[null,"resize"]],function(t,e,i){var s=!0,n=t.component;"resize"===e&&(s=!1!==n.resizing(i)&&s);return s},b.pd,b.wb)),s.Ib(36,4440064,null,0,a.fc,[s.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},{resize_:"resize"}),(t()(),s.Jb(37,0,null,0,1,"SwtCanvas",[["class","top"],["height","50%"],["id","gridContainer"],["width","100%"]],null,null,null,b.Nc,b.U)),s.Ib(38,4440064,[[2,4],["gridContainer",4]],0,a.db,[s.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),s.Jb(39,0,null,1,3,"SwtCanvas",[["class","bottom"],["height","50%"],["id","messageDetails"],["width","100%"]],null,null,null,b.Nc,b.U)),s.Ib(40,4440064,[[3,4],["messageDetails",4]],0,a.db,[s.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),s.Jb(41,0,null,0,1,"SwtTextArea",[["editable","false"],["height","90%"],["id","messageForm"],["width","100%"]],null,null,null,b.jd,b.rb)),s.Ib(42,4440064,[[13,4],["messageForm",4]],0,a.Qb,[s.r,a.i,s.L],{id:[0,"id"],width:[1,"width"],height:[2,"height"],editable:[3,"editable"]},null),(t()(),s.Jb(43,0,null,0,15,"SwtCanvas",[["width","100%"]],null,null,null,b.Nc,b.U)),s.Ib(44,4440064,null,0,a.db,[s.r,a.i],{width:[0,"width"]},null),(t()(),s.Jb(45,0,null,0,13,"HBox",[["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(46,4440064,null,0,a.C,[s.r,a.i],{width:[0,"width"]},null),(t()(),s.Jb(47,0,null,0,3,"HBox",[["marginTop","6"],["paddingLeft","5"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(48,4440064,null,0,a.C,[s.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"],marginTop:[2,"marginTop"]},null),(t()(),s.Jb(49,0,null,0,1,"SwtButton",[["id","closeButton"],["label","Close"]],null,[[null,"click"]],function(t,e,i){var s=!0,n=t.component;"click"===e&&(s=!1!==n.close()&&s);return s},b.Mc,b.T)),s.Ib(50,4440064,[[5,4],["closeButton",4]],0,a.cb,[s.r,a.i],{id:[0,"id"],label:[1,"label"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),s.Jb(51,0,null,0,7,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,b.Dc,b.K)),s.Ib(52,4440064,null,0,a.C,[s.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),s.Jb(53,0,null,0,5,"HBox",[["horizontalAlign","right"],["marginTop","2"],["paddingRight","10"]],null,null,null,b.Dc,b.K)),s.Ib(54,4440064,null,0,a.C,[s.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"],marginTop:[2,"marginTop"]},null),(t()(),s.Jb(55,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,null,null,b.Wc,b.db)),s.Ib(56,4440064,null,0,a.rb,[s.r,a.i],{id:[0,"id"]},null),(t()(),s.Jb(57,0,null,0,1,"SwtLoadingImage",[["id","loadingImage"]],null,null,null,b.Zc,b.gb)),s.Ib(58,114688,[[4,4],["loadingImage",4]],0,a.xb,[s.r],null,null)],function(t,e){t(e,14,0,"100%","700");t(e,16,0,"100%","100%","5","5","5","5");t(e,18,0,"100%","5%");t(e,20,0,"100%");t(e,22,0,"messageTypelbl","Interface:","bold");t(e,24,0,"messageTypeValue","","normal");t(e,26,0,"messageStatuslbl","Message Status:","bold");t(e,28,0,"messageStatusValue","normal");t(e,30,0,"right","false"),t(e,32,0);t(e,34,0,"100%","90%");t(e,36,0,"vSplit","100%","100%");t(e,38,0,"gridContainer","100%","50%");t(e,40,0,"messageDetails","100%","50%");t(e,42,0,"messageForm","100%","90%","false");t(e,44,0,"100%");t(e,46,0,"100%");t(e,48,0,"100%","5","6");t(e,50,0,"closeButton","Close",!0);t(e,52,0,"right","10");t(e,54,0,"right","10","2");t(e,56,0,"helpIcon"),t(e,58,0)},null)}function V(t){return s.dc(0,[(t()(),s.Jb(0,0,null,null,1,"app-interface-exception",[],null,null,null,_,k)),s.Ib(1,4440064,null,0,r,[a.i,s.r],null,null)],function(t,e){t(e,1,0)},null)}var z=s.Fb("app-interface-exception",r,V,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);