(window.webpackJsonp=window.webpackJsonp||[]).push([[108],{"pl6/":function(l,e,t){"use strict";t.r(e);var i=t("CcnG"),n=t("mrSG"),a=t("447K"),o=t("ZYCi"),r=function(l){function e(e,t){var i=l.call(this,t,e)||this;return i.commonService=e,i.element=t,i.jsonReader=new a.L,i.inputData=new a.G(i.commonService),i.baseURL=a.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.requestParams=[],i.logger=null,i.text1="",i.text2="",i.isCompared=!1,i.selectedLang="plaintext",i.selectedTheme="vs",i.languages=["bat","c","coffeescript","cpp","csharp","csp","css","dockerfile","fsharp","go","handlebars","html","ini","java","javascript","json","less","lua","markdown","msdax","mysql","objective-c","pgsql","php","plaintext","postiats","powershell","pug","python","r","razor","redis","redshift","ruby","rust","sb","scss","sol","sql","st","swift","typescript","vb","xml","yaml"],i.themes=[{value:"vs",name:"Visual Studio"},{value:"vs-dark",name:"Visual Studio Dark"},{value:"hc-black",name:"High Contrast Dark"}],i.inputOptions={theme:"vs",language:"plaintext",minimap:{enabled:!1},scrollbar:{useShadows:!1,verticalHasArrows:!1,horizontalHasArrows:!1,vertical:"hidden",horizontal:"hidden"}},i.diffOptions={theme:"vs",language:"plaintext",readOnly:!0,renderSideBySide:!0},i.originalModel={code:"",language:"plaintext"},i.modifiedModel={code:"",language:"plaintext"},i.logger=new a.R("Maintenance Log View Maintenance",i.commonService.httpclient),i}return n.d(e,l),e.prototype.ngOnInit=function(){var l=0;try{this.viewLogGrid=this.viewLogGridContainer.addChild(a.hb),l=10,this.closeButton.label=a.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=a.Wb.getPredictMessage("tooltip.close",null),this.facilityLbl.text=a.Wb.getPredictMessage("maintenanceLogView.facility",null),this.dateLbl.text=a.Wb.getPredictMessage("maintenanceLogView.date",null),this.ipAddressLbl.text=a.Wb.getPredictMessage("maintenanceLogView.ipAddress",null),this.userLbl.text=a.Wb.getPredictMessage("maintenanceLogView.user",null),this.recordRefLbl.text=a.Wb.getPredictMessage("maintenanceLogView.recordRef",null),this.actionLbl.text=a.Wb.getPredictMessage("maintenanceLogView.action",null),this.fullDetailsLbl.text=a.Wb.getPredictMessage("maintenanceLogView.fullDetails",null),this.oldValLbl.text=a.Wb.getPredictMessage("maintenanceLogView.oldVal",null),this.newValLbl.text=a.Wb.getPredictMessage("maintenanceLogView.newVal",null)}catch(e){this.logger.error("method [ngOnInit] - error: ",e,"errorLocation: ",l),a.Wb.logError(e,a.Wb.PREDICT_MODULE_ID,"MaintenanceLogView.ts","ngOnInit",l)}},e.prototype.onLoad=function(){var l=this;this.requestParams=[];var e=0;try{var t=a.x.call("eval","logDate");e=10;var i=a.x.call("eval","userId");e=20;var n=a.x.call("eval","ipAddress");e=30;var o=a.x.call("eval","tableName");e=40;var r=a.x.call("eval","reference");e=50;var d=a.x.call("eval","action");e=60,this.menuAccessId=a.x.call("eval","menuAccessId"),e=70,this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),e=80,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){l.inputDataResult(e)},e=90,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="maintenancelog.do?",this.actionMethod="method=displayViewLog",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.userId=i,this.requestParams.logDate=t,this.requestParams.ipAddress=n,this.requestParams.tableName=o,this.requestParams.reference=r,e=100,this.requestParams.action=d,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,e=110,this.inputData.send(this.requestParams),this.viewLogGrid.onRowClick=function(t){e=120,l.cellClickEventHandler(t)},this.vDivider.DIVIDER_DRAG_COMPLETE.subscribe(function(l){window.dispatchEvent(new Event("resize"))})}catch(u){this.logger.error("method [onLoad] - error: ",u,"errorLocation: ",e),a.Wb.logError(u,a.Wb.PREDICT_MODULE_ID,"MaintenanceLogView.ts","onLoad",e)}},e.prototype.cellClickEventHandler=function(l){var e=0;try{this.viewLogGrid.selectedIndex>=0?(e=10,this.fromValue=this.viewLogGrid.selectedItem.changedFrom.content,e=20,this.toValue=this.viewLogGrid.selectedItem.changedTo.content,e=30,this.onCompare()):(e=40,this.fromValue="",this.toValue="",this.onCompare())}catch(t){this.logger.error("method [cellClickEventHandler] - error: ",t,"errorLocation: ",e),a.Wb.logError(t,a.Wb.PREDICT_MODULE_ID,"MaintenanceLogView.ts","cellClickEventHandler",e)}},e.prototype.onCompare=function(){var l=0;try{this.originalModel=Object.assign({},this.originalModel,{code:this.fromValue}),l=10,this.modifiedModel=Object.assign({},this.originalModel,{code:this.toValue}),l=20,this.isCompared=!0,window.scrollTo(0,0)}catch(e){this.logger.error("method [onCompare] - error: ",e,"errorLocation: ",l),a.Wb.logError(e,a.Wb.PREDICT_MODULE_ID,"MaintenanceLogView.ts","onCompare",l)}},e.prototype.inputDataResult=function(l){var e=0;try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=l,this.jsonReader.setInputJSON(this.lastRecievedJSON),e=10,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&!this.jsonReader.isDataBuilding()){e=20;var t=this.jsonReader.getSingletons().logDate;this.dateVal.text=t;var i=this.jsonReader.getSingletons().userId;this.userIdVal.text=i;var n=this.jsonReader.getSingletons().userName;this.userNameVal.text=n?"<"+n+">":"";var o=this.jsonReader.getSingletons().ipAddress;this.ipAddressVal.text=o;var r=this.jsonReader.getSingletons().tableName;this.facilityVal.text=r;var d=this.jsonReader.getSingletons().reference;this.recordRefVal.text=d;var u=this.getActionDesc(this.jsonReader.getSingletons().action);this.actionVal.text=u,e=30;var h={columns:this.lastRecievedJSON.MaintLogView.maintLogViewGrid.metadata.columns};e=40,this.viewLogGrid.CustomGrid(h),e=50;var s=this.lastRecievedJSON.MaintLogView.maintLogViewGrid.rows;if(e=60,s.size>0){for(var b=0;b<s.size;b++)s.row.length||(s.row=[s.row]),e=70,s.row[b].changedFrom.content=s.row[b].changedFrom.content?a.t.decode64(s.row[b].changedFrom.content):"",e=80,s.row[b].changedTo.content=s.row[b].changedTo.content?a.t.decode64(s.row[b].changedTo.content):"";this.viewLogGrid.gridData=s,e=90,this.viewLogGrid.setRowSize=this.jsonReader.getRowSize(),e=100,this.viewLogGrid.refresh()}else this.viewLogGrid.gridData={size:0,row:[]};this.prevRecievedJSON=this.lastRecievedJSON}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error")}catch(c){this.logger.error("method [inputDataResult] - error: ",c,"errorLocation: ",e),a.Wb.logError(c,a.Wb.PREDICT_MODULE_ID,"MaintenanceLogView.ts","inputDataResult",e)}},e.prototype.getActionDesc=function(l){var e=null;switch(l){case"I":e="Added";break;case"U":e="Changed";break;case"D":e="Deleted"}return e},e.prototype.closeHandler=function(){window.close()},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(l){this._invalidComms=l.fault.faultString+"\n"+l.fault.faultCode+"\n"+l.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e}(a.yb),d=[{path:"",component:r}],u=(o.l.forChild(d),function(){return function(){}}()),h=t("pMnS"),s=t("RChO"),b=t("t6HQ"),c=t("WFGK"),g=t("5FqG"),w=t("Ip0R"),m=t("gIcY"),p=t("t/Na"),f=t("sE5F"),v=t("OzfB"),R=t("T7CS"),L=t("S7LP"),I=t("6aHO"),V=t("WzUx"),C=t("A7o+"),J=t("zCE2"),y=t("Jg5P"),D=t("3R0m"),S=t("hhbb"),W=t("5rxC"),A=t("Fzqc"),x=t("21Lb"),M=t("hUWP"),G=t("3pJQ"),O=t("V9q+"),k=t("VDKW"),B=t("kXfT"),_=t("BGbe"),T=t("WxzH"),N=t("i/5A");t.d(e,"MaintenanceLogViewModuleNgFactory",function(){return P}),t.d(e,"RenderType_MaintenanceLogView",function(){return j}),t.d(e,"View_MaintenanceLogView_0",function(){return H}),t.d(e,"View_MaintenanceLogView_Host_0",function(){return z}),t.d(e,"MaintenanceLogViewNgFactory",function(){return q});var P=i.Gb(u,[],function(l){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[h.a,s.a,b.a,c.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,q]],[3,i.n],i.J]),i.Rb(4608,w.m,w.l,[i.F,[2,w.u]]),i.Rb(4608,m.c,m.c,[]),i.Rb(4608,m.p,m.p,[]),i.Rb(4608,p.j,p.p,[w.c,i.O,p.n]),i.Rb(4608,p.q,p.q,[p.j,p.o]),i.Rb(5120,p.a,function(l){return[l,new a.tb]},[p.q]),i.Rb(4608,p.m,p.m,[]),i.Rb(6144,p.k,null,[p.m]),i.Rb(4608,p.i,p.i,[p.k]),i.Rb(6144,p.b,null,[p.i]),i.Rb(4608,p.f,p.l,[p.b,i.B]),i.Rb(4608,p.c,p.c,[p.f]),i.Rb(4608,f.c,f.c,[]),i.Rb(4608,f.g,f.b,[]),i.Rb(5120,f.i,f.j,[]),i.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),i.Rb(4608,f.f,f.a,[]),i.Rb(5120,f.d,f.k,[f.h,f.f]),i.Rb(5120,i.b,function(l,e){return[v.j(l,e)]},[w.c,i.O]),i.Rb(4608,R.a,R.a,[]),i.Rb(4608,L.a,L.a,[]),i.Rb(4608,I.a,I.a,[i.n,i.L,i.B,L.a,i.g]),i.Rb(4608,V.c,V.c,[i.n,i.g,i.B]),i.Rb(4608,V.e,V.e,[V.c]),i.Rb(4608,C.l,C.l,[]),i.Rb(4608,C.h,C.g,[]),i.Rb(4608,C.c,C.f,[]),i.Rb(4608,C.j,C.d,[]),i.Rb(4608,C.b,C.a,[]),i.Rb(4608,C.k,C.k,[C.l,C.h,C.c,C.j,C.b,C.m,C.n]),i.Rb(4608,V.i,V.i,[[2,C.k]]),i.Rb(4608,V.r,V.r,[V.L,[2,C.k],V.i]),i.Rb(4608,V.t,V.t,[]),i.Rb(4608,V.w,V.w,[]),i.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),i.Rb(1073742336,w.b,w.b,[]),i.Rb(1073742336,m.n,m.n,[]),i.Rb(1073742336,m.l,m.l,[]),i.Rb(1073742336,J.a,J.a,[]),i.Rb(1073742336,y.a,y.a,[]),i.Rb(1073742336,m.e,m.e,[]),i.Rb(1073742336,D.a,D.a,[]),i.Rb(1073742336,C.i,C.i,[]),i.Rb(1073742336,V.b,V.b,[]),i.Rb(1073742336,p.e,p.e,[]),i.Rb(1073742336,p.d,p.d,[]),i.Rb(1073742336,f.e,f.e,[]),i.Rb(1073742336,S.b,S.b,[]),i.Rb(1073742336,W.b,W.b,[]),i.Rb(1073742336,v.c,v.c,[]),i.Rb(1073742336,A.a,A.a,[]),i.Rb(1073742336,x.d,x.d,[]),i.Rb(1073742336,M.c,M.c,[]),i.Rb(1073742336,G.a,G.a,[]),i.Rb(1073742336,O.a,O.a,[[2,v.g],i.O]),i.Rb(1073742336,k.b,k.b,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,_.b,_.b,[]),i.Rb(1073742336,a.Tb,a.Tb,[]),i.Rb(1073742336,T.b,T.b,[]),i.Rb(1073742336,u,u,[]),i.Rb(256,p.n,"XSRF-TOKEN",[]),i.Rb(256,p.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,C.m,void 0,[]),i.Rb(256,C.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,o.i,function(){return[[{path:"",component:r}]]},[])])}),E=[[".monaco-editor .margin-view-overlays .cldr{width:0!important}.col-md-12,.row{height:100%!important}.editor-container{height:100%!important;border:2px solid #beb9b9!important}.diffOverview,.diffViewport,.monaco-editor.modified-in-monaco-diff-editor.no-user-select.vs,.monaco-editor.original-in-monaco-diff-editor.no-user-select.vs,.monaco-scrollable-element.editor-scrollable.vs,.overflow-guard,.view-lines,canvas.decorationsOverviewRuler,div.invisible.scrollbar.vertical,div.slider{height:100%!important}"]],j=i.Hb({encapsulation:2,styles:E,data:{}});function H(l){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{loadingImage:0}),i.Zb(402653184,3,{viewLogGridContainer:0}),i.Zb(402653184,4,{closeButton:0}),i.Zb(402653184,5,{facilityLbl:0}),i.Zb(402653184,6,{dateLbl:0}),i.Zb(402653184,7,{ipAddressLbl:0}),i.Zb(402653184,8,{userLbl:0}),i.Zb(402653184,9,{recordRefLbl:0}),i.Zb(402653184,10,{actionLbl:0}),i.Zb(402653184,11,{fullDetailsLbl:0}),i.Zb(402653184,12,{oldValLbl:0}),i.Zb(402653184,13,{newValLbl:0}),i.Zb(402653184,14,{facilityVal:0}),i.Zb(402653184,15,{dateVal:0}),i.Zb(402653184,16,{ipAddressVal:0}),i.Zb(402653184,17,{userIdVal:0}),i.Zb(402653184,18,{userNameVal:0}),i.Zb(402653184,19,{recordRefVal:0}),i.Zb(402653184,20,{actionVal:0}),i.Zb(402653184,21,{vDivider:0}),(l()(),i.Jb(21,0,null,null,105,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(l,e,t){var i=!0,n=l.component;"creationComplete"===e&&(i=!1!==n.onLoad()&&i);return i},g.ad,g.hb)),i.Ib(22,4440064,null,0,a.yb,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(l()(),i.Jb(23,0,null,0,103,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),i.Ib(24,4440064,null,0,a.ec,[i.r,a.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(l()(),i.Jb(25,0,null,0,65,"SwtCanvas",[["height","110"],["minWidth","640"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(26,4440064,null,0,a.db,[i.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(l()(),i.Jb(27,0,null,0,63,"Grid",[["height","100%"],["width","100%"]],null,null,null,g.Cc,g.H)),i.Ib(28,4440064,null,0,a.z,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(29,0,null,0,9,"GridRow",[["height","20"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(30,4440064,null,0,a.B,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(31,0,null,0,3,"GridItem",[["width","140"]],null,null,null,g.Ac,g.I)),i.Ib(32,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(33,0,null,0,1,"SwtLabel",[["id","facilityLbl"]],null,null,null,g.Yc,g.fb)),i.Ib(34,4440064,[[5,4],["facilityLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"]},null),(l()(),i.Jb(35,0,null,0,3,"GridItem",[["width","220"]],null,null,null,g.Ac,g.I)),i.Ib(36,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(37,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","facilityVal"]],null,null,null,g.Yc,g.fb)),i.Ib(38,4440064,[[14,4],["facilityVal",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(39,0,null,0,17,"GridRow",[["height","20"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(40,4440064,null,0,a.B,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(41,0,null,0,3,"GridItem",[["width","140"]],null,null,null,g.Ac,g.I)),i.Ib(42,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(43,0,null,0,1,"SwtLabel",[["id","dateLbl"]],null,null,null,g.Yc,g.fb)),i.Ib(44,4440064,[[6,4],["dateLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"]},null),(l()(),i.Jb(45,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),i.Ib(46,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(47,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","dateVal"]],null,null,null,g.Yc,g.fb)),i.Ib(48,4440064,[[15,4],["dateVal",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(49,0,null,0,3,"GridItem",[["width","100"]],null,null,null,g.Ac,g.I)),i.Ib(50,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(51,0,null,0,1,"SwtLabel",[["id","ipAddressLbl"]],null,null,null,g.Yc,g.fb)),i.Ib(52,4440064,[[7,4],["ipAddressLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"]},null),(l()(),i.Jb(53,0,null,0,3,"GridItem",[["width","220"]],null,null,null,g.Ac,g.I)),i.Ib(54,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(55,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","ipAddressVal"]],null,null,null,g.Yc,g.fb)),i.Ib(56,4440064,[[16,4],["ipAddressVal",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(57,0,null,0,13,"GridRow",[["height","20"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(58,4440064,null,0,a.B,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(59,0,null,0,3,"GridItem",[["width","140"]],null,null,null,g.Ac,g.I)),i.Ib(60,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(61,0,null,0,1,"SwtLabel",[["id","userLbl"]],null,null,null,g.Yc,g.fb)),i.Ib(62,4440064,[[8,4],["userLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"]},null),(l()(),i.Jb(63,0,null,0,3,"GridItem",[["width","100"]],null,null,null,g.Ac,g.I)),i.Ib(64,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(65,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","userIdVal"]],null,null,null,g.Yc,g.fb)),i.Ib(66,4440064,[[17,4],["userIdVal",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(67,0,null,0,3,"GridItem",[["width","220"]],null,null,null,g.Ac,g.I)),i.Ib(68,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(69,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","userNameVal"]],null,null,null,g.Yc,g.fb)),i.Ib(70,4440064,[[18,4],["userNameVal",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(71,0,null,0,9,"GridRow",[["height","20"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(72,4440064,null,0,a.B,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(73,0,null,0,3,"GridItem",[["width","140"]],null,null,null,g.Ac,g.I)),i.Ib(74,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(75,0,null,0,1,"SwtLabel",[["id","recordRefLbl"]],null,null,null,g.Yc,g.fb)),i.Ib(76,4440064,[[9,4],["recordRefLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"]},null),(l()(),i.Jb(77,0,null,0,3,"GridItem",[["width","220"]],null,null,null,g.Ac,g.I)),i.Ib(78,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(79,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","recordRefVal"]],null,null,null,g.Yc,g.fb)),i.Ib(80,4440064,[[19,4],["recordRefVal",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(81,0,null,0,9,"GridRow",[["height","20"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(82,4440064,null,0,a.B,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(83,0,null,0,3,"GridItem",[["width","140"]],null,null,null,g.Ac,g.I)),i.Ib(84,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(85,0,null,0,1,"SwtLabel",[["id","actionLbl"]],null,null,null,g.Yc,g.fb)),i.Ib(86,4440064,[[10,4],["actionLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"]},null),(l()(),i.Jb(87,0,null,0,3,"GridItem",[["width","220"]],null,null,null,g.Ac,g.I)),i.Ib(88,4440064,null,0,a.A,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(89,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","actionVal"]],null,null,null,g.Yc,g.fb)),i.Ib(90,4440064,[[20,4],["actionVal",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(91,0,null,0,23,"VDividedBox",[["height","100%"],["minHeight","250"],["width","100%"]],null,null,null,g.pd,g.wb)),i.Ib(92,4440064,[[21,4],["vDivider",4]],0,a.fc,[i.r,a.i],{width:[0,"width"],height:[1,"height"],minHeight:[2,"minHeight"]},null),(l()(),i.Jb(93,0,null,0,3,"SwtCanvas",[["class","top"],["height","50%"],["minWidth","640"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(94,4440064,null,0,a.db,[i.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(l()(),i.Jb(95,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","viewLogGridContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(96,4440064,[[3,4],["viewLogGridContainer",4]],0,a.db,[i.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(l()(),i.Jb(97,0,null,1,17,"SwtCanvas",[["class","bottom"],["height","50%"],["minWidth","640"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(98,4440064,null,0,a.db,[i.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(l()(),i.Jb(99,0,null,0,15,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),i.Ib(100,4440064,null,0,a.ec,[i.r,a.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(101,0,null,0,3,"HBox",[["height","20"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(102,4440064,null,0,a.C,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(103,0,null,0,1,"SwtLabel",[["id","fullDetailsLbl"]],null,null,null,g.Yc,g.fb)),i.Ib(104,4440064,[[11,4],["fullDetailsLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"]},null),(l()(),i.Jb(105,0,null,0,5,"HBox",[["height","20"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(106,4440064,null,0,a.C,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(107,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","oldValLbl"],["width","49%"]],null,null,null,g.Yc,g.fb)),i.Ib(108,4440064,[[12,4],["oldValLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(l()(),i.Jb(109,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","newValLbl"],["width","51%"]],null,null,null,g.Yc,g.fb)),i.Ib(110,4440064,[[13,4],["newValLbl",4]],0,a.vb,[i.r,a.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(l()(),i.Jb(111,0,null,0,3,"div",[["class","row"],["height","100%"]],null,null,null,null,null)),(l()(),i.Jb(112,0,null,null,2,"div",[["class","col-md-12 editor editor-size"],["height","100%"]],null,null,null,null,null)),(l()(),i.Jb(113,0,null,null,1,"ngx-monaco-diff-editor",[["id","diffeditor"],["style","height: 100%;"]],null,null,null,N.b,N.a)),i.Ib(114,4374528,null,0,T.d,[T.c],{options:[0,"options"],originalModel:[1,"originalModel"],modifiedModel:[2,"modifiedModel"]},null),(l()(),i.Jb(115,0,null,0,11,"SwtCanvas",[["height","35"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(116,4440064,null,0,a.db,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(117,0,null,0,9,"HBox",[["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(118,4440064,null,0,a.C,[i.r,a.i],{width:[0,"width"]},null),(l()(),i.Jb(119,0,null,0,3,"HBox",[["paddingLeft","5"],["width","90%"]],null,null,null,g.Dc,g.K)),i.Ib(120,4440064,null,0,a.C,[i.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(l()(),i.Jb(121,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(l,e,t){var i=!0,n=l.component;"click"===e&&(i=!1!==n.closeHandler()&&i);return i},g.Mc,g.T)),i.Ib(122,4440064,[[4,4],["closeButton",4]],0,a.cb,[i.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(l()(),i.Jb(123,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingLeft","5"],["width","10%"]],null,null,null,g.Dc,g.K)),i.Ib(124,4440064,null,0,a.C,[i.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(l()(),i.Jb(125,0,null,0,1,"SwtLoadingImage",[],null,null,null,g.Zc,g.gb)),i.Ib(126,114688,[[2,4],["loadingImage",4]],0,a.xb,[i.r],null,null)],function(l,e){var t=e.component;l(e,22,0,"100%","100%");l(e,24,0,"100%","100%","5","5","5");l(e,26,0,"100%","110","640");l(e,28,0,"100%","100%");l(e,30,0,"100%","20");l(e,32,0,"140");l(e,34,0,"facilityLbl");l(e,36,0,"220");l(e,38,0,"facilityVal","normal");l(e,40,0,"100%","20");l(e,42,0,"140");l(e,44,0,"dateLbl");l(e,46,0,"250");l(e,48,0,"dateVal","normal");l(e,50,0,"100");l(e,52,0,"ipAddressLbl");l(e,54,0,"220");l(e,56,0,"ipAddressVal","normal");l(e,58,0,"100%","20");l(e,60,0,"140");l(e,62,0,"userLbl");l(e,64,0,"100");l(e,66,0,"userIdVal","normal");l(e,68,0,"220");l(e,70,0,"userNameVal","normal");l(e,72,0,"100%","20");l(e,74,0,"140");l(e,76,0,"recordRefLbl");l(e,78,0,"220");l(e,80,0,"recordRefVal","normal");l(e,82,0,"100%","20");l(e,84,0,"140");l(e,86,0,"actionLbl");l(e,88,0,"220");l(e,90,0,"actionVal","normal");l(e,92,0,"100%","100%","250");l(e,94,0,"100%","50%","640");l(e,96,0,"viewLogGridContainer","canvasWithGreyBorder","100%","100%","false");l(e,98,0,"100%","50%","640");l(e,100,0,"100%","100%");l(e,102,0,"100%","20");l(e,104,0,"fullDetailsLbl");l(e,106,0,"100%","20");l(e,108,0,"oldValLbl","49%","normal");l(e,110,0,"newValLbl","51%","normal"),l(e,114,0,t.diffOptions,t.originalModel,t.modifiedModel);l(e,116,0,"100%","35");l(e,118,0,"100%");l(e,120,0,"90%","5");l(e,122,0,"closeButton",!0);l(e,124,0,"right","10%","5"),l(e,126,0)},null)}function z(l){return i.dc(0,[(l()(),i.Jb(0,0,null,null,1,"app-maintenance-log-view",[],null,null,null,H,j)),i.Ib(1,4440064,null,0,r,[a.i,i.r],null,null)],function(l,e){l(e,1,0)},null)}var q=i.Fb("app-maintenance-log-view",r,z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu",languages:"languages",themes:"themes"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize",selectedLang:"selectedLang",selectedTheme:"selectedTheme"},[])}}]);