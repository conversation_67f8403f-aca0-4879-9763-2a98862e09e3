(window.webpackJsonp=window.webpackJsonp||[]).push([[121],{dSjS:function(t,e,i){"use strict";i.r(e);var a=i("CcnG"),r=i("mrSG"),s=i("ZYCi"),l=i("447K"),n=i("EUZL"),d=i("EVdn"),o=function(t){function e(e,i){var a=t.call(this,i,e)||this;return a.commonService=e,a.element=i,a.ordertData=new l.G(a.commonService),a.jsonReader=new l.L,a.inputData=new l.G(a.commonService),a.baseURL=l.Wb.getBaseURL(),a.actionMethod="",a.actionPath="",a.requestParams=[],a.named="Paste it",a.importedColumns=[],a.defaultColumns=[],a.data=[],a.excelData=[],a.editedRow=[],a.initialdata=[],a.pastedData=[],a.gridRows=[],a.validRow=[],a.inValidRow=[],a.moduleId="Predict",a.clipboadDate="",a.callParent=!1,a.errorMsg=[],a.finalMsg="",a.currencyAccess=!1,a.accountAccess=!1,a.isSourceFormat=!1,a.isPreviewGrid=!1,a.editableFlag=!1,a.provider="",a.sendFlag=!0,a.savedSetting=[],a.newOrderColumns=[],a.columnDefinitionsTempArray=[],a.columnOrderChanged=!1,a.options=[],a.orderChangedRows=[],a.pasteFlag=!1,a.newHeaderGridData=[],a.headerChanged=!1,a.updatedHeader=[],a.resetFlag=!1,a.srcGridChanged=!1,a.stillCheckingFlag=!1,a.fromUpdateFlag=!1,a.uploadedFileName="",a.excelValFlag=!0,a.importedFileHeaders=[],a.sourceHeaderProvider=[{type:"",value:"",selected:0,content:""}],a.ScreenColumns=["Entity_ID","Ccy","Account_ID","Amount","Value_Date","Reference","Sign","Pred_Status","Book_Code","Post_Date","Product_Type","CounterParty_ID","Cparty_Text","Match_Party"],a.binarystrGlobal="",a.swtAlert=new l.bb(e),window.Main=a,a}return r.d(e,t),e.prototype.ngOnInit=function(){this.headersGrid=this.headersGridContainer.addChild(l.hb),this.sourceGrid=this.sourceFormatGridContainer.addChild(l.Ub),this.mvtGrid=this.mvtGridContainer.addChild(l.hb),this.headersGrid.hideHorizontalScrollBar=!0,this.headersGrid.hideVerticalScrollBar=!0,this.headersGrid.listenHorizontalScrollEvent=!0,this.headersGrid.editable=!0,this.sourceGrid.fireHorizontalScrollEvent=!0,this.sourceGrid.forceNoAlignRightTotalGrid=!0,this.sourceGrid.editable=!0,this.sourceGrid.selectable=!1,this.mvtGrid.editable=!0,this.dateSource.text=l.Wb.getPredictMessage("preAdviceInput.dataSource",null),this.headerLabel.text=l.Wb.getPredictMessage("preAdviceInput.headerLabel",null),this.headerValue.text=l.Wb.getPredictMessage("preAdviceInput.headerValue",null),this.headerType.text=l.Wb.getPredictMessage("preAdviceInput.headerType",null),this.sourceFormat.text=l.Wb.getPredictMessage("preAdviceInput.sourceFormat",null),this.importStatusLbl.text=l.Wb.getPredictMessage("preAdviceInput.importStatusLbl",null),this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importInitStatus",null),this.showLabel.text=l.Wb.getPredictMessage("preAdviceInput.showLabel",null),this.fieldSet1.legendText=l.Wb.getPredictMessage("preAdviceInput.fieldSetImport1.legendText",null),this.fieldSet2.legendText=l.Wb.getPredictMessage("preAdviceInput.fieldSetImport2.legendText",null),this.chkHeader.label=l.Wb.getPredictMessage("preAdviceInput.chkHeader",null),this.allRows.label=l.Wb.getPredictMessage("preAdviceInput.allRows",null),this.validRows.label=l.Wb.getPredictMessage("preAdviceInput.validRows",null),this.invalidRows.label=l.Wb.getPredictMessage("preAdviceInput.invalidRows",null),this.saveButton.label=l.Wb.getPredictMessage("button.save",null),this.saveButton.toolTip=l.Wb.getPredictMessage("tooltip.SaveChanges",null),this.closeButton.label=l.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=l.Wb.getPredictMessage("tooltip.close",null),this.resetButton.label=l.Wb.getPredictMessage("button.reset",null),this.resetButton.toolTip=l.Wb.getPredictMessage("tooltip.reset",null),this.loadButton.label=l.Wb.getPredictMessage("button.load",null),this.loadButton.toolTip=l.Wb.getPredictMessage("tooltip.load",null),this.uploadImage.toolTip=l.Wb.getPredictMessage("tooltip.chooseFile",null)},e.prototype.onLoad=function(){var t,e=this;this.requestParams=[],this.menuAccessId=l.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=displayDataDef",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.draggable=!this.chkHeader.selected,this.requestParams.fromMethod="onLoad",this.requestParams.dataType=this.dateSourceCombo.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.mvtGrid.ITEM_CHANGED.subscribe(function(t){e.onGridCellClick(t)}),this.headersGrid.mandatoryColumns="Entity_ID,Ccy,Account_ID,Amount,Value_Date,Sign,Pred_Status",this.headersGrid.columnWidthChanged.subscribe(function(t){e.resizeGrids()}),this.headersGrid.columnOrderChanged.subscribe(function(t){e.mvtGrid.gridData.length>0&&(e.orderChangedRows=e.mvtGrid.gridData),e.columnOrderChange(t)}),this.sourceGrid.ITEM_CHANGED.subscribe(function(t){e.confirmValidation(t)}),this.headersGrid.ITEM_CHANGED.subscribe(function(t){e.saveColumnsHeaders()}),this.headersGrid.enableDisableCells=function(t){return e.enableDisableRow(t)},document.onmousemove=function(e){clearTimeout(t),t=setTimeout(function(){d(document).trigger("onmousemove")},60)}},e.prototype.resizeGrids=function(){try{this.sourceGrid.setRefreshColumnWidths(this.headersGrid.gridObj.getColumns()),this.mvtGrid.setRefreshColumnWidths(this.headersGrid.gridObj.getColumns())}catch(t){console.log("resizeGrids",t)}},e.prototype.enableDisableRow=function(t){return"1"!=t.id},e.prototype.saveColumnsHeaders=function(){this.newHeaderGridData=[],this.updatedHeader=[],this.headerChanged=!0;var t=this.headersGrid.gridData[0];for(var e in t)"string"==typeof t[e]&&this.updatedHeader.push(t[e]);this.newHeaderGridData=this.headersGrid.gridData},e.prototype.confirmValidation=function(t){if(this.rowIndex=t.rowIndex,this.dataField=t.dataField,this.oldComboVal=t.listData.oldValue,this.newComboVal=t.listData.newValue,this.getComboValues(),this.options.includes(t.listData.newValue)){if(this.srcGridChanged=!0,this.mvtGrid.gridData.length>0){l.c.yesLabel=l.Wb.getPredictMessage("alert.yes.label"),l.c.noLabel=l.Wb.getPredictMessage("alert.no.label");var e=l.Z.substitute(l.Wb.getPredictMessage("preAdviceInput.validateAgain",null));this.swtAlert.confirm(e,l.Wb.getPredictMessage("alert_header.confirm"),l.c.YES|l.c.NO,null,this.validateRowsAgain.bind(this))}}else this.sourceGrid.dataProvider[t.rowIndex].slickgrid_rowcontent[t.dataField].content=t.listData.oldValue,this.sourceGrid.dataProvider[t.rowIndex][t.dataField]=t.listData.oldValue},e.prototype.getComboValues=function(){for(var t=this.selectValues.select,e=0;e<t.length;e++)for(var i=0;i<t[e].option.length;i++)-1===this.options.indexOf(t[e].option[i].content)&&this.options.push(t[e].option[i].content)},e.prototype.validateRowsAgain=function(t){if(t.detail==l.c.YES)if("Excel file"==this.dateSourceCombo.selectedLabel){this.excelValFlag=!1,this.pastedText="";var e=n.read(this.binarystrGlobal,{type:"binary",raw:!0,cellNF:!0}),i=e.SheetNames[0],a=e.Sheets[i];this.chkHeader.selected?this.excelData=n.utils.sheet_to_json(a,{raw:!0}):this.excelData=n.utils.sheet_to_json(a,{raw:!0,header:this.defaultColumns}),this.prepareExcelData(this.excelData,this.importedFileHeaders)}else this.getAccess(this.pastedData,"save");else this.sourceGrid.gridData[0][this.dataField]=this.oldComboVal,this.sourceGrid.refresh()},e.prototype.columnOrderChange=function(t){this.columnOrderChanged=!0,this.newOrderColumns=[],this.columnDefinitionsTempArray=t;for(var e=0;e<this.columnDefinitionsTempArray.length;e++)"dummy"!=this.columnDefinitionsTempArray[e].id&&this.newOrderColumns.push(this.columnDefinitionsTempArray[e].field);this.populateSourceGrid(this.newOrderColumns),this.updateBottomGridcol(this.newOrderColumns);var i=this.orderChangedRows?this.orderChangedRows.length:0,a=this.orderChangedRows?this.orderChangedRows:[];this.mvtGrid.gridData={size:i,row:a},this.defaultColumns=this.newOrderColumns},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&!this.jsonReader.isDataBuilding()){this.dateSourceCombo.setComboData(this.jsonReader.getSelects()),this.allRows.selected=!0,this.columnOrderChanged=!1,this.headerChanged=!1,this.srcGridChanged=!1,this.headerIsSaved=this.jsonReader.getSingletons().headerIsSaved;var e=this.jsonReader.getSingletons().headerChecked;this.dataSourceType=this.jsonReader.getSingletons().dataSourceType,this.dataSourceType&&(this.dateSourceCombo.selectedValue=this.dataSourceType),this.fromUpdateFlag||(this.chkHeader.selected=e||!0),this.headersGrid.forceHeaderRefresh=!0,this.mvtGrid.forceHeaderRefresh=!0;var i={columns:this.lastRecievedJSON.dataDefinition.HeadersGrid.metadata.columns};this.headerGridCol=this.lastRecievedJSON.dataDefinition.HeadersGrid.metadata.columns.column,this.headersGrid.CustomGrid(i),this.headerGridRows=this.lastRecievedJSON.dataDefinition.HeadersGrid.rows,this.headerGridRows.size>0?(this.headersGrid.gridData=this.headerGridRows,this.headersGrid.setRowSize=this.jsonReader.getRowSize()):this.headersGrid.gridData={size:0,row:[]};var a={columns:this.lastRecievedJSON.dataDefinition.SourceFormatGrid.metadata.columns};this.selectValues=this.lastRecievedJSON.dataDefinition.selects,this.sourceGrid.gridComboDataProviders(this.selectValues),this.sourceGrid.CustomGrid(a),this.sourceGridRows=this.lastRecievedJSON.dataDefinition.SourceFormatGrid.rows,this.sourceGridRows.size>0?(this.sourceGrid.gridData=this.sourceGridRows,this.sourceGrid.setRowSize=this.jsonReader.getRowSize()):this.sourceGrid.gridData={size:0,row:[]};var r={columns:this.lastRecievedJSON.dataDefinition.DataPreviewGrid.metadata.columns};this.mvtGrid.CustomGrid(r),this.mvtGrid.gridData={size:0,row:[]},this.validRow=[],this.inValidRow=[],this.gridRows=[],this.getDefaultHeader(),this.prevRecievedJSON=this.lastRecievedJSON,this.resetFlag=!1}this.updateDataSource()}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error");this.fromUpdateFlag=!1},e.prototype.onGridCellClick=function(t){if(this.editedRow=[],t.listData.newValue!=t.listData.oldValue){var e=t.listData.new_row.seq_num;if("Amount"==t.dataField){for(var i=this.getPattern(this.sourceGrid.gridData[0].Amount),a=t.listData.new_row.Ccy,r=checkCurrencyPlaces(t.listData.newValue,i,a),s=0;s<this.mvtGrid.dataProvider.length;s++)this.mvtGrid.dataProvider[s].seq_num==e&&(this.mvtGrid.dataProvider[s].slickgrid_rowcontent[t.dataField].content=r,this.mvtGrid.dataProvider[s][t.dataField]=r);this.mvtGrid.refresh()}var l=d.extend(!0,{},t.listData.new_row);delete l.TooltipMsg,delete l.slickgrid_rowcontent,this.editedRow.push(l),this.getAccess(this.editedRow,"update")}},e.prototype.pasteDataFromButton=function(){var t=this;this.data=[],this.initialdata=[],this.sendFlag=!0;try{navigator.clipboard.readText().then(function(e){var i=[];if(e.split("\n").forEach(function(t){""!=t.trim()&&i.push(t)}),t.columnOrderChanged&&(t.defaultColumns=t.newOrderColumns),i.length>0){if(t.chkHeader.selected){if(i[0]=i[0].trimEnd(),t.importedColumns=i[0].split("\t"),t.headerChanged||"true"==t.headerIsSaved)for(var a=0;a<t.importedColumns.length;a++)t.importedColumns[a]=t.columnsMapping(t.importedColumns[a]);t.checkManddatoryColumns()?(delete i[0],i.forEach(function(e){var i={},a={};e.match(/[\u3400-\u9FBF]/)&&(e=e.replace(/"[^"]+"/g,function(t){return t.replace(/\t/g," ")})),t.importedColumns.forEach(function(t,r){i[t]=e.split("\t")[r].trimEnd(),a[t]={clickable:!1,content:e.split("\t")[r].trimEnd(),negative:!1}}),t.data.push(a),t.initialdata.push(i)}),t.defaultColumns=t.importedColumns,t.populateHeaderGrid(t.importedColumns),t.populateSourceGrid(t.importedColumns),t.updateBottomGridcol(t.importedColumns)):(t.sendFlag=!1,t.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.invalidHeaderAlert"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,t.changeFailureStatus.bind(t)))}else t.checkFirstRowValues(i[0].split("\t"))?i.forEach(function(e){var i={},a={};t.defaultColumns.forEach(function(r,s){null!=e.split("\t")[s]?(i[r]=e.split("\t")[s].trimEnd(),a[r]={clickable:!1,content:e.split("\t")[s].trimEnd(),negative:!1}):(t.sendFlag=!1,t.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.invalidImportedData"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,t.changeFailureStatus.bind(t)))}),t.data.push(a),t.initialdata.push(i)}):(t.sendFlag=!1,t.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.extraHeader"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,t.changeFailureStatus.bind(t)));t.pastedData=d.extend(!0,[],t.initialdata),t.populateData()}})}catch(e){this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.importFailed",null),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this))}},e.prototype.pasteData=function(){var t=this;this.data=[],this.initialdata=[],this.sendFlag=!0;var e=[];try{if(this.pastedText.length>50){if(this.pastedText.split("\n").forEach(function(t){""!=t.trim()&&e.push(t)}),this.columnOrderChanged&&(this.defaultColumns=this.newOrderColumns),this.chkHeader.selected){if(e[0]=e[0].trimEnd(),this.importedColumns=e[0].split("\t"),this.headerChanged||"true"==this.headerIsSaved)for(var i=0;i<this.importedColumns.length;i++)this.importedColumns[i]=this.columnsMapping(this.importedColumns[i]);this.checkManddatoryColumns()?(delete e[0],e.forEach(function(e){var i={},a={};t.importedColumns.forEach(function(t,r){i[t]=e.split("\t")[r].trimEnd(),a[t]={clickable:!1,content:e.split("\t")[r].trimEnd(),negative:!1}}),t.data.push(a),t.initialdata.push(i)}),this.defaultColumns=this.importedColumns,this.populateHeaderGrid(this.importedColumns),this.populateSourceGrid(this.importedColumns),this.updateBottomGridcol(this.importedColumns)):(this.sendFlag=!1,this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.invalidHeaderAlert"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this)))}else this.checkFirstRowValues(e[0].split("\t"))?e.forEach(function(e){var i={},a={};t.defaultColumns.forEach(function(r,s){null!=e.split("\t")[s]?(i[r]=e.split("\t")[s].trimEnd(),a[r]={clickable:!1,content:e.split("\t")[s].trimEnd(),negative:!1}):(t.sendFlag=!1,t.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.invalidImportedData"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,t.changeFailureStatus.bind(t)))}),t.data.push(a),t.initialdata.push(i)}):(this.sendFlag=!1,this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.extraHeader"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this)));this.pastedData=d.extend(!0,[],this.initialdata),this.populateData()}}catch(a){this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.importFailed",null),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this))}},e.prototype.changeGridDragStatus=function(){if(this.mvtGrid.gridData.length>0){l.c.yesLabel=l.Wb.getPredictMessage("alert.yes.label"),l.c.noLabel=l.Wb.getPredictMessage("alert.no.label");var t=l.Z.substitute(l.Wb.getPredictMessage("preAdviceInput.unsavedData",null));this.swtAlert.confirm(t,l.Wb.getPredictMessage("alert_header.confirm"),l.c.YES|l.c.NO,null,this.confirmChange.bind(this))}else this.fromUpdateFlag=!0,this.updateData()},e.prototype.confirmChange=function(t){t.detail==l.c.YES?(this.fromUpdateFlag=!0,this.updateData()):this.chkHeader.selected=!this.chkHeader.selected},e.prototype.updateData=function(){var t=this;this.requestParams=[],this.menuAccessId=l.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=displayDataDef",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.draggable=!!this.fromUpdateFlag&&!this.chkHeader.selected,this.requestParams.fromMethod="updateData",this.requestParams.dataType=this.dateSourceCombo.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.updateDataTypeConfig=function(){var t=this;this.fileName.text="",this.binarystrGlobal="",this.requestParams=[],this.menuAccessId=l.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=displayDataDef",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.draggable=!!this.fromUpdateFlag&&!this.chkHeader.selected,this.requestParams.fromMethod="updateDataTypeConfig",this.requestParams.dataType=this.dateSourceCombo.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.saveSettings=function(){this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=saveUserSettings",this.requestParams.newHeader=this.defaultColumns,this.requestParams.newSourceHeader=JSON.stringify(this.headersGrid.gridData[0]),this.requestParams.newValDateFormat=this.sourceGrid.gridData[0].Value_Date,this.requestParams.newPostDateFormat=this.sourceGrid.gridData[0].Post_Date,this.requestParams.newAmountFormat=this.sourceGrid.gridData[0].Amount,this.requestParams.headerChecked=this.chkHeader.selected,this.requestParams.dataSourceType=this.dateSourceCombo.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.checkManddatoryColumns=function(){for(var t=["Entity_ID","Ccy","Account_ID","Amount","Value_Date","Sign","Pred_Status"],e=[],i=0;i<t.length;i++)this.importedColumns.includes(t[i])?e.push("true"):e.push("false");return!e.includes("false")},e.prototype.dragImportedCol=function(t){for(var e=[],i=0;i<t.length;i++)this.ScreenColumns.includes(t[i])&&(this.setDataProvider(t[i]),e.push({columnorder:i,dataelement:t[i],dataprovider:this.provider,draggable:!1,editable:this.editableFlag,filterable:!!this.isPreviewGrid,format:"",heading:this.getHeaderName(t[i]),maxChars:40,sort:!!this.isPreviewGrid,type:this.isSourceFormat&&this.isComboCol(t[i])?"combo":this.isPreviewGrid&&"Amount"==t[i]?"num":"str",visible:!0,visible_default:!0,width:this.getColumnWidth(t[i])}));this.metadataHeaderGrid={columns:{column:e}}},e.prototype.getHeaderName=function(t){var e=null;switch(t){case"Entity_ID":e="Entity";break;case"Account_ID":e="Account ID";break;case"Value_Date":e="Value Date";break;case"Pred_Status":e="Pred Status";break;case"Book_Code":e="Book Code";break;case"Post_Date":e="Post Date";break;case"CounterParty_ID":e="CounterParty ID";break;case"Cparty_Text":e="Cparty Text";break;case"Match_Party":e="Match Party";break;case"Sign":e="Sign";break;case"Reference":e="Reference";break;case"Product_Type":e="Product Type";break;case"Amount":e="Amount";break;case"Ccy":e="Ccy"}return e},e.prototype.columnsMapping=function(t){var e=null;switch(t){case this.headersGrid.gridData[0].Entity_ID:e="Entity_ID";break;case this.headersGrid.gridData[0].Account_ID:e="Account_ID";break;case this.headersGrid.gridData[0].Value_Date:e="Value_Date";break;case this.headersGrid.gridData[0].Pred_Status:e="Pred_Status";break;case this.headersGrid.gridData[0].Book_Code:e="Book_Code";break;case this.headersGrid.gridData[0].Post_Date:e="Post_Date";break;case this.headersGrid.gridData[0].CounterParty_ID:e="CounterParty_ID";break;case this.headersGrid.gridData[0].Cparty_Text:e="Cparty_Text";break;case this.headersGrid.gridData[0].Match_Party:e="Match_Party";break;case this.headersGrid.gridData[0].Sign:e="Sign";break;case this.headersGrid.gridData[0].Reference:e="Reference";break;case this.headersGrid.gridData[0].Product_Type:e="Product_Type";break;case this.headersGrid.gridData[0].Amount:e="Amount";break;case this.headersGrid.gridData[0].Ccy:e="Ccy"}return e},e.prototype.getColumnWidth=function(t){var e=null;switch(t){case"Entity_ID":case"Account_ID":e=120;break;case"Value_Date":e=135;break;case"Pred_Status":case"Book_Code":case"Post_Date":e=120;break;case"CounterParty_ID":e=150;break;case"Cparty_Text":case"Match_Party":e=120;break;case"Sign":e=80;break;case"Reference":e=120;break;case"Product_Type":e=130;break;case"Amount":e=100;break;case"Ccy":e=80}return e},e.prototype.populateHeaderGrid=function(t){this.isSourceFormat=!1,this.isPreviewGrid=!1,this.editableFlag=!0,this.dragImportedCol(t),this.headersGrid.forceHeaderRefresh=!0;var e={columns:this.metadataHeaderGrid.columns};if(this.headersGrid.CustomGrid(e),this.headerGridRows.size>0&&!this.headerChanged)this.headersGrid.gridData=this.headerGridRows,this.headersGrid.setRowSize=this.jsonReader.getRowSize();else{for(var i=[],a=0;a<this.newHeaderGridData.length;a++)i.push(this.newHeaderGridData[a].slickgrid_rowcontent);this.headersGrid.gridData={size:i.length,row:i},this.headersGrid.refresh()}},e.prototype.checkFirstRowValues=function(t){for(var e=[this.headersGrid.gridData[0].Entity_ID,this.headersGrid.gridData[0].Ccy,this.headersGrid.gridData[0].Account_ID,this.headersGrid.gridData[0].Amount,this.headersGrid.gridData[0].Value_Date,this.headersGrid.gridData[0].Sign,this.headersGrid.gridData[0].Pred_Status,this.headersGrid.gridData[0].Reference,this.headersGrid.gridData[0].Book_Code,this.headersGrid.gridData[0].Post_Date,this.headersGrid.gridData[0].Product_Type,this.headersGrid.gridData[0].CounterParty_ID,this.headersGrid.gridData[0].Cparty_Text,this.headersGrid.gridData[0].Match_Party],i=[],a=0;a<e.length;a++)t.includes(e[a])?i.push("true"):i.push("false");return!i.includes("true")},e.prototype.populateSourceGrid=function(t){this.isSourceFormat=!0,this.isPreviewGrid=!1,this.editableFlag=!1,this.dragImportedCol(t),this.sourceGrid.forceHeaderRefresh=!0;var e={columns:this.metadataHeaderGrid.columns};this.sourceGrid.gridComboDataProviders(this.selectValues),this.sourceGrid.CustomGrid(e),this.sourceGridRows.size>0?(this.sourceGrid.gridData=this.sourceGridRows,this.sourceGrid.setRowSize=this.jsonReader.getRowSize()):this.sourceGrid.gridData={size:0,row:[]},this.dataField&&this.newComboVal&&this.srcGridChanged&&(this.sourceGrid.gridData[0][this.dataField]=this.newComboVal,this.sourceGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content=this.newComboVal,this.sourceGrid.dataProvider[this.rowIndex][this.dataField]=this.newComboVal,this.sourceGrid.refresh()),this.resizeGrids()},e.prototype.updateBottomGridcol=function(t){this.isSourceFormat=!1,this.isPreviewGrid=!0,this.editableFlag=!0,this.dragImportedCol(t),this.mvtGrid.forceHeaderRefresh=!0;var e={columns:this.metadataHeaderGrid.columns};this.mvtGrid.CustomGrid(e),this.resizeGrids()},e.prototype.isComboCol=function(t){if(["Value_Date","Amount","Post_Date"].includes(t))return!0},e.prototype.setDataProvider=function(t){this.provider="",this.isSourceFormat?this.provider="Value_Date"==t?"listValDates":"Post_Date"==t?"listPostDates":"Amount"==t?"listAmount":"":this.provider=""},e.prototype.getDefaultHeader=function(){this.defaultColumns=[];for(var t=0;t<this.headerGridCol.length;t++){var e=this.headerGridCol[t].dataelement;this.defaultColumns.push(e)}this.resetFlag&&this.updateBottomGridcol(this.defaultColumns)},e.prototype.checkGrid=function(t){try{var e=!0;if(t)this.clipboardData=t.clipboardData,this.pastedText=this.clipboardData.getData("text"),this.pastedText&&this.pastedText.length<100&&(e=!1),this.pasteFlag=!0;else{this.pastedText="";var i=n.read(this.binarystrGlobal,{type:"binary",raw:!0,cellNF:!0}),a=i.SheetNames[0],r=i.Sheets[a];this.chkHeader.selected?this.excelData=n.utils.sheet_to_json(r,{raw:!0}):this.excelData=n.utils.sheet_to_json(r,{raw:!0,header:this.defaultColumns}),this.getImportedFileData(r)}if(this.mvtGrid.gridData.length>0&&e){l.c.yesLabel=l.Wb.getPredictMessage("alert.yes.label"),l.c.noLabel=l.Wb.getPredictMessage("alert.no.label");var s=l.Z.substitute(l.Wb.getPredictMessage("preAdviceInput.unsavedData",null));this.swtAlert.confirm(s,l.Wb.getPredictMessage("alert_header.confirm"),l.c.YES|l.c.NO,null,this.confirmListener.bind(this))}else this.paste()}catch(d){this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.importFailed",null),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this))}},e.prototype.confirmListener=function(t){t.detail==l.c.YES&&(this.allRows.selected=!0,this.mvtGrid.gridData={size:0,row:[]},this.paste())},e.prototype.paste=function(){try{"Clipboard"==this.dateSourceCombo.selectedLabel||this.pastedText?this.pasteFlag?(this.pasteData(),this.pasteFlag=!1):navigator.clipboard?this.pasteDataFromButton():this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.ClipboardAlert"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this)):(this.excelValFlag=!0,this.prepareExcelData(this.excelData,this.importedFileHeaders))}catch(t){this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.importFailed",null),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this))}},e.prototype.populateData=function(){var t=0;try{t=10,this.sendFlag&&(this.saveButton.enabled=!0,this.saveButton.buttonMode=!0,t=20,this.getAccess(this.initialdata,"save"),t=30)}catch(e){l.Wb.logError(e,this.moduleId,this.commonService.getQualifiedClassName(this),"importData",t)}},e.prototype.addZero=function(t){return t<10&&(t="0"+t),t},e.prototype.selectInvalidRowColor=function(t,e){var i,a=0;try{a=10,""!=t.slickgrid_rowcontent.TooltipMsg[0]?(a=20,i="#C0C0C0"):i=e}catch(r){l.Wb.logError(r,this.moduleId,this.getQualifiedClassName(this),"selectInvalidRowColor",a)}return i},e.prototype.setTooltipMessage=function(t){var e=0,i="",a="";try{e=10,(i=t.slickgrid_rowcontent.TooltipMsg?t.slickgrid_rowcontent.TooltipMsg[0]:"")?(i=(i=(i=i.split(" ").join("$#$")).split(",").join("&@&")).split(";").join("&_&"),e=20,a=i):a=""}catch(r){l.Wb.logError(r,this.moduleId,this.getQualifiedClassName(this),"setTooltipMessage",e)}return a},e.prototype.clearClipBoard=function(){navigator.clipboard.writeText("").then(function(){},function(){})},e.prototype.getAccess=function(t,e){var i=this;this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.stillCheckingFlag=!0,this.inputData.cbResult=function(t){i.checkAccess(t,e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=checkPreAdviceInputAccess",this.requestParams.preAdviceList=JSON.stringify(t),this.requestParams.ValDateFormat=this.sourceGrid.gridData[0].Value_Date,this.requestParams.PostDateFormat=this.sourceGrid.gridData[0].Post_Date,this.requestParams.amountFormat=this.sourceGrid.gridData[0].Amount,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,JSON.stringify(t).length>0&&this.inputData.send(this.requestParams)},e.prototype.b64DecodeUnicode=function(t){return decodeURIComponent(atob(t).split("").map(function(t){return"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)}).join(""))},e.prototype.checkAccess=function(t,e){var i=this;if(this.jsonReader.setInputJSON(t),this.jsonReader.getRequestReplyStatus()){this.saveButton.enabled=!0,this.saveButton.buttonMode=!0,this.validRow=[],this.inValidRow=[];var a=l.Z.replaceAll(t.preAdviceInput.updatedArray,{"\\(":"=","\\)":"+"});this.initialdata=JSON.parse(this.b64DecodeUnicode(a)),this.initialdata.forEach(function(t){i.mvtGrid.rowColorFunction=function(t,e){return i.selectInvalidRowColor(t,e)},i.mvtGrid.customTooltipFunction=function(t){return i.setTooltipMessage(t)},"update"==e&&t.TooltipMsg[0].length>0&&i.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.rowStillInvalid"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,i.changeFailureStatus.bind(i))});for(var r=0;r<this.initialdata.length;r++){for(var s in this.initialdata[r])"string"==typeof this.initialdata[r][s]&&(this.initialdata[r][s]={clickable:!1,content:this.initialdata[r][s],negative:!1});if("save"==e){var n=r;this.initialdata[r].seq_num={clickable:!1,content:n.toString(),negative:!1}}}if("save"==e&&(this.mvtGrid.gridData={size:this.initialdata.length,row:this.initialdata}),"update"==e){var d=[];for(r=0;r<this.gridRows.length;r++)this.gridRows[r].slickgrid_rowcontent.seq_num.content!=this.initialdata[0].seq_num.content?d.push(this.gridRows[r].slickgrid_rowcontent):d.push(this.initialdata[0]);this.mvtGrid.gridData={size:d.length,row:d}}this.gridRows=this.mvtGrid.gridData,this.mvtGrid.gridData.forEach(function(t){""==t.slickgrid_rowcontent.TooltipMsg[0]?i.validRow.push(t):i.inValidRow.push(t)}),"update"==e&&this.filterRows(),0==this.validRow.length&&(this.saveButton.enabled=!1,this.saveButton.buttonMode=!1)}else this.swtAlert.error(this.jsonReader.getRequestReplyMessage(),l.Wb.getPredictMessage("alert_header.error"));this.stillCheckingFlag=!1,"update"!=e&&this.swtAlert.confirm(l.Wb.getPredictMessage("preAdviceInput.importComplete",null),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeSuccesStatus.bind(this))},e.prototype.changeFailureStatus=function(){var t=new Date,e=this.addZero(t.getDate())+"/"+this.addZero(t.getMonth()+1)+"/"+this.addZero(t.getFullYear())+" "+(this.addZero(t.getHours())+":"+this.addZero(t.getMinutes())+":"+this.addZero(t.getSeconds()));"Clipboard"==this.dateSourceCombo.selectedLabel?this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importFrom",null)+" Clipboard failed at "+e:"Excel file"==this.dateSourceCombo.selectedLabel?this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importFrom",null)+" Excel failed at "+e:this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importFrom",null)+" Csv failed at "+e,this.clipboadDate=e},e.prototype.changeSuccesStatus=function(){var t=new Date,e=this.addZero(t.getDate())+"/"+this.addZero(t.getMonth()+1)+"/"+this.addZero(t.getFullYear())+" "+(this.addZero(t.getHours())+":"+this.addZero(t.getMinutes())+":"+this.addZero(t.getSeconds()));"Clipboard"==this.dateSourceCombo.selectedLabel?this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importFrom",null)+" Clipboard at "+e:"Excel file"==this.dateSourceCombo.selectedLabel?this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importFrom",null)+" Excel at "+e:this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importFrom",null)+" Csv at "+e,this.clipboadDate=e},e.prototype.getPattern=function(t){var e=null;switch(t){case"999,999.00":e="currencyPat1";break;case"999.999,00":e="currencyPat2"}return e},e.prototype.saveAll=function(){var t=this;this.stillCheckingFlag?setTimeout(function(){t.checkBeforeSaving()},1e3):(this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.saveSettings(),l.x.call("close"),window.opener.instanceElement.updateData()},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=saveAll",this.requestParams.preAdviceList=JSON.stringify(this.validRow),this.requestParams.newValDateFormat=this.sourceGrid.gridData[0].Value_Date,this.requestParams.newPostDateFormat=this.sourceGrid.gridData[0].Post_Date,this.requestParams.newAmountFormat=this.getPattern(this.sourceGrid.gridData[0].Amount),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,JSON.stringify(this.validRow).length>0&&this.inputData.send(this.requestParams))},e.prototype.checkBeforeSaving=function(){if(this.inValidRow.length>0){l.c.yesLabel=l.Wb.getPredictMessage("alert.yes.label"),l.c.noLabel=l.Wb.getPredictMessage("alert.no.label");var t=l.Z.substitute(l.Wb.getPredictMessage("preAdviceInput.invalidRowsAlert",null));this.swtAlert.confirm(t,l.Wb.getPredictMessage("alert_header.confirm"),l.c.YES|l.c.NO,null,this.confirmSave.bind(this))}else this.saveAll()},e.prototype.confirmSave=function(t){t.detail==l.c.YES&&this.saveAll()},e.prototype.filterRows=function(){this.allRows.selected&&(this.mvtGrid.gridData={size:this.gridRows.length,row:this.gridRows}),this.validRows.selected&&(this.mvtGrid.gridData={size:this.validRow.length,row:this.validRow}),this.invalidRows.selected&&(this.mvtGrid.gridData={size:this.inValidRow.length,row:this.inValidRow})},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.closeHandler=function(){if(this.mvtGrid.gridData.length>0){l.c.yesLabel=l.Wb.getPredictMessage("alert.yes.label"),l.c.noLabel=l.Wb.getPredictMessage("alert.no.label");var t=l.Z.substitute(l.Wb.getPredictMessage("preAdviceInput.closeAlert",null));this.swtAlert.confirm(t,l.Wb.getPredictMessage("alert_header.confirm"),l.c.YES|l.c.NO,null,this.confirmCloseScreen.bind(this))}else this.saveSettings(),l.x.call("close")},e.prototype.confirmCloseScreen=function(t){t.detail==l.c.YES&&(this.saveSettings(),l.x.call("close"))},e.prototype.resetConfig=function(){this.resetFlag=!0,l.c.yesLabel=l.Wb.getPredictMessage("alert.yes.label"),l.c.noLabel=l.Wb.getPredictMessage("alert.no.label");var t=l.Z.substitute(l.Wb.getPredictMessage("preAdviceInput.resetAlert",null));this.swtAlert.confirm(t,l.Wb.getPredictMessage("alert_header.confirm"),l.c.YES|l.c.NO,null,this.deleteConfig.bind(this))},e.prototype.deleteConfig=function(t){var e=this;t.detail==l.c.YES&&(this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(){e.updateData()},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=deleteUserSettings",this.requestParams.sourceDataType=this.dateSourceCombo.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.updateDataSource=function(){this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importInitStatus",null),"Clipboard"==this.dateSourceCombo.selectedLabel?(this.uploadImage.includeInLayout=!1,this.uploadImage.source="",this.uploadImage.id="uploadImage1"):(this.uploadImage.includeInLayout=!0,this.uploadImage.source=this.baseURL+l.x.call("eval","uploadFileImage"),this.uploadImage.id="uploadImage")},e.prototype.onFileChange=function(t){if(this.mvtGrid.gridData.length>0){l.c.yesLabel=l.Wb.getPredictMessage("alert.yes.label"),l.c.noLabel=l.Wb.getPredictMessage("alert.no.label");var e=l.Z.substitute(l.Wb.getPredictMessage("preAdviceInput.unsavedData",null));this.uploadEvent=t,this.swtAlert.confirm(e,l.Wb.getPredictMessage("alert_header.confirm"),l.c.YES|l.c.NO,null,this.confirmUpload.bind(this))}else this.readUploadedFile(t)},e.prototype.onInputClick=function(t){t.target.value=""},e.prototype.confirmUpload=function(t){t.detail==l.c.YES&&(this.allRows.selected=!0,this.importStatusTxt.text=l.Wb.getPredictMessage("preAdviceInput.importInitStatus",null),this.mvtGrid.gridData={size:0,row:[]},this.readUploadedFile(this.uploadEvent))},e.prototype.readUploadedFile=function(t){var e=this,i=t.target;if(this.uploadedFileName=t.target.files[0].name,this.fileName.text=this.uploadedFileName,1!==i.files.length)throw new Error("Cannot use multiple files");var a=new FileReader;a.readAsBinaryString(i.files[0]),a.onload=function(t){var i=t.target.result;e.binarystrGlobal=i}},e.prototype.getImportedFileData=function(t){this.importedFileHeaders=[];var e,i=n.utils.decode_range(t["!ref"]),a=i.s.r;for(e=i.s.c;e<=i.e.c;++e){var r=t[n.utils.encode_cell({c:e,r:a})],s="UNKNOWN "+e;r&&r.t&&(s=n.utils.format_cell(r))&&this.importedFileHeaders.push(s)}},e.prototype.prepareExcelData=function(t,e){var i=this,a={};this.data=[],this.initialdata=[],this.sendFlag=!0;var r=this.headersGrid.gridData[0].Amount,s=this.headersGrid.gridData[0].Ccy,n=this.headersGrid.gridData[0].Value_Date,o=this.headersGrid.gridData[0].Post_Date,h=this.getPattern(this.sourceGrid.gridData[0].Amount),u=this.sourceGrid.gridData[0].Value_Date,c=this.sourceGrid.gridData[0].Post_Date;try{if(this.columnOrderChanged&&(this.defaultColumns=this.newOrderColumns),this.chkHeader.selected){if(this.importedColumns=e,this.headerChanged||"true"==this.headerIsSaved)for(var p=0;p<this.importedColumns.length;p++)a[this.columnsMapping(this.importedColumns[p].replace(",",""))]=this.importedColumns[p],this.importedColumns[p]=this.columnsMapping(this.importedColumns[p].replace(",",""));this.checkManddatoryColumns()?(t.forEach(function(t){var e={},d={};if("Excel file"==i.dateSourceCombo.selectedLabel){var p=t[s];t[r]&&"number"==typeof t[r]&&(t[r]=checkCurrencyPlacesFromNumber(t[r].toString(),h,p)),t[n]&&"number"==typeof t[n]&&(t[n]=l.j.formatDate(i.ExcelDateToJSDate(t[n]),u)),t[o]&&"number"==typeof t[o]&&(t[o]=l.j.formatDate(i.ExcelDateToJSDate(t[o]),c))}for(var g=0;g<i.importedColumns.length;g++){var b=i.importedColumns[g],m=a[b]?a[b]:b;t[m]&&(e[b]=","!=t[m].toString()?t[m].toString():"",d[b]={clickable:!1,content:","!=t[m].toString()?t[m].toString():"",negative:!1})}i.data.push(d),i.initialdata.push(e)}),this.defaultColumns=this.importedColumns,this.excelValFlag&&(this.populateHeaderGrid(this.importedColumns),this.populateSourceGrid(this.importedColumns),this.updateBottomGridcol(this.importedColumns))):(this.sendFlag=!1,this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.invalidHeaderAlert"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this)))}else this.checkFirstRowValues(e)?t.forEach(function(t){var e={},a={};if("Excel file"==i.dateSourceCombo.selectedLabel){var d=t[s];t[r]&&"number"==typeof t[r]&&(t[r]=checkCurrencyPlacesFromNumber(t[r].toString(),h,d)),t[n]&&"number"==typeof t[n]&&(t[n]=l.j.formatDate(i.ExcelDateToJSDate(t[n]),u)),t[o]&&"number"==typeof t[o]&&(t[o]=l.j.formatDate(i.ExcelDateToJSDate(t[o]),c))}for(var p=0;p<i.defaultColumns.length;p++){var g=i.defaultColumns[p];null!=t[g]?e[g]=","!=t[g].toString()?t[g].toString():"":e[g]="",a[g]={clickable:!1,content:e[g],negative:!1}}i.data.push(a),i.initialdata.push(e)}):(this.sendFlag=!1,this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.extraHeader"),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this)));this.pastedData=d.extend(!0,[],this.initialdata),this.populateData()}catch(g){this.swtAlert.error(l.Wb.getPredictMessage("preAdviceInput.importFailed",null),l.Wb.getPredictMessage("alert_header.error"),l.c.OK,null,this.changeFailureStatus.bind(this))}},e.prototype.ExcelDateToJSDate=function(t){return new Date(Math.round(86400*(t-25569)*1e3))},e}(l.yb),h=[{path:"",component:o}],u=(s.l.forChild(h),function(){return function(){}}()),c=i("pMnS"),p=i("RChO"),g=i("t6HQ"),b=i("WFGK"),m=i("5FqG"),f=i("Ip0R"),w=i("gIcY"),v=i("t/Na"),D=i("sE5F"),C=i("OzfB"),S=i("T7CS"),I=i("S7LP"),R=i("6aHO"),G=i("WzUx"),y=i("A7o+"),P=i("zCE2"),A=i("Jg5P"),_=i("3R0m"),F=i("hhbb"),M=i("5rxC"),k=i("Fzqc"),T=i("21Lb"),W=i("hUWP"),x=i("3pJQ"),L=i("V9q+"),O=i("VDKW"),B=i("kXfT"),J=i("BGbe");i.d(e,"ImportPreAdvicesModuleNgFactory",function(){return N}),i.d(e,"RenderType_ImportPreAdvices",function(){return V}),i.d(e,"View_ImportPreAdvices_0",function(){return H}),i.d(e,"View_ImportPreAdvices_Host_0",function(){return q}),i.d(e,"ImportPreAdvicesNgFactory",function(){return Z});var N=a.Gb(u,[],function(t){return a.Qb([a.Rb(512,a.n,a.vb,[[8,[c.a,p.a,g.a,b.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,Z]],[3,a.n],a.J]),a.Rb(4608,f.m,f.l,[a.F,[2,f.u]]),a.Rb(4608,w.c,w.c,[]),a.Rb(4608,w.p,w.p,[]),a.Rb(4608,v.j,v.p,[f.c,a.O,v.n]),a.Rb(4608,v.q,v.q,[v.j,v.o]),a.Rb(5120,v.a,function(t){return[t,new l.tb]},[v.q]),a.Rb(4608,v.m,v.m,[]),a.Rb(6144,v.k,null,[v.m]),a.Rb(4608,v.i,v.i,[v.k]),a.Rb(6144,v.b,null,[v.i]),a.Rb(4608,v.f,v.l,[v.b,a.B]),a.Rb(4608,v.c,v.c,[v.f]),a.Rb(4608,D.c,D.c,[]),a.Rb(4608,D.g,D.b,[]),a.Rb(5120,D.i,D.j,[]),a.Rb(4608,D.h,D.h,[D.c,D.g,D.i]),a.Rb(4608,D.f,D.a,[]),a.Rb(5120,D.d,D.k,[D.h,D.f]),a.Rb(5120,a.b,function(t,e){return[C.j(t,e)]},[f.c,a.O]),a.Rb(4608,S.a,S.a,[]),a.Rb(4608,I.a,I.a,[]),a.Rb(4608,R.a,R.a,[a.n,a.L,a.B,I.a,a.g]),a.Rb(4608,G.c,G.c,[a.n,a.g,a.B]),a.Rb(4608,G.e,G.e,[G.c]),a.Rb(4608,y.l,y.l,[]),a.Rb(4608,y.h,y.g,[]),a.Rb(4608,y.c,y.f,[]),a.Rb(4608,y.j,y.d,[]),a.Rb(4608,y.b,y.a,[]),a.Rb(4608,y.k,y.k,[y.l,y.h,y.c,y.j,y.b,y.m,y.n]),a.Rb(4608,G.i,G.i,[[2,y.k]]),a.Rb(4608,G.r,G.r,[G.L,[2,y.k],G.i]),a.Rb(4608,G.t,G.t,[]),a.Rb(4608,G.w,G.w,[]),a.Rb(1073742336,s.l,s.l,[[2,s.r],[2,s.k]]),a.Rb(1073742336,f.b,f.b,[]),a.Rb(1073742336,w.n,w.n,[]),a.Rb(1073742336,w.l,w.l,[]),a.Rb(1073742336,P.a,P.a,[]),a.Rb(1073742336,A.a,A.a,[]),a.Rb(1073742336,w.e,w.e,[]),a.Rb(1073742336,_.a,_.a,[]),a.Rb(1073742336,y.i,y.i,[]),a.Rb(1073742336,G.b,G.b,[]),a.Rb(1073742336,v.e,v.e,[]),a.Rb(1073742336,v.d,v.d,[]),a.Rb(1073742336,D.e,D.e,[]),a.Rb(1073742336,F.b,F.b,[]),a.Rb(1073742336,M.b,M.b,[]),a.Rb(1073742336,C.c,C.c,[]),a.Rb(1073742336,k.a,k.a,[]),a.Rb(1073742336,T.d,T.d,[]),a.Rb(1073742336,W.c,W.c,[]),a.Rb(1073742336,x.a,x.a,[]),a.Rb(1073742336,L.a,L.a,[[2,C.g],a.O]),a.Rb(1073742336,O.b,O.b,[]),a.Rb(1073742336,B.a,B.a,[]),a.Rb(1073742336,J.b,J.b,[]),a.Rb(1073742336,l.Tb,l.Tb,[]),a.Rb(1073742336,u,u,[]),a.Rb(256,v.n,"XSRF-TOKEN",[]),a.Rb(256,v.o,"X-XSRF-TOKEN",[]),a.Rb(256,"config",{},[]),a.Rb(256,y.m,void 0,[]),a.Rb(256,y.n,void 0,[]),a.Rb(256,"popperDefaults",{},[]),a.Rb(1024,s.i,function(){return[[{path:"",component:o}]]},[])])}),E=[["#uploadImage img{border:1px solid #696969!important}"]],V=a.Hb({encapsulation:2,styles:E,data:{}});function H(t){return a.dc(0,[a.Zb(402653184,1,{_container:0}),a.Zb(402653184,2,{headersGridContainer:0}),a.Zb(402653184,3,{sourceFormatGridContainer:0}),a.Zb(402653184,4,{mvtGridContainer:0}),a.Zb(402653184,5,{dateSource:0}),a.Zb(402653184,6,{fileName:0}),a.Zb(402653184,7,{dateSourceCombo:0}),a.Zb(402653184,8,{headerLabel:0}),a.Zb(402653184,9,{headerValue:0}),a.Zb(402653184,10,{headerType:0}),a.Zb(402653184,11,{sourceFormat:0}),a.Zb(402653184,12,{importStatusLbl:0}),a.Zb(402653184,13,{importStatusTxt:0}),a.Zb(402653184,14,{showLabel:0}),a.Zb(402653184,15,{chkHeader:0}),a.Zb(402653184,16,{fieldSet1:0}),a.Zb(402653184,17,{fieldSet2:0}),a.Zb(402653184,18,{showOptions:0}),a.Zb(402653184,19,{allRows:0}),a.Zb(402653184,20,{validRows:0}),a.Zb(402653184,21,{invalidRows:0}),a.Zb(402653184,22,{loadButton:0}),a.Zb(402653184,23,{saveButton:0}),a.Zb(402653184,24,{closeButton:0}),a.Zb(402653184,25,{resetButton:0}),a.Zb(402653184,26,{loadingImage:0}),a.Zb(402653184,27,{uploadImage:0}),a.Zb(402653184,28,{swtModule:0}),(t()(),a.Jb(28,0,null,null,121,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var a=!0,r=t.component;"creationComplete"===e&&(a=!1!==r.onLoad()&&a);return a},m.ad,m.hb)),a.Ib(29,4440064,[[28,4],["swtModule",4]],0,l.yb,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),a.Jb(30,0,null,0,119,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),a.Ib(31,4440064,null,0,l.ec,[a.r,l.i,a.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),a.Jb(32,0,null,0,62,"SwtFieldSet",[["id","fieldSet1"],["style","height: 21%; width: 100%; color:blue;"]],null,null,null,m.Vc,m.cb)),a.Ib(33,4440064,[[16,4],["fieldSet1",4]],0,l.ob,[a.r,l.i],{id:[0,"id"]},null),(t()(),a.Jb(34,0,null,0,60,"Grid",[["height","100%"],["paddingLeft","5"],["width","100%"]],null,null,null,m.Cc,m.H)),a.Ib(35,4440064,null,0,l.z,[a.r,l.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),a.Jb(36,0,null,0,36,"GridRow",[["height","25%"],["width","100%"]],null,null,null,m.Bc,m.J)),a.Ib(37,4440064,null,0,l.B,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(38,0,null,0,30,"VBox",[["height","100%"],["width","91%"]],null,null,null,m.od,m.vb)),a.Ib(39,4440064,null,0,l.ec,[a.r,l.i,a.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(40,0,null,0,18,"GridRow",[["height","22"],["width","100%"]],null,null,null,m.Bc,m.J)),a.Ib(41,4440064,null,0,l.B,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(42,0,null,0,3,"GridItem",[["width","150"]],null,null,null,m.Ac,m.I)),a.Ib(43,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(44,0,null,0,1,"SwtLabel",[["id","dateSource"]],null,null,null,m.Yc,m.fb)),a.Ib(45,4440064,[[5,4],["dateSource",4]],0,l.vb,[a.r,l.i],{id:[0,"id"]},null),(t()(),a.Jb(46,0,null,0,3,"GridItem",[["width","140"]],null,null,null,m.Ac,m.I)),a.Ib(47,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(48,0,null,0,1,"SwtComboBox",[["dataLabel","dataSourcesList"],["id","dateSourceCombo"],["width","120"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var r=!0,s=t.component;"window:mousewheel"===e&&(r=!1!==a.Tb(t,49).mouseWeelEventHandler(i.target)&&r);"change"===e&&(r=!1!==s.updateDataTypeConfig()&&r);return r},m.Pc,m.W)),a.Ib(49,4440064,[[7,4],["dateSourceCombo",4]],0,l.gb,[a.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),a.Jb(50,0,null,0,4,"GridItem",[["width","40"]],null,null,null,m.Ac,m.I)),a.Ib(51,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(52,0,[["file",1]],0,0,"input",[["style","display: none"],["type","file"]],[[8,"accept",0]],[[null,"change"],[null,"click"]],function(t,e,i){var a=!0,r=t.component;"change"===e&&(a=!1!==r.readUploadedFile(i)&&a);"click"===e&&(a=!1!==r.onInputClick(i)&&a);return a},null,null)),(t()(),a.Jb(53,0,null,0,1,"SwtImage",[["id","uploadImage1"],["styleName","imageStyle"],["width","23"]],null,[[null,"click"]],function(t,e,i){var r=!0;"click"===e&&(r=!1!==a.Tb(t,52).click()&&r);return r},m.Xc,m.eb)),a.Ib(54,4440064,[[27,4],["uploadImage",4]],0,l.ub,[a.r],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"]},{onClick_:"click"}),(t()(),a.Jb(55,0,null,0,3,"GridItem",[["width","45%"]],null,null,null,m.Ac,m.I)),a.Ib(56,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(57,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","fileName"]],null,null,null,m.Yc,m.fb)),a.Ib(58,4440064,[[6,4],["fileName",4]],0,l.vb,[a.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),a.Jb(59,0,null,0,9,"GridRow",[["height","22"],["width","100%"]],null,null,null,m.Bc,m.J)),a.Ib(60,4440064,null,0,l.B,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(61,0,null,0,3,"GridItem",[["width","150"]],null,null,null,m.Ac,m.I)),a.Ib(62,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(63,0,null,0,1,"SwtLabel",[["id","headerLabel"]],null,null,null,m.Yc,m.fb)),a.Ib(64,4440064,[[8,4],["headerLabel",4]],0,l.vb,[a.r,l.i],{id:[0,"id"]},null),(t()(),a.Jb(65,0,null,0,3,"GridItem",[["width","88%"]],null,null,null,m.Ac,m.I)),a.Ib(66,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(67,0,null,0,1,"SwtCheckBox",[["id","chkHeader"],["selected","true"]],null,[[null,"change"]],function(t,e,i){var a=!0,r=t.component;"change"===e&&(a=!1!==r.changeGridDragStatus()&&a);return a},m.Oc,m.V)),a.Ib(68,4440064,[[15,4],["chkHeader",4]],0,l.eb,[a.r,l.i],{id:[0,"id"],selected:[1,"selected"]},{change_:"change"}),(t()(),a.Jb(69,0,null,0,3,"VBox",[["height","100%"],["paddingTop","15"],["width","9%"]],null,null,null,m.od,m.vb)),a.Ib(70,4440064,null,0,l.ec,[a.r,l.i,a.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),a.Jb(71,0,null,0,1,"SwtButton",[["id","resetButton"]],null,[[null,"click"]],function(t,e,i){var a=!0,r=t.component;"click"===e&&(a=!1!==r.resetConfig()&&a);return a},m.Mc,m.T)),a.Ib(72,4440064,[[25,4],["resetButton",4]],0,l.cb,[a.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),a.Jb(73,0,null,0,21,"GridRow",[["height","75%"],["paddingTop","10"],["width","100%"]],null,null,null,m.Bc,m.J)),a.Ib(74,4440064,null,0,l.B,[a.r,l.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),a.Jb(75,0,null,0,13,"VBox",[["height","100%"],["paddingTop","22"],["width","150"]],null,null,null,m.od,m.vb)),a.Ib(76,4440064,null,0,l.ec,[a.r,l.i,a.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),a.Jb(77,0,null,0,3,"GridItem",[["height","22"],["width","100%"]],null,null,null,m.Ac,m.I)),a.Ib(78,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(79,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","headerValue"]],null,null,null,m.Yc,m.fb)),a.Ib(80,4440064,[[9,4],["headerValue",4]],0,l.vb,[a.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),a.Jb(81,0,null,0,3,"GridItem",[["height","25"],["width","100%"]],null,null,null,m.Ac,m.I)),a.Ib(82,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(83,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","headerType"]],null,null,null,m.Yc,m.fb)),a.Ib(84,4440064,[[10,4],["headerType",4]],0,l.vb,[a.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),a.Jb(85,0,null,0,3,"GridItem",[["height","25"],["width","100%"]],null,null,null,m.Ac,m.I)),a.Ib(86,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(87,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","sourceFormat"]],null,null,null,m.Yc,m.fb)),a.Ib(88,4440064,[[11,4],["sourceFormat",4]],0,l.vb,[a.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),a.Jb(89,0,null,0,5,"VBox",[["height","100%"],["width","100%"]],null,null,null,m.od,m.vb)),a.Ib(90,4440064,null,0,l.ec,[a.r,l.i,a.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(91,0,null,0,1,"SwtCanvas",[["border","false"],["height","65"],["id","headersGridContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,m.Nc,m.U)),a.Ib(92,4440064,[[2,4],["headersGridContainer",4]],0,l.db,[a.r,l.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),a.Jb(93,0,null,0,1,"SwtCanvas",[["border","false"],["height","36"],["id","sourceFormatGridContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,m.Nc,m.U)),a.Ib(94,4440064,[[3,4],["sourceFormatGridContainer",4]],0,l.db,[a.r,l.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),a.Jb(95,0,null,0,38,"SwtFieldSet",[["id","fieldSet2"],["style","padding-bottom: 5px; height: 78%; width: 100%;color:blue;"]],null,null,null,m.Vc,m.cb)),a.Ib(96,4440064,[[17,4],["fieldSet2",4]],0,l.ob,[a.r,l.i],{id:[0,"id"]},null),(t()(),a.Jb(97,0,null,0,36,"Grid",[["height","100%"],["paddingLeft","5"],["width","100%"]],null,null,null,m.Cc,m.H)),a.Ib(98,4440064,null,0,l.z,[a.r,l.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),a.Jb(99,0,null,0,30,"GridRow",[["height","25"],["paddingTop","2"],["width","100%"]],null,null,null,m.Bc,m.J)),a.Ib(100,4440064,null,0,l.B,[a.r,l.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),a.Jb(101,0,null,0,9,"GridItem",[["width","70%"]],null,null,null,m.Ac,m.I)),a.Ib(102,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(103,0,null,0,3,"GridItem",[["width","150"]],null,null,null,m.Ac,m.I)),a.Ib(104,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(105,0,null,0,1,"SwtLabel",[["id","importStatusLbl"]],null,null,null,m.Yc,m.fb)),a.Ib(106,4440064,[[12,4],["importStatusLbl",4]],0,l.vb,[a.r,l.i],{id:[0,"id"]},null),(t()(),a.Jb(107,0,null,0,3,"GridItem",[["width","83%"]],null,null,null,m.Ac,m.I)),a.Ib(108,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(109,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","importStatusTxt"]],null,null,null,m.Yc,m.fb)),a.Ib(110,4440064,[[13,4],["importStatusTxt",4]],0,l.vb,[a.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),a.Jb(111,0,null,0,18,"GridItem",[["width","30%"]],null,null,null,m.Ac,m.I)),a.Ib(112,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(113,0,null,0,5,"GridItem",[["paddingRight","15"]],null,null,null,m.Ac,m.I)),a.Ib(114,4440064,null,0,l.A,[a.r,l.i],{paddingRight:[0,"paddingRight"]},null),(t()(),a.Jb(115,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),a.Ib(116,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(117,0,null,0,1,"SwtLabel",[["id","showLabel"]],null,null,null,m.Yc,m.fb)),a.Ib(118,4440064,[[14,4],["showLabel",4]],0,l.vb,[a.r,l.i],{id:[0,"id"]},null),(t()(),a.Jb(119,0,null,0,10,"GridItem",[["width","90%"]],null,null,null,m.Ac,m.I)),a.Ib(120,4440064,null,0,l.A,[a.r,l.i],{width:[0,"width"]},null),(t()(),a.Jb(121,0,null,0,8,"SwtRadioButtonGroup",[["align","horizontal"],["id","showOptions"],["width","100%"]],null,[[null,"change"]],function(t,e,i){var a=!0,r=t.component;"change"===e&&(a=!1!==r.filterRows()&&a);return a},m.ed,m.lb)),a.Ib(122,4440064,[[18,4],["showOptions",4]],1,l.Hb,[v.c,a.r,l.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),a.Zb(603979776,29,{radioItems:1}),(t()(),a.Jb(124,0,null,0,1,"SwtRadioItem",[["groupName","showOptions"],["id","allRows"],["selected","true"],["value","A"],["width","100"]],null,null,null,m.fd,m.mb)),a.Ib(125,4440064,[[29,4],[19,4],["allRows",4]],0,l.Ib,[a.r,l.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),a.Jb(126,0,null,0,1,"SwtRadioItem",[["groupName","showOptions"],["id","validRows"],["value","V"],["width","100"]],null,null,null,m.fd,m.mb)),a.Ib(127,4440064,[[29,4],[20,4],["validRows",4]],0,l.Ib,[a.r,l.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),a.Jb(128,0,null,0,1,"SwtRadioItem",[["groupName","showOptions"],["id","invalidRows"],["value","I"],["width","100"]],null,null,null,m.fd,m.mb)),a.Ib(129,4440064,[[29,4],[21,4],["invalidRows",4]],0,l.Ib,[a.r,l.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),a.Jb(130,0,null,0,3,"GridRow",[["height","100%"],["width","100%"]],null,null,null,m.Bc,m.J)),a.Ib(131,4440064,null,0,l.B,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(132,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","mvtGridContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,[[null,"paste"]],function(t,e,i){var a=!0,r=t.component;"paste"===e&&(a=!1!==r.checkGrid(i)&&a);return a},m.Nc,m.U)),a.Ib(133,4440064,[[4,4],["mvtGridContainer",4]],0,l.db,[a.r,l.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),a.Jb(134,0,null,0,15,"SwtCanvas",[["height","35"],["width","100%"]],null,null,null,m.Nc,m.U)),a.Ib(135,4440064,null,0,l.db,[a.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(136,0,null,0,13,"HBox",[["top","1"],["width","100%"]],null,null,null,m.Dc,m.K)),a.Ib(137,4440064,null,0,l.C,[a.r,l.i],{top:[0,"top"],width:[1,"width"]},null),(t()(),a.Jb(138,0,null,0,3,"HBox",[["paddingLeft","5"],["width","80%"]],null,null,null,m.Dc,m.K)),a.Ib(139,4440064,null,0,l.C,[a.r,l.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),a.Jb(140,0,null,0,1,"SwtButton",[["id","loadButton"]],null,[[null,"click"]],function(t,e,i){var a=!0,r=t.component;"click"===e&&(a=!1!==r.checkGrid(i)&&a);return a},m.Mc,m.T)),a.Ib(141,4440064,[[22,4],["loadButton",4]],0,l.cb,[a.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),a.Jb(142,0,null,0,7,"HBox",[["horizontalAlign","right"],["paddingRight","10"],["width","20%"]],null,null,null,m.Dc,m.K)),a.Ib(143,4440064,null,0,l.C,[a.r,l.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingRight:[2,"paddingRight"]},null),(t()(),a.Jb(144,0,null,0,1,"SwtButton",[["enabled","false"],["id","saveButton"]],null,[[null,"click"]],function(t,e,i){var a=!0,r=t.component;"click"===e&&(a=!1!==r.checkBeforeSaving()&&a);return a},m.Mc,m.T)),a.Ib(145,4440064,[[23,4],["saveButton",4]],0,l.cb,[a.r,l.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),a.Jb(146,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var a=!0,r=t.component;"click"===e&&(a=!1!==r.closeHandler()&&a);return a},m.Mc,m.T)),a.Ib(147,4440064,[[24,4],["closeButton",4]],0,l.cb,[a.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),a.Jb(148,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),a.Ib(149,114688,[[26,4],["loadingImage",4]],0,l.xb,[a.r],null,null)],function(t,e){t(e,29,0,"100%","100%");t(e,31,0,"100%","100%","5","5","5","5");t(e,33,0,"fieldSet1");t(e,35,0,"100%","100%","5");t(e,37,0,"100%","25%");t(e,39,0,"91%","100%");t(e,41,0,"100%","22");t(e,43,0,"150");t(e,45,0,"dateSource");t(e,47,0,"140");t(e,49,0,"dataSourcesList","120","dateSourceCombo");t(e,51,0,"40");t(e,54,0,"uploadImage1","imageStyle","23");t(e,56,0,"45%");t(e,58,0,"fileName","normal");t(e,60,0,"100%","22");t(e,62,0,"150");t(e,64,0,"headerLabel");t(e,66,0,"88%");t(e,68,0,"chkHeader","true");t(e,70,0,"9%","100%","15");t(e,72,0,"resetButton",!0);t(e,74,0,"100%","75%","10");t(e,76,0,"150","100%","22");t(e,78,0,"100%","22");t(e,80,0,"headerValue","normal");t(e,82,0,"100%","25");t(e,84,0,"headerType","normal");t(e,86,0,"100%","25");t(e,88,0,"sourceFormat","normal");t(e,90,0,"100%","100%");t(e,92,0,"headersGridContainer","canvasWithGreyBorder","100%","65","false");t(e,94,0,"sourceFormatGridContainer","canvasWithGreyBorder","100%","36","false");t(e,96,0,"fieldSet2");t(e,98,0,"100%","100%","5");t(e,100,0,"100%","25","2");t(e,102,0,"70%");t(e,104,0,"150");t(e,106,0,"importStatusLbl");t(e,108,0,"83%");t(e,110,0,"importStatusTxt","normal");t(e,112,0,"30%");t(e,114,0,"15");t(e,116,0,"10%");t(e,118,0,"showLabel");t(e,120,0,"90%");t(e,122,0,"showOptions","100%","horizontal");t(e,125,0,"allRows","100","showOptions","A","true");t(e,127,0,"validRows","100","showOptions","V");t(e,129,0,"invalidRows","100","showOptions","I");t(e,131,0,"100%","100%");t(e,133,0,"mvtGridContainer","canvasWithGreyBorder","100%","100%","false");t(e,135,0,"100%","35");t(e,137,0,"1","100%");t(e,139,0,"80%","5");t(e,141,0,"loadButton",!0);t(e,143,0,"right","20%","10");t(e,145,0,"saveButton","false",!0);t(e,147,0,"closeButton",!0),t(e,149,0)},function(t,e){t(e,52,0,a.Lb(1,"","Excel file"==a.Tb(e,49).selectedLabel?".xlsx":".csv",""))})}function q(t){return a.dc(0,[(t()(),a.Jb(0,0,null,null,1,"app-import-pre-advices",[],null,null,null,H,V)),a.Ib(1,4440064,null,0,o,[l.i,a.r],null,null)],function(t,e){t(e,1,0)},null)}var Z=a.Fb("app-import-pre-advices",o,q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);