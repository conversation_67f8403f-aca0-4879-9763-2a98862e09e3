(window.webpackJsonp=window.webpackJsonp||[]).push([[51],{"16Cx":function(t,e,s){"use strict";s.r(e);var i=s("CcnG"),a=s("mrSG"),r=s("ZYCi"),n=s("447K"),l=s("pAEI"),o=s("jvDV"),u=(s("elGS"),s("R1Kr"),function(t){function e(e,s){var i=t.call(this,s,e)||this;return i.commonService=e,i.element=s,i.jsonReader=new n.L,i.summaryJSONReader=new n.L,i.statusChanged=!1,i.inputData=new n.G(i.commonService),i.detailsData=new n.G(i.commonService),i.summaryData=new n.G(i.commonService),i.sendData=new n.G(i.commonService),i.requestParams=[],i.invalidComms="",i.baseURL=n.Wb.getBaseURL(),i.actionPath="",i.actionMethod="",i.tooltipOtherParams=[],i.TREE="tree",i.SCENARIO="scenario",i.formatIso="yyyy-mm-dd",i.formatIsoTime="yyyy-mm-dd hh24:mi:ss",i.comboOpen=!1,i.comboChange=!1,i.screenVersion=new n.V(i.commonService),i.screenName=n.Wb.getPredictMessage("scenarioSummary.title",null),i.versionNumber="1.0.0001",i.releaseDate="16 Apr 2020",i.currentUser="",i.menuAccessId="",i.callerMethod="",i.flashScenarios="",i.popupScenarios="",i.emailScenarios=!1,i.lastSelectedId="",i.lastSelectedNode="",i.firstLoad=!0,i.tabDataCategory=[],i.scenarioTitle="",i.fisrtTablastSelectedId=null,i.secondTablastSelectedId=null,i.firstTabTreeOpenedItems=[],i.firstTabTreeClosedItems=[],i.secondTabTreeOpenedItems=[],i.secondTabTreeClosedItems=[],i.previousSelectedTabIndex=-1,i.scenarioId="",i.facilityGui="",i.sqlParams="",i.selectedNodeId="",i.treeLevelValue="",i.fromScreenFacility=!1,i.autoRefresh=null,i.refreshRate=60,i.lastSelectedValue=null,i.logger=new n.R("Scenario Summary",i.commonService.httpclient),i.swtAlert=new n.bb(e),window.Main=i,i}return a.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.customSummary=this.summaryHbox.addChild(l.a),this.customSummary.width="100%",this.customSummary.height="100%",this.customSummary.summaryGrid.clientSideSort=!1,this.customSummary.summaryGrid.clientSideFilter=!1,this.closeButton.toolTip=n.Wb.getPredictMessage("tooltip.close",null),this.closeButton.label=n.Wb.getPredictMessage("button.genericdisplaymonitor.close",null),this.resolveButton.toolTip=n.Wb.getPredictMessage("tooltip.resolveButton",null),this.resolveButton.label=n.Wb.getPredictMessage("button.genericdisplaymonitor.resolve",null),this.reActiveButton.toolTip=n.Wb.getPredictMessage("tooltip.reActivateButton",null),this.reActiveButton.label=n.Wb.getPredictMessage("button.genericdisplaymonitor.reActivate",null),this.goToButton.toolTip=n.Wb.getPredictMessage("tooltip.goTo",null),this.goToButton.label=n.Wb.getPredictMessage("button.genericdisplaymonitor.goTo",null),this.detailsButton.toolTip=n.Wb.getPredictMessage("tooltip.details",null),this.detailsButton.label=n.Wb.getPredictMessage("button.genericdisplaymonitor.details",null),this.refreshButton.toolTip=n.Wb.getPredictMessage("tooltip.refresh",null),this.refreshButton.label=n.Wb.getPredictMessage("button.refresh",null),this.resolvedOnLbl.text=n.Wb.getPredictMessage("scenarioSummary.resolvedOn",null),this.lblCombo.text=n.Wb.getPredictMessage("scenarioSummary.Entity",null),this.lastRefTimeLabel.text=n.Wb.getPredictMessage("screen.lastRefresh",null),this.entityCombo.toolTip=n.Wb.getPredictMessage("tooltip.selectEntityid",null),this.alertLabel.text=n.Wb.getPredictMessage("scenarioSummary.alertableScen",null),this.zeroLabel.text=n.Wb.getPredictMessage("scenarioSummary.zeroTotals",null),this.currLabel.text=n.Wb.getPredictMessage("scenarioSummary.applyCcy",null),this.lostConnectionText.text=n.Wb.getPredictMessage("screen.connectionError",null),this.all.label=n.Wb.getPredictMessage("scenarioSummary.all",null),this.active.label=n.Wb.getPredictMessage("scenarioSummary.active",null),this.resolved.label=n.Wb.getPredictMessage("scenarioSummary.resolved",null),this.pending.label=n.Wb.getPredictMessage("scenarioSummary.pending",null),this.overdue.label=n.Wb.getPredictMessage("scenarioSummary.overdue",null),this.allOpen.label=n.Wb.getPredictMessage("scenarioSummary.allOpen",null),this.statusLabel.text=n.Wb.getPredictMessage("scenarioSummary.status",null),this.resolvedOnDate.toolTip=n.Wb.getPredictMessage("tooltip.resolvedOnDate",null)},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.onLoad=function(){var t=this;try{this.logger.info("method [onLoad] - START ");var e=!1;this.initializeMenus(),this.callerMethod=n.x.call("eval","callerMethod"),this.currentUser=n.x.call("eval","currentUser"),this.menuAccessId=n.x.call("eval","menuAccessId"),this.selectedNodeId=n.x.call("eval","selectedNodeId"),this.treeLevelValue=n.x.call("eval","treeLevelValue");var s=void 0;window.opener&&window.opener.instanceElement&&window.opener.instanceElement.getParamsFromParent?(this.status.enabled=!1,s=window.opener.instanceElement.getParamsFromParent(),this.facilityGui=s.facilityId,this.sqlParams=s.sqlParams,null!=s.selectedNodeId?(this.callerMethod="___",e=!0,this.selectedNodeId=s.selectedNodeId,this.treeLevelValue=s.treeLevelValue,this.tooltipCurrencyCode=s.tooltipCurrencyCode,this.tooltipEntityId=s.tooltipEntityId,this.tooltipSelectedDate=s.tooltipSelectedDate,this.tooltipOtherParams=s.tooltipOtherParams,this.tooltipSelectedAccount=s.tooltipSelectedAccount,this.tooltipMvtId=s.tooltipMvtId,this.tooltipMatchId=s.tooltipMatchId,null!=this.tooltipOtherParams&&this.tooltipOtherParams.currencythresholdFlag&&(this.currBox.selected="Y"==this.tooltipOtherParams.currencythresholdFlag),this.fromScreenFacility=!0):(this.facilityGui="SCENARIO_INSTANCE_MONITOR",this.status.selectedValue="")):(this.facilityGui="SCENARIO_INSTANCE_MONITOR",this.status.selectedValue=""),this.callerMethod||(this.callerMethod="_Y_"),this.customSummary.tree.ITEM_CLICK.subscribe(function(e){t.summaryTreeEventHandler(e)}),this.customSummary.summaryGrid.ITEM_CLICK.subscribe(function(e){t.cellLogic(e)}),this.customSummary.summaryGrid.onRowClick=function(e){e&&e.num&&null!=e.num.content&&null!=e.num.content&&(t.selectedIndex=e.num.content),"Y"==t.customSummary.rcdScenInstance&&t.handleButtonsStatus()},this.customSummary.summaryGrid.columnWidthChanged.subscribe(function(e){t.updateWidths(e)}),this.customSummary.summaryGrid.columnOrderChanged.subscribe(function(e){t.updateOrders(e)}),n.E.subscribe(function(e){var s=""+t.customSummary.divBox.widthLeft;t.currDividerPosition=Number(s.substr(0,s.length-1)),t.updateWidths(e)}),this.customSummary.summaryGrid.onFilterChanged=this.doUpdateSortFilter.bind(this),this.customSummary.summaryGrid.onSortChanged=this.doUpdateSortFilter.bind(this),this.requestParams=[],this.requestParams.currThreshold=1==this.currBox.selected?"Y":"N",null!=this.tooltipEntityId&&(this.requestParams.entityId=this.tooltipEntityId,this.entityCombo.enabled=!1),e&&(this.requestParams.alertScenarios="false"),this.actionPath="scenarioSummary.do?",this.actionMethod="method=summaryScreenInfo",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams.callerMethod=this.callerMethod,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.logger.info("method [onLoad] - END ")}catch(i){console.log(i),this.logger.error("GenericDisplay - method [onLoad] - error ",i)}},e.prototype.handleButtonsStatus=function(){this.customSummary.summaryGrid.selectedIndex>-1&&this.customSummary.summaryGrid.selectedItem&&"Y"==this.customSummary.rcdScenInstance?this.checkUserAccess():this.disableButtons()},e.prototype.changeStatus=function(){"All"==this.status.selectedValue||"R"==this.status.selectedValue?this.resolvedOnDate.enabled=!0:this.resolvedOnDate.enabled=!1,this.statusChanged=!0,this.lastSelectedNode="",this.customSummary.tree.selectedIndex=-1,this.customSummary.summaryGrid&&(this.customSummary.summaryGrid.resetFilter(),this.customSummary.summaryGrid.filteredGridColumns="",this.customSummary.resetGridData()),this.updateData("tree"),this.statusChanged=!1},e.prototype.updateDividerPosition=function(){var t=this.customSummary.mainHGroup.width,e=this.customSummary.treeContainer.width;this.currDividerPosition=t-e},e.prototype.comboFocusOutHandler=function(t){0===n.Z.trim(this.entityCombo.selectedItem.text).length&&this.entityCombo.setComboData(this.jsonReader.getSelects())},e.prototype.updateWidths=function(t){var e=this,s=[],i="N",a=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level),r=this.customSummary.summaryGrid.gridObj.getColumns().slice(0);this.customSummary.tree&&this.customSummary.tree.selectedItem&&this.customSummary.tree.selectedItem.record_scenario_instances&&(i="Y"==this.customSummary.tree.selectedItem.record_scenario_instances?"Y":"N");for(var n=r.length,l=0;l<n-1;l++)"expand"==r[l].id?r[l].width=5:null!=r[l].field&&s.push(r[l].field+"="+r[l].width);s.push("divider="+this.currDividerPosition),this.requestParams=[],this.sendData.encodeURL=!1,this.requestParams.width=s.join(","),this.requestParams.isScenInstance=i,this.requestParams.scenarioId=a,this.actionPath="scenarioSummary.do?",this.actionMethod="method=saveColumnWidth&",this.sendData.cbStart=this.startOfComms.bind(this),this.sendData.cbStop=this.endOfComms.bind(this),this.sendData.cbResult=function(t){e.inputDataResultColumnsChange(t)},this.sendData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.send(this.requestParams)},e.prototype.updateOrders=function(t){var e=[],s="N",i=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level),a=this.customSummary.summaryGrid.gridObj.getColumns().slice(0);this.customSummary.tree&&this.customSummary.tree.selectedItem&&this.customSummary.tree.selectedItem.record_scenario_instances&&(s="Y"==this.customSummary.tree.selectedItem.record_scenario_instances?"Y":"N");for(var r=a.length,n=0;n<r-1;n++)e.push(a[n].field);e.push("divider"),this.requestParams=[],this.sendData.encodeURL=!1,this.requestParams.order=e.join(","),this.requestParams.isScenInstance=s,this.requestParams.scenarioId=i,this.actionPath="scenarioSummary.do?",this.actionMethod="method=saveColumnOrder&",this.sendData.cbStart=this.startOfComms.bind(this),this.sendData.cbStop=this.endOfComms.bind(this),this.sendData.cbResult=function(t){},this.sendData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.send(this.requestParams)},e.prototype.inputDataResultColumnsChange=function(t){if(this.sendData.isBusy())this.sendData.cbStop();else{this.lastReceivedWidthJSON=t;var e=new n.L;e.setInputJSON(this.lastReceivedWidthJSON),"Column width saved ok"!==e.getRequestReplyMessage()&&this.swtAlert.error(n.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+e.getRequestReplyMessage())}},e.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0,this.currBox.enabled=!0,this.zeroBox.enabled=!0,this.alertBox.enabled=!0,this.customSummary.enableTree()},e.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1,this.currBox.enabled=!1,this.zeroBox.enabled=!1,this.alertBox.enabled=!1,this.customSummary.disableTree(),this.customSummary.tree.removeEventListener("treeItemClick",this.summaryTreeEventHandler,!1)},e.prototype.extractParentFilterDataFromNode=function(t,e){if(null!=t&&null!=t&&null!=t.treeLevelName){if("category_id"==t.treeLevelName||"CATEGORY_ID"==t.treeLevelName)e+=" AND P_SCENARIO."+t.treeLevelName+" = '"+t.treeLevelValue+"'";else e+=new RegExp(/^\d{4}-([0]\d|1[0-2])-([0-2]\d|3[01])$/).test(t.treeLevelValue)?"  AND PSI."+t.treeLevelName+" =  TO_DATE('"+t.treeLevelValue+"','YYYY-MM-DD')":" AND PSI."+t.treeLevelName+" = '"+t.treeLevelValue+"'";e=this.extractParentFilterDataFromNode(t.parentData,e)}return e},e.prototype.loadSummary=function(){var t=this,e="",s=null,i=null;e=this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,e),this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this),this.summaryData.cbResult=function(e){t.summaryDataResult(e)},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.status=this.status.selectedValue,this.requestParams.resolvedOnDate=this.resolvedOnDate.text,this.firstLoad?this.requestParams.firstLoad="true":(this.requestParams.firstLoad="false",this.customSummary.saveTreeOpenState(),0===this.previousSelectedTabIndex?(this.firstTabTreeOpenedItems=this.customSummary.treeOpenedItems.concat(),this.firstTabTreeClosedItems=this.customSummary.treeClosedItems.concat()):(this.secondTabTreeOpenedItems=this.customSummary.treeOpenedItems.concat(),this.secondTabTreeClosedItems=this.customSummary.treeClosedItems.concat()));var a=n.x.call("eval","document.hasFocus()");if(this.requestParams.hasFocus=a,this.requestParams.selectedTab=this.tabCategoryList.getSelectedTab().id,-1!==this.tabCategoryList.selectedIndex&&(this.requestParams.selectedCategory=this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName),this.requestParams.fromWorkFlow="false",this.requestParams.currThreshold=1==this.currBox.selected?"Y":"N",this.requestParams.facilityGuiId=this.facilityGui,this.requestParams.sqlParams=this.sqlParams,this.requestParams.scenarioId=this.firstLoad?this.treeLevelValue:this.scenarioId,this.requestParams.isScenarioAlertable=this.customSummary.getIsScenarioAlertable(),this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.fromScreenFacility=this.fromScreenFacility,this.requestParams.currencyCode=this.tooltipCurrencyCode,this.requestParams.selectedDate=this.tooltipSelectedDate,this.requestParams.selectedAccountId=this.tooltipSelectedAccount,this.requestParams.selectedMvtId=this.tooltipMvtId,this.requestParams.selectedMatchId=this.tooltipMatchId,this.requestParams.selectedTreeItemQuery=e,this.requestParams.statusChanged=this.statusChanged,this.customSummary.tree&&this.customSummary.tree.selectedItem&&this.customSummary.tree.selectedItem.record_scenario_instances&&"Y"==this.customSummary.tree.selectedItem.record_scenario_instances?(this.requestParams.selectedSort=this.customSummary.summaryGrid.sortedGridColumnId+"|"+(this.customSummary.summaryGrid.sortDirection?"DESC":"ASC"),s=this.getfilteredGridColumnsForInstanceGrid()):(i=this.customSummary.getSortedGridColumn(),this.requestParams.selectedSort=i,s=this.getfilteredGridColumns()),this.requestParams.selectedFilter=s,null!=this.tooltipOtherParams)for(var r in this.tooltipOtherParams)this.tooltipOtherParams.hasOwnProperty(r)&&(this.requestParams[r]=this.tooltipOtherParams[r]);this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioSummaryDetails",this.summaryData.encodeURL=!1,this.summaryData.url=this.baseURL+this.actionPath+this.actionMethod,this.summaryData.send(this.requestParams),null!=this.customSummary.tree.selectedItem&&(this.lastSelectedValue=this.customSummary.tree.selectedItem.id)},e.prototype.updateScenarioDetails=function(){var t=this;this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this);var e="";e=this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,e),this.summaryData.cbResult=function(e){t.summaryDataResult(e)},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.firstLoad?this.requestParams.firstLoad="true":this.requestParams.firstLoad="false";var s=n.x.call("eval","document.hasFocus()");this.requestParams.hasFocus=s,this.requestParams.selectedTab=this.tabCategoryList.getSelectedTab().id,-1!==this.tabCategoryList.selectedIndex&&(this.requestParams.selectedCategory=this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName),this.requestParams.fromScreenFacility=this.fromScreenFacility,this.requestParams.facilityGuiId=this.facilityGui,this.requestParams.sqlParams=this.sqlParams,this.requestParams.scenarioId=this.scenarioId,this.requestParams.selectedTreeItemQuery=e,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioSummaryDetails",this.summaryData.encodeURL=!1,this.summaryData.url=this.baseURL+this.actionPath+this.actionMethod,this.summaryData.send(this.requestParams)},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lostConnectionText.visible=!1,""!==this.jsonReader.getScreenAttributes().displayedDate&&(this.displayedDate=this.jsonReader.getScreenAttributes().displayedDate),""!==this.jsonReader.getScreenAttributes().dateFormat&&(this.dateFormat=this.jsonReader.getScreenAttributes().dateFormat,this.resolvedOnDate.formatString=this.dateFormat.toLowerCase()),""!==this.jsonReader.getScreenAttributes().currencyFormat&&(this.currencyFormat=this.jsonReader.getScreenAttributes().currencyFormat),""!==this.jsonReader.getScreenAttributes().lastRefTime){var e=this.jsonReader.getScreenAttributes().lastRefTime;this.lastRefTime.text=e.replace(/\\u0028/g,"(").replace(/\\u0029/g,")"),this.lastRefTime.visible=!0}if(this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!==this.prevRecievedJSON){if(null!==this.autoRefresh&&this.autoRefresh.stop(),this.resolvedOnDate.text=this.displayedDate,this.disableInterface(),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.entityCombo.selectedItem.value,this.currBox.selected="Y"===this.jsonReader.getScreenAttributes().currencythreshold,this.zeroBox.selected=Boolean(this.jsonReader.getScreenAttributes().hidezerocounts),this.alertBox.selected=Boolean(this.jsonReader.getScreenAttributes().alertablescenarios),this.popupScenarios="true"===this.jsonReader.getScreenAttributes().popupScenarios?"true":"false",this.flashScenarios="true"===this.jsonReader.getScreenAttributes().flashScenarios?"true":"false",this.emailScenarios="true"===this.jsonReader.getScreenAttributes().emailScenarios,this.firstLoad&&(this.tabDataCategory=[],0===this.tabCategoryList.getTabChildren().length&&this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab))for(var s=0;s<2;s++)this.tabDataCategory[s]=this.tabCategoryList.addChild(n.Xb),this.tabDataCategory[s].id=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[s].tabId,this.tabDataCategory[s].label=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[s].tabName,this.tabDataCategory[s].tabName=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[s].tabName,this.tabDataCategory[s].count=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[s].count;var i=Number(this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.selectedIndex.index);this.tabCategoryList.selectedIndex=i}}else n.Wb.getPredictMessage("error.contactAdmin",null),this.swtAlert.error(n.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation());null==this.autoRefresh&&(this.refreshRate<5&&(this.refreshRate=60),this.autoRefresh=new n.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.dataRefresh.bind(this))),this.prevRecievedJSON=this.lastRecievedJSON,this.loadSummary()}this.previousSelectedTabIndex=this.tabCategoryList.selectedIndex,null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.summaryDataResult=function(t){var e=this,s=!1;this.summaryData.isBusy()?this.summaryData.cbStop():(this.lastRecievedSummaryJSON=t,this.summaryJSONReader.setInputJSON(this.lastRecievedSummaryJSON),this.summaryJSONReader.getRequestReplyStatus()?this.lastRecievedSummaryJSON!==this.prevRecievedSummaryJSON&&(this.customSummary.summaryGrid.selectedIndex=-1,this.optionIdTypes=this.summaryJSONReader.getSelects().select.find(function(t){return"otherIdTypeCombo"==t.id}).option,this.customSummary.dataProvider(this.lastRecievedSummaryJSON),this.summaryJSONReader.getSingletons().scenarioTotalForTab1&&(this.tabCategoryList.getChildAt(0).label=this.tabDataCategory[0].tabName+"("+this.summaryJSONReader.getSingletons().scenarioTotalForTab1+")"),this.summaryJSONReader.getSingletons().scenarioTotalForTab2&&(this.tabCategoryList.getChildAt(1).label=this.tabDataCategory[1].tabName+"("+this.summaryJSONReader.getSingletons().scenarioTotalForTab2+")"),0===this.tabCategoryList.selectedIndex?(this.lastSelectedId=this.fisrtTablastSelectedId,this.customSummary.treeOpenedItems=this.firstTabTreeOpenedItems.concat(),this.customSummary.treeClosedItems=this.firstTabTreeClosedItems.concat()):(this.lastSelectedId=this.secondTablastSelectedId,this.customSummary.treeOpenedItems=this.secondTabTreeOpenedItems.concat(),this.customSummary.treeClosedItems=this.secondTabTreeClosedItems.concat()),0===this.customSummary.treeOpenedItems.length&&(this.customSummary.tree.openItems=[]),0===this.customSummary.treeClosedItems.length&&(this.customSummary.tree.closeItems=[]),this.customSummary.setBaseURL(this.baseURL),this.customSummary.setActionPath(this.actionPath),this.firstLoad?(this.customSummary.tree.expandAll(n.o.LEVEL_2_STR),this.selectedNodeId?this.customSummary.tree.selectNodeByAttribute("id",this.selectedNodeId):this.customSummary.tree.selectNodeByAttribute("levelAsInt","2",!0),this.firstLoad=!1,s=!0):(this.customSummary.openTreeItems(),this.customSummary.tree.selectNodeByAttribute("id",this.lastSelectedNode?this.lastSelectedNode:"N_"+this.scenarioId)),!1===this.summaryHbox.visible&&(this.summaryHbox.visible=!0)):this.swtAlert.error(n.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+this.summaryJSONReader.getRequestReplyMessage()+"\n"+this.summaryJSONReader.getRequestReplyLocation()),this.prevRecievedSummaryJSON=this.lastRecievedSummaryJSON),this.refreshTreeItemRender(),setTimeout(function(){e.handleButtonsStatus()},100),this.previousSelectedTabIndex=this.tabCategoryList.selectedIndex,this.customSummary.summaryGrid.selectedIndex=this.selectedIndex,s&&this.customSummary.tree&&this.customSummary.tree.selectedItem&&(this.scenarioId=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level),this.requestParams.refreshGridOnly="true",this.loadSummary())},e.prototype.refreshTreeItemRender=function(){},e.prototype.summaryTreeEventHandler=function(t){var e=!1;this.customSummary.summaryGrid.selectedIndex=-1;var s=this.customSummary.tree.selectedItem.data.treeLevelValue;s!=this.lastSelectedValue&&(e=!0),this.lastSelectedValue=s,this.lastSelectedNode=this.customSummary.tree.selectedItem.id;var i=t.level,a=this.getScenarioId(t,i);e&&(this.requestParams.refreshGridOnly="true",this.scenarioId=a,this.customSummary.summaryGrid.resetFilter(),this.customSummary.summaryGrid.filteredGridColumns="",this.customSummary.resetGridData(),this.loadSummary())},e.prototype.getScenarioId=function(t,e){var s=null;switch(e){case"Level1":s=this.scenarioId;break;case"Level2":s=t.data.treeLevelValue;break;case"Level3":s=t.data.parentData.treeLevelValue;break;case"Level4":s=t.data.parentData.parentData.treeLevelValue;break;case"Level5":s=t.data.parentData.parentData.parentData.treeLevelValue;break;case"Level6":s=t.data.parentData.parentData.parentData.parentData.treeLevelValue;break;case"Level7":s=t.data.parentData.parentData.parentData.parentData.parentData.treeLevelValue}return s},e.prototype.cellLogic=function(t){if(this.customSummary.summaryGrid.selectedIndex>-1){this.scenarioId=t.target.data.scenarioId;var e=t.target.field,s=t.target.data;if(s.slickgrid_rowcontent.attributesXml&&(this.attributeJSON=s.slickgrid_rowcontent.attributesXml.code,this.attributeJSON&&this.isValidJSON(this.attributeJSON))){var i=JSON.parse(this.attributeJSON);i=JSON.stringify(i,function(t,e){return""!==t&&"rowset"!==t&&"row"!==t&&"object"==typeof e?Object.entries(e).reduce(function(t,e){var s=e[0],i=e[1];return t[s]=JSON.stringify(i),t},{}):e},4),this.attributeJSON=this.htmlEntities(i.replace(/\\"/g,'"').replace(/"{/g,"{").replace(/}"/g,"}"))}(s.slickgrid_rowcontent[e]?s.slickgrid_rowcontent[e].clickable:null)&&("Y"==this.customSummary.rcdScenInstance?this.clickLink("fromLink"):this.openFacility(s,e))}},e.prototype.htmlEntities=function(t){try{return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/ /g,"&nbsp;")}catch(e){console.log("error",e,t)}},e.prototype.clickLink=function(t){var e=this;this.requestParams=[],this.detailsData.cbStart=this.startOfComms.bind(this),this.detailsData.cbStop=this.endOfComms.bind(this),this.detailsData.cbFault=this.inputDataFault.bind(this),this.detailsData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getInstanceXml",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.instanceId=this.customSummary.summaryGrid.selectedItem.id.content,this.detailsData.url=this.baseURL+this.actionPath+this.actionMethod,this.detailsData.cbResult=function(t){e.getInstanceDetails(t)},this.detailsData.send(this.requestParams),this.source=t},e.prototype.getInstanceDetails=function(t){if(this.detailsData.isBusy())this.detailsData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!==this.prevRecievedJSON){var e=this.jsonReader.getSingletons().instanceXml.replace(/#/g,">");if(e&&this.isValidJSON(e)){var s=JSON.parse(e);s=JSON.stringify(s,function(t,e){return""!==t&&"rowset"!==t&&"row"!==t&&"object"==typeof e?Object.entries(e).reduce(function(t,e){var s=e[0],i=e[1];return t[s]=JSON.stringify(i),t},{}):e},4),this.attributeJSON=this.htmlEntities(s.replace(/\\"/g,'"').replace(/"{/g,"{").replace(/}"/g,"}"))}else this.attributeJSON=e;"fromLink"==this.source?(this.win=n.Eb.createPopUp(this,o.a,{title:"Scenario Instance Attributes",attributeXmlText:this.attributeJSON}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="450",this.win.height="500",this.win.showControls=!0,this.win.id="AttributeXML",this.win.display()):this.openInstDetails(e)}},e.prototype.getfilteredGridColumns=function(){try{var t="",e=[];if(""===this.customSummary.summaryGrid.filteredGridColumns)return t="";for(var s=this.customSummary.summaryGrid.getFilterColumns(),i=this.customSummary.summaryGrid.filteredGridColumns,a=0;a<s.length;a++)e[a]=s[a].field;if(""!=i){var r=i.split("|");for(a=0;a<e.length-1;a++)t=t+r[a]+"|"}else t=this.customSummary.summaryGrid.getFilteredGridColumns();return t.slice(4,t.length)}catch(n){}},e.prototype.getfilteredGridColumnsForInstanceGrid=function(){var t="",e=[],s=this.customSummary.summaryGrid.getFilterColumns(),i=this.customSummary.summaryGrid.filteredGridColumns,a="dd/MM/yyyy"==this.dateFormat?"dd/mm/yyyy hh24:mi:ss":"mm/dd/yyyy hh24:mi:ss";try{for(var r=0;r<s.length;r++)e[r]=s[r].field;if(""!=i){var n=i.split("|");for(r=0;r<e.length;r++){if(""!=n[r]){if("All"!=n[r]&&null!=n[r])if("_"==e[r][e[r].length-1]&&(e[r]=e[r].slice(0,-1)),"_id"==e[r])t=t+"ID='"+n[r]+"' and ";else if("scenarioId"==e[r])t=t+"SCENARIO_ID='"+n[r]+"' and ";else if("uniqueIdentifier"==e[r])t=t+"UNIQUE_IDENTIFIER='"+n[r]+"' and ";else if("status"==e[r]){var l="";switch(n[r]){case"Active":l="A";break;case"Resolved":l="R";break;case"Pending":l="P";break;case"Overdue":l="O";break;default:l="A"}t=t+"STATUS='"+l+"' and "}else if("raisedDatetime"==e[r])t=t+"RAISED_DATETIME=TO_DATE ('"+n[r]+"' , '"+a+"') and ";else if("lastRaisedDatetime"==e[r])t=t+"LAST_RAISED_DATETIME=TO_DATE ('"+n[r]+"' , '"+a+"') and ";else if("resolvedDatetime"==e[r])t=t+"RESOLVED_DATETIME=TO_DATE ('"+n[r]+"' , '"+a+"') and ";else if("resolvedByUser"==e[r])t=t+"RESOLVED_BY_USER='"+n[r]+"' and ";else if("eventsLaunchStatus"==e[r]){var o="";switch(n[r]){case"No events to launch":o="N";break;case"Waiting to Launch":o="W";break;case"Launched":o="L";break;case"Failed":o="F";break;default:o="W"}t=t+"EVENTS_LAUNCH_STATUS='"+o+"' and "}else if("hostId"==e[r])t=t+"HOST_ID='"+n[r]+"' and ";else if("entityId"==e[r])t=t+"ENTITY_ID='"+n[r]+"' and ";else if("currencyCode"==e[r])t=t+"CURRENCY_CODE='"+n[r]+"' and ";else if("accountId"==e[r])t=t+"ACCOUNT_ID='"+n[r]+"' and ";else if("amount"==e[r]){var u=void 0;null!=n[r]&&null!=n[r]&&(u="currencyPat2"==this.currencyFormat?Number(n[r].replace(/\./g,"").replace(/,/g,".")):Number(n[r].replace(/,/g,""))),t=t+"AMOUNT='"+u+"' and "}else"sign"==e[r]?t=t+"SIGN='"+n[r]+"' and ":"overThreshold"==e[r]?t=t+"OVER_THRESHOLD='"+n[r]+"' and ":"movementId"==e[r]?t=t+"MOVEMENT_ID='"+n[r]+"' and ":"matchId"==e[r]?t=t+"MATCH_ID='"+n[r]+"' and ":"sweepId"==e[r]?t=t+"SWEEP_ID='"+n[r]+"' and ":"paymentId"==e[r]?t=t+"PAYMENT_ID='"+n[r]+"' and ":"valueDate"==e[r]?t=t+"VALUE_DATE=TO_DATE ('"+n[r]+"' , '"+a+"') and ":"otherId"==e[r]?t=t+"OTHER_ID='"+n[r]+"' and ":"otherIdType"==e[r]&&(t=t+"OTHER_ID_TYPE='"+n[r]+"' and ")}else"_"==e[r][e[r].length-1]&&(e[r]=e[r].slice(0,-1)),"scenarioId"==e[r]?t+="SCENARIO_ID is null and ":"uniqueIdentifier"==e[r]?t+="UNIQUE_IDENTIFIER is null and ":"status"==e[r]?t+="STATUS is null and ":"raisedDatetime"==e[r]?t+="RAISED_DATETIME is null and ":"lastRaisedDatetime"==e[r]?t+="LAST_RAISED_DATETIME is null and ":"resolvedDatetime"==e[r]?t+="RESOLVED_DATETIME is null and ":"resolvedByUser"==e[r]?t+="RESOLVED_BY_USER is null and ":"eventsLaunchStatus"==e[r]?t+="EVENTS_LAUNCH_STATUS is null and ":"hostId"==e[r]?t+="HOST_ID is null and ":"entityId"==e[r]?t+="ENTITY_ID is null and ":"currencyCode"==e[r]?t+="CURRENCY_CODE is null and ":"accountId"==e[r]?t+="ACCOUNT_ID is null and ":"amount"==e[r]?t+="AMOUNT is null and ":"sign"==e[r]?t+="SIGN is null and ":"overThreshold"==e[r]?t+="OVER_THRESHOLD is null and ":"movementId"==e[r]?t+="MOVEMENT_ID is null and ":"matchId"==e[r]?t+="MATCH_ID is null and ":"sweepId"==e[r]?t+="SWEEP_ID is null and ":"paymentId"==e[r]?t+="PAYMENT_ID is null and ":"valueDate"==e[r]?t+="VALUE_DATE is null and ":"otherId"==e[r]?t+="OTHER_ID is null and ":"otherIdType"==e[r]&&(t+="OTHER_ID_TYPE is null and ")}}return t=t.substring(0,t.length-5)}catch(h){console.log("error",h)}},e.prototype.doUpdateSortFilter=function(){var t=null,e=null,s=this.zeroBox.selected.toString(),i=this.currBox.selected,a=this.alertBox.selected.toString();try{this.customSummary.summaryGrid.selectedItem=null,this.requestParams=[],this.firstLoad=!1,this.requestParams.refreshGridOnly="true","Y"==this.customSummary.rcdScenInstance?(this.requestParams.selectedSort=this.customSummary.summaryGrid.sortedGridColumnId+"|"+(this.customSummary.summaryGrid.sortDirection?"DESC":"ASC"),e=this.getfilteredGridColumnsForInstanceGrid()):(t=this.customSummary.getSortedGridColumn(),this.requestParams.selectedSort=t,e=this.getfilteredGridColumns()),this.requestParams.selectedFilter=e,this.requestParams.callerMethod=this.callerMethod,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.userId=this.currentUser,this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.zeroBalances=s,this.requestParams.currThreshold=1==i?"Y":"N",this.requestParams.selectedScenario=this.customSummary.getSelectedItemID(),this.requestParams.isScenarioAlertable=this.customSummary.getIsScenarioAlertable(),this.requestParams.dividerPosition=this.customSummary.getDividerPosition(),this.requestParams.alertScenarios=a,this.requestParams.flashScenarios=this.flashScenarios,this.requestParams.popupScenarios=this.popupScenarios,this.requestParams.emailScenarios=this.emailScenarios.toString(),this.requestParams.fromScreenFacility=this.fromScreenFacility,this.customSummary.saveTreeOpenState(),0===this.previousSelectedTabIndex?(this.firstTabTreeOpenedItems=this.customSummary.treeOpenedItems.concat(),this.firstTabTreeClosedItems=this.customSummary.treeClosedItems.concat()):(this.secondTabTreeOpenedItems=this.customSummary.treeOpenedItems.concat(),this.secondTabTreeClosedItems=this.customSummary.treeClosedItems.concat()),this.loadSummary()}catch(r){}},e.prototype.updateData=function(t){this.selectedIndex=this.customSummary.summaryGrid.selectedIndex;var e=this.zeroBox.selected.toString(),s=this.currBox.selected,i=this.alertBox.selected.toString();if(this.requestParams=[],this.requestParams.callerMethod=this.callerMethod,this.requestParams.resolvedOnDate=this.resolvedOnDate.text,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.userId=this.currentUser,this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.zeroBalances=e,this.requestParams.currThreshold=1==s?"Y":"N",this.requestParams.selectedScenario=this.customSummary.getSelectedItemID(),this.requestParams.isScenarioAlertable=this.customSummary.getIsScenarioAlertable(),this.requestParams.dividerPosition=this.customSummary.getDividerPosition(),this.requestParams.selectedSort=this.customSummary.getSortedGridColumn(),this.requestParams.alertScenarios=i,this.requestParams.flashScenarios=this.flashScenarios,this.requestParams.popupScenarios=this.popupScenarios,this.requestParams.emailScenarios=this.emailScenarios.toString(),null!=this.customSummary.tree.selectedItem?this.scenarioId=this.getScenarioId(this.customSummary.tree.selectedItem,this.customSummary.tree.selectedItem.level):this.scenarioId="",this.requestParams.scenarioId=this.scenarioId,this.requestParams.selectedTab=this.tabCategoryList.getSelectedTab().id,this.requestParams.fromScreenFacility=this.fromScreenFacility,-1!==this.tabCategoryList.selectedIndex&&(this.requestParams.selectedCategory=this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName),this.customSummary.tree.selectedItem?0===this.previousSelectedTabIndex?this.fisrtTablastSelectedId=this.customSummary.tree.selectedItem.id:this.secondTablastSelectedId=this.customSummary.tree.selectedItem.id:0===this.previousSelectedTabIndex?this.fisrtTablastSelectedId=null:this.secondTablastSelectedId=null,this.customSummary.saveTreeOpenState(),0===this.previousSelectedTabIndex?(this.firstTabTreeOpenedItems=this.customSummary.treeOpenedItems.concat(),this.firstTabTreeClosedItems=this.customSummary.treeClosedItems.concat()):(this.secondTabTreeOpenedItems=this.customSummary.treeOpenedItems.concat(),this.secondTabTreeClosedItems=this.customSummary.treeClosedItems.concat()),t===this.TREE)this.loadSummary();else if(t===this.SCENARIO){var a="";this.customSummary.tree.selectedItem&&(a=this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,a)),this.requestParams.isScenarioAlertable=this.customSummary.getIsScenarioAlertable(),this.requestParams.selectedTreeItemQuery=a,this.inputData.send(this.requestParams)}this.previousSelectedTabIndex=this.tabCategoryList.selectedIndex,this.customSummary.summaryGrid.selectedIndex=this.selectedIndex},e.prototype.openedCombo=function(t){this.comboOpen=!0,this.inputData.isBusy()&&(this.inputData.cancel(),t.currentTarget.interruptComms=!0)},e.prototype.closedCombo=function(t){this.comboOpen=!1,null!==t.triggerEvent&&"mouseDownOutside"===t.triggerEvent.type&&t.currentTarget.interruptComms&&(t.currentTarget.interruptComms=!1,this.updateData("scenario"))},e.prototype.entityComboChange=function(t){this.comboChange=!0,this.updateData("scenario")},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface()},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface()},e.prototype.inputDataFault=function(t){this.lostConnectionText.visible=!0,this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error(this.invalidComms),null!==this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.disableButtons=function(){this.resolveButton.enabled=!1,this.resolveButton.buttonMode=!1,this.reActiveButton.enabled=!1,this.reActiveButton.buttonMode=!1,this.detailsButton.enabled=!1,this.detailsButton.buttonMode=!1,this.goToButton.enabled=!1,this.goToButton.buttonMode=!1},e.prototype.enableButtons=function(){this.detailsButton.enabled=!0,this.detailsButton.buttonMode=!0,this.customSummary.facilityId&&"None"!=this.customSummary.facilityId?(this.goToButton.enabled=!0,this.goToButton.buttonMode=!0):(this.goToButton.enabled=!1,this.goToButton.buttonMode=!1)},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate),this.screenVersion.svContextMenu.customItems=[];var t=new n.n("Expand All Groups");t.MenuItemSelect=this.contextMenuAction.bind(this,"expandAll"),this.screenVersion.svContextMenu.customItems.push(t);var e=new n.n("Expand To Level 1");e.MenuItemSelect=this.contextMenuAction.bind(this,"expandLevel1"),this.screenVersion.svContextMenu.customItems.push(e);var s=new n.n("Expand To Level 2");s.MenuItemSelect=this.contextMenuAction.bind(this,"expandLevel2"),this.screenVersion.svContextMenu.customItems.push(s);var i=new n.n("Collapse All Groups");i.MenuItemSelect=this.contextMenuAction.bind(this,"collapseAll"),this.screenVersion.svContextMenu.customItems.push(i),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.contextMenuAction=function(t){"expandAll"==t?this.customSummary.tree.expandAll():"expandLevel1"==t?(this.customSummary.tree.collapseAll(),this.customSummary.tree.expandAll("1")):"expandLevel2"==t?(this.customSummary.tree.collapseAll(),this.customSummary.tree.expandAll("2")):"collapseAll"==t&&this.customSummary.tree.collapseAll()},e.prototype.showXMLSelect=function(t){null!==this.lastRecievedJSON?(this.showXMLPopup=n.Eb.createPopUp(this,n.M,{jsonData:this.lastRecievedJSON}),this.showXMLPopup.width="700",this.showXMLPopup.title="Summary Details JSON",this.showXMLPopup.height="350",this.showXMLPopup.enableResize=!1,this.showXMLPopup.showControls=!0,this.showXMLPopup.display()):this.swtAlert.warning(n.Wb.getPredictMessage("alert.scenarioSummary.noData",null),n.Wb.getPredictMessage("alert.scenarioSummary.noData.title",null))},e.prototype.showSummaryXMLSelect=function(t){null!==this.lastRecievedSummaryJSON?(this.showJSON=n.Eb.createPopUp(this,n.M,{jsonData:this.lastRecievedSummaryJSON}),this.showXMLPopup.width="700",this.showXMLPopup.title="Summary Details JSON",this.showXMLPopup.height="350",this.showXMLPopup.enableResize=!1,this.showXMLPopup.showControls=!0,this.showXMLPopup.display()):this.swtAlert.show(n.Wb.getPredictMessage("alert.scenarioSummary.noData",null),n.Wb.getPredictMessage("alert.scenarioSummary.noData.title",null))},e.prototype.closeHandler=function(t){n.x.call("close")},e.prototype.refreshFromJsp=function(){this.updateData("scenario")},e.prototype.tabBarLabelCategory=function(t){return t.label},e.prototype.doHelp=function(){try{n.x.call("help")}catch(t){n.Wb.logError(t,"Predict","ScenarioSummary","doHelp",0)}},e.prototype.changeInstStatus=function(){var t=this,e="";e=this.extractParentFilterDataFromNode(this.customSummary.tree.selectedItem,e),this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this),this.summaryData.cbResult=function(e){t.summaryDataResult(e)},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.firstLoad?this.requestParams.firstLoad="true":this.requestParams.firstLoad="false";var s=n.x.call("eval","document.hasFocus()");this.requestParams.hasFocus=s,this.requestParams.selectedTab=this.tabCategoryList.getSelectedTab().id,this.requestParams.refreshGridOnly="false",-1!==this.tabCategoryList.selectedIndex&&(this.requestParams.selectedCategory=this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName),this.requestParams.status=this.status.selectedValue,this.requestParams.resolvedOnDate=this.resolvedOnDate.text,this.requestParams.fromScreenFacility=this.fromScreenFacility,this.requestParams.selectedTreeItemQuery=e,this.actionPath="scenarioSummary.do?"},e.prototype.openInstDetails=function(t){var e=[];e.push({scenarioId:this.checkColumnsValue("scenarioId"),instanceId:this.checkColumnsValue("id"),status:this.checkColumnsValue("status"),eventStatus:this.checkColumnsValue("eventsLaunchStatus"),firstRaisedDate:this.checkColumnsValue("raisedDatetime"),lastRaisedDate:this.checkColumnsValue("lastRaisedDatetime"),resolvedDate:this.checkColumnsValue("resolvedDatetime"),resolvedUser:this.checkColumnsValue("resolvedByUser"),uniqueIdent:this.checkColumnsValue("uniqueIdentifier"),hostId:this.checkColumnsValue("hostId"),entityId:this.checkColumnsValue("entityId"),ccyCode:this.checkColumnsValue("currencyCode"),acctId:this.checkColumnsValue("accountId"),valueDate:this.checkColumnsValue("valueDate"),amount:this.checkColumnsValue("amount"),sign:this.checkColumnsValue("sign"),mvtId:this.checkColumnsValue("movementId"),matchId:this.checkColumnsValue("matchId"),sweepId:this.checkColumnsValue("sweepId"),payment:this.checkColumnsValue("paymentId"),otherId:this.checkColumnsValue("otherId"),otherIdTypesList:this.optionIdTypes,scenOtherIdType:this.checkColumnsValue("otherIdType"),firstRaisedUser:this.checkColumnsValue("raisedUser"),lastRaisedUser:this.checkColumnsValue("lastRaisedUser"),attributesXml:t,currBox:!0===this.currBox.selected?"Y":"N",scenarioTitle:this.customSummary.scenarioTitle,useGeneric:this.customSummary.useGeneric,facilityId:this.customSummary.facilityId,facilityName:this.customSummary.facilityName}),n.x.call("openInstDetails","openInstanceDetails",n.Z.encode64(JSON.stringify(e)))},e.prototype.checkColumnsValue=function(t){var e;return this.customSummary.summaryGrid.selectedItem&&(e=this.customSummary.summaryGrid.selectedItem[t]),e?e.content:""},e.prototype.goTo=function(t){var e=this.customSummary.summaryGrid.selectedItem.hostId.content,s=this.customSummary.summaryGrid.selectedItem.entityId.content,i=this.customSummary.summaryGrid.selectedItem.matchId.content,a=this.customSummary.summaryGrid.selectedItem.currencyCode.content,r=this.customSummary.summaryGrid.selectedItem.movementId.content,l=this.customSummary.summaryGrid.selectedItem.sweepId.content,o=this.customSummary.summaryGrid.selectedItem.otherId.content;n.x.call("goTo",this.customSummary.facilityId,e,s,i,a,r,l,o)},e.prototype.updateStatus=function(t){var e=this;this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this),this.summaryData.cbResult=function(t){e.requestParams.refreshGridOnly="true",e.loadSummary()},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.id=this.customSummary.summaryGrid.selectedItem.id.content,this.requestParams.oldStatus=this.customSummary.summaryGrid.selectedItem.status.content,this.requestParams.newStatus=t,this.actionPath="scenarioSummary.do?",this.actionMethod="method=updateScenInstanceStatus",this.summaryData.encodeURL=!1,this.summaryData.url=this.baseURL+this.actionPath+this.actionMethod,this.summaryData.send(this.requestParams)},e.prototype.openFacility=function(t,e){var s=t.slickgrid_rowcontent.entity.content,i=t.slickgrid_rowcontent.ccy.content,a=t.slickgrid_rowcontent[e].content,r=this.customSummary.facilityId,l=this.customSummary.facilityName,o=this.customSummary.useGeneric,u=this.customSummary.scenarioTitle,h=!0===this.currBox.selected?"Y":"N",c=this.customSummary.selectedscenario;n.x.call("openFacility",u,o,r,l,c,s,i,h,a)},e.prototype.checkUserAccess=function(){var t=this;this.customSummary.summaryGrid.selectedIndex>-1&&(this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this),this.summaryData.cbResult=function(e){t.getUserAccess(e)},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.instanceId=this.customSummary.summaryGrid.selectedItem.id.content,this.requestParams.scenarioId=this.customSummary.summaryGrid.selectedItem.scenarioId.content,this.requestParams.entityId=this.customSummary.summaryGrid.selectedItem.entityId.content,this.requestParams.ccyCode=this.customSummary.summaryGrid.selectedItem.currencyCode.content,this.requestParams.hostId=this.customSummary.summaryGrid.selectedItem.hostId.content,this.requestParams.fromWorkflow="false",this.actionPath="scenarioSummary.do?",this.actionMethod="method=checkUserInstAccess",this.summaryData.encodeURL=!1,this.summaryData.url=this.baseURL+this.actionPath+this.actionMethod,this.summaryData.send(this.requestParams))},e.prototype.getUserAccess=function(t){"true"==t.scenarioDetails.singletons.hasAccess?this.handleRctRslvButtons():(this.resolveButton.enabled=!1,this.resolveButton.buttonMode=!1,this.reActiveButton.enabled=!1,this.reActiveButton.buttonMode=!1),this.enableButtons(),this.customSummary.summaryGrid.selectedIndex=this.selectedIndex},e.prototype.handleRctRslvButtons=function(){var t=this.customSummary.summaryGrid.selectedItem.status.content;"Resolved"==t?(this.resolveButton.enabled=!1,this.resolveButton.buttonMode=!1,this.reActiveButton.enabled=!0,this.reActiveButton.buttonMode=!0):"Pending"==t||"Active"==t?(this.resolveButton.enabled=!0,this.resolveButton.buttonMode=!0,this.reActiveButton.enabled=!1,this.reActiveButton.buttonMode=!1):(this.resolveButton.enabled=!0,this.resolveButton.buttonMode=!0,this.reActiveButton.enabled=!0,this.reActiveButton.buttonMode=!0)},e.prototype.dataRefresh=function(t){this.comboOpen||this.updateData("scenario"),this.autoRefresh.stop()},e.prototype.isValidJSON=function(t){try{return t=JSON.parse(t),!0}catch(e){return!1}},e}(n.yb)),h=[{path:"",component:u}],c=(r.l.forChild(h),function(){return function(){}}()),d=s("pMnS"),m=s("RChO"),b=s("t6HQ"),y=s("WFGK"),g=s("5FqG"),p=s("Ip0R"),S=s("gIcY"),I=s("t/Na"),f=s("sE5F"),v=s("OzfB"),C=s("T7CS"),R=s("S7LP"),D=s("6aHO"),T=s("WzUx"),w=s("A7o+"),P=s("zCE2"),O=s("Jg5P"),L=s("3R0m"),N=s("hhbb"),x=s("5rxC"),A=s("Fzqc"),B=s("21Lb"),M=s("hUWP"),q=s("3pJQ"),E=s("V9q+"),_=s("VDKW"),G=s("kXfT"),J=s("BGbe");s.d(e,"AlertInstanceSummaryModuleNgFactory",function(){return k}),s.d(e,"RenderType_AlertInstanceSummary",function(){return W}),s.d(e,"View_AlertInstanceSummary_0",function(){return V}),s.d(e,"View_AlertInstanceSummary_Host_0",function(){return U}),s.d(e,"AlertInstanceSummaryNgFactory",function(){return H});var k=i.Gb(c,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[d.a,m.a,b.a,y.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,H]],[3,i.n],i.J]),i.Rb(4608,p.m,p.l,[i.F,[2,p.u]]),i.Rb(4608,S.c,S.c,[]),i.Rb(4608,S.p,S.p,[]),i.Rb(4608,I.j,I.p,[p.c,i.O,I.n]),i.Rb(4608,I.q,I.q,[I.j,I.o]),i.Rb(5120,I.a,function(t){return[t,new n.tb]},[I.q]),i.Rb(4608,I.m,I.m,[]),i.Rb(6144,I.k,null,[I.m]),i.Rb(4608,I.i,I.i,[I.k]),i.Rb(6144,I.b,null,[I.i]),i.Rb(4608,I.f,I.l,[I.b,i.B]),i.Rb(4608,I.c,I.c,[I.f]),i.Rb(4608,f.c,f.c,[]),i.Rb(4608,f.g,f.b,[]),i.Rb(5120,f.i,f.j,[]),i.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),i.Rb(4608,f.f,f.a,[]),i.Rb(5120,f.d,f.k,[f.h,f.f]),i.Rb(5120,i.b,function(t,e){return[v.j(t,e)]},[p.c,i.O]),i.Rb(4608,C.a,C.a,[]),i.Rb(4608,R.a,R.a,[]),i.Rb(4608,D.a,D.a,[i.n,i.L,i.B,R.a,i.g]),i.Rb(4608,T.c,T.c,[i.n,i.g,i.B]),i.Rb(4608,T.e,T.e,[T.c]),i.Rb(4608,w.l,w.l,[]),i.Rb(4608,w.h,w.g,[]),i.Rb(4608,w.c,w.f,[]),i.Rb(4608,w.j,w.d,[]),i.Rb(4608,w.b,w.a,[]),i.Rb(4608,w.k,w.k,[w.l,w.h,w.c,w.j,w.b,w.m,w.n]),i.Rb(4608,T.i,T.i,[[2,w.k]]),i.Rb(4608,T.r,T.r,[T.L,[2,w.k],T.i]),i.Rb(4608,T.t,T.t,[]),i.Rb(4608,T.w,T.w,[]),i.Rb(1073742336,r.l,r.l,[[2,r.r],[2,r.k]]),i.Rb(1073742336,p.b,p.b,[]),i.Rb(1073742336,S.n,S.n,[]),i.Rb(1073742336,S.l,S.l,[]),i.Rb(1073742336,P.a,P.a,[]),i.Rb(1073742336,O.a,O.a,[]),i.Rb(1073742336,S.e,S.e,[]),i.Rb(1073742336,L.a,L.a,[]),i.Rb(1073742336,w.i,w.i,[]),i.Rb(1073742336,T.b,T.b,[]),i.Rb(1073742336,I.e,I.e,[]),i.Rb(1073742336,I.d,I.d,[]),i.Rb(1073742336,f.e,f.e,[]),i.Rb(1073742336,N.b,N.b,[]),i.Rb(1073742336,x.b,x.b,[]),i.Rb(1073742336,v.c,v.c,[]),i.Rb(1073742336,A.a,A.a,[]),i.Rb(1073742336,B.d,B.d,[]),i.Rb(1073742336,M.c,M.c,[]),i.Rb(1073742336,q.a,q.a,[]),i.Rb(1073742336,E.a,E.a,[[2,v.g],i.O]),i.Rb(1073742336,_.b,_.b,[]),i.Rb(1073742336,G.a,G.a,[]),i.Rb(1073742336,J.b,J.b,[]),i.Rb(1073742336,n.Tb,n.Tb,[]),i.Rb(1073742336,c,c,[]),i.Rb(256,I.n,"XSRF-TOKEN",[]),i.Rb(256,I.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,w.m,void 0,[]),i.Rb(256,w.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,r.i,function(){return[[{path:"",component:u}]]},[])])}),F=[[""]],W=i.Hb({encapsulation:0,styles:F,data:{}});function V(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{resolveButton:0}),i.Zb(402653184,3,{reActiveButton:0}),i.Zb(402653184,4,{goToButton:0}),i.Zb(402653184,5,{detailsButton:0}),i.Zb(402653184,6,{closeButton:0}),i.Zb(402653184,7,{refreshButton:0}),i.Zb(402653184,8,{lblCombo:0}),i.Zb(402653184,9,{selectedEntity:0}),i.Zb(402653184,10,{currLabel:0}),i.Zb(402653184,11,{zeroLabel:0}),i.Zb(402653184,12,{alertLabel:0}),i.Zb(402653184,13,{resolvedOnLbl:0}),i.Zb(402653184,14,{entityCombo:0}),i.Zb(402653184,15,{currBox:0}),i.Zb(402653184,16,{zeroBox:0}),i.Zb(402653184,17,{alertBox:0}),i.Zb(402653184,18,{loadingImage:0}),i.Zb(402653184,19,{exportContainer:0}),i.Zb(402653184,20,{lostConnectionText:0}),i.Zb(402653184,21,{lastRefTime:0}),i.Zb(402653184,22,{lastRefTimeLabel:0}),i.Zb(402653184,23,{tabCategoryList:0}),i.Zb(402653184,24,{displayContainerPredict:0}),i.Zb(402653184,25,{displayContainerPCM:0}),i.Zb(402653184,26,{statusLabel:0}),i.Zb(402653184,27,{status:0}),i.Zb(402653184,28,{all:0}),i.Zb(402653184,29,{active:0}),i.Zb(402653184,30,{resolved:0}),i.Zb(402653184,31,{pending:0}),i.Zb(402653184,32,{overdue:0}),i.Zb(402653184,33,{allOpen:0}),i.Zb(402653184,34,{summaryGridContainer:0}),i.Zb(402653184,35,{summaryHbox:0}),i.Zb(402653184,36,{resolvedOnDate:0}),(t()(),i.Jb(36,0,null,null,103,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,s){var i=!0,a=t.component;"creationComplete"===e&&(i=!1!==a.onLoad()&&i);return i},g.ad,g.hb)),i.Ib(37,4440064,null,0,n.yb,[i.r,n.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(38,0,null,0,101,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["verticalGap","0"],["width","100%"]],null,null,null,g.od,g.vb)),i.Ib(39,4440064,null,0,n.ec,[i.r,n.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),i.Jb(40,0,null,0,65,"SwtCanvas",[["height","15%"],["minWidth","1110"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(41,4440064,null,0,n.db,[i.r,n.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(42,0,null,0,63,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),i.Ib(43,4440064,null,0,n.ec,[i.r,n.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(44,0,null,0,61,"HBox",[["height","100%"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(45,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(46,0,null,0,37,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),i.Ib(47,4440064,null,0,n.ec,[i.r,n.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(48,0,null,0,7,"HBox",[["height","40"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(49,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(50,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblCombo"],["width","50"]],null,null,null,g.Yc,g.fb)),i.Ib(51,4440064,[[8,4],["lblCombo",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(52,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,s){var a=!0,r=t.component;"window:mousewheel"===e&&(a=!1!==i.Tb(t,53).mouseWeelEventHandler(s.target)&&a);"change"===e&&(a=!1!==r.entityComboChange(s)&&a);return a},g.Pc,g.W)),i.Ib(53,4440064,[[14,4],["entityCombo",4]],0,n.gb,[i.r,n.i],{dataLabel:[0,"dataLabel"],id:[1,"id"]},{change_:"change"}),(t()(),i.Jb(54,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"],["text",""],["textAlign","left"]],null,null,null,g.Yc,g.fb)),i.Ib(55,4440064,[[9,4],["selectedEntity",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textAlign:[1,"textAlign"],paddingLeft:[2,"paddingLeft"],text:[3,"text"],fontWeight:[4,"fontWeight"]},null),(t()(),i.Jb(56,0,null,0,22,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(57,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(58,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","statusLabel"],["width","55"]],null,null,null,g.Yc,g.fb)),i.Ib(59,4440064,[[26,4],["statusLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(60,0,null,0,14,"SwtRadioButtonGroup",[["align","horizontal"],["id","status"],["width","430"]],null,[[null,"change"]],function(t,e,s){var i=!0,a=t.component;"change"===e&&(i=!1!==a.changeStatus()&&i);return i},g.ed,g.lb)),i.Ib(61,4440064,[[27,4],["status",4]],1,n.Hb,[I.c,i.r,n.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),i.Zb(603979776,37,{radioItems:1}),(t()(),i.Jb(63,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","all"],["value","All"],["width","50"]],null,null,null,g.fd,g.mb)),i.Ib(64,4440064,[[37,4],[28,4],["all",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(65,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","allOpen"],["selected","true"],["value",""],["width","80"]],null,null,null,g.fd,g.mb)),i.Ib(66,4440064,[[37,4],[33,4],["allOpen",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),i.Jb(67,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","active"],["value","A"],["width","65"]],null,null,null,g.fd,g.mb)),i.Ib(68,4440064,[[37,4],[29,4],["active",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(69,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","pending"],["value","P"],["width","80"]],null,null,null,g.fd,g.mb)),i.Ib(70,4440064,[[37,4],[31,4],["pending",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(71,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","overdue"],["value","O"],["width","80"]],null,null,null,g.fd,g.mb)),i.Ib(72,4440064,[[37,4],[32,4],["overdue",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(73,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","resolved"],["value","R"],["width","80"]],null,null,null,g.fd,g.mb)),i.Ib(74,4440064,[[37,4],[30,4],["resolved",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(75,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","resolvedOnLbl"],["width","85"]],null,null,null,g.Yc,g.fb)),i.Ib(76,4440064,[[13,4],["resolvedOnLbl",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(77,0,null,0,1,"SwtDateField",[["enabled","false"],["id","resolvedOnDate"],["width","70"]],null,[[null,"change"]],function(t,e,s){var i=!0,a=t.component;"change"===e&&(i=!1!==a.updateData("tree")&&i);return i},g.Tc,g.ab)),i.Ib(78,4308992,[[36,4],["resolvedOnDate",4]],0,n.lb,[i.r,n.i,i.T],{id:[0,"id"],enabled:[1,"enabled"],width:[2,"width"]},{changeEventOutPut:"change"}),(t()(),i.Jb(79,0,null,0,4,"HBox",[["height","20"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(80,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(81,0,null,0,2,"SwtTabNavigator",[["borderBottom","false"],["height","100%"],["id","tabCategoryList"],["width","100%"]],null,[[null,"onChange"]],function(t,e,s){var i=!0,a=t.component;"onChange"===e&&(i=!1!==a.updateData("tree")&&i);return i},g.id,g.pb)),i.Ib(82,4440064,[[23,4],["tabCategoryList",4]],1,n.Ob,[i.r,n.i,i.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],borderBottom:[3,"borderBottom"]},{onChange_:"onChange"}),i.Zb(603979776,38,{tabChildren:1}),(t()(),i.Jb(84,0,null,0,1,"VBox",[["height","100%"],["verticalGap","0"],["width","20%"]],null,null,null,g.od,g.vb)),i.Ib(85,4440064,null,0,n.ec,[i.r,n.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(86,0,null,0,19,"VBox",[["height","100%"],["verticalGap","0"],["width","30%"]],null,null,null,g.od,g.vb)),i.Ib(87,4440064,null,0,n.ec,[i.r,n.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(88,0,null,0,5,"HBox",[["height","30"],["horizontalAlign","right"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(89,4440064,null,0,n.C,[i.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(90,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["width","200"]],null,null,null,g.Yc,g.fb)),i.Ib(91,4440064,[[10,4],["currLabel",4]],0,n.vb,[i.r,n.i],{width:[0,"width"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(92,0,null,0,1,"SwtCheckBox",[["id","currBox"],["selected","false"],["value","N"]],null,[[null,"change"]],function(t,e,s){var i=!0,a=t.component;"change"===e&&(i=!1!==a.updateData("scenario")&&i);return i},g.Oc,g.V)),i.Ib(93,4440064,[[15,4],["currBox",4]],0,n.eb,[i.r,n.i],{id:[0,"id"],value:[1,"value"],selected:[2,"selected"]},{change_:"change"}),(t()(),i.Jb(94,0,null,0,5,"HBox",[["height","30"],["horizontalAlign","right"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(95,4440064,null,0,n.C,[i.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(96,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["width","200"]],null,null,null,g.Yc,g.fb)),i.Ib(97,4440064,[[11,4],["zeroLabel",4]],0,n.vb,[i.r,n.i],{width:[0,"width"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(98,0,null,0,1,"SwtCheckBox",[["id","zeroBox"],["selected","false"],["value","N"]],null,[[null,"change"]],function(t,e,s){var i=!0,a=t.component;"change"===e&&(i=!1!==a.updateData("scenario")&&i);return i},g.Oc,g.V)),i.Ib(99,4440064,[[16,4],["zeroBox",4]],0,n.eb,[i.r,n.i],{id:[0,"id"],value:[1,"value"],selected:[2,"selected"]},{change_:"change"}),(t()(),i.Jb(100,0,null,0,5,"HBox",[["height","30"],["horizontalAlign","right"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(101,4440064,null,0,n.C,[i.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(102,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["width","200"]],null,null,null,g.Yc,g.fb)),i.Ib(103,4440064,[[12,4],["alertLabel",4]],0,n.vb,[i.r,n.i],{width:[0,"width"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(104,0,null,0,1,"SwtCheckBox",[["id","alertBox"],["selected","false"],["value","N"]],null,[[null,"change"]],function(t,e,s){var i=!0,a=t.component;"change"===e&&(i=!1!==a.updateData("scenario")&&i);return i},g.Oc,g.V)),i.Ib(105,4440064,[[17,4],["alertBox",4]],0,n.eb,[i.r,n.i],{id:[0,"id"],value:[1,"value"],selected:[2,"selected"]},{change_:"change"}),(t()(),i.Jb(106,0,null,0,1,"HBox",[["height","73%"],["id","summaryHbox"],["minWidth","1110"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(107,4440064,[[35,4],["summaryHbox",4]],0,n.C,[i.r,n.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"]},null),(t()(),i.Jb(108,0,null,0,11,"HBox",[["height","30"],["minWidth","1110"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(109,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(110,0,null,0,9,"HBox",[["height","100%"],["paddingLeft","5"],["width","60%"]],null,null,null,g.Dc,g.K)),i.Ib(111,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(112,0,null,0,1,"SwtButton",[["enabled","false"],["id","detailsButton"]],null,[[null,"click"]],function(t,e,s){var i=!0,a=t.component;"click"===e&&(i=!1!==a.clickLink("frDetailsBtn")&&i);return i},g.Mc,g.T)),i.Ib(113,4440064,[[5,4],["detailsButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(114,0,null,0,1,"SwtButton",[["enabled","false"],["id","goToButton"]],null,[[null,"click"]],function(t,e,s){var i=!0,a=t.component;"click"===e&&(i=!1!==a.goTo(s)&&i);return i},g.Mc,g.T)),i.Ib(115,4440064,[[4,4],["goToButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(116,0,null,0,1,"SwtButton",[["enabled","false"],["id","reActiveButton"]],null,[[null,"click"]],function(t,e,s){var i=!0,a=t.component;"click"===e&&(i=!1!==a.updateStatus("A")&&i);return i},g.Mc,g.T)),i.Ib(117,4440064,[[3,4],["reActiveButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(118,0,null,0,1,"SwtButton",[["enabled","false"],["id","resolveButton"]],null,[[null,"click"]],function(t,e,s){var i=!0,a=t.component;"click"===e&&(i=!1!==a.updateStatus("R")&&i);return i},g.Mc,g.T)),i.Ib(119,4440064,[[2,4],["resolveButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(120,0,null,0,19,"SwtCanvas",[["height","6%"],["minWidth","1110"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(121,4440064,null,0,n.db,[i.r,n.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(122,0,null,0,17,"HBox",[["height","100%"],["id","controlContainer"],["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(123,4440064,null,0,n.C,[i.r,n.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(124,0,null,0,5,"HBox",[["height","100%"],["paddingLeft","8"],["width","40%"]],null,null,null,g.Dc,g.K)),i.Ib(125,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(126,0,null,0,1,"SwtButton",[["enabled","false"],["id","refreshButton"]],null,[[null,"click"]],function(t,e,s){var i=!0,a=t.component;"click"===e&&(i=!1!==a.updateData("tree")&&i);return i},g.Mc,g.T)),i.Ib(127,4440064,[[7,4],["refreshButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(128,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,s){var i=!0,a=t.component;"click"===e&&(i=!1!==a.closeHandler(s)&&i);return i},g.Mc,g.T)),i.Ib(129,4440064,[[6,4],["closeButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(130,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","50%"]],null,null,null,g.Dc,g.K)),i.Ib(131,4440064,null,0,n.C,[i.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(132,0,null,0,1,"SwtText",[["height","16"],["id","lostConnectionText"],["right","45"],["styleName","redText"],["text","CONNECTION ERROR"],["visible","false"]],null,null,null,g.ld,g.qb)),i.Ib(133,4440064,[[20,4],["lostConnectionText",4]],0,n.Pb,[i.r,n.i],{id:[0,"id"],right:[1,"right"],styleName:[2,"styleName"],height:[3,"height"],visible:[4,"visible"],text:[5,"text"]},null),(t()(),i.Jb(134,0,null,0,1,"SwtText",[["fontWeight","normal"],["height","16"],["id","lastRefTimeLabel"],["right","65"]],null,null,null,g.ld,g.qb)),i.Ib(135,4440064,[[22,4],["lastRefTimeLabel",4]],0,n.Pb,[i.r,n.i],{id:[0,"id"],right:[1,"right"],height:[2,"height"],fontWeight:[3,"fontWeight"]},null),(t()(),i.Jb(136,0,null,0,1,"SwtText",[["height","16"],["id","lastRefTime"],["right","65"],["visible","false"],["width","90"]],null,null,null,g.ld,g.qb)),i.Ib(137,4440064,[[21,4],["lastRefTime",4]],0,n.Pb,[i.r,n.i],{id:[0,"id"],right:[1,"right"],width:[2,"width"],height:[3,"height"],visible:[4,"visible"]},null),(t()(),i.Jb(138,0,null,0,1,"SwtLoadingImage",[],null,null,null,g.Zc,g.gb)),i.Ib(139,114688,[[18,4],["loadingImage",4]],0,n.xb,[i.r],null,null)],function(t,e){t(e,37,0,"100%","100%");t(e,39,0,"0","100%","100%","5","5","5","5");t(e,41,0,"100%","15%","1110");t(e,43,0,"100%","100%");t(e,45,0,"100%","100%");t(e,47,0,"100%","100%");t(e,49,0,"100%","40");t(e,51,0,"lblCombo","50","bold");t(e,53,0,"entity","entityCombo");t(e,55,0,"selectedEntity","left","10","","normal");t(e,57,0,"100%","30");t(e,59,0,"statusLabel","55","bold");t(e,61,0,"status","430","horizontal");t(e,64,0,"all","50","status","All");t(e,66,0,"allOpen","80","status","","true");t(e,68,0,"active","65","status","A");t(e,70,0,"pending","80","status","P");t(e,72,0,"overdue","80","status","O");t(e,74,0,"resolved","80","status","R");t(e,76,0,"resolvedOnLbl","85","normal");t(e,78,0,"resolvedOnDate","false","70");t(e,80,0,"100%","20");t(e,82,0,"tabCategoryList","100%","100%","false");t(e,85,0,"0","20%","100%");t(e,87,0,"0","30%","100%");t(e,89,0,"right","100%","30");t(e,91,0,"200","bold");t(e,93,0,"currBox","N","false");t(e,95,0,"right","100%","30");t(e,97,0,"200","bold");t(e,99,0,"zeroBox","N","false");t(e,101,0,"right","100%","30");t(e,103,0,"200","bold");t(e,105,0,"alertBox","N","false");t(e,107,0,"summaryHbox","100%","73%","1110");t(e,109,0,"100%","30","1110");t(e,111,0,"60%","100%","5");t(e,113,0,"detailsButton","false");t(e,115,0,"goToButton","false");t(e,117,0,"reActiveButton","false");t(e,119,0,"resolveButton","false");t(e,121,0,"100%","6%","1110");t(e,123,0,"controlContainer","100%","100%");t(e,125,0,"40%","100%","8");t(e,127,0,"refreshButton","false");t(e,129,0,"closeButton");t(e,131,0,"right","50%");t(e,133,0,"lostConnectionText","45","redText","16","false","CONNECTION ERROR");t(e,135,0,"lastRefTimeLabel","65","16","normal");t(e,137,0,"lastRefTime","65","90","16","false"),t(e,139,0)},null)}function U(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-alert-instance-summary",[],null,null,null,V,W)),i.Ib(1,4440064,null,0,u,[n.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var H=i.Fb("app-alert-instance-summary",u,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);