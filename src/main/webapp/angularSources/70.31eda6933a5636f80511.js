(window.webpackJsonp=window.webpackJsonp||[]).push([[70],{"7Ljs":function(e,t,i){"use strict";i.r(t);var l=i("CcnG"),n=i("mrSG"),a=i("ZYCi"),s=i("447K"),o=i("sMtq"),u=i("wd/R"),r=i.n(u),d=i("EVdn"),b=i("xRo1"),h=i("R1Kr"),c=function(e){function t(t,i){var l=e.call(this,i,t)||this;return l.commonService=t,l.element=i,l.inputData=new s.G(l.commonService),l.requestParams=[],l.baseURL=s.Wb.getBaseURL(),l.actionPath="",l.actionMethod="",l.jsonReader=new s.L,l.queryColumns=[],l.txtQueryColumns=[],l.nbrQueryColumns=[],l.dateQueryColumns=[],l.copyData=[],l.nbrQueryColsCopy=[],l.emailGridRows=[],l.eventXml="",l.scenarioId=null,l.selectedUsers=[],l.selectedRoles=[],l.eventFacilityArray=[],l.swtalert=new s.bb(t),window.Main=l,l}return n.d(t,e),t.prototype.ngOnInit=function(){var e,t=this;if(instanceElement=this,this.usersMainGrid=this.usersGridContainer.addChild(s.hb),this.rolesMainGrid=this.rolesGridContainer.addChild(s.hb),this.txtQueryColumns=[],this.nbrQueryColumns=[],this.dateQueryColumns=[],this.subEventsGrid=this.subEventCanvas.addChild(s.hb),this.subEventsGrid.editable=!0,this.msgCombo.required=!0,window.opener&&window.opener.instanceElement2){e=window.opener.instanceElement2.sendEventDataToSub(),this.methodName=e.operation,this.queryColumns=e.listMapFrom,this.txtQueryColumns=e.listTxtMapFrom,this.nbrQueryColumns=e.listNbrMapFrom,this.dateQueryColumns=e.listDateMapFrom,this.queryColumns&&(this.copyData=d.extend(!0,[],this.queryColumns),this.copyData.splice(0,1),this.copyData.unshift({type:"",value:"",selected:0,content:""},{type:"",value:this.queryColumns.length,selected:0,content:'"INSTANCE_ID"'})),this.nbrQueryColumns&&(this.nbrQueryColsCopy=d.extend(!0,[],this.nbrQueryColumns),this.nbrQueryColsCopy.splice(0,1),this.nbrQueryColsCopy.unshift({type:"",value:"",selected:0,content:""},{type:"",value:this.nbrQueryColumns.length,selected:0,content:'"INSTANCE_ID"'})),this.scenarioIdtxt.text=e.scenarioId,this.eventFacilityCombo.dataProvider=e.eventFacilityList.option,this.excecuteCombo.dataProvider=e.executeWhenList.option,this.subEventsGrid.CustomGrid(e.gridData.metadata),this.usersMainGrid.CustomGrid(e.usersMainGridData.metadata),this.usersMainGrid.hideHorizontalScrollBar=!0,this.rolesMainGrid.CustomGrid(e.rolesMainGridData.metadata),this.rolesMainGrid.hideHorizontalScrollBar=!0;var i=e.emailTemplatesList?e.emailTemplatesList.option:"";i&&(i.length||(i=[i]),this.emailFormatCombo.setComboData(i),this.emailFormatCombo.dataProvider=i),this.emailFormatDescLbl.text=this.emailFormatCombo.selectedValue,this.eventFacilityArray=e.eventFacilityArray,this.eventSeqTxt.text=e.eventSequence,this.msgComboList=e.messageFormatsList?e.messageFormatsList.option:"",this.msgComboList?(this.msgComboList.length||(this.msgComboList=[this.msgComboList]),this.msgCombo.setComboData(this.msgComboList),this.msgCombo.dataProvider=this.msgComboList):this.msgComboList=[],this.msgLabel.text=this.msgCombo.selectedValue,this.scenarioId=e.scenarioId,"add"==e.parentMethodName&&this.updateMsgFormatCombo(),"add"!=e.operation?(this.eventFacilityCombo.enabled=!1,this.eventFacilityCombo.selectedLabel=e.selectedEventId,this.selectedEventFacility.text=this.eventFacilityCombo.selectedValue,this.eventFacilityDescTxt.text=e.userDescription?e.userDescription:"",this.xmlAsString=e.parameterXML,this.excecuteCombo.selectedValue=e.selectedExecuteWhen,this.allowRepeatCheck.selected="Y"==e.allowRepeat,setTimeout(function(){"SEND_MESSAGE"!=t.eventFacilityCombo.selectedLabel&&(t.showXMLButton.enabled=!0,t.showXMLButton.buttonMode=!0,t.generateGridRows())},0)):(this.showXMLButton.enabled=!1,this.showXMLButton.buttonMode=!1)}this.scenarioIdLbl.text=s.Wb.getPredictMessage("scenario.scenarioId",null),this.eventSeqLbl.text=s.Wb.getPredictMessage("scenario.events.eventSeq",null),this.executeWhenLbl.text=s.Wb.getPredictMessage("scenario.events.executeWhen",null),this.allowRepLbl.text=s.Wb.getPredictMessage("scenario.events.allowRepeat",null),this.eventFacilityLbl.text=s.Wb.getPredictMessage("scenario.events.eventFacility",null)+"*",this.eventFacilityDescLbl.text=s.Wb.getPredictMessage("scenario.events.eventFacilityDesc",null),this.valueLbl.text=s.Wb.getPredictMessage("scenario.events.value",null),this.parameterIdLbl.text=s.Wb.getPredictMessage("scenario.guiHighlight.paramId",null),this.desLbl.text=s.Wb.getPredictMessage("scenario.guiHighlight.decription",null),this.mapFromLbl.text=s.Wb.getPredictMessage("scenario.guiHighlight.mapFrom",null),this.infoLbl.text=s.Wb.getPredictMessage("scenario.events.info",null),this.usersLbl.text=s.Wb.getPredictMessage("scenario.events.usersLbl",null),this.rolesLbl.text=s.Wb.getPredictMessage("scenario.events.rolesLbl",null),this.emailFormatLbl.text=s.Wb.getPredictMessage("scenario.events.emailFormat",null),this.updateButton.label=s.Wb.getPredictMessage("button.update",null),this.okButton.label=s.Wb.getPredictMessage("button.ok",null),this.cancelButton.label=s.Wb.getPredictMessage("button.cancel",null),this.showXMLButton.label=s.Wb.getPredictMessage("screen.showXML",null),this.configureButton.label=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.updateButton.toolTip=s.Wb.getPredictMessage("button.update",null),this.okButton.toolTip=s.Wb.getPredictMessage("button.ok",null),this.cancelButton.toolTip=s.Wb.getPredictMessage("button.cancel",null),this.showXMLButton.toolTip=s.Wb.getPredictMessage("screen.showXML",null),this.configureButton.toolTip=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.instAttr.label=s.Wb.getPredictMessage("scenario.events.instAttr",null),this.literal.label=s.Wb.getPredictMessage("scenario.events.literal",null),this.ignore.label=s.Wb.getPredictMessage("scenario.events.ignore",null),this.null.label=s.Wb.getPredictMessage("scenario.events.null",null),this.scenarioIdtxt.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.scenarioId",null),this.excecuteCombo.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.executeWhen",null),this.allowRepeatCheck.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.allowRepeat",null),this.eventSeqTxt.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.eventSeq",null),this.eventFacilityCombo.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.eventFacility",null),this.instAttr.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.instAttr",null),this.literal.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.literal",null),this.ignore.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.ignore",null),this.null.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.null",null),this.valueCombo.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.value",null),this.msgCombo.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.msgCombo",null),this.emailFormatCombo.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.emailFormatCombo",null),this.eventFacilityDescTxt.toolTip=s.Wb.getPredictMessage("scenario.events.tooltip.eventFacilityDesc",null),"change"!=e.operation&&"view"!=e.operation||"SEND_MESSAGE"!=this.eventFacilityCombo.selectedLabel?"change"!=e.operation&&"view"!=e.operation||"SEND_EMAIL"!=this.eventFacilityCombo.selectedLabel?(this.valueTxt.visible=!1,this.valueTxt.includeInLayout=!1,this.emailGrid.visible=!1,this.emailGrid.includeInLayout=!1,this.emailCanvas.visible=!1,this.emailCanvas.includeInLayout=!1,this.emailFormatLbl.visible=!1,this.emailFormatLbl.includeInLayout=!1,this.emailFormatCombo.visible=!1,this.emailFormatDescLbl.visible=!1,this.emailFormatDescLbl.includeInLayout=!1,this.configureButton.visible=!1,this.configureButton.includeInLayout=!1,this.configureButton.label=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.configureButton.toolTip=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.msgGrid.visible=!1,this.msgGrid.includeInLayout=!1,this.msgCanvas.visible=!1,this.msgCanvas.includeInLayout=!1,this.msgCombo.visible=!1,this.addFormatButton.visible=!1,this.addFormatButton.includeInLayout=!1,this.addFormatButton.label=s.Wb.getPredictMessage("scenario.events.msgFormat",null),this.addFormatButton.toolTip=s.Wb.getPredictMessage("scenario.events.msgFormat",null),this.eventFacilityCombo.required=!0):(this.emailGrid.visible=!0,this.emailGrid.includeInLayout=!0,this.emailCanvas.visible=!0,this.emailCanvas.includeInLayout=!0,this.emailFormatLbl.visible=!0,this.emailFormatLbl.includeInLayout=!0,this.emailFormatCombo.visible=!0,this.emailFormatDescLbl.visible=!0,this.emailFormatDescLbl.includeInLayout=!0,this.configureButton.visible=!0,this.configureButton.includeInLayout=!0,this.emailFormatDescLbl.text=this.emailFormatCombo.selectedValue,this.configureButton.label=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.configureButton.toolTip=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.msgGrid.visible=!1,this.msgGrid.includeInLayout=!1,this.msgCanvas.visible=!1,this.msgCanvas.includeInLayout=!1,this.msgCombo.visible=!1,this.addFormatButton.visible=!1,this.addFormatButton.includeInLayout=!1,this.addFormatButton.label=s.Wb.getPredictMessage("scenario.events.msgFormat",null),this.addFormatButton.toolTip=s.Wb.getPredictMessage("scenario.events.msgFormat",null),this.eventFacilityCombo.required=!0,this.subEventGrid.visible=!1,this.subEventGrid.includeInLayout=!1,this.subEventCanvas.visible=!1,this.subEventCanvas.includeInLayout=!1,this.subEventsGrid.visible=!1,this.subEventsGrid.includeInLayout=!1,this.parameterIdLbl.visible=!1,this.parameterIdLbl.includeInLayout=!1,this.parameterIdTxt.visible=!1,this.parameterIdTxt.includeInLayout=!1,this.desLbl.visible=!1,this.desLbl.includeInLayout=!1,this.descTxt.visible=!1,this.descTxt.includeInLayout=!1,this.mapFromLbl.visible=!1,this.mapFromLbl.includeInLayout=!1,this.mapFrom.visible=!1,this.valueCombo.visible=!1,this.valueTxt.visible=!1,this.valueTxt.includeInLayout=!1,this.updateButton.visible=!1,this.updateButton.includeInLayout=!1):(this.addFormatButton.label=s.Wb.getPredictMessage("scenario.events.msgFormat",null),this.addFormatButton.toolTip=s.Wb.getPredictMessage("scenario.events.msgFormat",null),this.eventFacilityCombo.required=!1,this.eventFacilityCombo.editable=!1,this.eventFacilityCombo.enabled=!1,this.msgCombo.required=!0,this.msgCombo.selectedLabel=e.parameterXML,this.savedMsgComboLabel=e.parameterXML,this.msgLabel.text=this.msgCombo.selectedValue,this.openSendMessageView())},t.prototype.onLoad=function(){var e=this;"view"==this.methodName?(this.excecuteCombo.enabled=!1,this.allowRepeatCheck.enabled=!1,this.eventFacilityCombo.enabled=!1,this.eventFacilityDescTxt.enabled=!1,this.subEventGrid.enabled=!1,this.mapFrom.enabled=!1,this.valueCombo.enabled=!1,this.addFormatButton.enabled=!1,this.msgCombo.enabled=!1,this.okButton.enabled=!1,this.cancelButton.enabled=!0,this.configureButton.enabled=!1,this.emailFormatCombo.enabled=!1,this.usersMainGrid.enabled=!1,this.rolesMainGrid.enabled=!1):"change"==this.methodName?(this.excecuteCombo.enabled=!0,this.allowRepeatCheck.enabled=!0,this.eventFacilityCombo.enabled=!1,this.eventFacilityDescTxt.enabled=!0,this.subEventGrid.enabled=!0,this.mapFrom.enabled=!0,this.valueCombo.enabled=!0,this.addFormatButton.enabled=!0,this.msgCombo.enabled=!0,this.okButton.enabled=!0,this.cancelButton.enabled=!0,this.configureButton.enabled=!0,this.emailFormatCombo.enabled=!0,this.usersMainGrid.enabled=!0,this.rolesMainGrid.enabled=!0):(this.excecuteCombo.enabled=!0,this.allowRepeatCheck.enabled=!0,this.eventFacilityCombo.enabled=!0,this.eventFacilityDescTxt.enabled=!0,this.subEventGrid.enabled=!0,this.mapFrom.enabled=!1,this.valueCombo.enabled=!1,this.addFormatButton.enabled=!0,this.msgCombo.enabled=!0,this.okButton.enabled=!0,this.cancelButton.enabled=!0,this.configureButton.enabled=!0,this.emailFormatCombo.enabled=!0,this.usersMainGrid.enabled=!0,this.rolesMainGrid.enabled=!0),this.subEventsGrid.onRowClick=function(){e.rowClick()}},t.prototype.rowClick=function(){this.valueTxt.text="";var e=this.subEventsGrid.selectedItem.subEvType.content;this.subEventsGrid.selectedIndex>=0?("text"==e?(this.valueCombo.setComboData(this.txtQueryColumns),this.valueCombo.dataProvider=this.txtQueryColumns):"number"==e||"integer"==e?this.valueCombo.dataProvider=this.nbrQueryColsCopy:(this.valueCombo.setComboData(this.dateQueryColumns),this.valueCombo.dataProvider=this.dateQueryColumns),this.parameterIdTxt.text=this.subEventsGrid.selectedItem.subEvId.content,this.descTxt.text=this.subEventsGrid.selectedItem.subEvDescription.content,"A"==this.getMapFromValue(this.subEventsGrid.selectedItem.subEvMapFrom.content)?this.valueCombo.selectedLabel=this.subEventsGrid.selectedItem.subEvMapFromValue.content?this.subEventsGrid.selectedItem.subEvMapFromValue.content:"":this.valueTxt.text=this.subEventsGrid.selectedItem.subEvMapFromValue.content?this.subEventsGrid.selectedItem.subEvMapFromValue.content:"",this.mapFrom.selectedValue=this.subEventsGrid.selectedItem.subEvMapFrom.content?this.getMapFromValue(this.subEventsGrid.selectedItem.subEvMapFrom.content):"A",this.changeValueComponent(),this.updateButton.enabled=!0,this.mapFrom.enabled=!0,this.valueCombo.enabled=!0):(this.parameterIdTxt.text="",this.descTxt.text="",this.mapFrom.selectedValue="A",this.changeValueComponent(),this.valueCombo.selectedLabel="",this.updateButton.enabled=!1,this.mapFrom.enabled=!1,this.valueCombo.enabled=!1)},t.prototype.changeEventFacility=function(){var e=this;this.changeComponents(),this.selectedEventFacility.text=this.eventFacilityCombo.selectedValue,this.showXMLButton.enabled=this.checkEmpty(this.eventFacilityCombo)&&"SEND_MESSAGE"!=this.eventFacilityCombo.selectedLabel,this.subEventsGrid.gridData={row:[],size:0},this.eventFacilityCombo.selectedLabel&&(this.requestParams=[],this.actionPath="scenMaintenance.do?",this.actionMethod="method=getEventFacilityData",this.inputData.cbResult=function(t){e.inputDataResult(t)},this.requestParams.selectedEventFacility=this.eventFacilityCombo.selectedLabel,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},t.prototype.changeMsgFormat=function(){this.msgLabel.text=this.msgCombo.selectedValue},t.prototype.changeEmailFormat=function(){this.emailFormatDescLbl.text=this.emailFormatCombo.selectedValue},t.prototype.changeValueComponent=function(){if("A"==this.mapFrom.selectedValue)this.valueTxt.visible=!1,this.valueTxt.includeInLayout=!1,this.valueCombo.visible=!0,this.infoText.text=s.Wb.getPredictMessage("scenario.events.instanceAttribute.info",null);else if("L"==this.mapFrom.selectedValue){this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvType;var e=this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvAddInfo;this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvRegExp;this.valueTxt.visible=!0,this.valueTxt.includeInLayout=!0,this.valueTxt.editable=!0,this.valueTxt.enabled=!0,this.valueCombo.visible=!1,this.infoText.text=e||""}else this.valueTxt.text="",this.valueTxt.visible=!0,this.valueTxt.includeInLayout=!0,this.valueTxt.editable=!1,this.valueTxt.enabled=!1,this.valueCombo.visible=!1,this.infoText.text=""},t.prototype.changeComponents=function(){"SEND_EMAIL"==this.eventFacilityCombo.selectedLabel?(this.msgGrid.visible=!1,this.msgGrid.includeInLayout=!1,this.msgCanvas.visible=!1,this.msgCanvas.includeInLayout=!1,this.msgCombo.visible=!1,this.addFormatButton.visible=!1,this.addFormatButton.includeInLayout=!1,this.emailGrid.visible=!0,this.emailGrid.includeInLayout=!0,this.emailCanvas.visible=!0,this.emailCanvas.includeInLayout=!0,this.emailFormatLbl.visible=!0,this.emailFormatLbl.includeInLayout=!0,this.emailFormatCombo.visible=!0,this.emailFormatDescLbl.visible=!0,this.emailFormatDescLbl.includeInLayout=!0,this.configureButton.visible=!0,this.configureButton.includeInLayout=!0,this.configureButton.label=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.configureButton.toolTip=s.Wb.getPredictMessage("scenario.events.button.configuration",null),this.subEventGrid.visible=!1,this.subEventGrid.includeInLayout=!1,this.subEventCanvas.visible=!1,this.subEventCanvas.includeInLayout=!1,this.subEventsGrid.visible=!1,this.subEventsGrid.includeInLayout=!1,this.parameterIdLbl.visible=!1,this.parameterIdLbl.includeInLayout=!1,this.parameterIdTxt.visible=!1,this.parameterIdTxt.includeInLayout=!1,this.desLbl.visible=!1,this.desLbl.includeInLayout=!1,this.descTxt.visible=!1,this.descTxt.includeInLayout=!1,this.mapFromLbl.visible=!1,this.mapFromLbl.includeInLayout=!1,this.mapFrom.visible=!1,this.valueCombo.visible=!1,this.valueTxt.visible=!1,this.valueTxt.includeInLayout=!1,this.updateButton.visible=!1,this.updateButton.includeInLayout=!1):"SEND_MESSAGE"==this.eventFacilityCombo.selectedLabel?this.openSendMessageView():(this.msgGrid.visible=!1,this.msgGrid.includeInLayout=!1,this.msgCanvas.visible=!1,this.msgCanvas.includeInLayout=!1,this.msgCombo.visible=!1,this.addFormatButton.visible=!1,this.addFormatButton.includeInLayout=!1,this.emailGrid.visible=!1,this.emailGrid.includeInLayout=!1,this.emailCanvas.visible=!1,this.emailCanvas.includeInLayout=!1,this.emailFormatLbl.visible=!1,this.emailFormatLbl.includeInLayout=!1,this.emailFormatCombo.visible=!1,this.emailFormatDescLbl.visible=!1,this.emailFormatDescLbl.includeInLayout=!1,this.configureButton.visible=!1,this.configureButton.includeInLayout=!1,this.subEventGrid.visible=!0,this.subEventGrid.includeInLayout=!0,this.subEventCanvas.visible=!0,this.subEventCanvas.includeInLayout=!0,this.subEventsGrid.visible=!0,this.subEventsGrid.includeInLayout=!0,this.parameterIdLbl.visible=!0,this.parameterIdLbl.includeInLayout=!0,this.parameterIdTxt.visible=!0,this.parameterIdTxt.includeInLayout=!0,this.desLbl.visible=!0,this.desLbl.includeInLayout=!0,this.descTxt.visible=!0,this.descTxt.includeInLayout=!0,this.mapFromLbl.visible=!0,this.mapFromLbl.includeInLayout=!0,this.mapFrom.visible=!0,this.valueCombo.visible=!0,this.valueTxt.visible=!1,this.valueTxt.includeInLayout=!1,this.updateButton.visible=!0,this.updateButton.includeInLayout=!0)},t.prototype.openSendMessageView=function(){this.msgGrid.visible=!0,this.msgGrid.includeInLayout=!0,this.msgCanvas.visible=!0,this.msgCanvas.includeInLayout=!0,this.msgCombo.visible=!0,this.addFormatButton.visible=!0,this.addFormatButton.includeInLayout=!0,this.emailGrid.visible=!1,this.emailGrid.includeInLayout=!1,this.emailCanvas.visible=!1,this.emailCanvas.includeInLayout=!1,this.emailFormatLbl.visible=!1,this.emailFormatLbl.includeInLayout=!1,this.emailFormatCombo.visible=!1,this.emailFormatDescLbl.visible=!1,this.emailFormatDescLbl.includeInLayout=!1,this.configureButton.visible=!1,this.configureButton.includeInLayout=!1,this.subEventGrid.visible=!1,this.subEventGrid.includeInLayout=!1,this.subEventCanvas.visible=!1,this.subEventCanvas.includeInLayout=!1,this.subEventsGrid.visible=!1,this.subEventsGrid.includeInLayout=!1,this.parameterIdLbl.visible=!1,this.parameterIdLbl.includeInLayout=!1,this.parameterIdTxt.visible=!1,this.parameterIdTxt.includeInLayout=!1,this.desLbl.visible=!1,this.desLbl.includeInLayout=!1,this.descTxt.visible=!1,this.descTxt.includeInLayout=!1,this.mapFromLbl.visible=!1,this.mapFromLbl.includeInLayout=!1,this.mapFrom.visible=!1,this.valueCombo.visible=!1,this.valueTxt.visible=!1,this.valueTxt.includeInLayout=!1,this.updateButton.visible=!1,this.updateButton.includeInLayout=!1},t.prototype.addMsgFormat=function(){s.x.call("openMsgFormatScreen","unspecified",this.scenarioId)},t.prototype.changeMessageFormat=function(){s.x.call("openMsgFormatScreen","unspecified")},t.prototype.inputDataResult=function(e){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),"SEND_MESSAGE"!=this.eventFacilityCombo.selectedLabel&&(this.xmlAsString=this.jsonReader.getSingletons().parameterXML.replace(/#/g,">"),this.generateGridRows()))},t.prototype.generateGridRows=function(){if(this.xmlAsString){var e=b.xml2js(this.xmlAsString,{object:!1,reversible:!1,coerce:!1,sanitize:!0,trim:!0,arrayNotation:!1,alternateTextNode:!1,compact:!0}),t=e.mappedParameters?e.mappedParameters.parameter:e.requiredParameters.parameter,i=void 0,l=void 0;if("SEND_EMAIL"==this.eventFacilityCombo.selectedLabel){if(t[0]&&(this.emailFormatCombo.selectedLabel=t[0].templateId&&t[0].templateId._cdata&&"undefined"!=t[0].templateId._cdata?t[0].templateId._cdata:"",this.emailFormatDescLbl.text=this.emailFormatCombo.selectedValue),t[1]){var n=t[1].users.user;n.length||(n=[n]);for(var a=0;a<n.length;a++)l={userId:{content:n[a].userId&&n[a].userId._cdata&&"undefined"!=n[a].userId._cdata?n[a].userId._cdata:""},name:{content:n[a].name&&n[a].name._cdata&&"undefined"!=n[a].name._cdata?n[a].name._cdata:""}},this.usersMainGrid.appendRow(l,!0,!0),this.selectedUsers.push(n[a].userId._cdata)}if(t[2]){var s=t[2].roles.role;s.length||(s=[s]);for(a=0;a<s.length;a++)i={roleId:{content:s[a].roleId&&s[a].roleId._cdata&&"undefined"!=s[a].roleId._cdata?s[a].roleId._cdata:""},name:{content:s[a].name&&s[a].name._cdata&&"undefined"!=s[a].name._cdata?s[a].name._cdata:""}},this.rolesMainGrid.appendRow(i,!0,!0),this.selectedRoles.push(s[a].roleId._cdata)}}else for(a=0;a<t.length;a++)(i={subEvReq:{content:t[a].isMandatory&&t[a].isMandatory._cdata&&"undefined"!=t[a].isMandatory._cdata?t[a].isMandatory._cdata:""},subEvId:{content:t[a].name&&"undefined"!=t[a].name._cdata?t[a].name._cdata:""},subEvDescription:{content:t[a].description&&t[a].description._cdata&&"undefined"!=t[a].description._cdata?t[a].description._cdata:""},subEvType:{content:t[a].data_type&&t[a].data_type._cdata&&"undefined"!=t[a].data_type._cdata?t[a].data_type._cdata:""},subEvMapFrom:{content:t[a].useType&&t[a].useType._cdata&&"undefined"!=t[a].useType._cdata?t[a].useType._cdata:""},subEvMapFromValue:{content:t[a].value&&t[a].value._cdata&&"undefined"!=t[a].value._cdata?t[a].value._cdata:""},subEvAddInfo:{content:t[a].additional_infomation&&t[a].additional_infomation._cdata&&"undefined"!=t[a].additional_infomation._cdata?t[a].additional_infomation._cdata:""},subEvRegExp:{content:t[a].regular_expression&&t[a].regular_expression._cdata&&"undefined"!=t[a].regular_expression._cdata?t[a].regular_expression._cdata:""},subEvRegExpMsg:{content:t[a].regular_expression_msg&&t[a].regular_expression_msg._cdata&&"undefined"!=t[a].regular_expression_msg._cdata?t[a].regular_expression_msg._cdata:""}}).validation={length:{content:t[a].length&&t[a].length._cdata&&"undefined"!=t[a].length._cdata?t[a].length._cdata:""},maximum:{content:t[a].maximum&&t[a].maximum._cdata&&"undefined"!=t[a].maximum._cdata?t[a].maximum._cdata:""},minimum:{content:t[a].minimum&&t[a].minimum._cdata&&"undefined"!=t[a].minimum._cdata?t[a].minimum._cdata:""}},this.subEventsGrid.appendRow(i,!0,!0)}},t.prototype.inputDataFault=function(){this.swtalert.error(s.Wb.getPredictMessage("alert.generic_exception"))},t.prototype.updateHandle=function(){if("A"==this.mapFrom.selectedValue)this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFrom=this.getMapFromLabel(this.mapFrom.selectedValue),this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFrom.content=this.getMapFromLabel(this.mapFrom.selectedValue),this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFromValue=this.valueCombo.selectedLabel,this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFromValue.content=this.valueCombo.selectedLabel;else if("L"==this.mapFrom.selectedValue){var e=new RegExp(this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvRegExp),t=this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvRegExpMsg;if(this.checkRegularExpression(e,this.valueTxt.text))return this.swtalert.error(t),void(this.valueTxt.text="");var i=this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvType,l=this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.validation,n=l.length?l.length.content:"",a=l.maximum?l.maximum.content:"",o=l.minimum?l.minimum.content:"";if("date"==i||"datetime"==i){var u=r()(this.valueTxt.text,"YYYY-MM-DD HH:mm:ss",!0),d=r()(this.valueTxt.text,"YYYY-MM-DD",!0);if(!u.isValid()&&!d.isValid())return void this.swtalert.error(s.Wb.getPredictMessage("Please enter a valid Date"))}else if("number"==i){if(isNaN(this.valueTxt.text)&&isNaN(parseFloat(this.valueTxt.text)))return void this.swtalert.error(s.Wb.getPredictMessage("Please enter a valid Number"));if(""!=a&&Number(this.valueTxt.text)>a)return void this.swtalert.error(s.Wb.getPredictMessage("Please enter a valid Number lower than "+a));if(""!=o&&Number(this.valueTxt.text)<o)return void this.swtalert.error(s.Wb.getPredictMessage("Please enter a valid Number greater than "+o))}else if("integer"==i){if(!new RegExp(/^(0|-*[1-9]+[0-9]*)$/).test(this.valueTxt.text))return void this.swtalert.error(s.Wb.getPredictMessage("Please enter a valid Integer"));if(""!=a&&Number(this.valueTxt.text)>a)return void this.swtalert.error(s.Wb.getPredictMessage("Please enter a valid Number lower than "+a));if(""!=o&&Number(this.valueTxt.text)<o)return void this.swtalert.error(s.Wb.getPredictMessage("Please enter a valid Number greater than "+o))}else if(""!=n&&Number(this.valueTxt.text.length)>n)return void this.swtalert.error(s.Wb.getPredictMessage("Value cannot be greater than "+n+" characters"));this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFrom=this.getMapFromLabel(this.mapFrom.selectedValue),this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFrom.content=this.getMapFromLabel(this.mapFrom.selectedValue),this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].subEvMapFromValue=this.valueTxt.text,this.subEventsGrid.dataProvider[this.subEventsGrid.selectedIndex].slickgrid_rowcontent.subEvMapFromValue.content=this.valueTxt.text}this.subEventsGrid.refresh(),this.gridParamsToXml()},t.prototype.gridParamsToXml=function(){if(this.eventXml="",this.subEventsGrid.gridData.length>0){this.eventXml="<mappedParameters>";for(var e=0;e<this.subEventsGrid.gridData.length;e++){var t=this.subEventsGrid.gridData[e].subEvMapFrom?this.subEventsGrid.gridData[e].subEvMapFrom:"",i=this.subEventsGrid.gridData[e].subEvMapFromValue?this.subEventsGrid.gridData[e].subEvMapFromValue:"",l=this.subEventsGrid.gridData[e].subEvAddInfo?this.subEventsGrid.gridData[e].subEvAddInfo:"",n=this.subEventsGrid.gridData[e].subEvRegExp?this.subEventsGrid.gridData[e].subEvRegExp:"",a=this.subEventsGrid.gridData[e].subEvRegExpMsg?this.subEventsGrid.gridData[e].subEvRegExpMsg:"";this.eventXml+="<parameter>",this.eventXml+="<isMandatory><![CDATA["+this.subEventsGrid.gridData[e].subEvReq+"]]></isMandatory>",this.eventXml+="<data_type><![CDATA["+this.subEventsGrid.gridData[e].subEvType+"]]></data_type>",this.eventXml+="<description><![CDATA["+this.subEventsGrid.gridData[e].subEvDescription+"]]></description>",this.eventXml+="<name><![CDATA["+this.subEventsGrid.gridData[e].subEvId+"]]></name>",this.eventXml+="<useType><![CDATA["+t+"]]></useType>",this.eventXml+="<value><![CDATA["+i+"]]></value>",this.eventXml+="<regular_expression><![CDATA["+n+"]]></regular_expression>",this.eventXml+="<regular_expression_msg><![CDATA["+a+"]]></regular_expression_msg>",this.eventXml+="<additional_infomation><![CDATA["+l+"]]></additional_infomation>",this.eventXml+="</parameter>"}this.eventXml+="</mappedParameters>",this.eventXml=h.pd.xml(this.eventXml)}this.xmlAsString=this.eventXml},t.prototype.saveHandler=function(){"SEND_MESSAGE"==this.eventFacilityCombo.selectedLabel&&(this.xmlAsString=this.msgCombo.selectedLabel),"SEND_EMAIL"==this.eventFacilityCombo.selectedLabel&&this.prepareEmailXml(),this.checkMandatoryFilled()?""==this.eventFacilityCombo.selectedLabel?this.swtalert.warning(s.Wb.getPredictMessage("scenario.eventTab.alert.missingFacility",null)):""==this.msgCombo.selectedLabel&&"SEND_MESSAGE"==this.eventFacilityCombo.selectedLabel?this.swtalert.warning(s.Wb.getPredictMessage("scenario.eventTab.alert.missingMsgFormat",null)):window.opener&&window.opener.instanceElement2&&(window.opener.instanceElement2.refreshParent(this.eventFacilityCombo.selectedLabel,this.eventFacilityDescTxt.text,this.xmlAsString,this.allowRepeatCheck.selected?"Y":"N",this.excecuteCombo.selectedValue),window.opener.instanceElement2.selectedUsers=this.selectedUsers,window.opener.instanceElement2.selectedRoles=this.selectedRoles,s.x.call("close")):this.swtalert.warning(s.Wb.getPredictMessage("scenario.events.mandatoryFiels",null))},t.prototype.prepareEmailXml=function(){var e="";if(e="<mappedParameters>",e+="<parameter>",e+="<templateId><![CDATA["+(this.emailFormatCombo.selectedLabel?this.emailFormatCombo.selectedLabel:"")+"]]></templateId>",e+="</parameter>",this.usersMainGrid.gridData.length>0){e+="<parameter>",e+="<users>";for(var t=0;t<this.usersMainGrid.gridData.length;t++)e+="<user>",e+="<userId><![CDATA["+this.usersMainGrid.gridData[t].userId+"]]></userId>",e+="<name><![CDATA["+this.usersMainGrid.gridData[t].name+"]]></name>",e+="</user>";e+="</users>",e+="</parameter>"}if(this.rolesMainGrid.gridData.length>0){e+="<parameter>",e+="<roles>";for(t=0;t<this.rolesMainGrid.gridData.length;t++)e+="<role>",e+="<roleId><![CDATA["+this.rolesMainGrid.gridData[t].roleId+"]]></roleId>",e+="<name><![CDATA["+this.rolesMainGrid.gridData[t].name+"]]></name>",e+="</role>";e+="</roles>",e+="</parameter>"}e+="</mappedParameters>",e=h.pd.xml(e),this.xmlAsString=e},t.prototype.checkMandatoryFilled=function(){for(var e=[],t=0;t<this.subEventsGrid.gridData.length;t++)"M"!=this.subEventsGrid.gridData[t].subEvReq||this.subEventsGrid.gridData[t].subEvMapFromValue||"Ignore"==this.subEventsGrid.gridData[t].subEvMapFrom||"Null"==this.subEventsGrid.gridData[t].subEvMapFrom?e.push("true"):e.push("false");return!e.includes("false")},t.prototype.cancelHandler=function(){s.x.call("close")},t.prototype.showXmlHandler=function(){"SEND_EMAIL"==this.eventFacilityCombo.selectedLabel&&this.prepareEmailXml();var e=this.xmlAsString.replace(/<\!\[CDATA\[|\]\]>/g,"");this.win=s.Eb.createPopUp(this,o.a,{title:"Show XML",xmlData:e}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="400",this.win.height="500",this.win.showControls=!0,this.win.id="guiShowXML",this.win.display()},t.prototype.updateMsgFormatCombo=function(){var e=this;this.requestParams=[],this.menuAccessId=s.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.setMsgFormatComboValues(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="scenMaintenance.do?",this.actionMethod="method=getMsgFormatsList",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.setMsgFormatComboValues=function(e){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()?this.lastRecievedJSON!=this.prevRecievedJSON&&(this.msgCombo.setComboData(this.jsonReader.getSelects()),this.msgCombo.selectedLabel=this.savedMsgComboLabel?this.savedMsgComboLabel:"",this.msgLabel.text=this.msgCombo.selectedValue?this.msgCombo.selectedValue:"",this.jsonReader.isDataBuilding()||(this.prevRecievedJSON=this.lastRecievedJSON)):this.lastRecievedJSON.hasOwnProperty("request_reply"))},t.prototype.checkEmpty=function(e){return!!e.selectedLabel},t.prototype.getMapFromLabel=function(e){var t="";switch(e){case"A":t="Instance Attribute";break;case"L":t="Literal";break;case"I":t="Ignore";break;case"N":t="Null"}return t},t.prototype.getMapFromValue=function(e){var t="";switch(e){case"Instance Attribute":t="A";break;case"Literal":t="L";break;case"Ignore":t="I";break;case"Null":t="N"}return t},t.prototype.checkRegularExpression=function(e,t){var i=!1;return e.test(t)||(i=!0),i},t.prototype.configureRecipient=function(){s.x.call("openConfigScreen","configureRecipients")},t.prototype.refreshParent=function(e,t){this.usersMainGrid.gridData={size:e.length,row:e},this.usersMainGrid.refresh(),this.rolesMainGrid.gridData={size:t.length,row:t},this.rolesMainGrid.refresh()},t.prototype.changeEmailTemplate=function(){},t.prototype.startOfComms=function(){},t.prototype.endOfComms=function(){},t}(s.yb),m=[{path:"",component:c}],g=(a.l.forChild(m),function(){return function(){}}()),v=i("pMnS"),p=i("RChO"),w=i("t6HQ"),I=i("WFGK"),L=i("5FqG"),C=i("Ip0R"),y=i("gIcY"),x=i("t/Na"),F=i("sE5F"),f=i("OzfB"),E=i("T7CS"),G=i("S7LP"),M=i("6aHO"),T=i("WzUx"),R=i("A7o+"),A=i("zCE2"),B=i("Jg5P"),D=i("3R0m"),J=i("hhbb"),S=i("5rxC"),_=i("Fzqc"),P=i("21Lb"),W=i("hUWP"),k=i("3pJQ"),N=i("V9q+"),V=i("VDKW"),Z=i("kXfT"),X=i("BGbe");i.d(t,"EventsAddModuleNgFactory",function(){return H}),i.d(t,"RenderType_EventsAdd",function(){return z}),i.d(t,"View_EventsAdd_0",function(){return O}),i.d(t,"View_EventsAdd_Host_0",function(){return Y}),i.d(t,"EventsAddNgFactory",function(){return U});var H=l.Gb(g,[],function(e){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[v.a,p.a,w.a,I.a,L.Cb,L.Pb,L.r,L.rc,L.s,L.Ab,L.Bb,L.Db,L.qd,L.Hb,L.k,L.Ib,L.Nb,L.Ub,L.yb,L.Jb,L.v,L.A,L.e,L.c,L.g,L.d,L.Kb,L.f,L.ec,L.Wb,L.bc,L.ac,L.sc,L.fc,L.lc,L.jc,L.Eb,L.Fb,L.mc,L.Lb,L.nc,L.Mb,L.dc,L.Rb,L.b,L.ic,L.Yb,L.Sb,L.kc,L.y,L.Qb,L.cc,L.hc,L.pc,L.oc,L.xb,L.p,L.q,L.o,L.h,L.j,L.w,L.Zb,L.i,L.m,L.Vb,L.Ob,L.Gb,L.Xb,L.t,L.tc,L.zb,L.n,L.qc,L.a,L.z,L.rd,L.sd,L.x,L.td,L.gc,L.l,L.u,L.ud,L.Tb,U]],[3,l.n],l.J]),l.Rb(4608,C.m,C.l,[l.F,[2,C.u]]),l.Rb(4608,y.c,y.c,[]),l.Rb(4608,y.p,y.p,[]),l.Rb(4608,x.j,x.p,[C.c,l.O,x.n]),l.Rb(4608,x.q,x.q,[x.j,x.o]),l.Rb(5120,x.a,function(e){return[e,new s.tb]},[x.q]),l.Rb(4608,x.m,x.m,[]),l.Rb(6144,x.k,null,[x.m]),l.Rb(4608,x.i,x.i,[x.k]),l.Rb(6144,x.b,null,[x.i]),l.Rb(4608,x.f,x.l,[x.b,l.B]),l.Rb(4608,x.c,x.c,[x.f]),l.Rb(4608,F.c,F.c,[]),l.Rb(4608,F.g,F.b,[]),l.Rb(5120,F.i,F.j,[]),l.Rb(4608,F.h,F.h,[F.c,F.g,F.i]),l.Rb(4608,F.f,F.a,[]),l.Rb(5120,F.d,F.k,[F.h,F.f]),l.Rb(5120,l.b,function(e,t){return[f.j(e,t)]},[C.c,l.O]),l.Rb(4608,E.a,E.a,[]),l.Rb(4608,G.a,G.a,[]),l.Rb(4608,M.a,M.a,[l.n,l.L,l.B,G.a,l.g]),l.Rb(4608,T.c,T.c,[l.n,l.g,l.B]),l.Rb(4608,T.e,T.e,[T.c]),l.Rb(4608,R.l,R.l,[]),l.Rb(4608,R.h,R.g,[]),l.Rb(4608,R.c,R.f,[]),l.Rb(4608,R.j,R.d,[]),l.Rb(4608,R.b,R.a,[]),l.Rb(4608,R.k,R.k,[R.l,R.h,R.c,R.j,R.b,R.m,R.n]),l.Rb(4608,T.i,T.i,[[2,R.k]]),l.Rb(4608,T.r,T.r,[T.L,[2,R.k],T.i]),l.Rb(4608,T.t,T.t,[]),l.Rb(4608,T.w,T.w,[]),l.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),l.Rb(1073742336,C.b,C.b,[]),l.Rb(1073742336,y.n,y.n,[]),l.Rb(1073742336,y.l,y.l,[]),l.Rb(1073742336,A.a,A.a,[]),l.Rb(1073742336,B.a,B.a,[]),l.Rb(1073742336,y.e,y.e,[]),l.Rb(1073742336,D.a,D.a,[]),l.Rb(1073742336,R.i,R.i,[]),l.Rb(1073742336,T.b,T.b,[]),l.Rb(1073742336,x.e,x.e,[]),l.Rb(1073742336,x.d,x.d,[]),l.Rb(1073742336,F.e,F.e,[]),l.Rb(1073742336,J.b,J.b,[]),l.Rb(1073742336,S.b,S.b,[]),l.Rb(1073742336,f.c,f.c,[]),l.Rb(1073742336,_.a,_.a,[]),l.Rb(1073742336,P.d,P.d,[]),l.Rb(1073742336,W.c,W.c,[]),l.Rb(1073742336,k.a,k.a,[]),l.Rb(1073742336,N.a,N.a,[[2,f.g],l.O]),l.Rb(1073742336,V.b,V.b,[]),l.Rb(1073742336,Z.a,Z.a,[]),l.Rb(1073742336,X.b,X.b,[]),l.Rb(1073742336,s.Tb,s.Tb,[]),l.Rb(1073742336,g,g,[]),l.Rb(256,x.n,"XSRF-TOKEN",[]),l.Rb(256,x.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,R.m,void 0,[]),l.Rb(256,R.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,a.i,function(){return[[{path:"",component:c}]]},[])])}),q=[[""]],z=l.Hb({encapsulation:0,styles:q,data:{}});function O(e){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{scenarioIdLbl:0}),l.Zb(402653184,3,{executeWhenLbl:0}),l.Zb(402653184,4,{eventSeqLbl:0}),l.Zb(402653184,5,{eventFacilityLbl:0}),l.Zb(402653184,6,{selectedEventFacility:0}),l.Zb(402653184,7,{eventFacilityDescLbl:0}),l.Zb(402653184,8,{allowRepLbl:0}),l.Zb(402653184,9,{desLbl:0}),l.Zb(402653184,10,{parameterIdLbl:0}),l.Zb(402653184,11,{mapFromLbl:0}),l.Zb(402653184,12,{valueLbl:0}),l.Zb(402653184,13,{msgLabel:0}),l.Zb(402653184,14,{infoLbl:0}),l.Zb(402653184,15,{emailFormatLbl:0}),l.Zb(402653184,16,{emailFormatDescLbl:0}),l.Zb(402653184,17,{usersLbl:0}),l.Zb(402653184,18,{rolesLbl:0}),l.Zb(402653184,19,{scenarioIdtxt:0}),l.Zb(402653184,20,{eventSeqTxt:0}),l.Zb(402653184,21,{valueTxt:0}),l.Zb(402653184,22,{eventFacilityDescTxt:0}),l.Zb(402653184,23,{valueCombo:0}),l.Zb(402653184,24,{descTxt:0}),l.Zb(402653184,25,{parameterIdTxt:0}),l.Zb(402653184,26,{excecuteCombo:0}),l.Zb(402653184,27,{eventFacilityCombo:0}),l.Zb(402653184,28,{msgCombo:0}),l.Zb(402653184,29,{emailFormatCombo:0}),l.Zb(402653184,30,{allowRepeatCheck:0}),l.Zb(402653184,31,{subEventCanvas:0}),l.Zb(402653184,32,{emailCanvas:0}),l.Zb(402653184,33,{msgCanvas:0}),l.Zb(402653184,34,{updateButton:0}),l.Zb(402653184,35,{okButton:0}),l.Zb(402653184,36,{cancelButton:0}),l.Zb(402653184,37,{showXMLButton:0}),l.Zb(402653184,38,{addFormatButton:0}),l.Zb(402653184,39,{configureButton:0}),l.Zb(402653184,40,{mapFrom:0}),l.Zb(402653184,41,{instAttr:0}),l.Zb(402653184,42,{literal:0}),l.Zb(402653184,43,{ignore:0}),l.Zb(402653184,44,{null:0}),l.Zb(402653184,45,{msgGrid:0}),l.Zb(402653184,46,{emailGrid:0}),l.Zb(402653184,47,{subEventGrid:0}),l.Zb(402653184,48,{usersGridContainer:0}),l.Zb(402653184,49,{rolesGridContainer:0}),l.Zb(402653184,50,{infoText:0}),(e()(),l.Jb(50,0,null,null,220,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var l=!0,n=e.component;"creationComplete"===t&&(l=!1!==n.onLoad()&&l);return l},L.ad,L.hb)),l.Ib(51,4440064,null,0,s.yb,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),l.Jb(52,0,null,0,218,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,L.od,L.vb)),l.Ib(53,4440064,null,0,s.ec,[l.r,s.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(e()(),l.Jb(54,0,null,0,206,"SwtCanvas",[["height","95%"],["width","100%"]],null,null,null,L.Nc,L.U)),l.Ib(55,4440064,null,0,s.db,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(56,0,null,0,204,"VBox",[["height","100%"],["minWidth","950"],["width","100%"]],null,null,null,L.od,L.vb)),l.Ib(57,4440064,null,0,s.ec,[l.r,s.i,l.T],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(e()(),l.Jb(58,0,null,0,61,"Grid",[["height","125"],["paddingTop","10"],["width","100%"]],null,null,null,L.Cc,L.H)),l.Ib(59,4440064,null,0,s.z,[l.r,s.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(60,0,null,0,17,"GridRow",[],null,null,null,L.Bc,L.J)),l.Ib(61,4440064,null,0,s.B,[l.r,s.i],null,null),(e()(),l.Jb(62,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(63,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(64,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(65,4440064,[[2,4],["scenarioIdLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(66,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(67,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(68,0,null,0,1,"SwtTextInput",[["enabled","false"],["width","200"]],null,null,null,L.kd,L.sb)),l.Ib(69,4440064,[[19,4],["scenarioIdtxt",4]],0,s.Rb,[l.r,s.i],{width:[0,"width"],enabled:[1,"enabled"]},null),(e()(),l.Jb(70,0,null,0,7,"GridItem",[["width","100%"]],null,null,null,L.Ac,L.I)),l.Ib(71,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(72,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,L.Dc,L.K)),l.Ib(73,4440064,null,0,s.C,[l.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),l.Jb(74,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(75,4440064,[[3,4],["executeWhenLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(76,0,null,0,1,"SwtComboBox",[["dataLabel",""],["width","350"]],null,[["window","mousewheel"]],function(e,t,i){var n=!0;"window:mousewheel"===t&&(n=!1!==l.Tb(e,77).mouseWeelEventHandler(i.target)&&n);return n},L.Pc,L.W)),l.Ib(77,4440064,[[26,4],["excecuteCombo",4]],0,s.gb,[l.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"]},null),(e()(),l.Jb(78,0,null,0,17,"GridRow",[],null,null,null,L.Bc,L.J)),l.Ib(79,4440064,null,0,s.B,[l.r,s.i],null,null),(e()(),l.Jb(80,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(81,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(82,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(83,4440064,[[4,4],["eventSeqLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(84,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(85,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(86,0,null,0,1,"SwtTextInput",[["enabled","false"],["textAlign","right"],["width","200"]],null,null,null,L.kd,L.sb)),l.Ib(87,4440064,[[20,4],["eventSeqTxt",4]],0,s.Rb,[l.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"]},null),(e()(),l.Jb(88,0,null,0,7,"GridItem",[["width","100%"]],null,null,null,L.Ac,L.I)),l.Ib(89,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(90,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,L.Dc,L.K)),l.Ib(91,4440064,null,0,s.C,[l.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),l.Jb(92,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(93,4440064,[[8,4],["allowRepLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(94,0,null,0,1,"SwtCheckBox",[],null,null,null,L.Oc,L.V)),l.Ib(95,4440064,[[30,4],["allowRepeatCheck",4]],0,s.eb,[l.r,s.i],null,null),(e()(),l.Jb(96,0,null,0,13,"GridRow",[],null,null,null,L.Bc,L.J)),l.Ib(97,4440064,null,0,s.B,[l.r,s.i],null,null),(e()(),l.Jb(98,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(99,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(100,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(101,4440064,[[5,4],["eventFacilityLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(102,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(103,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(104,0,null,0,1,"SwtComboBox",[],null,[[null,"change"],["window","mousewheel"]],function(e,t,i){var n=!0,a=e.component;"window:mousewheel"===t&&(n=!1!==l.Tb(e,105).mouseWeelEventHandler(i.target)&&n);"change"===t&&(n=!1!==a.changeEventFacility()&&n);return n},L.Pc,L.W)),l.Ib(105,4440064,[[27,4],["eventFacilityCombo",4]],0,s.gb,[l.r,s.i],null,{change_:"change"}),(e()(),l.Jb(106,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(107,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(108,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,L.Yc,L.fb)),l.Ib(109,4440064,[[6,4],["selectedEventFacility",4]],0,s.vb,[l.r,s.i],{fontWeight:[0,"fontWeight"]},null),(e()(),l.Jb(110,0,null,0,9,"GridRow",[],null,null,null,L.Bc,L.J)),l.Ib(111,4440064,null,0,s.B,[l.r,s.i],null,null),(e()(),l.Jb(112,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(113,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(114,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(115,4440064,[[7,4],["eventFacilityDescLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(116,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(117,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(118,0,null,0,1,"SwtTextInput",[["width","380"]],null,null,null,L.kd,L.sb)),l.Ib(119,4440064,[[22,4],["eventFacilityDescTxt",4]],0,s.Rb,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(120,0,null,0,27,"SwtCanvas",[["height","90%"],["width","100%"]],null,null,null,L.Nc,L.U)),l.Ib(121,4440064,[[33,4],["msgCanvas",4]],0,s.db,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(122,0,null,0,25,"Grid",[["height","100%"],["paddingTop","10"],["width","100%"]],null,null,null,L.Cc,L.H)),l.Ib(123,4440064,[[45,4],["msgGrid",4]],0,s.z,[l.r,s.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(124,0,null,0,1,"GridRow",[["height","10px"],["width","100%"]],null,null,null,L.Bc,L.J)),l.Ib(125,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(126,0,null,0,13,"GridRow",[["height","95%"],["width","100%"]],null,null,null,L.Bc,L.J)),l.Ib(127,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(128,0,null,0,3,"GridItem",[["paddingLeft","20"],["width","80"]],null,null,null,L.Ac,L.I)),l.Ib(129,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(e()(),l.Jb(130,0,null,0,1,"SwtLabel",[["id","msgComboLbl"],["text","Format"]],null,null,null,L.Yc,L.fb)),l.Ib(131,4440064,[["msgComboLbl",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],text:[1,"text"]},null),(e()(),l.Jb(132,0,null,0,3,"GridItem",[["width","350"]],null,null,null,L.Ac,L.I)),l.Ib(133,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(134,0,null,0,1,"SwtComboBox",[["dataLabel","msgFormatList"],["id","msgCombo"],["width","300"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,i){var n=!0,a=e.component;"window:mousewheel"===t&&(n=!1!==l.Tb(e,135).mouseWeelEventHandler(i.target)&&n);"change"===t&&(n=!1!==a.changeMsgFormat()&&n);return n},L.Pc,L.W)),l.Ib(135,4440064,[[28,4],["msgCombo",4]],0,s.gb,[l.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(e()(),l.Jb(136,0,null,0,3,"GridItem",[["width","30%"]],null,null,null,L.Ac,L.I)),l.Ib(137,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(138,0,null,0,1,"SwtLabel",[["id","msgLabel"],["width","300"]],null,null,null,L.Yc,L.fb)),l.Ib(139,4440064,[[13,4],["msgLabel",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],width:[1,"width"]},null),(e()(),l.Jb(140,0,null,0,7,"GridRow",[["height","7%"],["width","100%"]],null,null,null,L.Bc,L.J)),l.Ib(141,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(142,0,null,0,1,"GridItem",[["width","90%"]],null,null,null,L.Ac,L.I)),l.Ib(143,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(144,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,L.Ac,L.I)),l.Ib(145,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(146,0,null,0,1,"SwtButton",[["id","addFormatButton"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.addMsgFormat()&&l);return l},L.Mc,L.T)),l.Ib(147,4440064,[[38,4],["addFormatButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(e()(),l.Jb(148,0,null,0,37,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,L.Nc,L.U)),l.Ib(149,4440064,[[32,4],["emailCanvas",4]],0,s.db,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(150,0,null,0,35,"Grid",[["height","100%"],["paddingTop","10"],["width","100%"]],null,null,null,L.Cc,L.H)),l.Ib(151,4440064,[[46,4],["emailGrid",4]],0,s.z,[l.r,s.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(152,0,null,0,13,"GridRow",[["height","40"],["width","100%"]],null,null,null,L.Bc,L.J)),l.Ib(153,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(154,0,null,0,3,"GridItem",[["width","105"]],null,null,null,L.Ac,L.I)),l.Ib(155,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(156,0,null,0,1,"SwtLabel",[["id","emailFormatLbl"]],null,null,null,L.Yc,L.fb)),l.Ib(157,4440064,[[15,4],["emailFormatLbl",4]],0,s.vb,[l.r,s.i],{id:[0,"id"]},null),(e()(),l.Jb(158,0,null,0,3,"GridItem",[["width","350"]],null,null,null,L.Ac,L.I)),l.Ib(159,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(160,0,null,0,1,"SwtComboBox",[["dataLabel","emailTemplatesList"],["id","emailFormatCombo"],["width","300"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,i){var n=!0,a=e.component;"window:mousewheel"===t&&(n=!1!==l.Tb(e,161).mouseWeelEventHandler(i.target)&&n);"change"===t&&(n=!1!==a.changeEmailFormat()&&n);return n},L.Pc,L.W)),l.Ib(161,4440064,[[29,4],["emailFormatCombo",4]],0,s.gb,[l.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(e()(),l.Jb(162,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(163,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(164,0,null,0,1,"SwtLabel",[["id","emailFormatDescLbl"],["width","300"]],null,null,null,L.Yc,L.fb)),l.Ib(165,4440064,[[16,4],["emailFormatDescLbl",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],width:[1,"width"]},null),(e()(),l.Jb(166,0,null,0,7,"GridRow",[["height","40"],["width","100%"]],null,null,null,L.Bc,L.J)),l.Ib(167,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(168,0,null,0,1,"GridItem",[["width","105"]],null,null,null,L.Ac,L.I)),l.Ib(169,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(170,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(171,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(172,0,null,0,1,"SwtButton",[["id","configureButton"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.configureRecipient()&&l);return l},L.Mc,L.T)),l.Ib(173,4440064,[[39,4],["configureButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(e()(),l.Jb(174,0,null,0,5,"GridRow",[["height","40%"],["paddingTop","10"],["width","100%"]],null,null,null,L.Bc,L.J)),l.Ib(175,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(176,0,null,0,1,"SwtLabel",[["id","usersLbl"],["width","120"]],null,null,null,L.Yc,L.fb)),l.Ib(177,4440064,[[17,4],["usersLbl",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],width:[1,"width"]},null),(e()(),l.Jb(178,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","usersGridContainer"],["minHeight","80"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,L.Nc,L.U)),l.Ib(179,4440064,[[48,4],["usersGridContainer",4]],0,s.db,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minHeight:[4,"minHeight"],border:[5,"border"]},null),(e()(),l.Jb(180,0,null,0,5,"GridRow",[["height","40%"],["paddingTop","10"],["width","100%"]],null,null,null,L.Bc,L.J)),l.Ib(181,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(182,0,null,0,1,"SwtLabel",[["id","rolesLbl"],["width","120"]],null,null,null,L.Yc,L.fb)),l.Ib(183,4440064,[[18,4],["rolesLbl",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],width:[1,"width"]},null),(e()(),l.Jb(184,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","rolesGridContainer"],["minHeight","80"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,L.Nc,L.U)),l.Ib(185,4440064,[[49,4],["rolesGridContainer",4]],0,s.db,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minHeight:[4,"minHeight"],border:[5,"border"]},null),(e()(),l.Jb(186,0,null,0,1,"SwtCanvas",[["height","40%"],["minHeight","120"],["width","100%"]],null,null,null,L.Nc,L.U)),l.Ib(187,4440064,[[31,4],["subEventCanvas",4]],0,s.db,[l.r,s.i],{width:[0,"width"],height:[1,"height"],minHeight:[2,"minHeight"]},null),(e()(),l.Jb(188,0,null,0,72,"Grid",[["height","150"],["paddingTop","5"],["width","100%"]],null,null,null,L.Cc,L.H)),l.Ib(189,4440064,[[47,4],["subEventGrid",4]],0,s.z,[l.r,s.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(190,0,null,0,9,"GridRow",[["height","25"]],null,null,null,L.Bc,L.J)),l.Ib(191,4440064,null,0,s.B,[l.r,s.i],{height:[0,"height"]},null),(e()(),l.Jb(192,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(193,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(194,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(195,4440064,[[10,4],["parameterIdLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(196,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(197,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(198,0,null,0,1,"SwtText",[],null,null,null,L.ld,L.qb)),l.Ib(199,4440064,[[25,4],["parameterIdTxt",4]],0,s.Pb,[l.r,s.i],null,null),(e()(),l.Jb(200,0,null,0,9,"GridRow",[["height","25"]],null,null,null,L.Bc,L.J)),l.Ib(201,4440064,null,0,s.B,[l.r,s.i],{height:[0,"height"]},null),(e()(),l.Jb(202,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(203,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(204,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(205,4440064,[[9,4],["desLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(206,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(207,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(208,0,null,0,1,"SwtText",[],null,null,null,L.ld,L.qb)),l.Ib(209,4440064,[[24,4],["descTxt",4]],0,s.Pb,[l.r,s.i],null,null),(e()(),l.Jb(210,0,null,0,18,"GridRow",[["height","25"]],null,null,null,L.Bc,L.J)),l.Ib(211,4440064,null,0,s.B,[l.r,s.i],{height:[0,"height"]},null),(e()(),l.Jb(212,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(213,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(214,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(215,4440064,[[11,4],["mapFromLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(216,0,null,0,12,"GridItem",[["width","400"]],null,null,null,L.Ac,L.I)),l.Ib(217,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(218,0,null,0,10,"SwtRadioButtonGroup",[["align","horizontal"],["id","mapFrom"],["width","100%"]],null,[[null,"change"]],function(e,t,i){var l=!0,n=e.component;"change"===t&&(l=!1!==n.changeValueComponent()&&l);return l},L.ed,L.lb)),l.Ib(219,4440064,[[40,4],["mapFrom",4]],1,s.Hb,[x.c,l.r,s.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),l.Zb(603979776,51,{radioItems:1}),(e()(),l.Jb(221,0,null,0,1,"SwtRadioItem",[["groupName","mapFrom"],["id","instAttr"],["selected","true"],["value","A"],["width","140"]],null,null,null,L.fd,L.mb)),l.Ib(222,4440064,[[51,4],[41,4],["instAttr",4]],0,s.Ib,[l.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(e()(),l.Jb(223,0,null,0,1,"SwtRadioItem",[["groupName","mapFrom"],["id","literal"],["value","L"],["width","70"]],null,null,null,L.fd,L.mb)),l.Ib(224,4440064,[[51,4],[42,4],["literal",4]],0,s.Ib,[l.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(225,0,null,0,1,"SwtRadioItem",[["groupName","mapFrom"],["id","ignore"],["value","I"],["width","80"]],null,null,null,L.fd,L.mb)),l.Ib(226,4440064,[[51,4],[43,4],["ignore",4]],0,s.Ib,[l.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(227,0,null,0,1,"SwtRadioItem",[["groupName","mapFrom"],["id","null"],["value","N"],["width","60"]],null,null,null,L.fd,L.mb)),l.Ib(228,4440064,[[51,4],[44,4],["null",4]],0,s.Ib,[l.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(229,0,null,0,9,"GridRow",[["height","40"]],null,null,null,L.Bc,L.J)),l.Ib(230,4440064,null,0,s.B,[l.r,s.i],{height:[0,"height"]},null),(e()(),l.Jb(231,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(232,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(233,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(234,4440064,[[14,4],["infoLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(235,0,null,0,3,"GridItem",[["height","100%"],["width","100%"]],null,null,null,L.Ac,L.I)),l.Ib(236,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(237,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["height","100%"],["id","infoText"],["width","100%"]],null,null,null,L.Yc,L.fb)),l.Ib(238,4440064,[[50,4],["infoText",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],fontWeight:[3,"fontWeight"]},null),(e()(),l.Jb(239,0,null,0,13,"GridRow",[["height","25"],["paddingTop","5"]],null,null,null,L.Bc,L.J)),l.Ib(240,4440064,null,0,s.B,[l.r,s.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(e()(),l.Jb(241,0,null,0,3,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(242,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(243,0,null,0,1,"SwtLabel",[],null,null,null,L.Yc,L.fb)),l.Ib(244,4440064,[[12,4],["valueLbl",4]],0,s.vb,[l.r,s.i],null,null),(e()(),l.Jb(245,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(246,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(247,0,null,0,1,"SwtComboBox",[["enabled","false"],["id","valueCombo"],["shiftUp","180"],["width","200"]],null,[["window","mousewheel"]],function(e,t,i){var n=!0;"window:mousewheel"===t&&(n=!1!==l.Tb(e,248).mouseWeelEventHandler(i.target)&&n);return n},L.Pc,L.W)),l.Ib(248,4440064,[[23,4],["valueCombo",4]],0,s.gb,[l.r,s.i],{width:[0,"width"],id:[1,"id"],enabled:[2,"enabled"],shiftUp:[3,"shiftUp"]},null),(e()(),l.Jb(249,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(250,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(251,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","valueTxt"],["width","200"]],null,null,null,L.kd,L.sb)),l.Ib(252,4440064,[[21,4],["valueTxt",4]],0,s.Rb,[l.r,s.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},null),(e()(),l.Jb(253,0,null,0,7,"GridRow",[["height","30"],["paddingTop","10"]],null,null,null,L.Bc,L.J)),l.Ib(254,4440064,null,0,s.B,[l.r,s.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(e()(),l.Jb(255,0,null,0,1,"GridItem",[["width","140"]],null,null,null,L.Ac,L.I)),l.Ib(256,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(e()(),l.Jb(257,0,null,0,3,"GridItem",[],null,null,null,L.Ac,L.I)),l.Ib(258,4440064,null,0,s.A,[l.r,s.i],null,null),(e()(),l.Jb(259,0,null,0,1,"SwtButton",[["enabled","false"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.updateHandle()&&l);return l},L.Mc,L.T)),l.Ib(260,4440064,[[34,4],["updateButton",4]],0,s.cb,[l.r,s.i],{enabled:[0,"enabled"]},{onClick_:"click"}),(e()(),l.Jb(261,0,null,0,9,"SwtCanvas",[["height","35"],["width","100%"]],null,null,null,L.Nc,L.U)),l.Ib(262,4440064,null,0,s.db,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(263,0,null,0,7,"HBox",[["horizontalGap","5"],["width","100%"]],null,null,null,L.Dc,L.K)),l.Ib(264,4440064,null,0,s.C,[l.r,s.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"]},null),(e()(),l.Jb(265,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.saveHandler()&&l);return l},L.Mc,L.T)),l.Ib(266,4440064,[[35,4],["okButton",4]],0,s.cb,[l.r,s.i],null,{onClick_:"click"}),(e()(),l.Jb(267,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.cancelHandler()&&l);return l},L.Mc,L.T)),l.Ib(268,4440064,[[36,4],["cancelButton",4]],0,s.cb,[l.r,s.i],null,{onClick_:"click"}),(e()(),l.Jb(269,0,null,0,1,"SwtButton",[["enabled","false"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.showXmlHandler()&&l);return l},L.Mc,L.T)),l.Ib(270,4440064,[[37,4],["showXMLButton",4]],0,s.cb,[l.r,s.i],{enabled:[0,"enabled"]},{onClick_:"click"})],function(e,t){e(t,51,0,"100%","100%");e(t,53,0,"100%","100%","5","5","5","5");e(t,55,0,"100%","95%");e(t,57,0,"100%","100%","950");e(t,59,0,"100%","125","10"),e(t,61,0);e(t,63,0,"140"),e(t,65,0),e(t,67,0);e(t,69,0,"200","false");e(t,71,0,"100%");e(t,73,0,"right","100%"),e(t,75,0);e(t,77,0,"","350"),e(t,79,0);e(t,81,0,"140"),e(t,83,0),e(t,85,0);e(t,87,0,"right","200","false");e(t,89,0,"100%");e(t,91,0,"right","100%"),e(t,93,0),e(t,95,0),e(t,97,0);e(t,99,0,"140"),e(t,101,0),e(t,103,0),e(t,105,0),e(t,107,0);e(t,109,0,"normal"),e(t,111,0);e(t,113,0,"140"),e(t,115,0),e(t,117,0);e(t,119,0,"380");e(t,121,0,"100%","90%");e(t,123,0,"100%","100%","10");e(t,125,0,"100%","10px");e(t,127,0,"100%","95%");e(t,129,0,"80","20");e(t,131,0,"msgComboLbl","Format");e(t,133,0,"350");e(t,135,0,"msgFormatList","300","msgCombo");e(t,137,0,"30%");e(t,139,0,"msgLabel","300");e(t,141,0,"100%","7%");e(t,143,0,"90%");e(t,145,0,"10%");e(t,147,0,"addFormatButton");e(t,149,0,"100%","100%");e(t,151,0,"100%","100%","10");e(t,153,0,"100%","40");e(t,155,0,"105");e(t,157,0,"emailFormatLbl");e(t,159,0,"350");e(t,161,0,"emailTemplatesList","300","emailFormatCombo"),e(t,163,0);e(t,165,0,"emailFormatDescLbl","300");e(t,167,0,"100%","40");e(t,169,0,"105"),e(t,171,0);e(t,173,0,"configureButton");e(t,175,0,"100%","40%","10");e(t,177,0,"usersLbl","120");e(t,179,0,"usersGridContainer","canvasWithGreyBorder","100%","100%","80","false");e(t,181,0,"100%","40%","10");e(t,183,0,"rolesLbl","120");e(t,185,0,"rolesGridContainer","canvasWithGreyBorder","100%","100%","80","false");e(t,187,0,"100%","40%","120");e(t,189,0,"100%","150","5");e(t,191,0,"25");e(t,193,0,"140"),e(t,195,0),e(t,197,0),e(t,199,0);e(t,201,0,"25");e(t,203,0,"140"),e(t,205,0),e(t,207,0),e(t,209,0);e(t,211,0,"25");e(t,213,0,"140"),e(t,215,0);e(t,217,0,"400");e(t,219,0,"mapFrom","100%","horizontal");e(t,222,0,"instAttr","140","mapFrom","A","true");e(t,224,0,"literal","70","mapFrom","L");e(t,226,0,"ignore","80","mapFrom","I");e(t,228,0,"null","60","mapFrom","N");e(t,230,0,"40");e(t,232,0,"140"),e(t,234,0);e(t,236,0,"100%","100%");e(t,238,0,"infoText","100%","100%","normal");e(t,240,0,"25","5");e(t,242,0,"140"),e(t,244,0),e(t,246,0);e(t,248,0,"200","valueCombo","false","180"),e(t,250,0);e(t,252,0,"valueTxt","200","false");e(t,254,0,"30","10");e(t,256,0,"140"),e(t,258,0);e(t,260,0,"false");e(t,262,0,"100%","35");e(t,264,0,"5","100%"),e(t,266,0),e(t,268,0);e(t,270,0,"false")},null)}function Y(e){return l.dc(0,[(e()(),l.Jb(0,0,null,null,1,"app-events-add",[],null,null,null,O,z)),l.Ib(1,4440064,null,0,c,[s.i,l.r],null,null)],function(e,t){e(t,1,0)},null)}var U=l.Fb("app-events-add",c,Y,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);