(window.webpackJsonp=window.webpackJsonp||[]).push([[65],{BxM8:function(t,e,n){"use strict";n.r(e);var i=n("CcnG"),a=n("mrSG"),l=n("ZYCi"),o=n("447K"),s=function(t){function e(e,n){var i=t.call(this,n,e)||this;return i.commonService=e,i.element=n,i.logger=null,i.jsonReader=new o.L,i.inputData=new o.G(i.commonService),i.baseURL=o.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.requestParams=[],i.templateId=null,i.logger=new o.R("Account Currency Period maintenance",i.commonService.httpclient),i.swtAlert=new o.bb(e),i.logger.info("method [constructor] - START/END "),i}return a.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.emailTemplateMaintenanceGrid=this.emailTemplateMaintenanceGridContainer.addChild(o.hb),this.addButton.label=o.Wb.getPredictMessage("button.add",null),this.addButton.toolTip=o.Wb.getPredictMessage("ccyAccMaintPeriod.tooltip.addButton",null),this.changeButton.label=o.Wb.getPredictMessage("button.change",null),this.changeButton.toolTip=o.Wb.getPredictMessage("ccyAccMaintPeriod.tooltip.changeButton",null),this.viewButton.label=o.Wb.getPredictMessage("button.view",null),this.viewButton.toolTip=o.Wb.getPredictMessage("ccyAccMaintPeriod.tooltip.viewButton",null),this.deleteButton.label=o.Wb.getPredictMessage("button.delete",null),this.deleteButton.toolTip=o.Wb.getPredictMessage("ccyAccMaintPeriod.tooltip.deleteButton",null),this.closeButton.label=o.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=o.Wb.getPredictMessage("tooltip.close",null)},e.prototype.onLoad=function(){var t=this,e=0;try{this.requestParams=[],this.menuAccessId="0",this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),e=10,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},e=20,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,"0"!=this.menuAccessId&&(this.addButton.enabled=!1),this.actionPath="emailTemplateMaintenance.do?method=",this.actionMethod="displayEmailTemplateList",this.requestParams.menuAccessId=this.menuAccessId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),e=30,this.emailTemplateMaintenanceGrid.onRowClick=function(e){t.cellClickEventHandler(e)}}catch(n){this.logger.error("method [onLoad] - error: ",n,"errorLocation: ",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaint.ts","onLoad",e)}},e.prototype.cellClickEventHandler=function(t){var e=0;try{this.emailTemplateMaintenanceGrid.refresh(),e=10,this.emailTemplateMaintenanceGrid.selectedIndex>=0?(this.changeButton.enabled=!0,this.changeButton.buttonMode=!0,this.viewButton.enabled=!0,this.viewButton.buttonMode=!0,this.deleteButton.enabled=!0,this.deleteButton.buttonMode=!0,e=20,this.selectedtRow=this.emailTemplateMaintenanceGrid.selectedItem,this.templateId=this.selectedtRow.templateId.content):(e=30,this.changeButton.enabled=!1,this.changeButton.buttonMode=!1,this.viewButton.enabled=!1,this.viewButton.buttonMode=!1,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1)}catch(n){this.logger.error("method [cellClickEventHandler] - error: ",n,"errorLocation: ",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"","cellClickEventHandler",e)}},e.prototype.inputDataResult=function(t){var e=0;try{if(this.inputData.isBusy())this.inputData.cbStop();else if(e=10,this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),e=20,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&(this.emailTemplateMaintenanceGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.viewButton.enabled=!1,this.deleteButton.enabled=!1,e=30,this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,e=40,this.dateFormat=this.jsonReader.getSingletons().dateformat,this.emailTemplateMaintenanceGrid.dateFormat=this.dateFormat,e=50,e=60,e=100,!this.jsonReader.isDataBuilding())){var n={columns:this.lastRecievedJSON.emailTemplateMaintenance.emailTemplateMaintenanceGrid.metadata.columns};e=110,this.emailTemplateMaintenanceGrid.CustomGrid(n),e=120;var i=this.lastRecievedJSON.emailTemplateMaintenance.emailTemplateMaintenanceGrid.rows;i.size>0&&i.row?(this.emailTemplateMaintenanceGrid.gridData=i,e=130,this.emailTemplateMaintenanceGrid.setRowSize=this.jsonReader.getRowSize(),e=140,this.emailTemplateMaintenanceGrid.refresh()):this.emailTemplateMaintenanceGrid.gridData={size:0,row:[]},this.prevRecievedJSON=this.lastRecievedJSON}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error")}catch(a){this.logger.error("method [inputDataResult] - error: ",a,"errorLocation: ",e),o.Wb.logError(a,o.Wb.PREDICT_MODULE_ID,"","inputDataResult",e)}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.closeHandler=function(){o.x.call("close")},e.prototype.updateData=function(){var t=this,e=0;try{this.requestParams=[],this.menuAccessId=o.x.call("eval","menuAccessId"),e=10,this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),e=20,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},e=30,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="emailTemplateMaintenance.do?method=",this.actionMethod="displayEmailTemplateList",this.requestParams.menuAccessId=this.menuAccessId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(n){this.logger.error("method [updateData] - error: ",n,"errorLocation: ",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"","updateData",e)}},e.prototype.addEmailTemplateHandler=function(){var t=0;try{this.operation="add";t=10,o.x.call("subEmailTemplateMaintenance","add","")}catch(e){this.logger.error("method [addEmailTemplateHandler] - error: ",e,"errorLocation: ",t),o.Wb.logError(e,o.Wb.PREDICT_MODULE_ID,"","addEmailTemplateHandler",t)}},e.prototype.changeEmailTemplateHandler=function(){var t=0;try{this.operation="change";var e=[];e.push({templateId:this.selectedtRow.templateId.content}),t=10,console.log("params",e),o.x.call("subEmailTemplateMaintenance","change",JSON.stringify(e))}catch(n){this.logger.error("method [changeEmailTemplateHandler] - error: ",n,"errorLocation: ",t),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"","changeEmailTemplateHandler",t)}},e.prototype.viewEmailTemplateHandler=function(){var t=0;try{this.operation="view";var e=[];e.push({templateId:this.selectedtRow.templateId.content}),t=10,console.log("params",e),o.x.call("subEmailTemplateMaintenance","view",JSON.stringify(e))}catch(n){this.logger.error("method [viewEmailTemplateHandler] - error: ",n,"errorLocation: ",t),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"","viewEmailTemplateHandler",t)}},e.prototype.deleteCheck=function(){this.swtAlert.confirm(o.Wb.getPredictMessage("confirm.delete",null),o.Wb.getPredictMessage("alert.deletion.confirm",null),o.bb.YES|o.bb.NO,null,this.deleteEmailTemplateHandler.bind(this),null)},e.prototype.deleteResult=function(t){var e=o.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninDelete",null);this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyMessage()&&-1!=this.jsonReader.getRequestReplyMessage().indexOf("RECORD_CANNOT_DELETED")?this.swtAlert.error(e,null,o.c.OK,null,function(){}):this.updateData())},e.prototype.deleteEmailTemplateHandler=function(t){var e=this;if(t.detail==o.bb.YES){var n=0;try{this.requestParams=[],this.menuAccessId=o.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),n=10,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.deleteResult(t)},n=20,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="emailTemplateMaintenance.do?method=",this.actionMethod="deleteEmailTemplate",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.templateId=this.selectedtRow.templateId.content,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,n=40,this.inputData.send(this.requestParams)}catch(i){this.logger.error("method [deleteEmailTemplateHandler] - error: ",i,"errorLocation: ",n),o.Wb.logError(i,o.Wb.PREDICT_MODULE_ID,"","delete",n)}}},e}(o.yb),r=[{path:"",component:s}],c=(l.l.forChild(r),function(){return function(){}}()),d=n("pMnS"),u=n("RChO"),h=n("t6HQ"),b=n("WFGK"),m=n("5FqG"),p=n("Ip0R"),g=n("gIcY"),R=n("t/Na"),f=n("sE5F"),M=n("OzfB"),T=n("T7CS"),w=n("S7LP"),v=n("6aHO"),D=n("WzUx"),I=n("A7o+"),C=n("zCE2"),B=n("Jg5P"),y=n("3R0m"),E=n("hhbb"),S=n("5rxC"),k=n("Fzqc"),L=n("21Lb"),P=n("hUWP"),O=n("3pJQ"),_=n("V9q+"),A=n("VDKW"),W=n("kXfT"),N=n("BGbe");n.d(e,"EmailTemplateMaintenanceModuleNgFactory",function(){return G}),n.d(e,"RenderType_EmailTemplateMaintenance",function(){return H}),n.d(e,"View_EmailTemplateMaintenance_0",function(){return x}),n.d(e,"View_EmailTemplateMaintenance_Host_0",function(){return q}),n.d(e,"EmailTemplateMaintenanceNgFactory",function(){return U});var G=i.Gb(c,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[d.a,u.a,h.a,b.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,U]],[3,i.n],i.J]),i.Rb(4608,p.m,p.l,[i.F,[2,p.u]]),i.Rb(4608,g.c,g.c,[]),i.Rb(4608,g.p,g.p,[]),i.Rb(4608,R.j,R.p,[p.c,i.O,R.n]),i.Rb(4608,R.q,R.q,[R.j,R.o]),i.Rb(5120,R.a,function(t){return[t,new o.tb]},[R.q]),i.Rb(4608,R.m,R.m,[]),i.Rb(6144,R.k,null,[R.m]),i.Rb(4608,R.i,R.i,[R.k]),i.Rb(6144,R.b,null,[R.i]),i.Rb(4608,R.f,R.l,[R.b,i.B]),i.Rb(4608,R.c,R.c,[R.f]),i.Rb(4608,f.c,f.c,[]),i.Rb(4608,f.g,f.b,[]),i.Rb(5120,f.i,f.j,[]),i.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),i.Rb(4608,f.f,f.a,[]),i.Rb(5120,f.d,f.k,[f.h,f.f]),i.Rb(5120,i.b,function(t,e){return[M.j(t,e)]},[p.c,i.O]),i.Rb(4608,T.a,T.a,[]),i.Rb(4608,w.a,w.a,[]),i.Rb(4608,v.a,v.a,[i.n,i.L,i.B,w.a,i.g]),i.Rb(4608,D.c,D.c,[i.n,i.g,i.B]),i.Rb(4608,D.e,D.e,[D.c]),i.Rb(4608,I.l,I.l,[]),i.Rb(4608,I.h,I.g,[]),i.Rb(4608,I.c,I.f,[]),i.Rb(4608,I.j,I.d,[]),i.Rb(4608,I.b,I.a,[]),i.Rb(4608,I.k,I.k,[I.l,I.h,I.c,I.j,I.b,I.m,I.n]),i.Rb(4608,D.i,D.i,[[2,I.k]]),i.Rb(4608,D.r,D.r,[D.L,[2,I.k],D.i]),i.Rb(4608,D.t,D.t,[]),i.Rb(4608,D.w,D.w,[]),i.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),i.Rb(1073742336,p.b,p.b,[]),i.Rb(1073742336,g.n,g.n,[]),i.Rb(1073742336,g.l,g.l,[]),i.Rb(1073742336,C.a,C.a,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,g.e,g.e,[]),i.Rb(1073742336,y.a,y.a,[]),i.Rb(1073742336,I.i,I.i,[]),i.Rb(1073742336,D.b,D.b,[]),i.Rb(1073742336,R.e,R.e,[]),i.Rb(1073742336,R.d,R.d,[]),i.Rb(1073742336,f.e,f.e,[]),i.Rb(1073742336,E.b,E.b,[]),i.Rb(1073742336,S.b,S.b,[]),i.Rb(1073742336,M.c,M.c,[]),i.Rb(1073742336,k.a,k.a,[]),i.Rb(1073742336,L.d,L.d,[]),i.Rb(1073742336,P.c,P.c,[]),i.Rb(1073742336,O.a,O.a,[]),i.Rb(1073742336,_.a,_.a,[[2,M.g],i.O]),i.Rb(1073742336,A.b,A.b,[]),i.Rb(1073742336,W.a,W.a,[]),i.Rb(1073742336,N.b,N.b,[]),i.Rb(1073742336,o.Tb,o.Tb,[]),i.Rb(1073742336,c,c,[]),i.Rb(256,R.n,"XSRF-TOKEN",[]),i.Rb(256,R.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,I.m,void 0,[]),i.Rb(256,I.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,l.i,function(){return[[{path:"",component:s}]]},[])])}),J=[[""]],H=i.Hb({encapsulation:0,styles:J,data:{}});function x(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{loadingImage:0}),i.Zb(402653184,3,{emailTemplateMaintenanceGridContainer:0}),i.Zb(402653184,4,{addButton:0}),i.Zb(402653184,5,{changeButton:0}),i.Zb(402653184,6,{viewButton:0}),i.Zb(402653184,7,{deleteButton:0}),i.Zb(402653184,8,{closeButton:0}),(t()(),i.Jb(8,0,null,null,25,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var i=!0,a=t.component;"creationComplete"===e&&(i=!1!==a.onLoad()&&i);return i},m.ad,m.hb)),i.Ib(9,4440064,null,0,o.yb,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(10,0,null,0,23,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),i.Ib(11,4440064,null,0,o.ec,[i.r,o.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(t()(),i.Jb(12,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","emailTemplateMaintenanceGridContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,m.Nc,m.U)),i.Ib(13,4440064,[[3,4],["emailTemplateMaintenanceGridContainer",4]],0,o.db,[i.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),i.Jb(14,0,null,0,19,"SwtCanvas",[["height","35"],["width","100%"]],null,null,null,m.Nc,m.U)),i.Ib(15,4440064,null,0,o.db,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(16,0,null,0,17,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),i.Ib(17,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(18,0,null,0,9,"HBox",[["paddingLeft","5"],["width","90%"]],null,null,null,m.Dc,m.K)),i.Ib(19,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(20,0,null,0,1,"SwtButton",[["id","addButton"]],null,[[null,"click"]],function(t,e,n){var i=!0,a=t.component;"click"===e&&(i=!1!==a.addEmailTemplateHandler()&&i);return i},m.Mc,m.T)),i.Ib(21,4440064,[[4,4],["addButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(22,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"]],null,[[null,"click"]],function(t,e,n){var i=!0,a=t.component;"click"===e&&(i=!1!==a.changeEmailTemplateHandler()&&i);return i},m.Mc,m.T)),i.Ib(23,4440064,[[5,4],["changeButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(24,0,null,0,1,"SwtButton",[["enabled","false"],["id","viewButton"]],null,[[null,"click"]],function(t,e,n){var i=!0,a=t.component;"click"===e&&(i=!1!==a.viewEmailTemplateHandler()&&i);return i},m.Mc,m.T)),i.Ib(25,4440064,[[6,4],["viewButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(26,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"]],null,[[null,"click"]],function(t,e,n){var i=!0,a=t.component;"click"===e&&(i=!1!==a.deleteCheck()&&i);return i},m.Mc,m.T)),i.Ib(27,4440064,[[7,4],["deleteButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(28,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingLeft","5"],["width","10%"]],null,null,null,m.Dc,m.K)),i.Ib(29,4440064,null,0,o.C,[i.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(30,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,n){var i=!0,a=t.component;"click"===e&&(i=!1!==a.closeHandler()&&i);return i},m.Mc,m.T)),i.Ib(31,4440064,[[8,4],["closeButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(32,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),i.Ib(33,114688,[[2,4],["loadingImage",4]],0,o.xb,[i.r],null,null)],function(t,e){t(e,9,0,"100%","100%");t(e,11,0,"100%","100%","5","5","5");t(e,13,0,"emailTemplateMaintenanceGridContainer","canvasWithGreyBorder","100%","100%","false");t(e,15,0,"100%","35");t(e,17,0,"100%");t(e,19,0,"90%","5");t(e,21,0,"addButton",!0);t(e,23,0,"changeButton","false",!0);t(e,25,0,"viewButton","false",!0);t(e,27,0,"deleteButton","false",!0);t(e,29,0,"right","10%","5");t(e,31,0,"closeButton",!0),t(e,33,0)},null)}function q(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-emailtemplate",[],null,null,null,x,H)),i.Ib(1,4440064,null,0,s,[o.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var U=i.Fb("app-emailtemplate",s,q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);