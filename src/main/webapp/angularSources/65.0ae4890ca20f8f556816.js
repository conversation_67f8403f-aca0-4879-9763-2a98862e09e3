(window.webpackJsonp=window.webpackJsonp||[]).push([[65],{FigZ:function(t,e,n){"use strict";n.r(e);var l=n("CcnG"),i=n("mrSG"),o=n("447K"),a=n("ZYCi"),s=(n("CQIl"),n("hOyB"),n("cZOo"),n("DWi2"),function(t){function e(e,n){var l=t.call(this,n,e)||this;return l.commonService=e,l.element=n,l.logger=null,l.jsonReader=new o.L,l.inputData=new o.G(l.commonService),l.baseURL=o.Wb.getBaseURL(),l.actionMethod="",l.actionPath="",l.requestParams=[],l.templateId=null,l.lastFocusedElement=null,l.logger=new o.R("Account Currency Period maintenance",l.commonService.httpclient),l.swtAlert=new o.bb(e),l}return i.d(e,t),e.prototype.showText=function(){},e.prototype.showhtmlText=function(){},e.prototype.ngOnInit=function(){instanceElement=this,this.descriptionLabel.text=o.Wb.getPredictMessage("emailTemplateMaintenance.description",null),this.descriptionTxtInput.toolTip=o.Wb.getPredictMessage("emailTemplateMaintenance.tooltip.description",null),this.subjectContentLabel.text=o.Wb.getPredictMessage("emailTemplateMaintenance.subjectContent",null),this.bodyContentLabel.text=o.Wb.getPredictMessage("emailTemplateMaintenance.bodyContent",null),this.templateIdLabel.text=o.Wb.getPredictMessage("emailTemplateMaintenance.templateId",null),this.templateIdTxtInput.toolTip=o.Wb.getPredictMessage("emailTemplateMaintenance.tooltip.templateId",null),this.keywordsComboLabel.text=o.Wb.getPredictMessage("emailTemplateMaintenance.keywordsCombo",null),this.keywordsCombo.toolTip=o.Wb.getPredictMessage("emailTemplateMaintenance.tooltip.keywordsCombo",null),this.templateIdTxtInput.required=!0,this.saveButton.label=o.Wb.getPredictMessage("button.save",null),this.saveButton.toolTip=o.Wb.getPredictMessage("ccyAccMaintPeriod.tooltip.save",null),this.cancelButton.label=o.Wb.getPredictMessage("button.cancel",null),this.cancelButton.toolTip=o.Wb.getPredictMessage("ccyAccMaintPeriod.tooltip.cancel",null)},e.prototype.initTinyMCE=function(){var t=this;console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ initTinyMCE ~ initTinyMCE:");try{tinymce.init({selector:"#emailContent",skin:!1,content_css:"./assets/css/tinymce/SwtTextArea.css",body_class:"swtTextArea",plugins:["advlist","lists"],menubar:!1,branding:!1,width:"100%",height:"100%",toolbar:["fontselect | fontsizeselect | bold italic underline | forecolor | alignleft aligncenter alignright alignjustify | numlist"],setup:function(e){var n=null;e.on("focus",function(){t.lastFocusedElement="content"}),e.on("click",function(t){n=e.selection.getRng(),t.target.classList.contains("keyword-remove")&&(t.target.parentNode.remove(),n&&e.selection.setRng(n))})},content_style:"\n      .keyword {\n          display: inline-block;\n          background-color: #e1f5fe;\n          border: 1px solid #81d4fa;\n          border-radius: 4px;\n          padding: 2px 5px;\n          margin: 0 2px;\n          font-size: 0.8em;\n          cursor: pointer;\n      }\n      .keyword:hover {\n          background-color: #b3e5fc;\n      }\n      .keyword-remove {\n          color: #e74c3c;\n          margin-left: 5px;\n          font-weight: bold;\n      }\n      .swtTextArea {\n          line-height: 2.2;\n      }\n\n      .mce-offscreen-selection {\n    display: none !important;\n  }\n    \n  "}),tinymce.init({selector:"#subjectContent",skin:!1,content_css:"./assets/css/tinymce/SwtTextArea.css",body_class:"swtTextArea",plugins:[],menubar:!1,branding:!1,width:"100%",height:"23",toolbar:!1,statusbar:!1,setup:function(e){var n=null;e.on("focus",function(){t.lastFocusedElement="subject"}),e.on("click",function(t){n=e.selection.getRng(),t.target.classList.contains("keyword-remove")&&(t.target.parentNode.remove(),n&&e.selection.setRng(n))})},content_style:"\n     .swtTextArea {\n        height: 100%;\n        overflow: hidden; /* Prevent scrolling */\n        white-space: nowrap; /* Keep text in one line */\n    }\n      .keyword {\n          display: inline-block;\n          background-color: #e1f5fe;\n          border: 1px solid #81d4fa;\n          border-radius: 4px;\n          padding: 2px 5px;\n          margin: 0 2px;\n          font-size: 0.8em;\n          cursor: pointer;\n      }\n      .keyword:hover {\n          background-color: #b3e5fc;\n      }\n      .keyword-remove {\n          color: #e74c3c;\n          margin-left: 5px;\n          font-weight: bold;\n      }\n\n      .mce-offscreen-selection {\n    display: none !important;\n  }\n\n  "})}catch(e){console.log(e)}},e.prototype.ngAfterViewInit=function(){console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ ngAfterViewInit ~ ngAfterViewInit:"),this.initTinyMCE(),$(".close").on("click",function(){$(this).closest(".modal").hide()}),$(window).on("click",function(t){$(t.target).hasClass("modal")&&$(".modal").hide()})},e.prototype.focusChange=function(){console.log("focus Change")},e.prototype.onLoad=function(){var t=this;console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ onLoad ~ onLoad:");var e=0;try{this.requestParams=[],this.menuAccessId=o.x.call("eval","menuAccessId"),this.screenName=o.x.call("eval","screenName"),this.parameters=o.x.call("eval","params")?JSON.parse(o.x.call("eval","params")):"",this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),e=10,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},e=20,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="emailTemplateMaintenance.do?",this.actionMethod="method=displayEmailTemplate",this.requestParams.screenName=this.screenName,this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.templateId="change"==this.screenName||"view"==this.screenName?this.parameters[0].templateId:"",console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ onLoad ~ this.requestParams:",this.requestParams),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,e=30,this.inputData.send(this.requestParams)}catch(n){this.logger.error("method [onLoad] - error: ",n,"errorLocation: ",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"EmailTemplateMaintenanceAdd.ts","onLoad",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintAdd.ts","onLoad",e)}},e.prototype.changeKeywords=function(){console.log("tjis",this.keywordsCombo.selectedLabel),this.keywordsCombo.selectedLabel&&(this.insertField(this.keywordsCombo.selectedLabel),this.keywordsCombo.selectedIndex=-1)},e.prototype.inputDataResult=function(t){var e=0;try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),e=10,this.jsonReader.getRequestReplyStatus()?this.lastRecievedJSON!=this.prevRecievedJSON&&(this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,e=20,this.dateFormat=this.jsonReader.getSingletons().dateformat,this.systemDate=this.jsonReader.getSingletons().systemDate,this.keywordsCombo.setComboData(this.jsonReader.getSelects(),!1),e=30,this.templateIdTxtInput.text=this.jsonReader.getSingletons().templateId.content,this.descriptionTxtInput.text=this.jsonReader.getSingletons().description.content,this.setTinyMCEContentWithPlaceholders(o.t.decode64(this.jsonReader.getSingletons().subjectContent.content),!0),this.setTinyMCEContentWithPlaceholders(o.t.decode64(this.jsonReader.getSingletons().bodyContent.content),!1),e=50,console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ inputDataResult ~ this.screenName:",this.screenName),"change"!=this.screenName&&"view"!=this.screenName||(e=100,this.disableComponents()),this.jsonReader.isDataBuilding()||(this.prevRecievedJSON=this.lastRecievedJSON)):this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error"))}catch(n){this.logger.error("method [inputDataResult] - error: ",n,"errorLocation: ",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"EmailTemplateMaintenanceAdd.ts","inputDataResult",e)}},e.prototype.disableComponents=function(){try{this.templateIdTxtInput.enabled=!1,this.descriptionTxtInput.enabled=!1,"view"==this.screenName&&(tinymce.get("subjectContent").setMode("readonly"),tinymce.get("emailContent").setMode("readonly"),this.saveButton.enabled=!1)}catch(t){this.logger.error("method [populateValues] - error: ",t,"errorLocation: ",0),o.Wb.logError(t,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintAdd.ts","populateValues",0)}},e.prototype.refreshComboList=function(){var t=this,e=0;try{this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.setComboLists(e)},e=10,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,e=20,this.actionPath="emailTemplateMaintenance.do?",this.actionMethod="method=getLists",e=30,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),e=40}catch(n){this.logger.error("method [refreshComboList] - error: ",n,"errorLocation: ",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"","refreshComboList",e)}},e.prototype.setComboLists=function(t){var e=0;try{this.inputData.isBusy()?(e=10,this.inputData.cbStop()):(e=20,this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),e=30,this.jsonReader.getRequestReplyStatus()?this.lastRecievedJSON!=this.prevRecievedJSON&&(e=40,this.jsonReader.isDataBuilding()||(this.prevRecievedJSON=this.lastRecievedJSON)):this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error"))}catch(n){this.logger.error("method [setComboLists] - error: ",n,"errorLocation: ",e),o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintAdd.ts","setComboLists",e)}},e.prototype.saveHandler=function(){var t=this;this.showhtmlText(),this.showText();var e=0;try{this.requestParams=[],e=10;var n="change"==this.screenName?"update":"save";e=40,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.saveResult(e)},e=50,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="emailTemplateMaintenance.do?",this.actionMethod="method=saveEmailTemplate",e=60,this.requestParams.description=this.descriptionTxtInput.text,this.requestParams.subjectContent=o.t.encode64(this.getTinyMCEContentWithReplacements(!0)),this.requestParams.bodyContent=o.t.encode64(this.getTinyMCEContentWithReplacements(!1)),this.requestParams.templateId=this.templateIdTxtInput.text,e=130,this.requestParams.screenName=n,e=140,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,e=150,this.inputData.send(this.requestParams)}catch(l){this.logger.error("method [saveHandler] - error: ",l,"errorLocation: ",e),o.Wb.logError(l,o.Wb.PREDICT_MODULE_ID,".ts","saveHandler",e)}},e.prototype.saveResult=function(t){var e=o.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninAdd",null);this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyMessage()&&-1!=this.jsonReader.getRequestReplyMessage().indexOf("RECORD_EXIST")?this.swtAlert.error(e,null,o.c.OK,null,function(){}):this.jsonReader.getRequestReplyStatus()?(window.opener.instanceElement.updateData(!0),o.x.call("close")):this.inputDataFault(t))},e.prototype.cancelHandler=function(){o.x.call("close")},e.prototype.popupClosed=function(){window.close()},e.prototype.closePopup=function(t){"previewDialog"==t?$("#previewDialog").hide():$("#previewModal").hide()},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.insertField=function(t){console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ insertField ~ field:",t);var e='<span class="keyword" contenteditable="false">P${'+t+'}<span class="keyword-remove">\xd7</span></span>';"subject"===this.lastFocusedElement?(tinymce.get("subjectContent").insertContent(e),tinymce.get("subjectContent").focus()):(tinymce.get("emailContent").insertContent(e),tinymce.get("emailContent").focus())},e.prototype.getTinyMCEContentWithReplacements=function(t){if(t){var e=tinymce.get("subjectContent").getContent({format:"text"});console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ getTinyMCEContentWithReplacements ~ subjectContent:",e);var n=this.replaceSpansWithTemplates(e);return console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ getTinyMCEContentWithReplacements ~ subjectText:",n),n}var l=tinymce.get("emailContent").getContent(),i=this.replaceSpansWithTemplates(l);return console.log("\ud83d\ude80 ~ EmailTemplateMaintenanceAdd ~ getTinyMCEContentWithReplacements ~ emailText:",i),i},e.prototype.replaceSpansWithTemplates=function(t){return t.replace(/<span class="keyword" contenteditable="false">(.*?)<span class="keyword-remove">&times;<\/span><\/span>/g,"$1").replace(/\xd7/g,"")},e.prototype.replaceTemplatesWithSpans=function(t){return t.replace(/P\$\{(.*?)\}/g,'<span class="keyword" contenteditable="false">P${$1}<span class="keyword-remove">\xd7</span></span>')},e.prototype.replaceDynamicFields=function(t){return t.replace(/<span class="keyword" contenteditable="false">P\$\{(.*?)\}<span class="keyword-remove">\xd7<\/span><\/span>/g,function(t,e){return"P${"+e+"}"})},e.prototype.setTinyMCEContentWithPlaceholders=function(t,e){var n=this.replaceTemplatesWithSpans(t);e?(tinymce.get("subjectContent").setContent(n),tinymce.get("subjectContent").focus()):(tinymce.get("emailContent").setContent(n),tinymce.get("emailContent").focus())},e.prototype.replacePlaceholdersWithSpans=function(t){return t.replace(/P\$\{(\w+)\}/g,function(t,e){return'<span class="keyword" contenteditable="false">P${'+e+'}<span class="keyword-remove">\xd7</span></span>'})},e.prototype.showPreviewDialog=function(){var t=(tinymce.get("subjectContent").getContent({format:"text"})+" "+tinymce.get("emailContent").getContent()).match(/P\$\{([^}]+)\}/g)||[],e=Array.from(new Set(t.map(function(t){return t.slice(3,-1)}))),n="";e.forEach(function(t){n+='<div class="input-group-2">\n                     <label>'+t+' </label>\n                     <input class="input-content" type="text" id="'+t+'">\n                   </div>'}),$("#fieldInputs").html(n),$("#previewDialog").show()},e.prototype.escapeRegExp=function(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")},e.prototype.generatePreview=function(){var t=this;try{var e=tinymce.get("subjectContent").getContent({format:"text"}),n=tinymce.get("emailContent").getContent(),l=this.replaceSpansWithTemplates(e+" "+n).match(/P\$\{([^}]+)\}/g)||[];Array.from(new Set(l.map(function(t){return t.slice(3,-1)}))).forEach(function(l){var i=$("#"+t.escapeRegExp(l)).val(),o=new RegExp("P\\$\\{"+t.escapeRegExp(l)+"\\}","g");e=e.replace(o,i),n=n.replace(o,i)}),$("#previewSubject").html(e),$("#previewContent").html(n),$("#previewDialog").hide(),$("#previewModal").show()}catch(i){console.error(i)}},e}(o.yb)),r=[{path:"",component:s}],u=(a.l.forChild(r),function(){return function(){}}()),d=n("pMnS"),c=n("RChO"),h=n("t6HQ"),p=n("WFGK"),b=n("5FqG"),m=n("Ip0R"),g=n("gIcY"),w=n("t/Na"),f=n("sE5F"),y=n("OzfB"),R=n("T7CS"),v=n("S7LP"),C=n("6aHO"),x=n("WzUx"),I=n("A7o+"),T=n("zCE2"),k=n("Jg5P"),S=n("3R0m"),M=n("hhbb"),L=n("5rxC"),A=n("Fzqc"),J=n("21Lb"),P=n("hUWP"),D=n("3pJQ"),E=n("V9q+"),j=n("VDKW"),_=n("kXfT"),O=n("BGbe");n.d(e,"EmailTemplateMaintenanceAddModuleNgFactory",function(){return B}),n.d(e,"RenderType_EmailTemplateMaintenanceAdd",function(){return N}),n.d(e,"View_EmailTemplateMaintenanceAdd_0",function(){return q}),n.d(e,"View_EmailTemplateMaintenanceAdd_Host_0",function(){return G}),n.d(e,"EmailTemplateMaintenanceAddNgFactory",function(){return F});var B=l.Gb(u,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[d.a,c.a,h.a,p.a,b.Cb,b.Pb,b.r,b.rc,b.s,b.Ab,b.Bb,b.Db,b.qd,b.Hb,b.k,b.Ib,b.Nb,b.Ub,b.yb,b.Jb,b.v,b.A,b.e,b.c,b.g,b.d,b.Kb,b.f,b.ec,b.Wb,b.bc,b.ac,b.sc,b.fc,b.lc,b.jc,b.Eb,b.Fb,b.mc,b.Lb,b.nc,b.Mb,b.dc,b.Rb,b.b,b.ic,b.Yb,b.Sb,b.kc,b.y,b.Qb,b.cc,b.hc,b.pc,b.oc,b.xb,b.p,b.q,b.o,b.h,b.j,b.w,b.Zb,b.i,b.m,b.Vb,b.Ob,b.Gb,b.Xb,b.t,b.tc,b.zb,b.n,b.qc,b.a,b.z,b.rd,b.sd,b.x,b.td,b.gc,b.l,b.u,b.ud,b.Tb,F]],[3,l.n],l.J]),l.Rb(4608,m.m,m.l,[l.F,[2,m.u]]),l.Rb(4608,g.c,g.c,[]),l.Rb(4608,g.p,g.p,[]),l.Rb(4608,w.j,w.p,[m.c,l.O,w.n]),l.Rb(4608,w.q,w.q,[w.j,w.o]),l.Rb(5120,w.a,function(t){return[t,new o.tb]},[w.q]),l.Rb(4608,w.m,w.m,[]),l.Rb(6144,w.k,null,[w.m]),l.Rb(4608,w.i,w.i,[w.k]),l.Rb(6144,w.b,null,[w.i]),l.Rb(4608,w.f,w.l,[w.b,l.B]),l.Rb(4608,w.c,w.c,[w.f]),l.Rb(4608,f.c,f.c,[]),l.Rb(4608,f.g,f.b,[]),l.Rb(5120,f.i,f.j,[]),l.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),l.Rb(4608,f.f,f.a,[]),l.Rb(5120,f.d,f.k,[f.h,f.f]),l.Rb(5120,l.b,function(t,e){return[y.j(t,e)]},[m.c,l.O]),l.Rb(4608,R.a,R.a,[]),l.Rb(4608,v.a,v.a,[]),l.Rb(4608,C.a,C.a,[l.n,l.L,l.B,v.a,l.g]),l.Rb(4608,x.c,x.c,[l.n,l.g,l.B]),l.Rb(4608,x.e,x.e,[x.c]),l.Rb(4608,I.l,I.l,[]),l.Rb(4608,I.h,I.g,[]),l.Rb(4608,I.c,I.f,[]),l.Rb(4608,I.j,I.d,[]),l.Rb(4608,I.b,I.a,[]),l.Rb(4608,I.k,I.k,[I.l,I.h,I.c,I.j,I.b,I.m,I.n]),l.Rb(4608,x.i,x.i,[[2,I.k]]),l.Rb(4608,x.r,x.r,[x.L,[2,I.k],x.i]),l.Rb(4608,x.t,x.t,[]),l.Rb(4608,x.w,x.w,[]),l.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),l.Rb(1073742336,m.b,m.b,[]),l.Rb(1073742336,g.n,g.n,[]),l.Rb(1073742336,g.l,g.l,[]),l.Rb(1073742336,T.a,T.a,[]),l.Rb(1073742336,k.a,k.a,[]),l.Rb(1073742336,g.e,g.e,[]),l.Rb(1073742336,S.a,S.a,[]),l.Rb(1073742336,I.i,I.i,[]),l.Rb(1073742336,x.b,x.b,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,w.d,w.d,[]),l.Rb(1073742336,f.e,f.e,[]),l.Rb(1073742336,M.b,M.b,[]),l.Rb(1073742336,L.b,L.b,[]),l.Rb(1073742336,y.c,y.c,[]),l.Rb(1073742336,A.a,A.a,[]),l.Rb(1073742336,J.d,J.d,[]),l.Rb(1073742336,P.c,P.c,[]),l.Rb(1073742336,D.a,D.a,[]),l.Rb(1073742336,E.a,E.a,[[2,y.g],l.O]),l.Rb(1073742336,j.b,j.b,[]),l.Rb(1073742336,_.a,_.a,[]),l.Rb(1073742336,O.b,O.b,[]),l.Rb(1073742336,o.Tb,o.Tb,[]),l.Rb(1073742336,u,u,[]),l.Rb(256,w.n,"XSRF-TOKEN",[]),l.Rb(256,w.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,I.m,void 0,[]),l.Rb(256,I.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,a.i,function(){return[[{path:"",component:s}]]},[])])}),W=[[".field-selector{margin-bottom:20px}.input-group{margin-bottom:15px}label{display:block;margin-bottom:5px;font-weight:700}button{background-color:#3498db;color:#fff;border:none;padding:10px 15px;border-radius:4px;cursor:pointer;font-size:14px;transition:background-color .3s}button:hover{background-color:#2980b9}.modal{display:none;position:fixed;z-index:1000!important;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,.4)!important}.modal-content{background-color:#d6e3fe;margin:5% auto;padding:20px;border:1px solid #888;width:80%;max-width:1200px;max-height:80vh;overflow-y:auto;border-radius:8px;box-shadow:0 4px 6px rgba(0,0,0,.1)}.modal-content2{background-color:#d6e3fe;margin:5% auto;padding:20px;border:1px solid #888;width:80%;max-width:1200px;max-height:100vh;overflow-y:hidden;border-radius:8px;box-shadow:0 4px 6px rgba(0,0,0,.1)}.close{color:#000;font-size:15px;font-weight:700;cursor:pointer;position:absolute;top:10px;right:10px}.close:focus,.close:hover{color:#000;text-decoration:none;cursor:pointer}.keyword{display:inline-block;background-color:#e1f5fe;border:1px solid #81d4fa;border-radius:4px;padding:2px 5px;margin:0 2px;font-size:.9em;cursor:pointer}.keyword:hover{background-color:#b3e5fc}.keyword-remove{color:#e74c3c;margin-left:5px;font-weight:700}#subjectLineContainer:focus{border:1px solid #7f9db9;outline:#49b9ff solid 1px}.input-content{border:1px solid #7f9db9;width:300px;font-size:11px;height:23px;line-height:23px;cursor:text;color:#000}.input-group-2{display:flex;align-items:center;margin-bottom:10px}.input-group-2 label{width:200px;margin-right:10px;font-size:11px;color:#000;height:23px;line-height:22px;font-family:verdana,helvetica;vertical-align:bottom;pointer-events:auto!important}.input-group-2 .input-content{flex-grow:1}"]],N=l.Hb({encapsulation:2,styles:W,data:{}});function q(t){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{loadingImage:0}),l.Zb(402653184,3,{templateIdLabel:0}),l.Zb(402653184,4,{templateIdTxtInput:0}),l.Zb(402653184,5,{descriptionLabel:0}),l.Zb(402653184,6,{descriptionTxtInput:0}),l.Zb(402653184,7,{subjectContentLabel:0}),l.Zb(402653184,8,{bodyContentLabel:0}),l.Zb(402653184,9,{saveButton:0}),l.Zb(402653184,10,{cancelButton:0}),l.Zb(402653184,11,{keywordsComboLabel:0}),l.Zb(402653184,12,{keywordsCombo:0}),(t()(),l.Jb(12,0,null,null,110,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var l=!0,i=t.component;"creationComplete"===e&&(l=!1!==i.onLoad()&&l);return l},b.ad,b.hb)),l.Ib(13,4440064,null,0,o.yb,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(14,0,null,0,77,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,b.od,b.vb)),l.Ib(15,4440064,null,0,o.ec,[l.r,o.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(t()(),l.Jb(16,0,null,0,59,"Grid",[["height","94%"],["paddingLeft","5"],["width","100%"]],null,null,null,b.Cc,b.H)),l.Ib(17,4440064,null,0,o.z,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),l.Jb(18,0,null,0,11,"GridRow",[["height","28"],["width","100%"]],null,null,null,b.Bc,b.J)),l.Ib(19,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(20,0,null,0,9,"GridItem",[["width","300"]],null,null,null,b.Ac,b.I)),l.Ib(21,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(22,0,null,0,3,"GridItem",[["width","120"]],null,null,null,b.Ac,b.I)),l.Ib(23,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(24,0,null,0,1,"SwtLabel",[["id","templateIdLabel"]],null,null,null,b.Yc,b.fb)),l.Ib(25,4440064,[[3,4],["templateIdLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"]},null),(t()(),l.Jb(26,0,null,0,3,"GridItem",[],null,null,null,b.Ac,b.I)),l.Ib(27,4440064,null,0,o.A,[l.r,o.i],null,null),(t()(),l.Jb(28,0,null,0,1,"SwtTextInput",[["editable","true"],["id","templateIdTxtInput"],["textAlign","left"],["width","200"]],null,null,null,b.kd,b.sb)),l.Ib(29,4440064,[[4,4],["templateIdTxtInput",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],editable:[3,"editable"]},null),(t()(),l.Jb(30,0,null,0,11,"GridRow",[["height","28"],["width","100%"]],null,null,null,b.Bc,b.J)),l.Ib(31,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(32,0,null,0,9,"GridItem",[["width","300"]],null,null,null,b.Ac,b.I)),l.Ib(33,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(34,0,null,0,3,"GridItem",[["width","120"]],null,null,null,b.Ac,b.I)),l.Ib(35,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(36,0,null,0,1,"SwtLabel",[["id","descriptionLabel"]],null,null,null,b.Yc,b.fb)),l.Ib(37,4440064,[[5,4],["descriptionLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"]},null),(t()(),l.Jb(38,0,null,0,3,"GridItem",[],null,null,null,b.Ac,b.I)),l.Ib(39,4440064,null,0,o.A,[l.r,o.i],null,null),(t()(),l.Jb(40,0,null,0,1,"SwtTextInput",[["editable","true"],["id","descriptionTxtInput"],["textAlign","left"],["width","200"]],null,null,null,b.kd,b.sb)),l.Ib(41,4440064,[[6,4],["descriptionTxtInput",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],editable:[3,"editable"]},null),(t()(),l.Jb(42,0,null,0,10,"GridRow",[["height","28"],["width","100%"]],null,null,null,b.Bc,b.J)),l.Ib(43,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(44,0,null,0,8,"GridItem",[["height","100%"],["width","100%"]],null,null,null,b.Ac,b.I)),l.Ib(45,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(46,0,null,0,3,"GridItem",[["width","120"]],null,null,null,b.Ac,b.I)),l.Ib(47,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(48,0,null,0,1,"SwtLabel",[["id","subjectContentLabel"]],null,null,null,b.Yc,b.fb)),l.Ib(49,4440064,[[7,4],["subjectContentLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"]},null),(t()(),l.Jb(50,0,null,0,2,"GridItem",[["height","100%"],["width","100%"]],null,null,null,b.Ac,b.I)),l.Ib(51,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(52,0,[["subjectContent",1]],0,0,"textarea",[["id","subjectContent"]],null,null,null,null,null)),(t()(),l.Jb(53,0,null,0,10,"GridRow",[["height","100%"],["paddingTop","5"],["width","100%"]],null,null,null,b.Bc,b.J)),l.Ib(54,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),l.Jb(55,0,null,0,8,"GridItem",[["height","100%"],["width","100%"]],null,null,null,b.Ac,b.I)),l.Ib(56,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(57,0,null,0,3,"GridItem",[["width","120"]],null,null,null,b.Ac,b.I)),l.Ib(58,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(59,0,null,0,1,"SwtLabel",[["id","bodyContentLabel"]],null,null,null,b.Yc,b.fb)),l.Ib(60,4440064,[[8,4],["bodyContentLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"]},null),(t()(),l.Jb(61,0,null,0,2,"GridItem",[["height","100%"],["width","100%"]],null,null,null,b.Ac,b.I)),l.Ib(62,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(63,0,[["emailContent",1]],0,0,"textarea",[["id","emailContent"]],null,null,null,null,null)),(t()(),l.Jb(64,0,null,0,11,"GridRow",[["height","28"],["paddingTop","2"],["width","100%"]],null,null,null,b.Bc,b.J)),l.Ib(65,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),l.Jb(66,0,null,0,9,"GridItem",[["width","300"]],null,null,null,b.Ac,b.I)),l.Ib(67,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(68,0,null,0,3,"GridItem",[["width","120"]],null,null,null,b.Ac,b.I)),l.Ib(69,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(70,0,null,0,1,"SwtLabel",[["id","keywordsComboLabel"]],null,null,null,b.Yc,b.fb)),l.Ib(71,4440064,[[11,4],["keywordsComboLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"]},null),(t()(),l.Jb(72,0,null,0,3,"GridItem",[],null,null,null,b.Ac,b.I)),l.Ib(73,4440064,null,0,o.A,[l.r,o.i],null,null),(t()(),l.Jb(74,0,null,0,1,"SwtComboBox",[["dataLabel","keywords"],["enabled","true"],["id","keywordsCombo"],["prompt","Select Specific Keyword to add"],["shiftUp","100"],["width","300"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,n){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==l.Tb(t,75).mouseWeelEventHandler(n.target)&&i);"change"===e&&(i=!1!==o.changeKeywords()&&i);return i},b.Pc,b.W)),l.Ib(75,4440064,[[12,4],["keywordsCombo",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],prompt:[1,"prompt"],width:[2,"width"],id:[3,"id"],enabled:[4,"enabled"],shiftUp:[5,"shiftUp"]},{change_:"change"}),(t()(),l.Jb(76,0,null,0,15,"SwtCanvas",[["height","35"],["width","100%"]],null,null,null,b.Nc,b.U)),l.Ib(77,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(78,0,null,0,13,"HBox",[["width","100%"]],null,null,null,b.Dc,b.K)),l.Ib(79,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(80,0,null,0,5,"HBox",[["paddingLeft","5"],["width","90%"]],null,null,null,b.Dc,b.K)),l.Ib(81,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(82,0,null,0,1,"SwtButton",[["id","saveButton"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.saveHandler()&&l);return l},b.Mc,b.T)),l.Ib(83,4440064,[[9,4],["saveButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(84,0,null,0,1,"SwtButton",[["id","cancelButton"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.popupClosed()&&l);return l},b.Mc,b.T)),l.Ib(85,4440064,[[10,4],["cancelButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(86,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingLeft","5"],["width","10%"]],null,null,null,b.Dc,b.K)),l.Ib(87,4440064,null,0,o.C,[l.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(t()(),l.Jb(88,0,null,0,1,"SwtButton",[["id","previewEmailTemplateButton"],["label","Preview"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.showPreviewDialog()&&l);return l},b.Mc,b.T)),l.Ib(89,4440064,[["previewEmailTemplateButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],label:[1,"label"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(90,0,null,0,1,"SwtLoadingImage",[],null,null,null,b.Zc,b.gb)),l.Ib(91,114688,[[2,4],["loadingImage",4]],0,o.xb,[l.r],null,null),(t()(),l.Jb(92,0,null,0,12,"div",[["class","modal"],["id","previewDialog"]],null,null,null,null,null)),(t()(),l.Jb(93,0,null,null,11,"div",[["class","modal-content"]],null,null,null,null,null)),(t()(),l.Jb(94,0,null,null,1,"span",[["class","close"]],null,null,null,null,null)),(t()(),l.bc(-1,null,["\xd7"])),(t()(),l.Jb(96,0,null,null,2,"SwtFieldSet",[["id","fieldSet1"],["legendText","Enter Field Values"],["style","height: 100%; width: 100%; color:blue;"]],null,null,null,b.Vc,b.cb)),l.Ib(97,4440064,[["fieldSet1",4]],0,o.ob,[l.r,o.i],{id:[0,"id"],legendText:[1,"legendText"]},null),(t()(),l.Jb(98,0,null,0,0,"div",[["id","fieldInputs"],["style","padding-top: 20px;"]],null,null,null,null,null)),(t()(),l.Jb(99,0,null,null,5,"SwtCanvas",[["height","35"],["style","padding-top: 10px;"],["width","100%"]],null,null,null,b.Nc,b.U)),l.Ib(100,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(101,0,null,0,1,"SwtButton",[["id","previewEmail"],["label","Generate"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.generatePreview()&&l);return l},b.Mc,b.T)),l.Ib(102,4440064,[["previewEmail",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],label:[1,"label"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(103,0,null,0,1,"SwtButton",[["id","closePopup2"],["label","Close"],["style","padding-left:10px"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.closePopup("previewDialog")&&l);return l},b.Mc,b.T)),l.Ib(104,4440064,[["closePopup2",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],label:[1,"label"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(105,0,null,0,17,"div",[["class","modal"],["id","previewModal"]],null,null,null,null,null)),(t()(),l.Jb(106,0,null,null,16,"div",[["class","modal-content2"]],null,null,null,null,null)),(t()(),l.Jb(107,0,null,null,1,"span",[["class","close"]],null,null,null,null,null)),(t()(),l.bc(-1,null,["\xd7"])),(t()(),l.Jb(109,0,null,null,9,"SwtFieldSet",[["id","fieldSet2"],["legendText","Preview"],["style","max-height: 80vh;height: 100%; width: 100%; color:blue;"]],null,null,null,b.Vc,b.cb)),l.Ib(110,4440064,[["fieldSet1",4]],0,o.ob,[l.r,o.i],{id:[0,"id"],legendText:[1,"legendText"]},null),(t()(),l.Jb(111,0,null,0,7,"div",[["style","margin-top: 15px; margin-bottom: 5px; max-width: 1200px;\n            max-height: 75vh; \n            overflow-y: auto; "]],null,null,null,null,null)),(t()(),l.Jb(112,0,null,null,3,"div",[["id","aaazz"]],null,null,null,null,null)),(t()(),l.Jb(113,0,null,null,1,"strong",[],null,null,null,null,null)),(t()(),l.bc(-1,null,["Subject:"])),(t()(),l.Jb(115,0,null,null,0,"div",[["id","previewSubject"],["style","color:black;"]],null,null,null,null,null)),(t()(),l.Jb(116,0,null,null,1,"strong",[],null,null,null,null,null)),(t()(),l.bc(-1,null,["Body:"])),(t()(),l.Jb(118,0,null,null,0,"div",[["id","previewContent"],["style","color:black;"]],null,null,null,null,null)),(t()(),l.Jb(119,0,null,null,3,"SwtCanvas",[["height","35"],["style","margin-top: 10px;"],["width","100%"]],null,null,null,b.Nc,b.U)),l.Ib(120,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(121,0,null,0,1,"SwtButton",[["id","closePopup2"],["label","Close"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.closePopup("previewModal")&&l);return l},b.Mc,b.T)),l.Ib(122,4440064,[["closePopup2",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],label:[1,"label"],buttonMode:[2,"buttonMode"]},{onClick_:"click"})],function(t,e){t(e,13,0,"100%","100%");t(e,15,0,"100%","100%","5","5","5");t(e,17,0,"100%","94%","5");t(e,19,0,"100%","28");t(e,21,0,"300");t(e,23,0,"120");t(e,25,0,"templateIdLabel"),t(e,27,0);t(e,29,0,"templateIdTxtInput","left","200","true");t(e,31,0,"100%","28");t(e,33,0,"300");t(e,35,0,"120");t(e,37,0,"descriptionLabel"),t(e,39,0);t(e,41,0,"descriptionTxtInput","left","200","true");t(e,43,0,"100%","28");t(e,45,0,"100%","100%");t(e,47,0,"120");t(e,49,0,"subjectContentLabel");t(e,51,0,"100%","100%");t(e,54,0,"100%","100%","5");t(e,56,0,"100%","100%");t(e,58,0,"120");t(e,60,0,"bodyContentLabel");t(e,62,0,"100%","100%");t(e,65,0,"100%","28","2");t(e,67,0,"300");t(e,69,0,"120");t(e,71,0,"keywordsComboLabel"),t(e,73,0);t(e,75,0,"keywords","Select Specific Keyword to add","300","keywordsCombo","true","100");t(e,77,0,"100%","35");t(e,79,0,"100%");t(e,81,0,"90%","5");t(e,83,0,"saveButton",!0);t(e,85,0,"cancelButton",!0);t(e,87,0,"right","10%","5");t(e,89,0,"previewEmailTemplateButton","Preview",!0),t(e,91,0);t(e,97,0,"fieldSet1","Enter Field Values");t(e,100,0,"100%","35");t(e,102,0,"previewEmail","Generate",!0);t(e,104,0,"closePopup2","Close",!0);t(e,110,0,"fieldSet2","Preview");t(e,120,0,"100%","35");t(e,122,0,"closePopup2","Close",!0)},null)}function G(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-emailtemplate-add",[],null,null,null,q,N)),l.Ib(1,4440064,null,0,s,[o.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var F=l.Fb("app-emailtemplate-add",s,G,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);