(window.webpackJsonp=window.webpackJsonp||[]).push([[84],{fyPu:function(t,e,n){"use strict";n.r(e);var i=n("CcnG"),o=n("mrSG"),s=n("447K"),l=n("ZYCi"),a=function(t){function e(e,n){var i=t.call(this,n,e)||this;return i.commonService=e,i.element=n,i.jsonReader=new s.L,i.inputData=new s.G(i.commonService),i.sendData=new s.G(i.commonService),i.requestParams=[],i.baseURL=s.Wb.getBaseURL(),i.invalidComms="",i.menuAccessId=null,i.templateId=null,i.userId=null,i.fourEyesRequired=!0,i.screenName="Forecast Assumption Screen",i.versionNumber="1.1.0001",i.releaseDate="20 August 2020",i.screenVersion=new s.V(i.commonService),i.menuAccessIdParent=0,i.currencyFormat="",Window.Main=i,i.swtAlert=new s.bb(e),i}return o.d(e,t),e.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this,this.assumptionGrid=this.forecastMonitorAssumptionsCanvas.addChild(s.hb),this.addButton.label=s.Wb.getPredictMessage("button.forecastMonitor.add",null),this.btnChange.label=s.Wb.getPredictMessage("button.forecastMonitor.change",null),this.btnDelete.label=s.Wb.getPredictMessage("button.forecastMonitor.delete",null),this.closeButton.label=s.Wb.getPredictMessage("button.forecastMonitor.close",null),this.addButton.toolTip=s.Wb.getPredictMessage("tooltip.forecastMonitor.add",null),this.btnChange.toolTip=s.Wb.getPredictMessage("tooltip.forecastMonitor.change",null),this.btnDelete.toolTip=s.Wb.getPredictMessage("tooltip.forecastMonitor.delete",null),this.closeButton.toolTip=s.Wb.getPredictMessage("tooltip.forecastMonitor.close",null),this.lostConnectionText.text=s.Wb.getPredictMessage("screen.connectionError",null),this.templateIdLabel.text=s.Wb.getPredictMessage("label.forecastMonitor.templateId",null),this.assumptionGrid.onFilterChanged=this.disableButtons.bind(this),this.assumptionGrid.onSortChanged=this.disableButtons.bind(this)},e.prototype.onLoad=function(){var t=this;try{this.requestParams=[],this.initializeMenus(),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.btnChange.enabled=!1,this.btnDelete.enabled=!1,this.requestParams["forecastAssumption.currencyCode"]=s.x.call("eval","currencyCode"),this.requestParams["forecastAssumption.entityId"]=s.x.call("eval","entityId"),this.requestParams["forecastAssumption.valueDateAsString"]=s.x.call("eval","date"),this.requestParams["forecastAssumption.templateId"]=s.x.call("eval","templateId"),this.lblTemplateId.text=s.x.call("eval","templateId"),this.sendData.cbStart=this.startOfComms.bind(this),this.sendData.cbStop=this.endOfComms.bind(this),this.sendData.cbResult=function(e){t.sendDataResult(e)},this.sendData.cbFault=this.sendDataFault.bind(this),this.sendData.encodeURL=!1,this.menuAccessIdParent=s.x.call("eval","menuAccessIdParent"),1==this.menuAccessIdParent&&(this.btnDelete.enabled=!1),this.actionMethod="method=displayAssumption",this.actionPath="forecastMonitor.do?",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.url=this.baseURL+this.actionPath+"method=deleteForecastAssumption",this.inputData.send(this.requestParams),this.assumptionGrid.onRowClick=function(e){t.onGridCellClick(e)},s.v.subscribe(function(e){t.report(e)})}catch(e){console.log("errr",e)}},e.prototype.sendDataFault=function(t){this.lostConnectionText.visible=!0,this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail},e.prototype.inputDataResult=function(t){try{if(this.inputData&&this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.exportContainer.enabled=!0,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lostConnectionText.visible=!1,this.jsonReader.getRequestReplyStatus()){if(this.currencyFormat=this.jsonReader.getScreenAttributes().currencyFormat,this.lastRecievedJSON!=this.prevRecievedJSON){if(!this.jsonReader.isDataBuilding()){var e={columns:this.jsonReader.getColumnData()};this.assumptionGrid.CustomGrid(e),this.jsonReader.getGridData().size>0?(this.assumptionGrid.gridData=this.jsonReader.getGridData(),this.assumptionGrid.setRowSize=this.jsonReader.getRowSize(),this.assumptionGrid.rowHeight=22):(this.assumptionGrid.dataProvider=[],this.assumptionGrid.selectedIndex=-1)}this.jsonReader.getRowSize()<1?this.exportContainer.enabled=!1:this.exportContainer.enabled=!0}this.prevRecievedJSON=this.lastRecievedJSON}}catch(n){console.log("error in inputData",n)}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.updateData=function(){this.requestParams=[],this.requestParams["forecastAssumption.currencyCode"]=s.x.call("eval","currencyCode"),this.requestParams["forecastAssumption.entityId"]=s.x.call("eval","entityId"),this.requestParams["forecastAssumption.valueDateAsString"]=s.x.call("eval","date"),this.requestParams["forecastAssumption.templateId"]=s.x.call("eval","templateId"),this.inputData.send(this.requestParams)},e.prototype.inputDataFault=function(t){this.lostConnectionText.visible=!0,this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate);var t=new s.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=s.Eb.createPopUp(this,s.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e.prototype.sendDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&(this.updateData(),s.x.call("refreshParent")))},e.prototype.connError=function(t){this.swtAlert.error(""+this.invalidComms,s.Wb.getPredictMessage("screen.error",null))},e.prototype.closeHandler=function(){s.x.call("close")},e.prototype.doHelp=function(){s.x.call("help")},e.prototype.onGridCellClick=function(t){try{this.assumptionGrid.selectedIndices.length>0?(this.btnChange.enabled=!0,this.btnDelete.enabled=!0):(this.btnChange.enabled=!1,this.btnDelete.enabled=!1),1==this.menuAccessIdParent&&(this.btnDelete.enabled=!1)}catch(e){console.log("error event onGridCellClick",e)}},e.prototype.deleteAssumption=function(){this.swtAlert.confirm(s.Wb.getPredictMessage("alert.assumption.deleteConfirm",null),"Delete",s.c.OK|s.c.CANCEL,this,this.alertListener.bind(this),null)},e.prototype.alertListener=function(t){if(t.detail==s.c.OK){this.btnDelete.enabled=!1,this.btnChange.enabled=!1,this.requestParams=[],this.actionMethod="method=deleteForecastAssumption";this.requestParams["forecastAssumption.currencyCode"]=String(this.assumptionGrid.selectedItem.currency.content),this.requestParams["forecastAssumption.entityId"]=String(this.assumptionGrid.selectedItem.entity.content),this.requestParams["forecastAssumption.assumptionId"]=String(this.assumptionGrid.selectedItem.id.content),this.assumptionGrid.selectedItem&&this.assumptionGrid.selectedItem.id&&this.assumptionGrid.selectedItem.id.content?this.requestParams["forecastAssumption.assumptionId"]=String(this.assumptionGrid.selectedItem.id.content):this.requestParams["forecastAssumption.assumptionId"]="",this.sendData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.send(this.requestParams)}},e.prototype.assumptionClick=function(t){var e=new Object;e.entity=s.x.call("eval","entityId"),e.currency=s.x.call("eval","currencyCode"),e.date=s.x.call("eval","date"),e.currencyName=s.x.call("eval","currencyName"),e.templateId=this.lblTemplateId.text,"add"==t||"change"==t&&(e.amount=String(this.assumptionGrid.selectedItem.amount.content),e.assumption=String(this.assumptionGrid.selectedItem.assumption.content),e.date=String(this.assumptionGrid.selectedItem.date.content),e.currency=String(this.assumptionGrid.selectedItem.currency.content),e.entity=String(this.assumptionGrid.selectedItem.entity.content),this.assumptionGrid.selectedItem&&this.assumptionGrid.selectedItem.id&&this.assumptionGrid.selectedItem.id.content?e.assumptionId=String(this.assumptionGrid.selectedItem.id.content):e.assumptionId="");s.x.call("openModifyAssumptionWindow",e)},e.prototype.disableOrEnableButtons=function(t){t?(this.btnChange.enabled=!0,this.btnDelete.enabled=!("*DEFAULT*"==this.templateId&&"*DEFAULT*"==this.userId)):(this.btnChange.enabled=!1,this.btnDelete.enabled=!1)},e.prototype.disableButtons=function(){-1==this.assumptionGrid.selectedIndex?(this.btnChange.enabled=!1,this.btnDelete.enabled=!1):(this.btnChange.enabled=!0,this.btnDelete.enabled=!0)},e.prototype.report=function(t){var e=[];e.push("Template ID="+this.lblTemplateId.text),this.exportContainer.convertData(this.lastRecievedJSON.assumption.grid.metadata.columns,this.assumptionGrid,null,e,t,!1)},e}(s.yb),r=[{path:"",component:a}],d=(l.l.forChild(r),function(){return function(){}}()),u=n("pMnS"),c=n("RChO"),h=n("t6HQ"),b=n("WFGK"),p=n("5FqG"),m=n("Ip0R"),g=n("gIcY"),f=n("t/Na"),R=n("sE5F"),C=n("OzfB"),w=n("T7CS"),I=n("S7LP"),v=n("6aHO"),S=n("WzUx"),y=n("A7o+"),D=n("zCE2"),x=n("Jg5P"),k=n("3R0m"),A=n("hhbb"),M=n("5rxC"),P=n("Fzqc"),T=n("21Lb"),G=n("hUWP"),J=n("3pJQ"),B=n("V9q+"),O=n("VDKW"),N=n("kXfT"),L=n("BGbe");n.d(e,"ForecastMonitorAssumptionsModuleNgFactory",function(){return F}),n.d(e,"RenderType_ForecastMonitorAssumptions",function(){return q}),n.d(e,"View_ForecastMonitorAssumptions_0",function(){return _}),n.d(e,"View_ForecastMonitorAssumptions_Host_0",function(){return E}),n.d(e,"ForecastMonitorAssumptionsNgFactory",function(){return z});var F=i.Gb(d,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[u.a,c.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,z]],[3,i.n],i.J]),i.Rb(4608,m.m,m.l,[i.F,[2,m.u]]),i.Rb(4608,g.c,g.c,[]),i.Rb(4608,g.p,g.p,[]),i.Rb(4608,f.j,f.p,[m.c,i.O,f.n]),i.Rb(4608,f.q,f.q,[f.j,f.o]),i.Rb(5120,f.a,function(t){return[t,new s.tb]},[f.q]),i.Rb(4608,f.m,f.m,[]),i.Rb(6144,f.k,null,[f.m]),i.Rb(4608,f.i,f.i,[f.k]),i.Rb(6144,f.b,null,[f.i]),i.Rb(4608,f.f,f.l,[f.b,i.B]),i.Rb(4608,f.c,f.c,[f.f]),i.Rb(4608,R.c,R.c,[]),i.Rb(4608,R.g,R.b,[]),i.Rb(5120,R.i,R.j,[]),i.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),i.Rb(4608,R.f,R.a,[]),i.Rb(5120,R.d,R.k,[R.h,R.f]),i.Rb(5120,i.b,function(t,e){return[C.j(t,e)]},[m.c,i.O]),i.Rb(4608,w.a,w.a,[]),i.Rb(4608,I.a,I.a,[]),i.Rb(4608,v.a,v.a,[i.n,i.L,i.B,I.a,i.g]),i.Rb(4608,S.c,S.c,[i.n,i.g,i.B]),i.Rb(4608,S.e,S.e,[S.c]),i.Rb(4608,y.l,y.l,[]),i.Rb(4608,y.h,y.g,[]),i.Rb(4608,y.c,y.f,[]),i.Rb(4608,y.j,y.d,[]),i.Rb(4608,y.b,y.a,[]),i.Rb(4608,y.k,y.k,[y.l,y.h,y.c,y.j,y.b,y.m,y.n]),i.Rb(4608,S.i,S.i,[[2,y.k]]),i.Rb(4608,S.r,S.r,[S.L,[2,y.k],S.i]),i.Rb(4608,S.t,S.t,[]),i.Rb(4608,S.w,S.w,[]),i.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),i.Rb(1073742336,m.b,m.b,[]),i.Rb(1073742336,g.n,g.n,[]),i.Rb(1073742336,g.l,g.l,[]),i.Rb(1073742336,D.a,D.a,[]),i.Rb(1073742336,x.a,x.a,[]),i.Rb(1073742336,g.e,g.e,[]),i.Rb(1073742336,k.a,k.a,[]),i.Rb(1073742336,y.i,y.i,[]),i.Rb(1073742336,S.b,S.b,[]),i.Rb(1073742336,f.e,f.e,[]),i.Rb(1073742336,f.d,f.d,[]),i.Rb(1073742336,R.e,R.e,[]),i.Rb(1073742336,A.b,A.b,[]),i.Rb(1073742336,M.b,M.b,[]),i.Rb(1073742336,C.c,C.c,[]),i.Rb(1073742336,P.a,P.a,[]),i.Rb(1073742336,T.d,T.d,[]),i.Rb(1073742336,G.c,G.c,[]),i.Rb(1073742336,J.a,J.a,[]),i.Rb(1073742336,B.a,B.a,[[2,C.g],i.O]),i.Rb(1073742336,O.b,O.b,[]),i.Rb(1073742336,N.a,N.a,[]),i.Rb(1073742336,L.b,L.b,[]),i.Rb(1073742336,s.Tb,s.Tb,[]),i.Rb(1073742336,d,d,[]),i.Rb(256,f.n,"XSRF-TOKEN",[]),i.Rb(256,f.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,y.m,void 0,[]),i.Rb(256,y.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,l.i,function(){return[[{path:"",component:a}]]},[])])}),W=[[""]],q=i.Hb({encapsulation:0,styles:W,data:{}});function _(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{forecastMonitorAssumptionsCanvas:0}),i.Zb(402653184,3,{addButton:0}),i.Zb(402653184,4,{btnChange:0}),i.Zb(402653184,5,{btnDelete:0}),i.Zb(402653184,6,{closeButton:0}),i.Zb(402653184,7,{loadingImage:0}),i.Zb(402653184,8,{exportContainer:0}),i.Zb(402653184,9,{helpIcon:0}),i.Zb(402653184,10,{templateIdLabel:0}),i.Zb(402653184,11,{lblTemplateId:0}),i.Zb(402653184,12,{lostConnectionText:0}),(t()(),i.Jb(12,0,null,null,41,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var i=!0,o=t.component;"creationComplete"===e&&(i=!1!==o.onLoad()&&i);return i},p.ad,p.hb)),i.Ib(13,4440064,null,0,s.yb,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(14,0,null,0,39,"VBox",[["height","100%"],["paddingBottom","10"],["paddingLeft","10"],["paddingRight","10"],["paddingTop","10"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(15,4440064,null,0,s.ec,[i.r,s.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),i.Jb(16,0,null,0,11,"SwtCanvas",[["height","10%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(17,4440064,null,0,s.db,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(18,0,null,0,9,"Grid",[["height","100%"],["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(19,4440064,null,0,s.z,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(20,0,null,0,7,"GridRow",[["height","100%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(21,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(22,0,null,0,5,"GridItem",[["height","100%"],["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(23,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(24,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["paddingTop","-4"]],null,null,null,p.Yc,p.fb)),i.Ib(25,4440064,[[10,4],["templateIdLabel",4]],0,s.vb,[i.r,s.i],{paddingTop:[0,"paddingTop"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(26,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblTemplateId"],["paddingTop","-4"]],null,null,null,p.Yc,p.fb)),i.Ib(27,4440064,[[11,4],["lblTemplateId",4]],0,s.vb,[i.r,s.i],{id:[0,"id"],paddingTop:[1,"paddingTop"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(28,0,null,0,1,"SwtCanvas",[["height","80%"],["id","forecastMonitorAssumptionsCanvas"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(29,4440064,[[2,4],["forecastMonitorAssumptionsCanvas",4]],0,s.db,[i.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(30,0,null,0,23,"SwtCanvas",[["height","9%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(31,4440064,null,0,s.db,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(32,0,null,0,21,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(33,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"]},null),(t()(),i.Jb(34,0,null,0,9,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(35,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(36,0,null,0,1,"SwtButton",[["id","addButton"],["width","70"]],null,[[null,"click"]],function(t,e,n){var i=!0,o=t.component;"click"===e&&(i=!1!==o.assumptionClick("add")&&i);return i},p.Mc,p.T)),i.Ib(37,4440064,[[3,4],["addButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(38,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"],["width","70"]],null,[[null,"click"]],function(t,e,n){var i=!0,o=t.component;"click"===e&&(i=!1!==o.assumptionClick("change")&&i);return i},p.Mc,p.T)),i.Ib(39,4440064,[[4,4],["changeButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(40,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"],["width","70"]],null,[[null,"click"]],function(t,e,n){var i=!0,o=t.component;"click"===e&&(i=!1!==o.deleteAssumption()&&i);return i},p.Mc,p.T)),i.Ib(41,4440064,[[5,4],["deleteButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(42,0,null,0,1,"SwtButton",[["id","closeButton"],["width","70"]],null,[[null,"click"]],function(t,e,n){var i=!0,o=t.component;"click"===e&&(i=!1!==o.closeHandler()&&i);return i},p.Mc,p.T)),i.Ib(43,4440064,[[6,4],["closeButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),i.Jb(44,0,null,0,9,"HBox",[["horizontalAlign","right"],["top","3"]],null,null,null,p.Dc,p.K)),i.Ib(45,4440064,null,0,s.C,[i.r,s.i],{top:[0,"top"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),i.Jb(46,0,null,0,1,"SwtText",[["color","red"],["fontWeight","bold"],["id","lostConnectionText"],["paddingTop","1"],["visible","false"]],null,[[null,"click"]],function(t,e,n){var i=!0,o=t.component;"click"===e&&(i=!1!==o.connError(n)&&i);return i},p.ld,p.qb)),i.Ib(47,4440064,[[12,4],["lostConnectionText",4]],0,s.Pb,[i.r,s.i],{id:[0,"id"],visible:[1,"visible"],paddingTop:[2,"paddingTop"],fontWeight:[3,"fontWeight"],color:[4,"color"]},{onClick_:"click"}),(t()(),i.Jb(48,0,null,0,1,"DataExport",[["id","dataExport"]],null,null,null,p.Sc,p.Z)),i.Ib(49,4440064,[[8,4],["dataExport",4]],0,s.kb,[s.i,i.r],{id:[0,"id"]},null),(t()(),i.Jb(50,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,n){var i=!0,o=t.component;"click"===e&&(i=!1!==o.doHelp()&&i);return i},p.Wc,p.db)),i.Ib(51,4440064,[[9,4],["helpIcon",4]],0,s.rb,[i.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(52,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),i.Ib(53,114688,[[7,4],["loadingImage",4]],0,s.xb,[i.r],null,null)],function(t,e){t(e,13,0,"100%","100%");t(e,15,0,"100%","100%","10","10","10","10");t(e,17,0,"100%","10%");t(e,19,0,"100%","100%");t(e,21,0,"100%","100%");t(e,23,0,"100%","100%");t(e,25,0,"-4","bold");t(e,27,0,"lblTemplateId","-4","bold");t(e,29,0,"forecastMonitorAssumptionsCanvas","100%","80%");t(e,31,0,"100%","9%");t(e,33,0,"100%");t(e,35,0,"100%","5");t(e,37,0,"addButton","70",!0);t(e,39,0,"changeButton","70","false");t(e,41,0,"deleteButton","70","false");t(e,43,0,"closeButton","70");t(e,45,0,"3","right");t(e,47,0,"lostConnectionText","false","1","bold","red");t(e,49,0,"dataExport");t(e,51,0,"helpIcon"),t(e,53,0)},null)}function E(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-forcast-monitor-assumptions",[],null,null,null,_,q)),i.Ib(1,4440064,null,0,a,[s.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var z=i.Fb("app-forcast-monitor-assumptions",a,E,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);