(window.webpackJsonp=window.webpackJsonp||[]).push([[106],{M2ga:function(t,o,e){"use strict";e.r(o);var n=e("CcnG"),l=e("mrSG"),i=e("447K"),a=e("ZYCi"),r=function(t){function o(o,e){var n=t.call(this,e,o)||this;return n.commonService=o,n.element=e,n.inputData=new i.G(n.commonService),n.baseURL=i.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.requestParams=[],n.invalidComms="",n.moduleId="PREDICT",n.callerMethod=null,n.errorLocation=0,n.jsonReader=new i.L,n.fromComboBox=!1,n.logger=null,n.logger=new i.R("Logon Screen",n.commonService.httpclient),n.swtAlert=new i.bb(o),n.logger.info("method [constructor] - START/END "),n}return l.d(o,t),o.prototype.ngOnInit=function(){this.authMethodHBox.visible=!1,this.authMethodHBox.includeInLayout=!1,this.messageLabel.text=""},o.prototype.onLoad=function(){var t=this,o=0;try{this.callerMethod=i.x.call("eval","callerMethod"),o=10,this.isLogoutRedirect=i.x.call("eval","isLogoutRedirect"),o=20,this.requestParams=[],this.requestParams.callerMethod=this.callerMethod,this.requestParams.isLogoutRedirect=this.isLogoutRedirect,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(o){t.errorLocation=30,t.inputDataResult(o)},this.errorLocation=40,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="logon.do?",this.actionMethod="method=preLoginScreenData",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,o=50,this.inputData.send(this.requestParams)}catch(e){this.logger.error("method [onLoad] - error: ",e,"errorLocation: ",o),i.Wb.logError(e,i.Wb.PREDICT_MODULE_ID,"LogonScreen.ts","onLoad",o)}},o.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error(this.invalidComms)},o.prototype.inputDataResult=function(t){var o=0;try{this.inputData.isBusy()?this.inputData.cbStop():(this.jsonReader.setInputJSON(t),o=10,this.jsonReader.getRequestReplyStatus()?(o=20,this.comboAuthMethod.setComboData(this.jsonReader.getSelects(),!0),o=30,this.allowInternalAuthentication=this.jsonReader.getSingletons().allowInternalAuthentication,o=40,this.isLogoutRedirect=this.jsonReader.getSingletons().isLogoutRedirect,o=50,this.userStillConnected=this.jsonReader.getSingletons().userStillConnected,o=60,i.Z.isTrue(this.userStillConnected)?(o=70,this.messageLabel.text="Your session is still active, Login Button to proceed"):i.Z.isTrue(this.isLogoutRedirect)?(o=80,i.Z.isTrue(this.allowInternalAuthentication)?(this.messageLabel.text="You have been successfully logged out",this.fromComboBox=!0,this.authMethodHBox.visible=!0,this.authMethodHBox.includeInLayout=!0,this.authMethodHBox.paddingTop="0"):(this.messageLabel.height="50px",this.messageLabel.text="You have been successfully logged out",this.reloginBox.visible=!0,this.reloginBox.includeInLayout=!0,this.reloginBox.paddingTop="0")):i.Z.isTrue(this.allowInternalAuthentication)?(o=100,this.fromComboBox=!0,this.authMethodHBox.visible=!0,this.authMethodHBox.includeInLayout=!0,this.logoutHBox.visible=!1,this.logoutHBox.includeInLayout=!1):(o=90,this.messageLabel.text="Please click on Login Button to proceed")):this.swtAlert.error(i.Wb.getPredictMessage("label.errorContactSystemAdmin",null)+"\n"+this.jsonReader.getRequestReplyMessage(),i.Wb.getPredictMessage("screen.error",null)))}catch(e){this.logger.error("method [inputDataResult] - error: ",e,"errorLocation: ",o),i.Wb.logError(e,i.Wb.PREDICT_MODULE_ID,"LogonScreen.ts","inputDataResult",o)}},o.prototype.loginHandler=function(){var t=0;try{this.fromComboBox?(t=10,"internal"==this.comboAuthMethod.selectedValue?(t=20,i.x.call("doRedirectToLoginPage")):(t=30,i.x.call("doRedirectToMFAPage"))):i.Z.isTrue(this.userStillConnected)?(t=40,i.x.call("doRedirectToLoginPage")):(t=50,i.x.call("doRedirectToMFAPage"))}catch(o){this.logger.error("method [loginHandler] - error: ",o,"errorLocation: ",t),i.Wb.logError(o,i.Wb.PREDICT_MODULE_ID,"LogonScreen.ts","loginHandler",t)}},o.prototype.keyDownEventHandler=function(t){var o=0;try{var e=Object(i.ic.getFocus()).name;o=10,t.keyCode==i.N.ENTER&&(o=20,"loginButton"==e?(o=30,this.loginHandler()):"cancelButton"==e&&(o=40,window.close()))}catch(n){this.logger.error("method [keyDownEventHandler] - error: ",n,"errorLocation: ",o),i.Wb.logError(n,i.Wb.PREDICT_MODULE_ID,"LogonScreen.ts","keyDownEventHandler",o)}},o.prototype.startOfComms=function(){},o.prototype.endOfComms=function(){},o}(i.yb),u=[{path:"",component:r}],d=(a.l.forChild(u),function(){return function(){}}()),h=e("pMnS"),c=e("RChO"),b=e("t6HQ"),s=e("WFGK"),g=e("5FqG"),p=e("Ip0R"),m=e("gIcY"),R=e("t/Na"),w=e("sE5F"),f=e("OzfB"),L=e("T7CS"),x=e("S7LP"),C=e("6aHO"),T=e("WzUx"),v=e("A7o+"),S=e("zCE2"),B=e("Jg5P"),y=e("3R0m"),I=e("hhbb"),A=e("5rxC"),D=e("Fzqc"),M=e("21Lb"),k=e("hUWP"),H=e("3pJQ"),z=e("V9q+"),_=e("VDKW"),J=e("kXfT"),P=e("BGbe");e.d(o,"LogonScreenModuleNgFactory",function(){return E}),e.d(o,"RenderType_LogonScreen",function(){return O}),e.d(o,"View_LogonScreen_0",function(){return W}),e.d(o,"View_LogonScreen_Host_0",function(){return G}),e.d(o,"LogonScreenNgFactory",function(){return U});var E=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[h.a,c.a,b.a,s.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,U]],[3,n.n],n.J]),n.Rb(4608,p.m,p.l,[n.F,[2,p.u]]),n.Rb(4608,m.c,m.c,[]),n.Rb(4608,m.p,m.p,[]),n.Rb(4608,R.j,R.p,[p.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(t){return[t,new i.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,w.c,w.c,[]),n.Rb(4608,w.g,w.b,[]),n.Rb(5120,w.i,w.j,[]),n.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),n.Rb(4608,w.f,w.a,[]),n.Rb(5120,w.d,w.k,[w.h,w.f]),n.Rb(5120,n.b,function(t,o){return[f.j(t,o)]},[p.c,n.O]),n.Rb(4608,L.a,L.a,[]),n.Rb(4608,x.a,x.a,[]),n.Rb(4608,C.a,C.a,[n.n,n.L,n.B,x.a,n.g]),n.Rb(4608,T.c,T.c,[n.n,n.g,n.B]),n.Rb(4608,T.e,T.e,[T.c]),n.Rb(4608,v.l,v.l,[]),n.Rb(4608,v.h,v.g,[]),n.Rb(4608,v.c,v.f,[]),n.Rb(4608,v.j,v.d,[]),n.Rb(4608,v.b,v.a,[]),n.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),n.Rb(4608,T.i,T.i,[[2,v.k]]),n.Rb(4608,T.r,T.r,[T.L,[2,v.k],T.i]),n.Rb(4608,T.t,T.t,[]),n.Rb(4608,T.w,T.w,[]),n.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),n.Rb(1073742336,p.b,p.b,[]),n.Rb(1073742336,m.n,m.n,[]),n.Rb(1073742336,m.l,m.l,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,m.e,m.e,[]),n.Rb(1073742336,y.a,y.a,[]),n.Rb(1073742336,v.i,v.i,[]),n.Rb(1073742336,T.b,T.b,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,R.d,R.d,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,I.b,I.b,[]),n.Rb(1073742336,A.b,A.b,[]),n.Rb(1073742336,f.c,f.c,[]),n.Rb(1073742336,D.a,D.a,[]),n.Rb(1073742336,M.d,M.d,[]),n.Rb(1073742336,k.c,k.c,[]),n.Rb(1073742336,H.a,H.a,[]),n.Rb(1073742336,z.a,z.a,[[2,f.g],n.O]),n.Rb(1073742336,_.b,_.b,[]),n.Rb(1073742336,J.a,J.a,[]),n.Rb(1073742336,P.b,P.b,[]),n.Rb(1073742336,i.Tb,i.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,v.m,void 0,[]),n.Rb(256,v.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,a.i,function(){return[[{path:"",component:r}]]},[])])}),F=[[""]],O=n.Hb({encapsulation:0,styles:F,data:{}});function W(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{comboAuthMethod:0}),n.Zb(402653184,3,{lblAuthMethod:0}),n.Zb(402653184,4,{messageLabel:0}),n.Zb(402653184,5,{unauthorisedLabel:0}),n.Zb(402653184,6,{reloginLabel:0}),n.Zb(402653184,7,{authMethodHBox:0}),n.Zb(402653184,8,{logoutHBox:0}),n.Zb(402653184,9,{reloginBox:0}),(t()(),n.Jb(9,0,null,null,48,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,o,e){var n=!0,l=t.component;"creationComplete"===o&&(n=!1!==l.onLoad()&&n);return n},g.ad,g.hb)),n.Ib(10,4440064,null,0,i.yb,[n.r,i.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(11,0,null,0,46,"VBox",[["backGroundColor","#F6F9FB"],["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(12,4440064,null,0,i.ec,[n.r,i.i,n.T],{backGroundColor:[0,"backGroundColor"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(13,0,null,0,44,"div",[["id","mydiv"],["style","position:absolute;  top: 0;  left: 0;  right: 0;  bottom: 0;  width:400px;  height:250px;  margin:auto; "]],null,null,null,null,null)),(t()(),n.Jb(14,0,null,null,43,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(15,4440064,null,0,i.ec,[n.r,i.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(16,0,null,0,41,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(17,4440064,null,0,i.db,[n.r,i.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(18,0,null,0,39,"VBox",[["height","100%"],["paddingLeft","10"],["paddingRight","10"],["paddingTop","20"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(19,4440064,null,0,i.ec,[n.r,i.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(t()(),n.Jb(20,0,null,0,25,"SwtCanvas",[["backGroundColor","#1F63AA"],["height","100%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(21,4440064,null,0,i.db,[n.r,i.i],{backGroundColor:[0,"backGroundColor"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(22,0,null,0,23,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(23,4440064,null,0,i.ec,[n.r,i.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(24,0,null,0,3,"HBox",[["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(25,4440064,null,0,i.C,[n.r,i.i],{width:[0,"width"]},null),(t()(),n.Jb(26,0,null,0,1,"SwtLabel",[["color","white"],["fontSize","18"],["paddingLeft","25"],["text","Welcome to SMART Predict"]],null,null,null,g.Yc,g.fb)),n.Ib(27,4440064,[[3,4],["lblAuthMethod",4]],0,i.vb,[n.r,i.i],{paddingLeft:[0,"paddingLeft"],text:[1,"text"],fontSize:[2,"fontSize"],color:[3,"color"]},null),(t()(),n.Jb(28,0,null,0,3,"HBox",[["horizontalAlign","center"],["paddingTop","15"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(29,4440064,[[8,4],["logoutHBox",4]],0,i.C,[n.r,i.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),n.Jb(30,0,null,0,1,"SwtLabel",[["color","white"],["horizontalAlign","right"],["text","You have been successfully logged out"]],null,null,null,g.Yc,g.fb)),n.Ib(31,4440064,[[4,4],["messageLabel",4]],0,i.vb,[n.r,i.i],{horizontalAlign:[0,"horizontalAlign"],text:[1,"text"],color:[2,"color"]},null),(t()(),n.Jb(32,0,null,0,5,"HBox",[["paddingTop","15"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(33,4440064,[[7,4],["authMethodHBox",4]],0,i.C,[n.r,i.i],{width:[0,"width"],paddingTop:[1,"paddingTop"]},null),(t()(),n.Jb(34,0,null,0,1,"SwtLabel",[["color","white"],["text","Authentication Method"]],null,null,null,g.Yc,g.fb)),n.Ib(35,4440064,[[3,4],["lblAuthMethod",4]],0,i.vb,[n.r,i.i],{text:[0,"text"],color:[1,"color"]},null),(t()(),n.Jb(36,0,null,0,1,"SwtComboBox",[["dataLabel","authMethod"],["height","19"],["id","comboAuthMethod"],["tabIndex","3"],["width","190"]],null,[["window","mousewheel"]],function(t,o,e){var l=!0;"window:mousewheel"===o&&(l=!1!==n.Tb(t,37).mouseWeelEventHandler(e.target)&&l);return l},g.Pc,g.W)),n.Ib(37,4440064,[[2,4],["comboAuthMethod",4]],0,i.gb,[n.r,i.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],height:[2,"height"],id:[3,"id"]},null),(t()(),n.Jb(38,0,null,0,3,"HBox",[["horizontalAlign","center"],["includeInLayout","false"],["paddingTop","15"],["visible","false"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(39,4440064,[[9,4],["reloginBox",4]],0,i.C,[n.r,i.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],includeInLayout:[2,"includeInLayout"],visible:[3,"visible"],paddingTop:[4,"paddingTop"]},null),(t()(),n.Jb(40,0,null,0,1,"SwtLabel",[["color","white"],["horizontalAlign","right"],["text","Please click on Login Button to proceed"]],null,null,null,g.Yc,g.fb)),n.Ib(41,4440064,[[6,4],["reloginLabel",4]],0,i.vb,[n.r,i.i],{horizontalAlign:[0,"horizontalAlign"],text:[1,"text"],color:[2,"color"]},null),(t()(),n.Jb(42,0,null,0,3,"HBox",[["horizontalAlign","center"],["paddingTop","25"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(43,4440064,null,0,i.C,[n.r,i.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),n.Jb(44,0,null,0,1,"SwtLabel",[["color","white"],["horizontalAlign","right"],["text","Unauthorised Access Prohibited"]],null,null,null,g.Yc,g.fb)),n.Ib(45,4440064,[[5,4],["unauthorisedLabel",4]],0,i.vb,[n.r,i.i],{horizontalAlign:[0,"horizontalAlign"],text:[1,"text"],color:[2,"color"]},null),(t()(),n.Jb(46,0,null,0,9,"HBox",[["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(47,4440064,null,0,i.C,[n.r,i.i],{width:[0,"width"]},null),(t()(),n.Jb(48,0,null,0,5,"HBox",[["paddingLeft","90"],["paddingTop","5"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(49,4440064,null,0,i.C,[n.r,i.i],{width:[0,"width"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"]},null),(t()(),n.Jb(50,0,null,0,1,"SwtButton",[["id","saveButton"],["label","Login"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,o,e){var n=!0,l=t.component;"click"===o&&(n=!1!==l.loginHandler()&&n);"keyDown"===o&&(n=!1!==l.keyDownEventHandler(e)&&n);return n},g.Mc,g.T)),n.Ib(51,4440064,[["loginButton",4]],0,i.cb,[n.r,i.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(52,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["label","Cancel"],["width","70"]],null,null,null,g.Mc,g.T)),n.Ib(53,4440064,[["cancelButton",4]],0,i.cb,[n.r,i.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"],buttonMode:[3,"buttonMode"]},null),(t()(),n.Jb(54,0,null,0,1,"HBox",[["horizontalAlign","right"],["paddingTop","10"]],null,null,null,g.Dc,g.K)),n.Ib(55,4440064,null,0,i.C,[n.r,i.i],{horizontalAlign:[0,"horizontalAlign"],paddingTop:[1,"paddingTop"]},null),(t()(),n.Jb(56,0,null,0,1,"SwtLabel",[["fontSize","10"],["paddingTop","5"],["text","\xa9 2006 - 2021 SwallowTech"]],null,null,null,g.Yc,g.fb)),n.Ib(57,4440064,[["balanceTypeLabel",4]],0,i.vb,[n.r,i.i],{paddingTop:[0,"paddingTop"],text:[1,"text"],fontSize:[2,"fontSize"]},null)],function(t,o){t(o,10,0,"100%","100%");t(o,12,0,"#F6F9FB","100%","100%");t(o,15,0,"100%","100%","5","5","5","5");t(o,17,0,"100%","100%");t(o,19,0,"100%","100%","20","10","10");t(o,21,0,"#1F63AA","100%","100%");t(o,23,0,"100%","100%");t(o,25,0,"100%");t(o,27,0,"25","Welcome to SMART Predict","18","white");t(o,29,0,"center","100%","15");t(o,31,0,"right","You have been successfully logged out","white");t(o,33,0,"100%","15");t(o,35,0,"Authentication Method","white");t(o,37,0,"authMethod","190","19","comboAuthMethod");t(o,39,0,"center","100%","false","false","15");t(o,41,0,"right","Please click on Login Button to proceed","white");t(o,43,0,"center","100%","25");t(o,45,0,"right","Unauthorised Access Prohibited","white");t(o,47,0,"100%");t(o,49,0,"100%","5","90");t(o,51,0,"saveButton","70","Login");t(o,53,0,"cancelButton","70","Cancel","true");t(o,55,0,"right","10");t(o,57,0,"5","\xa9 2006 - 2021 SwallowTech","10")},null)}function G(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-logon-screen",[],null,null,null,W,O)),n.Ib(1,4440064,null,0,r,[i.i,n.r],null,null)],function(t,o){t(o,1,0)},null)}var U=n.Fb("app-logon-screen",r,G,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);