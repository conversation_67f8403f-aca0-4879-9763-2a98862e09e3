(window.webpackJsonp=window.webpackJsonp||[]).push([[57],{V9uj:function(t,e,i){"use strict";i.r(e);var l=i("CcnG"),n=i("mrSG"),a=i("447K"),o=i("ZYCi"),r=i("wd/R"),s=i.n(r),u=i("nxpO"),d=function(t){function e(e,i){var l=t.call(this,i,e)||this;return l.commonService=e,l.element=i,l.jsonReader=new a.L,l.inputData=new a.G(l.commonService),l.updateRefreshRate=new a.G(l.commonService),l.requestParams=[],l.baseURL=a.Wb.getBaseURL(),l.onLoadFlag=!0,l.comboChange=!1,l.valueBeforeEdit=null,l.valueAfterEdit=null,l.clkColumnHeader=null,l.refreshStatus="N",l.interval=null,l.refreshRate=10,l.versionNumber="1.1.021",l.screenVersion=new a.V(l.commonService),l.showBuildInProgress=!1,l.arrayOfDates=[],window.Main=l,l.swtAlert=new a.bb(e),l}return n.d(e,t),e.prototype.ngOnInit=function(){this.centralGrid=this.cbCanvas.addChild(a.hb),this.centralGrid.lockedColumnCount=1,this.centralGrid.editable=!0,this.dateFormat=a.x.call("eval","dateFormat"),this.testDate=a.x.call("eval","testDate"),this.fromDate.formatString=this.dateFormat,"DD/MM/YYYY"==this.dateFormat?this.fromDate.toolTip=a.x.call("getBundle","tip","datefromDDMMYY","Select From date('DD/MM/YYYY')"):this.fromDate.toolTip=a.x.call("getBundle","tip","datefromMMDDYY","Select From date('MM/DD/YYYY')"),this.accountRadio.toolTip=a.x.call("getBundle","tip","acctbrkdown","Refresh Account Breakdown Grid"),this.movementRadio.toolTip=a.x.call("getBundle","tip","mvmntbrkdown","Select Movement to view Movement Summary Detail")},e.prototype.onLoad=function(){var t=this;this.initializeMenus(),this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="centralBankMonitor.do?",this.actionMethod="method=unspecified",this.requestParams["centralMonitor.currTimeStamp"]=a.x.call("eval","timeStamp"),this.centralGrid.ITEM_CLICK.subscribe(function(e){t.cellLogic(e)}),this.centralGrid.enableDisableCells=function(e,i){return t.enableDisableRow(e,i)},this.centralGrid.ITEM_CHANGED.subscribe(function(e){t.changeCurrLimit(e)}),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),a.v.subscribe(function(e){t.report(e)})},e.prototype.enableDisableRow=function(t,e){return"What If Analysis"==t.balance},e.prototype.startOfComms=function(){1==this.showBuildInProgress?this.dataBuildingText.visible=!0:this.dataBuildingText.visible=!1,this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(){var t=a.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(t)},e.prototype.inputDataResult=function(t){var e=this;if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lastRefTime.text=this.jsonReader.getScreenAttributes().lastRefTime,this.lostConnectionText.visible=!1,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON){this.fromDate.showToday=!1,this.d1=s()(this.jsonReader.getScreenAttributes().from,this.dateFormat.toUpperCase()),this.prevChosenDate=this.d1,this.d2=s()(this.jsonReader.getScreenAttributes().to,this.dateFormat.toUpperCase()),this.fromDate.text=this.jsonReader.getScreenAttributes().from;this.showDays.text=Math.round(this.d2.diff(this.d1)/864e5+1),this.prevNumberOfDays=this.showDays.text,this.updateDayLabel(),this.entityFromDate=s()(this.jsonReader.getScreenAttributes().entityfromdate,this.dateFormat.toUpperCase()),this.entityToDate=s()(this.jsonReader.getScreenAttributes().entitytodate,this.dateFormat.toUpperCase());var i=s()(this.fromDate.text,this.dateFormat.toUpperCase());if(this.onLoadFlag||this.comboChange){var l=s()(this.fromDate.text,this.dateFormat.toUpperCase()).add(Number(this.showDays.text)-1,"days");this.checkDateRange(i,l),this.onLoadFlag=!1,this.comboChange=!1}if(this.currLimitText.text=String(this.jsonReader.getScreenAttributes().currencylimit),this.currencyCode=String(this.jsonReader.getScreenAttributes().currencycode),this.accountId=String(this.jsonReader.getScreenAttributes().accountid),this.multiplier.text=String(this.jsonReader.getScreenAttributes().multiplier),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.entityCombo.selectedValue,this.jsonReader.isDataBuilding())this.dataBuildingText.visible=!0;else{this.dataBuildingText.visible=!1;for(var n={columns:this.jsonReader.getColumnData()},o=1;o<this.jsonReader.getColumnData().column.length;o++)this.arrayOfDates[this.jsonReader.getColumnData().column[o].dataelement]=this.jsonReader.getColumnData().column[o].date;this.centralGrid.CustomGrid(n),this.centralGrid.gridData=this.jsonReader.getGridData(),this.centralGrid.setRowSize=this.jsonReader.getRowSize(),this.centralGrid.entityID=this.entityCombo.selectedLabel,this.centralGrid.colWidthURL(this.baseURL+"centralBankMonitor.do?"),this.centralGrid.saveWidths=!0,this.currentFontSize=String(this.jsonReader.getScreenAttributes().currfontsize),"Normal"==this.currentFontSize?(this.centralGrid.styleName="dataGridNormal",this.centralGrid.rowHeight=18):"Small"==this.currentFontSize&&(this.centralGrid.styleName="dataGridSmall",this.centralGrid.rowHeight=15),this.showBuildInProgress=!1}this.refreshRate=parseInt(this.jsonReader.getRefreshRate(),10);var r=this.refreshRate;clearInterval(this.interval),this.interval=setInterval(function(){e.updateData("yes",!0,!1)},1e3*r),this.centralGrid.rowColorFunction=function(t,i,l){return e.drawRowBackground(t,i,l)},this.prevRecievedJSON=this.lastRecievedJSON}}else this.swtAlert.warning(this.jsonReader.getRequestReplyMessage(),a.x.call("getBundle","text","alert-error","Error"),a.c.OK,null,function(){e.closeHandler()})},e.prototype.drawRowBackground=function(t,e,i){var l;try{11!=e&&13!=e&&15!=e||(l="#FBEAD8")}catch(n){}return l},e.prototype.checkDateRange=function(t,e){var i=!1,l=!1;(t.diff(this.entityFromDate)<0||t.diff(this.entityToDate)>0)&&(i=!0),(this.entityToDate.diff(e)<0||e.diff(this.entityFromDate)<0)&&(l=!0),i&&l?this.swtAlert.warning(a.x.call("getBundle","text","label-fromToOutsideRange","From date and To date are outside the defined range"),a.x.call("getBundle","text","alert-warning","Warning")):(i&&this.swtAlert.warning(a.x.call("getBundle","text","label-fromOutsideRange","From date is outside the defined range"),a.x.call("getBundle","text","alert-warning","Warning")),l&&this.swtAlert.warning(a.x.call("getBundle","text","label-ToOutsideRange","To date is outside the defined range"),a.x.call("getBundle","text","alert-warning","Warning")))},e.prototype.analysisFunc=function(t,e,i){this.valueBeforeEdit=e,this.valueAfterEdit=i,this.clkColumnHeader=t;var l=a.x.call("saveMonitorDetails",t,e,i,this.entityCombo.selectedLabel.toString(),!0);return"true"==l?0:(this.swtAlert.warning(l.toString(),a.x.call("getBundle","text","label-whatIfAnalysis","Central Bank Monitor - What If Analsysis"),a.c.OK|a.c.CANCEL,this.alertListener.bind(this),null),1)},e.prototype.alertListener=function(t){if(t.detail==a.c.OK)a.x.call("saveMonitorDetails",this.clkColumnHeader,this.valueBeforeEdit,this.valueAfterEdit,this.entityCombo.selectedLabel.toString(),!1);else this.updateData()},e.prototype.dataRefresh=function(){this.refreshStatus="Y",clearInterval(this.interval)},e.prototype.startAutoRefresh=function(t){"Y"==t&&this.updateData("yes",!0)},e.prototype.refreshParent=function(){this.updateData("yes",!0,!1)},e.prototype.updateData=function(t,e,i){void 0===t&&(t="no"),void 0===e&&(e=!1),void 0===i&&(i=!1),this.requestParams=[],this.d1=s()(this.fromDate.text,this.dateFormat.toUpperCase()),this.d2=s()(this.fromDate.text,this.dateFormat.toUpperCase()).add(Number(this.showDays.text)-1,"days");var l=s()(this.d2).format(this.dateFormat.toUpperCase());if(i)return this.showDays.text=this.prevNumberOfDays,void this.showDays.setFocusAndSelect();!e&&this.checkDateRangeCommon(t,this.d1,this.d2,this.showDays.text,a.x.call("eval","testDate"),this.dateFormat)||(this.validateDateField(this.fromDate)&&("no"==t&&this.checkDateRange(this.d1,this.d2),this.requestParams["centralMonitor.fromDateAsString"]=this.fromDate.text,this.requestParams["centralMonitor.toDateAsString"]=l,this.requestParams["centralMonitor.entityId"]=this.entityCombo.selectedItem.content,this.comboChange?this.requestParams["centralMonitor.currencyLimit"]="":this.requestParams["centralMonitor.currencyLimit"]=this.currLimitText.text,this.requestParams["centralMonitor.currTimeStamp"]=a.x.call("eval","timeStamp")),this.inputData.send(this.requestParams))},e.prototype.validateShowDaysValue=function(){var t=this;try{var e=s()(this.fromDate.text,this.dateFormat.toUpperCase()),i=s()(this.prevChosenDate,this.dateFormat.toUpperCase());this.updateDayLabel();var l=""+this.showDays.text;(""==l||0!=l.indexOf("0")&&""!=l||0==l.indexOf("0")&&1==l.indexOf("0",1)||0==l.indexOf("0")&&-1==l.indexOf("0",1))&&this.validateNumberOfDays(this.showDays)&&this.validateDateField(this.fromDate)&&(l==this.prevNumberOfDays&&0==e.diff(i)||setTimeout(function(){t.updateData("yes")},0))}catch(n){console.log("error in focusout",n)}},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.optionHandler=function(){a.x.call("openOptionsWindow",this.entityCombo.selectedLabel),clearInterval(this.interval),this.refreshStatus="N"},e.prototype.cellLogic=function(t){var e=t.target.field;(t.target.data.slickgrid_rowcontent[e]?t.target.data.slickgrid_rowcontent[e].clickable:null)&&this.clickLink(this.entityCombo.selectedLabel.toString(),this.selectedEntity.text,this.currencyCode,this.accountId,t.target.data.balance,t.target.data.slickgrid_rowcontent[e].date,this.breakdown.selectedValue)},e.prototype.clickLink=function(t,e,i,l,n,o,r){try{a.x.call("clickLink",t,e,i,l,n,o,r)}catch(s){console.log("error",s)}},e.prototype.updateDayLabel=function(){0==parseInt(this.showDays.text,10)||1==parseInt(this.showDays.text,10)?this.daysLabel.text=a.x.call("getBundle","text","day","Day"):this.daysLabel.text=a.x.call("getBundle","text","days","Days")},e.prototype.changeCombo=function(){this.validateDateField(this.fromDate)&&null!=this.entityCombo.selectedLabel&&this.updateData()},e.prototype.validateStartDate=function(t){},e.prototype.validateDateEnterPress=function(t){13==t.keyCode&&this.validateDateField(this.fromDate)&&this.updateData("no",!0)},e.prototype.validateDateFocusOut=function(t){this.validateDateField(this.fromDate)&&this.showDays.setFocusAndSelect()},e.prototype.validateDateField=function(t){var e=this;try{var i=void 0,l=a.Wb.getPredictMessage("alert.enterValidDate",null);if(!t.text)return this.swtAlert.error(l,null,null,null,function(){e.setFocusDateField(t)}),clearInterval(this.interval),!1;if(!(i=s()(t.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.error(l,null,null,null,function(){e.setFocusDateField(t)}),clearInterval(this.interval),!1;t.selectedDate=i.toDate()}catch(n){console.log("error in validateDateField",n)}return!0},e.prototype.setFocusDateField=function(t){t.setFocus(),t.text=this.jsonReader.getScreenAttributes().from},e.prototype.rateHandler=function(){this.win=a.Eb.createPopUp(this,u.a,{title:"Refresh Rate",refreshText:this.refreshRate.toString()}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="340",this.win.height="150",this.win.showControls=!0,this.win.id="optionsWindow",this.win.display()},e.prototype.saveRefreshRate=function(t){if(""==t||null==t)this.swtAlert.error(a.x.call("getBundle","text","label-validNumber","Not Valid Number"),a.x.call("getBundle","text","alert-error","Error"));else{var e=!1;this.refreshRate=Number(t),this.refreshRate<5&&(this.refreshRate=5,e=!0);var i=a.x.call("getUpdateRefreshRequest",this.refreshRate);null!=i&&""!=i&&(this.updateRefreshRate.url=this.baseURL+i,this.updateRefreshRate.send()),e&&this.swtAlert.error("Refresh rate selected was below minimum.\nSet to 5 seconds")}},e.prototype.report=function(t){var e=[],i=!1;e.push(a.x.call("getBundle","text","label-entity","Entity")+"="+this.entityCombo.selectedLabel),e.push(a.x.call("getBundle","text","label-currencyLimit","Currency Limit")+"="+this.currLimitText.text),e.push(a.x.call("getBundle","text","label-currencyCode","Currency Code")+"="+this.currencyCode);var l=this.multiplier.text,n=l.indexOf("("),o=l.indexOf(")"),r=l.substring(n+1,o);-1!=n?e.push(a.x.call("getBundle","text","label-currencyMultiplier","Currency Multiplier")+"="+r):e.push(a.x.call("getBundle","text","label-currencyMultiplier","Currency Multiplier")+"=None");var s="",u="";"dd/mm/yyyy"==this.dateFormat.toLowerCase()?(s=a.j.formatDate(this.d1,"DD/MM/YYYY"),u=a.j.formatDate(this.d2,"DD/MM/YYYY")):(s=a.j.formatDate(this.d1,"MM/DD/YYYY"),u=a.j.formatDate(this.d2,"MM/DD/YYYY")),e.push(a.x.call("getBundle","text","label-fromDate","From Date")+"="+s),e.push(a.x.call("getBundle","text","label-toDate","To Date")+"="+u),this.lastRecievedJSON.centralbankmonitor.grid.totals&&(i=!0),this.dataExport.convertData(this.lastRecievedJSON.centralbankmonitor.grid.metadata.columns,this.centralGrid,this.centralGrid.gridData,e,t,i)},e.prototype.doHelp=function(){a.x.call("help")},e.prototype.keyDownInNumberOfDays=function(t){var e=s()(this.fromDate.text,this.dateFormat.toUpperCase()),i=s()(this.prevChosenDate,this.dateFormat.toUpperCase());13==t.keyCode&&this.validateNumberOfDays(this.showDays)&&this.validateDateField(this.fromDate)&&(this.showDays.text!=this.prevNumberOfDays||0!=e.diff(i)?this.updateData("no"):this.updateData("no",!0))},e.prototype.validateNumberOfDays=function(t){var e=parseInt(t.text,10);return!(isNaN(e)||e<=1||e>14)||(this.showAlertForNumberOfDays(t),!1)},e.prototype.showAlertForNumberOfDays=function(t){this.swtAlert.show(" 'Show' value must be between 2 and 14. ","Error",a.c.OK,null,this.resetShowDay.bind(this))},e.prototype.resetShowDay=function(t){t.detail==a.c.OK&&(this.showDays.setFocusAndSelect(),clearInterval(this.interval),this.showDays.text=this.prevNumberOfDays)},e.prototype.checkDateRangeCommon=function(t,e,i,l,n,o){var r=!1,u=a.x.call("eval","nDaysPriorToToday"),d=s()(n,this.dateFormat.toUpperCase()).subtract(u,"days"),h=a.x.call("eval","nDaysAheadToToday"),c=s()(n,this.dateFormat.toUpperCase()).add(h,"days");if(e.diff(d)<0||i.diff(c)>0){r=!0;this.swtAlert.confirm("The data for this date range selection may not be available <br/>in the cache and will take time to be calculated. Do you want to continue?","",a.c.OK|a.c.CANCEL,null,this.checkDateRangeListener.bind(this))}return r},e.prototype.checkDateRangeListener=function(t){try{t.detail==a.c.OK?(this.showBuildInProgress=!0,this.updateData("no",!0)):this.updateData("no",null,!0)}catch(e){}},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,"Central Bank Monitor",this.versionNumber,"");var t=new a.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e.prototype.changeCurrLimit=function(t){if(t.listData.newValue!=t.listData.oldValue){var e;e=t.listData.newValue;var i=a.x.call("formatCurrency",e);if("0"==e)this.swtAlert.show(a.x.call("getBundle","text","label-amountGreaterThanZero","Amount should be greater than zero"),"",a.c.OK,null,null),this.centralGrid.dataProvider[t.rowIndex].slickgrid_rowcontent[t.dataField].content=t.listData.oldValue,this.centralGrid.dataProvider[t.rowIndex][t.dataField]=t.listData.oldValue,this.centralGrid.refresh();else if("invalid"==i||i.length<=0)this.swtAlert.warning(a.x.call("getBundle","text","label-validAmount","Please enter a valid amount"),"",a.c.OK,null,null),this.centralGrid.dataProvider[t.rowIndex].slickgrid_rowcontent[t.dataField].content=t.listData.oldValue,this.centralGrid.dataProvider[t.rowIndex][t.dataField]=t.listData.oldValue,this.centralGrid.refresh();else{""!=a.x.call("saveMonitorDetails",this.arrayOfDates[t.dataField],t.listData.oldValue,i,this.entityCombo.selectedLabel.toString(),!0)&&this.updateData("yes",!0,!1)}}},e}(a.yb),h=[{path:"",component:d}],c=(o.l.forChild(h),function(){return function(){}}()),b=i("pMnS"),m=i("RChO"),p=i("t6HQ"),w=i("WFGK"),f=i("5FqG"),g=i("Ip0R"),y=i("gIcY"),R=i("t/Na"),D=i("sE5F"),v=i("OzfB"),x=i("T7CS"),C=i("S7LP"),I=i("6aHO"),S=i("WzUx"),k=i("A7o+"),B=i("zCE2"),A=i("Jg5P"),T=i("3R0m"),L=i("hhbb"),O=i("5rxC"),F=i("Fzqc"),J=i("21Lb"),N=i("hUWP"),M=i("3pJQ"),G=i("V9q+"),E=i("VDKW"),W=i("kXfT"),P=i("BGbe");i.d(e,"CentralBankMonitorModuleNgFactory",function(){return Y}),i.d(e,"RenderType_CentralBankMonitor",function(){return j}),i.d(e,"View_CentralBankMonitor_0",function(){return U}),i.d(e,"View_CentralBankMonitor_Host_0",function(){return Z}),i.d(e,"CentralBankMonitorNgFactory",function(){return V});var Y=l.Gb(c,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[b.a,m.a,p.a,w.a,f.Cb,f.Pb,f.r,f.rc,f.s,f.Ab,f.Bb,f.Db,f.qd,f.Hb,f.k,f.Ib,f.Nb,f.Ub,f.yb,f.Jb,f.v,f.A,f.e,f.c,f.g,f.d,f.Kb,f.f,f.ec,f.Wb,f.bc,f.ac,f.sc,f.fc,f.lc,f.jc,f.Eb,f.Fb,f.mc,f.Lb,f.nc,f.Mb,f.dc,f.Rb,f.b,f.ic,f.Yb,f.Sb,f.kc,f.y,f.Qb,f.cc,f.hc,f.pc,f.oc,f.xb,f.p,f.q,f.o,f.h,f.j,f.w,f.Zb,f.i,f.m,f.Vb,f.Ob,f.Gb,f.Xb,f.t,f.tc,f.zb,f.n,f.qc,f.a,f.z,f.rd,f.sd,f.x,f.td,f.gc,f.l,f.u,f.ud,f.Tb,V]],[3,l.n],l.J]),l.Rb(4608,g.m,g.l,[l.F,[2,g.u]]),l.Rb(4608,y.c,y.c,[]),l.Rb(4608,y.p,y.p,[]),l.Rb(4608,R.j,R.p,[g.c,l.O,R.n]),l.Rb(4608,R.q,R.q,[R.j,R.o]),l.Rb(5120,R.a,function(t){return[t,new a.tb]},[R.q]),l.Rb(4608,R.m,R.m,[]),l.Rb(6144,R.k,null,[R.m]),l.Rb(4608,R.i,R.i,[R.k]),l.Rb(6144,R.b,null,[R.i]),l.Rb(4608,R.f,R.l,[R.b,l.B]),l.Rb(4608,R.c,R.c,[R.f]),l.Rb(4608,D.c,D.c,[]),l.Rb(4608,D.g,D.b,[]),l.Rb(5120,D.i,D.j,[]),l.Rb(4608,D.h,D.h,[D.c,D.g,D.i]),l.Rb(4608,D.f,D.a,[]),l.Rb(5120,D.d,D.k,[D.h,D.f]),l.Rb(5120,l.b,function(t,e){return[v.j(t,e)]},[g.c,l.O]),l.Rb(4608,x.a,x.a,[]),l.Rb(4608,C.a,C.a,[]),l.Rb(4608,I.a,I.a,[l.n,l.L,l.B,C.a,l.g]),l.Rb(4608,S.c,S.c,[l.n,l.g,l.B]),l.Rb(4608,S.e,S.e,[S.c]),l.Rb(4608,k.l,k.l,[]),l.Rb(4608,k.h,k.g,[]),l.Rb(4608,k.c,k.f,[]),l.Rb(4608,k.j,k.d,[]),l.Rb(4608,k.b,k.a,[]),l.Rb(4608,k.k,k.k,[k.l,k.h,k.c,k.j,k.b,k.m,k.n]),l.Rb(4608,S.i,S.i,[[2,k.k]]),l.Rb(4608,S.r,S.r,[S.L,[2,k.k],S.i]),l.Rb(4608,S.t,S.t,[]),l.Rb(4608,S.w,S.w,[]),l.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),l.Rb(1073742336,g.b,g.b,[]),l.Rb(1073742336,y.n,y.n,[]),l.Rb(1073742336,y.l,y.l,[]),l.Rb(1073742336,B.a,B.a,[]),l.Rb(1073742336,A.a,A.a,[]),l.Rb(1073742336,y.e,y.e,[]),l.Rb(1073742336,T.a,T.a,[]),l.Rb(1073742336,k.i,k.i,[]),l.Rb(1073742336,S.b,S.b,[]),l.Rb(1073742336,R.e,R.e,[]),l.Rb(1073742336,R.d,R.d,[]),l.Rb(1073742336,D.e,D.e,[]),l.Rb(1073742336,L.b,L.b,[]),l.Rb(1073742336,O.b,O.b,[]),l.Rb(1073742336,v.c,v.c,[]),l.Rb(1073742336,F.a,F.a,[]),l.Rb(1073742336,J.d,J.d,[]),l.Rb(1073742336,N.c,N.c,[]),l.Rb(1073742336,M.a,M.a,[]),l.Rb(1073742336,G.a,G.a,[[2,v.g],l.O]),l.Rb(1073742336,E.b,E.b,[]),l.Rb(1073742336,W.a,W.a,[]),l.Rb(1073742336,P.b,P.b,[]),l.Rb(1073742336,a.Tb,a.Tb,[]),l.Rb(1073742336,c,c,[]),l.Rb(256,R.n,"XSRF-TOKEN",[]),l.Rb(256,R.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,k.m,void 0,[]),l.Rb(256,k.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,o.i,function(){return[[{path:"",component:d}]]},[])])}),_=[[""]],j=l.Hb({encapsulation:0,styles:_,data:{}});function U(t){return l.dc(0,[l.Zb(*********,1,{_container:0}),l.Zb(*********,2,{lblEntity:0}),l.Zb(*********,3,{entityCombo:0}),l.Zb(*********,4,{selectedEntity:0}),l.Zb(*********,5,{lblCurrency:0}),l.Zb(*********,6,{currLimitText:0}),l.Zb(*********,7,{multiplier:0}),l.Zb(*********,8,{lblWeek:0}),l.Zb(*********,9,{fromDate:0}),l.Zb(*********,10,{lblDays:0}),l.Zb(*********,11,{showDays:0}),l.Zb(*********,12,{daysLabel:0}),l.Zb(*********,13,{lblbreak:0}),l.Zb(*********,14,{breakdown:0}),l.Zb(*********,15,{accountRadio:0}),l.Zb(*********,16,{movementRadio:0}),l.Zb(*********,17,{cbCanvas:0}),l.Zb(*********,18,{refreshButton:0}),l.Zb(*********,19,{rateButton:0}),l.Zb(*********,20,{optionButton:0}),l.Zb(*********,21,{closeButton:0}),l.Zb(*********,22,{dataBuildingText:0}),l.Zb(*********,23,{lostConnectionText:0}),l.Zb(*********,24,{lastRefTime:0}),l.Zb(*********,25,{lastRefText:0}),l.Zb(*********,26,{loadingImage:0}),l.Zb(*********,27,{dataExport:0}),(t()(),l.Jb(27,0,null,null,108,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var l=!0,n=t.component;"creationComplete"===e&&(l=!1!==n.onLoad()&&l);return l},f.ad,f.hb)),l.Ib(28,4440064,null,0,a.yb,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(29,0,null,0,106,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,f.od,f.vb)),l.Ib(30,4440064,null,0,a.ec,[l.r,a.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),l.Jb(31,0,null,0,72,"SwtCanvas",[["height","70"],["minWidth","1100"],["width","100%"]],null,null,null,f.Nc,f.U)),l.Ib(32,4440064,null,0,a.db,[l.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),l.Jb(33,0,null,0,70,"Grid",[["height","100%"],["width","100%"]],null,null,null,f.Cc,f.H)),l.Ib(34,4440064,null,0,a.z,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(35,0,null,0,37,"GridRow",[["paddingLeft","5"],["width","100%"]],null,null,null,f.Bc,f.J)),l.Ib(36,4440064,null,0,a.B,[l.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(37,0,null,0,13,"GridItem",[["width","65%"]],null,null,null,f.Ac,f.I)),l.Ib(38,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(39,0,null,0,3,"GridItem",[["width","120"]],null,null,null,f.Ac,f.I)),l.Ib(40,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(41,0,null,0,1,"SwtLabel",[["text","Entity"]],null,null,null,f.Yc,f.fb)),l.Ib(42,4440064,[[2,4],["lblEntity",4]],0,a.vb,[l.r,a.i],{text:[0,"text"]},null),(t()(),l.Jb(43,0,null,0,3,"GridItem",[["width","180"]],null,null,null,f.Ac,f.I)),l.Ib(44,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(45,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["toolTip","Select an Entity ID"],["width","170"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var n=!0,a=t.component;"window:mousewheel"===e&&(n=!1!==l.Tb(t,46).mouseWeelEventHandler(i.target)&&n);"change"===e&&(n=!1!==a.changeCombo()&&n);return n},f.Pc,f.W)),l.Ib(46,4440064,[[3,4],["entityCombo",4]],0,a.gb,[l.r,a.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],id:[3,"id"]},{change_:"change"}),(t()(),l.Jb(47,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,f.Ac,f.I)),l.Ib(48,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(49,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,f.Yc,f.fb)),l.Ib(50,4440064,[[4,4],["selectedEntity",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(51,0,null,0,21,"GridItem",[["width","35%"]],null,null,null,f.Ac,f.I)),l.Ib(52,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(53,0,null,0,3,"GridItem",[["width","35%"]],null,null,null,f.Ac,f.I)),l.Ib(54,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(55,0,null,0,1,"SwtLabel",[["text","Week Commencing"]],null,null,null,f.Yc,f.fb)),l.Ib(56,4440064,[[8,4],["lblWeek",4]],0,a.vb,[l.r,a.i],{text:[0,"text"]},null),(t()(),l.Jb(57,0,null,0,3,"GridItem",[["width","28%"]],null,null,null,f.Ac,f.I)),l.Ib(58,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(59,0,null,0,1,"SwtDateField",[["id","fromDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"],[null,"keypress"]],function(t,e,i){var l=!0,n=t.component;"change"===e&&(l=!1!==n.validateDateFocusOut(i)&&l);"keypress"===e&&(l=!1!==n.validateDateEnterPress(i)&&l);return l},f.Tc,f.ab)),l.Ib(60,4308992,[[9,4],["fromDate",4]],0,a.lb,[l.r,a.i,l.T],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{changeEventOutPut:"change"}),(t()(),l.Jb(61,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,f.Ac,f.I)),l.Ib(62,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(63,0,null,0,1,"SwtLabel",[["text","show"]],null,null,null,f.Yc,f.fb)),l.Ib(64,4440064,[[10,4],["lblDays",4]],0,a.vb,[l.r,a.i],{text:[0,"text"]},null),(t()(),l.Jb(65,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,f.Ac,f.I)),l.Ib(66,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(67,0,null,0,1,"SwtNumericInput",[["maxChars","2"],["width","30"]],null,[[null,"keypress"],[null,"focusOut"]],function(t,e,i){var l=!0,n=t.component;"keypress"===e&&(l=!1!==n.keyDownInNumberOfDays(i)&&l);"focusOut"===e&&(l=!1!==n.validateShowDaysValue()&&l);return l},f.cd,f.jb)),l.Ib(68,4440064,[[11,4],["showDays",4]],0,a.Ab,[l.r,a.i],{maxChars:[0,"maxChars"],width:[1,"width"]},{onFocusOut_:"focusOut"}),(t()(),l.Jb(69,0,null,0,3,"GridItem",[["width","5%"]],null,null,null,f.Ac,f.I)),l.Ib(70,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(71,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["text","days"],["toolTip","Number of days to show"]],null,null,null,f.Yc,f.fb)),l.Ib(72,4440064,[[12,4],["daysLabel",4]],0,a.vb,[l.r,a.i],{toolTip:[0,"toolTip"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),l.Jb(73,0,null,0,30,"GridRow",[["paddingLeft","5"],["width","100%"]],null,null,null,f.Bc,f.J)),l.Ib(74,4440064,null,0,a.B,[l.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(75,0,null,0,13,"GridItem",[["width","65%"]],null,null,null,f.Ac,f.I)),l.Ib(76,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(77,0,null,0,3,"GridItem",[["width","120"]],null,null,null,f.Ac,f.I)),l.Ib(78,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(79,0,null,0,1,"SwtLabel",[["text","Currency Limit"]],null,null,null,f.Yc,f.fb)),l.Ib(80,4440064,[[8,4],["lblWeek",4]],0,a.vb,[l.r,a.i],{text:[0,"text"]},null),(t()(),l.Jb(81,0,null,0,3,"GridItem",[["width","180"]],null,null,null,f.Ac,f.I)),l.Ib(82,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(83,0,null,0,1,"SwtTextInput",[["editable","false"],["id","currLimitText"],["restrict","0-9.,bBtTmM"],["textAlign","right"],["toolTip","Enter the currency limit"],["width","95%"]],null,null,null,f.kd,f.sb)),l.Ib(84,4440064,[[6,4],["currLimitText",4]],0,a.Rb,[l.r,a.i],{restrict:[0,"restrict"],id:[1,"id"],textAlign:[2,"textAlign"],toolTip:[3,"toolTip"],width:[4,"width"],editable:[5,"editable"]},null),(t()(),l.Jb(85,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,f.Ac,f.I)),l.Ib(86,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(87,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,f.Yc,f.fb)),l.Ib(88,4440064,[[7,4],["multiplier",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(89,0,null,0,14,"GridItem",[["width","35%"]],null,null,null,f.Ac,f.I)),l.Ib(90,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(91,0,null,0,3,"GridItem",[["width","35%"]],null,null,null,f.Ac,f.I)),l.Ib(92,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(93,0,null,0,1,"SwtLabel",[["paddingTop","2"],["text","Breakdown"],["width","100"]],null,null,null,f.Yc,f.fb)),l.Ib(94,4440064,[[13,4],["lblbreak",4]],0,a.vb,[l.r,a.i],{width:[0,"width"],paddingTop:[1,"paddingTop"],text:[2,"text"]},null),(t()(),l.Jb(95,0,null,0,8,"GridItem",[],null,null,null,f.Ac,f.I)),l.Ib(96,4440064,null,0,a.A,[l.r,a.i],null,null),(t()(),l.Jb(97,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","breakdown"]],null,null,null,f.ed,f.lb)),l.Ib(98,4440064,[[14,4],["breakdown",4]],1,a.Hb,[R.c,l.r,a.i],{id:[0,"id"],align:[1,"align"]},null),l.Zb(*********,28,{radioItems:1}),(t()(),l.Jb(100,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","accountRadio"],["label","Account Breakdown"],["selected","true"],["value","A"]],null,null,null,f.fd,f.mb)),l.Ib(101,4440064,[[28,4],[15,4],["accountRadio",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],groupName:[1,"groupName"],label:[2,"label"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),l.Jb(102,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","movementRadio"],["label","Movement"],["value","M"]],null,null,null,f.fd,f.mb)),l.Ib(103,4440064,[[28,4],[16,4],["movementRadio",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],groupName:[1,"groupName"],label:[2,"label"],value:[3,"value"]},null),(t()(),l.Jb(104,0,null,0,1,"SwtCanvas",[["height","100%"],["minWidth","1100"],["width","100%"]],null,null,null,f.Nc,f.U)),l.Ib(105,4440064,[[17,4],["cbCanvas",4]],0,a.db,[l.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),l.Jb(106,0,null,0,29,"SwtCanvas",[["height","40"],["id","canvasButtons"],["marginBottom","0"],["minWidth","1100"],["width","100%"]],null,null,null,f.Nc,f.U)),l.Ib(107,4440064,null,0,a.db,[l.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],marginBottom:[4,"marginBottom"]},null),(t()(),l.Jb(108,0,null,0,27,"HBox",[["width","100%"]],null,null,null,f.Dc,f.K)),l.Ib(109,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(110,0,null,0,9,"HBox",[["paddingLeft","5"],["width","42%"]],null,null,null,f.Dc,f.K)),l.Ib(111,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(112,0,null,0,1,"SwtButton",[["id","refreshButton"],["label","Refresh"],["toolTip","Refresh window"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.updateData("yes",!0,!1)&&l);return l},f.Mc,f.T)),l.Ib(113,4440064,[[18,4],["refreshButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],label:[3,"label"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(114,0,null,0,1,"SwtButton",[["id","rateButton"],["label","Rate"],["toolTip","Rate Window"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.rateHandler()&&l);return l},f.Mc,f.T)),l.Ib(115,4440064,[[19,4],["rateButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],label:[3,"label"]},{onClick_:"click"}),(t()(),l.Jb(116,0,null,0,1,"SwtButton",[["id","optionButton"],["label","Options"],["toolTip","Change options"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.optionHandler()&&l);return l},f.Mc,f.T)),l.Ib(117,4440064,[[20,4],["optionButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],label:[3,"label"]},{onClick_:"click"}),(t()(),l.Jb(118,0,null,0,1,"SwtButton",[["id","closeButton"],["label","Close"],["toolTip","Close window"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.closeHandler()&&l);return l},f.Mc,f.T)),l.Ib(119,4440064,[[21,4],["closeButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],label:[3,"label"]},{onClick_:"click"}),(t()(),l.Jb(120,0,null,0,15,"HBox",[["horizontalAlign","right"],["top","4"],["width","58%"]],null,null,null,f.Dc,f.K)),l.Ib(121,4440064,null,0,a.C,[l.r,a.i],{top:[0,"top"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"]},null),(t()(),l.Jb(122,0,null,0,1,"SwtLabel",[["color","red"],["text","DATA BUILD IN PORGRESS"],["visible","false"]],null,null,null,f.Yc,f.fb)),l.Ib(123,4440064,[[22,4],["dataBuildingText",4]],0,a.vb,[l.r,a.i],{visible:[0,"visible"],text:[1,"text"],color:[2,"color"]},null),(t()(),l.Jb(124,0,null,0,1,"SwtLabel",[["color","red"],["text","CONNECTION ERROR"],["visible","false"]],null,null,null,f.Yc,f.fb)),l.Ib(125,4440064,[[23,4],["lostConnectionText",4]],0,a.vb,[l.r,a.i],{visible:[0,"visible"],text:[1,"text"],color:[2,"color"]},null),(t()(),l.Jb(126,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["text","Last Refresh:"]],null,null,null,f.Yc,f.fb)),l.Ib(127,4440064,[[25,4],["lastRefText",4]],0,a.vb,[l.r,a.i],{text:[0,"text"],fontWeight:[1,"fontWeight"]},null),(t()(),l.Jb(128,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,f.Yc,f.fb)),l.Ib(129,4440064,[[24,4],["lastRefTime",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(130,0,null,0,1,"DataExport",[["id","dataExport"]],null,null,null,f.Sc,f.Z)),l.Ib(131,4440064,[[27,4],["dataExport",4]],0,a.kb,[a.i,l.r],{id:[0,"id"]},null),(t()(),l.Jb(132,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.doHelp()&&l);return l},f.Wc,f.db)),l.Ib(133,4440064,[["helpIcon",4]],0,a.rb,[l.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(134,0,null,0,1,"SwtLoadingImage",[],null,null,null,f.Zc,f.gb)),l.Ib(135,114688,[[26,4],["loadingImage",4]],0,a.xb,[l.r],null,null)],function(t,e){t(e,28,0,"100%","100%");t(e,30,0,"100%","100%","5","5","5","5");t(e,32,0,"100%","70","1100");t(e,34,0,"100%","100%");t(e,36,0,"100%","5");t(e,38,0,"65%");t(e,40,0,"120");t(e,42,0,"Entity");t(e,44,0,"180");t(e,46,0,"entity","Select an Entity ID","170","entityCombo");t(e,48,0,"10%");t(e,50,0,"normal");t(e,52,0,"35%");t(e,54,0,"35%");t(e,56,0,"Week Commencing");t(e,58,0,"28%");t(e,60,0,"0-9/","fromDate","70");t(e,62,0,"10%");t(e,64,0,"show");t(e,66,0,"10%");t(e,68,0,"2","30");t(e,70,0,"5%");t(e,72,0,"Number of days to show","days","normal");t(e,74,0,"100%","5");t(e,76,0,"65%");t(e,78,0,"120");t(e,80,0,"Currency Limit");t(e,82,0,"180");t(e,84,0,"0-9.,bBtTmM","currLimitText","right","Enter the currency limit","95%","false");t(e,86,0,"10%");t(e,88,0,"normal");t(e,90,0,"35%");t(e,92,0,"35%");t(e,94,0,"100","2","Breakdown"),t(e,96,0);t(e,98,0,"breakdown","horizontal");t(e,101,0,"accountRadio","breakdown","Account Breakdown","A","true");t(e,103,0,"movementRadio","breakdown","Movement","M");t(e,105,0,"100%","100%","1100");t(e,107,0,"canvasButtons","100%","40","1100","0");t(e,109,0,"100%");t(e,111,0,"42%","5");t(e,113,0,"refreshButton","Refresh window","70","Refresh",!0);t(e,115,0,"rateButton","Rate Window","70","Rate");t(e,117,0,"optionButton","Change options","70","Options");t(e,119,0,"closeButton","Close window","70","Close");t(e,121,0,"4","right","58%");t(e,123,0,"false","DATA BUILD IN PORGRESS","red");t(e,125,0,"false","CONNECTION ERROR","red");t(e,127,0,"Last Refresh:","normal");t(e,129,0,"normal");t(e,131,0,"dataExport");t(e,133,0,"helpIcon"),t(e,135,0)},null)}function Z(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-central-bank-monitor",[],null,null,null,U,j)),l.Ib(1,4440064,null,0,d,[a.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var V=l.Fb("app-central-bank-monitor",d,Z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);