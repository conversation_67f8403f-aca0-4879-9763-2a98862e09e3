(window.webpackJsonp=window.webpackJsonp||[]).push([[92],{TpCh:function(t,e,i){"use strict";i.r(e);var l=i("CcnG"),a=i("mrSG"),n=i("447K"),s=i("ZYCi"),o=i("wd/R"),r=i.n(o),c=function(t){function e(e,i){var l=t.call(this,i,e)||this;return l.commonService=e,l.element=i,l.jsonReader=new n.L,l.inputData=new n.G(l.commonService),l.baseURL=n.Wb.getBaseURL(),l.actionMethod=null,l.actionPath=null,l.requestParams=[],l.invalidComms=null,l.closeWindow=!1,l.refreshRate=5,l.ccyEntityAccess=null,l.ccyIsEmpty=null,l.errorLocation=0,l.moduleId="Predict",l.isCalculationLaunched=!1,l.menuAccessId=0,l.menuAccess="",l.status="",l.autoRefresh=null,l.versionNumber="1",l.screenVersion=new n.V(l.commonService),l.screenName="ILM Calculation Launcher",l.versionDate="04/11/2019",l.canceled=!1,l.showJsonPopup=null,l.alertShown=!0,l.logger=new n.R("Interface Monitor Screen",l.commonService.httpclient),l.swtAlert=new n.bb(e),window.Main=l,l}return a.d(e,t),e.prototype.ngOnInit=function(){this.currencyCalculationGrid=this.gridCanvas.addChild(n.hb),this.entityLabel.text=n.Wb.getPredictMessage("label.accountattribute.entity",null),this.entityCombo.toolTip=n.Wb.getPredictMessage("tip.accountattribute.entity",null),this.currencyLabel.text=n.Wb.getPredictMessage("label.accountattribute.currency",null),this.ccyCombo.toolTip=n.Wb.getPredictMessage("tip.accountattribute.currency",null),this.dateGroupLabel.text="Date",this.screens.label="ILM data for screens",this.screenAndReports.label=n.Wb.getPredictMessage("label.ilmdataandreportscreens",null),this.calculateGroupLabel.text="Calculate",this.calculateGroup.toolTip=n.Wb.getPredictMessage("tooltip.ilmdatascreenreports",null),this.range.label=n.Wb.getPredictMessage("ilmReport.dateRange",null),this.single.label=n.Wb.getPredictMessage("ilmReport.singleDay",null),this.processButton.label=n.Wb.getPredictMessage("reports.process",null),this.processButton.toolTip=n.Wb.getPredictMessage("tooltip.processButton",null),this.cancelButton.label=n.Wb.getPredictMessage("tooltip.cancel",null),this.cancelButton.label=n.Wb.getPredictMessage("button.cancel",null),this.toDateLabel.text="To",this.menuAccess=n.x.call("eval","menuAccessId"),this.ccyEntityAccess=n.x.call("eval","ccyEntityAccess"),this.ccyIsEmpty=n.x.call("eval","ccyIsEmpty"),"true"==this.ccyIsEmpty?this.processButton.enabled=!1:"0"==this.menuAccess?this.processButton.enabled="0"==this.ccyEntityAccess:this.processButton.enabled=!1,this.cancelButton.enabled=!1},e.prototype.onLoad=function(){var t=this;this.loadingImage.setVisible(!1),this.menuAccess&&""!==this.menuAccess&&(this.menuAccessId=Number(this.menuAccess)),this.initializeMenus(),this.entityId=n.x.call("eval","entityId"),this.currencyCode=n.x.call("eval","currencyCode"),this.selectedStartDate=n.x.call("eval","selectedStartDate"),this.selectedEndDate=n.x.call("eval","selectedEndDate"),this.processOption=n.x.call("eval","processOption"),this.singleOrRange=n.x.call("eval","singleOrRange"),this.allProcess=n.x.call("eval","allProcess"),this.isSingleDate=n.x.call("eval","isSingleDate"),this.dateFormat=n.x.call("eval","dateFormat"),this.dateFormatValue=n.x.call("eval","dateFormatValue"),this.toHbox.visible=!this.isSingleDate,this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="intraDayLiquidity.do?method=",this.actionMethod="listCurrencyProcessStatus",this.requestParams=[],this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.selectedStartDate=this.selectedStartDate,this.requestParams.selectedEndDate=this.selectedEndDate,this.requestParams.processOption=this.processOption,this.requestParams.singleOrRange=this.singleOrRange,this.requestParams.sequenceNumber=this.sequenceNumber,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.currencyCalculationGrid.rowColorFunction=function(e,i,l,a){return t.drawRowBackground(e,i,l,a)}},e.prototype.drawRowBackground=function(t,e,i,l){var a;try{t.slickgrid_rowcontent&&t.slickgrid_rowcontent.hasViewAccessOnly.content&&n.Z.isTrue(t.slickgrid_rowcontent.hasViewAccessOnly.content)&&(a="#DDDDDD")}catch(s){console.log("error drawRowBackground ",s)}return a},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.versionDate);var t=new n.n("Show JSON");t.MenuItemSelect=this.showJSONSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showJSONSelect=function(t){this.showJsonPopup=n.Eb.createPopUp(this,n.M,{jsonData:this.lastReceivedJSON}),this.showJsonPopup.width="700",this.showJsonPopup.title="Last Received JSON",this.showJsonPopup.height="500",this.showJsonPopup.enableResize=!1,this.showJsonPopup.showControls=!0,this.showJsonPopup.display()},e.prototype.inputDataRefresh=function(t){this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()?(n.x.call("checkProcessingResult",""+this.jsonReader.getprossesInfoStatus(),""+this.jsonReader.getprossesInfoRunning()),this.lastReceivedJSON!=this.prevReceivedJSON&&(this.currencyCalculationGrid.gridData=this.jsonReader.getGridData(),this.currencyCalculationGrid.setRowSize=this.jsonReader.getRowSize(),this.currencyCalculationGrid.selectedIndex=-1,this.prevReceivedJSON=this.lastReceivedJSON,this.allIsAllowed=this.jsonReader.getSingletons().allIsAllowed,n.Z.isTrue(this.allIsAllowed)||this.alertShown||this.swtAlert.warning("You don't have full access on at least one currency.  The Process button will act only on currencies where you have Full Access"),this.alertShown=!0)):this.swtAlert.warning(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",n.c.OK,this,this.errorHandler.bind(this))},e.prototype.inputDataResult=function(t){if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()){if(this.lastReceivedJSON!=this.prevReceivedJSON){if(!this.jsonReader.isDataBuilding()){this.ccyCombo.setComboData(this.jsonReader.getSelects(),!1),this.entityCombo.setComboData(this.jsonReader.getSelects(),!1),this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.selectedEntity.text=this.entityCombo.selectedItem.value,this.selectedStartDate=this.jsonReader.getSingletons().selectedStartDate,this.leftThresholdDay=this.jsonReader.getSingletons().firstThresholdDay,this.rightThresholdDay=this.jsonReader.getSingletons().lastThresholdDay,this.selecteDefaultDate=this.jsonReader.getSingletons().selecteDefaultDate,this.allIsAllowed=this.jsonReader.getSingletons().allIsAllowed,n.Z.isTrue(this.allIsAllowed)||this.alertShown||this.swtAlert.warning("You don't have full access on at least one currency.  The Process button will act only on currencies where you have Full Access"),this.alertShown=!0,this.startDate.formatString=this.dateFormatValue,this.startDate.selectedDate=new Date(n.j.parseDate(this.selectedStartDate,this.dateFormatValue.toUpperCase())),this.fromDateAsString=this.jsonReader.getSingletons().fromDateAsString,this.toDateAsString=this.jsonReader.getSingletons().toDateAsString;var e=new Date(n.j.parseDate(this.fromDateAsString,this.dateFormatValue.toUpperCase())),i=new Date(n.j.parseDate(this.toDateAsString,this.dateFormatValue.toUpperCase()));this.startDate.selectableRange={rangStart:e,rangeEnd:i},this.toDate.selectableRange={rangStart:e,rangeEnd:i},"S"!==this.singleOrRange&&(this.selectedEndDate=this.jsonReader.getSingletons().selectedEndDate,this.toDate.formatString=this.dateFormatValue,this.toDate.selectedDate=new Date(n.j.parseDate(this.selectedEndDate,this.dateFormatValue.toUpperCase())));var l={columns:this.jsonReader.getColumnData()};this.currencyCalculationGrid.doubleClickEnabled=!0,this.currencyCalculationGrid.CustomGrid(l),this.jsonReader.getGridData()&&this.jsonReader.getGridData().size>0?(this.currencyCalculationGrid.gridData=this.jsonReader.getGridData(),this.currencyCalculationGrid.setRowSize=this.jsonReader.getRowSize()):(this.currencyCalculationGrid.dataProvider=null,this.currencyCalculationGrid.selectedIndex=-1),null==this.autoRefresh?(this.autoRefresh=new n.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.refreshdetails.bind(this))):this.autoRefresh.delay(1e3*this.refreshRate)}this.prevReceivedJSON=this.lastReceivedJSON}}else this.lastReceivedJSON.hasOwnProperty("requestthis.reply")&&"true"==this.lastReceivedJSON.requestthis.reply.closewindow&&(this.closeWindow=!0),this.swtAlert.warning(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",n.c.OK,this);null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.setScreenStatusView=function(t,e){if("RUNNING"==t)this.labelStatus.fontWeight="bold",this.labelStatus.color="red",this.labelStatus.text=n.Wb.getPredictMessage("label.ilmccyprocess.calculinprogress",null),this.loadingImage.setVisible(!0),this.cancelButton.enabled=!0,this.disableEnableComponents(!1),this.processButton.enabled=!1,this.cancelButton.enabled=!0,this.isCalculationLaunched=!0;else{"CANCEL"==t&&this.isCalculationLaunched?this.labelStatus.text=n.Wb.getPredictMessage("label.ilmccyprocess.calculationcancelled",null):"LAUNCH_FAILED"==t?this.labelStatus.text=n.Wb.getPredictMessage("label.ilmccyprocess.calculationlaunchfailed",null):"SUCCESS"==t&&this.isCalculationLaunched?this.labelStatus.text=n.Wb.getPredictMessage("label.ilmcalculationsuccess",null):"FAIL"==t&&this.isCalculationLaunched&&(this.labelStatus.text=n.Wb.getPredictMessage("label.ilmcalculationfailed",null)),this.loadingImage.setVisible(!1),this.disableEnableComponents(!0);var i=n.x.call("setScreenStatusView",this.entityId,this.currencyCode);0==this.menuAccessId&&(this.processButton.enabled="0"==i),this.cancelButton.enabled=!1,"CANCELED"!=this.status?(this.labelStatus.color="",this.labelStatus.fontWeight="normal",this.isCalculationLaunched=!1):1==this.isCalculationLaunched&&(this.labelStatus.fontWeight="normal",this.labelStatus.color="",this.labelStatus.text=n.Wb.getPredictMessage("label.ilmccyprocess.calculationcancelled",null),this.isCalculationLaunched=!1)}},e.prototype.disableEnableComponents=function(t){try{this.gridRowEntity.enabled=t,this.gridRowCurrency.enabled=t,this.gridRowCalculateGroup.enabled=t,this.calculateGroup.enabled=t,this.gridRowDateGroup.enabled=t,this.dateGroup.enabled=t,this.gridRowDates.enabled=t}catch(e){n.Wb.logError(e,this.moduleId,"IlmCurrencyCalculation","disableComponents",this.errorLocation)}},e.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error("fault "+this.invalidComms)},e.prototype.validateField=function(t){try{var e=void 0;return t.text&&10==t.text.length&&(e=r()(t.text,this.dateFormatValue.toUpperCase(),!0)).isValid()?(t.selectedDate=e.toDate(),!0):(this.swtAlert.warning("Please enter a valid Date"),!1)}catch(i){n.Wb.logError(i,n.Wb.SYSTEM_MODULE_ID,"IlmCurrencyCalculation"," validateDateField",this.errorLocation)}return!0},e.prototype.validateDateField=function(t,e){try{var i=!1;return"datePat2"==this.dateFormat?i=this.validateField(t):"datePat1"==this.dateFormat&&(i=this.validateField(t)),i||(t.selectedDate=null),i}catch(l){n.Wb.logError(l,n.Wb.SYSTEM_MODULE_ID,"ILM CALCULATION"," validateDateField",this.errorLocation)}},e.prototype.validateReportDates=function(){var t=!1;return this.isSingleDate?""!=this.startDate.text?(this.toDate.selectedDate=null,this.validateDateField(this.startDate,"fromDateAsString")):(n.Wb.getPredictMessage("alert.enterValidDate",null),void this.swtAlert.error(n.Wb.getPredictMessage("alert.enterValidDate",null))):(""==this.startDate.text?this.swtAlert.error(n.Wb.getPredictMessage("alert.enterValidFromDate",null)):""==this.toDate.text?this.swtAlert.error(n.Wb.getPredictMessage("alert.enterValidToDate",null)):t=n.x.call("comparedates",this.startDate.text,this.toDate.text,this.dateFormat,"Start Date","End Date"),t)},e.prototype.launchCalculationProcess=function(){var t;this.isCalculationLaunched=!1,this.canceled=!1,t=this.validateReportDates();var e;e=this.testThresholdDates(),this.entityId=this.entityCombo.selectedItem.content,this.currencyCode=this.ccyCombo.selectedItem.content,this.selectedStartDate=this.startDate.text,this.selectedEndDate=this.toDate.text,this.processOption=this.allProcess?"A":"M",this.singleOrRange=this.isSingleDate?"S":"R",n.x.call("launchCalculation",this.entityId,this.currencyCode,this.selectedStartDate,this.selectedEndDate,this.processOption,this.isSingleDate,this.singleOrRange,t,e)},e.prototype.testThresholdDates=function(){var t=!0,e=!1,i=new Array(3);return""!=this.leftThresholdDay&&this.leftThresholdDay!=this.rightThresholdDay&&(t=this.stringToDate(this.leftThresholdDay,this.dateFormatValue,"/").getTime()!=this.stringToDate(this.startDate.text,this.dateFormatValue,"/").getTime()&&n.x.call("compareTwoDates",this.leftThresholdDay,this.startDate.text,this.dateFormat,"00:00","00:00")),this.isSingleDate?this.stringToDate(this.rightThresholdDay,this.dateFormatValue,"/").getTime()!=this.stringToDate(this.startDate.text,this.dateFormatValue,"/").getTime()&&(e=n.x.call("compareTwoDates",this.startDate.text,this.rightThresholdDay,this.dateFormat,"00:00","00:00")):this.stringToDate(this.rightThresholdDay,this.dateFormatValue,"/").getTime()!=this.stringToDate(this.toDate.text,this.dateFormatValue,"/").getTime()&&(e=n.x.call("compareTwoDates",this.toDate.text,this.rightThresholdDay,this.dateFormat,"00:00","00:00")),i[0]=t,i[1]=e,i[2]=t&&e,i},e.prototype.stringToDate=function(t,e,i){var l=e.toLowerCase().split(i),a=t.split(i),n=l.indexOf("mm"),s=l.indexOf("dd"),o=l.indexOf("yyyy"),r=parseInt(a[n],10);return r-=1,new Date(a[o],r,a[s])},e.prototype.resetOffendingDate=function(t){t[0]?!t[1]&&this.isSingleDate?this.startDate.text=this.toDateAsString:this.toDate.text=this.toDateAsString:this.startDate.text=this.fromDateAsString,this.refreshGrid()},e.prototype.refreshGrid=function(){this.alertShown=!1,this.entityId=this.entityCombo.selectedItem.content,this.currencyCode=this.ccyCombo.selectedItem.content,this.selectedStartDate=this.startDate.text,this.selectedEndDate=this.toDate.text,this.processOption=this.allProcess?"A":"M",this.singleOrRange=this.isSingleDate?"S":"R",this.validateReportDates()&&this.refreshdetails(null)},e.prototype.updateData=function(){var t=this;this.actionPath="intraDayLiquidity.do?method=",this.actionMethod="listCurrencyProcessStatus",this.inputData.cbResult=function(e){t.inputDataResult(e)},this.entityId=this.entityCombo.selectedItem.content,this.selectedStartDate=this.startDate.text,this.selectedEndDate=this.toDate.text,this.processOption=this.allProcess?"A":"M",this.singleOrRange=this.isSingleDate?"S":"R",this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams=[],this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.selectedStartDate=this.selectedStartDate,this.requestParams.selectedEndDate=this.selectedEndDate,this.requestParams.processOption=this.processOption,this.requestParams.singleOrRange=this.singleOrRange,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.cancelCalculation=function(){this.canceled=!0,this.entityId=this.entityCombo.selectedItem.content,this.currencyCode=this.ccyCombo.selectedItem.content,this.selectedStartDate=this.startDate.text,this.selectedEndDate=this.toDate.text,this.processOption=this.allProcess?"A":"M",this.singleOrRange=this.isSingleDate?"S":"R",n.x.call("cancelCalculation",this.entityId,this.currencyCode,this.selectedStartDate,this.selectedEndDate,this.processOption,this.singleOrRange,this.singleOrRange)},e.prototype.checkValidationDates=function(t,e,i){if("R"==i){if(null==t||""==t||t.length<10||null==e||""==e||e.length<10)return!1;if(0==n.x.call("compareTwoDates",this.selectedStartDate,this.selectedEndDate,this.dateFormat))return!1}else if(null==t||""==t||t.length<10)return!1;return!0},e.prototype.entityComboChange=function(){this.labelStatus.color="",this.labelStatus.text="",this.selectedEntity.text=this.entityCombo.selectedItem.value,this.entityId=this.entityCombo.selectedItem.content,this.currencyCode="",this.alertShown=!1,this.updateData()},e.prototype.currencyComboChange=function(){this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.refreshGrid()},e.prototype.refreshdetails=function(t){var e=this;this.entityId=this.entityCombo.selectedItem.content,this.currencyCode=this.ccyCombo.selectedItem.content,this.selectedStartDate=this.startDate.text,this.selectedEndDate=this.toDate.text,this.processOption=this.allProcess?"A":"M",this.singleOrRange=this.isSingleDate?"S":"R",this.sequenceNumber=n.x.call("eval","sequenceNumber"),this.checkValidationDates(this.selectedStartDate,this.selectedEndDate,this.singleOrRange)&&(this.inputData.cbResult=function(t){e.inputDataRefresh(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="intraDayLiquidity.do?method=",this.actionMethod="listCurrencyProcessStatus",this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.selectedEntity.text=this.entityCombo.selectedItem.value,this.requestParams=[],this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.selectedStartDate=this.selectedStartDate,this.requestParams.selectedEndDate=this.selectedEndDate,this.requestParams.processOption=this.processOption,this.requestParams.singleOrRange=this.singleOrRange,this.requestParams.sequenceNumber=this.sequenceNumber,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.changeProcessOption=function(){this.labelStatus.color="",this.labelStatus.text="",this.allProcess="M"!=this.calculateGroup.selectedValue,this.refreshGrid()},e.prototype.changeDateGroup=function(){this.labelStatus.color="",this.labelStatus.text="","S"==this.dateGroup.selectedValue?(this.startDate.formatString=this.dateFormatValue,this.startDate.selectedDate=new Date(n.j.parseDate(this.selecteDefaultDate,this.dateFormatValue.toUpperCase())),this.isSingleDate=!0,this.toHbox.visible=!1):(this.startDate.formatString=this.dateFormatValue,this.startDate.selectedDate=new Date(n.j.parseDate(this.fromDateAsString,this.dateFormatValue.toUpperCase())),this.toDate.formatString=this.dateFormatValue,this.toDate.selectedDate=new Date(n.j.parseDate(this.toDateAsString,this.dateFormatValue.toUpperCase())),this.isSingleDate=!1,this.toHbox.visible=!0),this.refreshGrid()},e.prototype.ChangeDate=function(t,e){this.labelStatus.color="",this.labelStatus.text="",this.testValidateDateField(t,e)&&this.refreshGrid()},e.prototype.testValidateDateField=function(t,e){var i=this.validateDateField(t,e);if(i){var l=this.testThresholdDates();l[2]||(this.swtAlert.warning(n.Wb.getPredictMessage("warn.outsideRange",null)),this.resetOffendingDate(l),i=!1)}return i},e.prototype.doHelp=function(){try{n.x.call("help")}catch(t){n.Wb.logError(t,this.moduleId,"IlmCurrencyCalculation","doHelp",this.errorLocation)}},e.prototype.errorHandler=function(t){t.detail==n.c.OK&&this.closeWindow&&(this.closeWindow=!1)},e}(n.yb),u=[{path:"",component:c}],h=(s.l.forChild(u),function(){return function(){}}()),d=i("pMnS"),b=i("RChO"),g=i("t6HQ"),p=i("WFGK"),m=i("5FqG"),D=i("Ip0R"),w=i("gIcY"),y=i("t/Na"),R=i("sE5F"),S=i("OzfB"),f=i("T7CS"),C=i("S7LP"),I=i("6aHO"),v=i("WzUx"),x=i("A7o+"),A=i("zCE2"),L=i("Jg5P"),P=i("3R0m"),G=i("hhbb"),J=i("5rxC"),O=i("Fzqc"),T=i("21Lb"),E=i("hUWP"),B=i("3pJQ"),M=i("V9q+"),F=i("VDKW"),W=i("kXfT"),N=i("BGbe");i.d(e,"IlmCurrencyCalculationModuleNgFactory",function(){return q}),i.d(e,"RenderType_IlmCurrencyCalculation",function(){return k}),i.d(e,"View_IlmCurrencyCalculation_0",function(){return j}),i.d(e,"View_IlmCurrencyCalculation_Host_0",function(){return _}),i.d(e,"IlmCurrencyCalculationNgFactory",function(){return Z});var q=l.Gb(h,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[d.a,b.a,g.a,p.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,Z]],[3,l.n],l.J]),l.Rb(4608,D.m,D.l,[l.F,[2,D.u]]),l.Rb(4608,w.c,w.c,[]),l.Rb(4608,w.p,w.p,[]),l.Rb(4608,y.j,y.p,[D.c,l.O,y.n]),l.Rb(4608,y.q,y.q,[y.j,y.o]),l.Rb(5120,y.a,function(t){return[t,new n.tb]},[y.q]),l.Rb(4608,y.m,y.m,[]),l.Rb(6144,y.k,null,[y.m]),l.Rb(4608,y.i,y.i,[y.k]),l.Rb(6144,y.b,null,[y.i]),l.Rb(4608,y.f,y.l,[y.b,l.B]),l.Rb(4608,y.c,y.c,[y.f]),l.Rb(4608,R.c,R.c,[]),l.Rb(4608,R.g,R.b,[]),l.Rb(5120,R.i,R.j,[]),l.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),l.Rb(4608,R.f,R.a,[]),l.Rb(5120,R.d,R.k,[R.h,R.f]),l.Rb(5120,l.b,function(t,e){return[S.j(t,e)]},[D.c,l.O]),l.Rb(4608,f.a,f.a,[]),l.Rb(4608,C.a,C.a,[]),l.Rb(4608,I.a,I.a,[l.n,l.L,l.B,C.a,l.g]),l.Rb(4608,v.c,v.c,[l.n,l.g,l.B]),l.Rb(4608,v.e,v.e,[v.c]),l.Rb(4608,x.l,x.l,[]),l.Rb(4608,x.h,x.g,[]),l.Rb(4608,x.c,x.f,[]),l.Rb(4608,x.j,x.d,[]),l.Rb(4608,x.b,x.a,[]),l.Rb(4608,x.k,x.k,[x.l,x.h,x.c,x.j,x.b,x.m,x.n]),l.Rb(4608,v.i,v.i,[[2,x.k]]),l.Rb(4608,v.r,v.r,[v.L,[2,x.k],v.i]),l.Rb(4608,v.t,v.t,[]),l.Rb(4608,v.w,v.w,[]),l.Rb(1073742336,s.l,s.l,[[2,s.r],[2,s.k]]),l.Rb(1073742336,D.b,D.b,[]),l.Rb(1073742336,w.n,w.n,[]),l.Rb(1073742336,w.l,w.l,[]),l.Rb(1073742336,A.a,A.a,[]),l.Rb(1073742336,L.a,L.a,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,P.a,P.a,[]),l.Rb(1073742336,x.i,x.i,[]),l.Rb(1073742336,v.b,v.b,[]),l.Rb(1073742336,y.e,y.e,[]),l.Rb(1073742336,y.d,y.d,[]),l.Rb(1073742336,R.e,R.e,[]),l.Rb(1073742336,G.b,G.b,[]),l.Rb(1073742336,J.b,J.b,[]),l.Rb(1073742336,S.c,S.c,[]),l.Rb(1073742336,O.a,O.a,[]),l.Rb(1073742336,T.d,T.d,[]),l.Rb(1073742336,E.c,E.c,[]),l.Rb(1073742336,B.a,B.a,[]),l.Rb(1073742336,M.a,M.a,[[2,S.g],l.O]),l.Rb(1073742336,F.b,F.b,[]),l.Rb(1073742336,W.a,W.a,[]),l.Rb(1073742336,N.b,N.b,[]),l.Rb(1073742336,n.Tb,n.Tb,[]),l.Rb(1073742336,h,h,[]),l.Rb(256,y.n,"XSRF-TOKEN",[]),l.Rb(256,y.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,x.m,void 0,[]),l.Rb(256,x.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,s.i,function(){return[[{path:"",component:c}]]},[])])}),V=[[""]],k=l.Hb({encapsulation:0,styles:V,data:{}});function j(t){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{loadingImage:0}),l.Zb(402653184,3,{labelStatus:0}),l.Zb(402653184,4,{gridCanvas:0}),l.Zb(402653184,5,{entityCombo:0}),l.Zb(402653184,6,{ccyCombo:0}),l.Zb(402653184,7,{currencyLabel:0}),l.Zb(402653184,8,{selectedCcy:0}),l.Zb(402653184,9,{toDateLabel:0}),l.Zb(402653184,10,{entityLabel:0}),l.Zb(402653184,11,{selectedEntity:0}),l.Zb(402653184,12,{calculateGroupLabel:0}),l.Zb(402653184,13,{dateGroupLabel:0}),l.Zb(402653184,14,{calculateGroup:0}),l.Zb(402653184,15,{dateGroup:0}),l.Zb(402653184,16,{screens:0}),l.Zb(402653184,17,{screenAndReports:0}),l.Zb(402653184,18,{single:0}),l.Zb(402653184,19,{range:0}),l.Zb(402653184,20,{startDate:0}),l.Zb(402653184,21,{toDate:0}),l.Zb(402653184,22,{processButton:0}),l.Zb(402653184,23,{cancelButton:0}),l.Zb(402653184,24,{helpIcon:0}),l.Zb(402653184,25,{toHbox:0}),l.Zb(402653184,26,{gridRowEntity:0}),l.Zb(402653184,27,{gridRowCurrency:0}),l.Zb(402653184,28,{gridRowCalculateGroup:0}),l.Zb(402653184,29,{gridRowDateGroup:0}),l.Zb(402653184,30,{gridRowDates:0}),(t()(),l.Jb(30,0,null,null,103,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var l=!0,a=t.component;"creationComplete"===e&&(l=!1!==a.onLoad()&&l);return l},m.ad,m.hb)),l.Ib(31,4440064,null,0,n.yb,[l.r,n.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(32,0,null,0,101,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),l.Ib(33,4440064,null,0,n.ec,[l.r,n.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),l.Jb(34,0,null,0,75,"SwtCanvas",[["height","25%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(35,4440064,null,0,n.db,[l.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(36,0,null,0,73,"Grid",[["height","100%"],["verticalGap","2"],["width","100%"]],null,null,null,m.Cc,m.H)),l.Ib(37,4440064,null,0,n.z,[l.r,n.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(38,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(39,4440064,[[26,4],["gridRowEntity",4]],0,n.B,[l.r,n.i],null,null),(t()(),l.Jb(40,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(41,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(42,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","entityLabel"]],null,null,null,m.Yc,m.fb)),l.Ib(43,4440064,[[10,4],["entityLabel",4]],0,n.vb,[l.r,n.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),l.Jb(44,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,m.Ac,m.I)),l.Ib(45,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(46,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,n=t.component;"window:mousewheel"===e&&(a=!1!==l.Tb(t,47).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==n.entityComboChange()&&a);return a},m.Pc,m.W)),l.Ib(47,4440064,[[5,4],["entityCombo",4]],0,n.gb,[l.r,n.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(48,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(49,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(50,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["textAlign","left"]],null,null,null,m.Yc,m.fb)),l.Ib(51,4440064,[[11,4],["selectedEntity",4]],0,n.vb,[l.r,n.i],{id:[0,"id"],textAlign:[1,"textAlign"],fontWeight:[2,"fontWeight"]},null),(t()(),l.Jb(52,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(53,4440064,[[27,4],["gridRowCurrency",4]],0,n.B,[l.r,n.i],null,null),(t()(),l.Jb(54,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(55,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(56,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","currencyLabel"]],null,null,null,m.Yc,m.fb)),l.Ib(57,4440064,[[7,4],["currencyLabel",4]],0,n.vb,[l.r,n.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),l.Jb(58,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,m.Ac,m.I)),l.Ib(59,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(60,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","ccyCombo"],["width","85"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,n=t.component;"window:mousewheel"===e&&(a=!1!==l.Tb(t,61).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==n.currencyComboChange()&&a);return a},m.Pc,m.W)),l.Ib(61,4440064,[[6,4],["ccyCombo",4]],0,n.gb,[l.r,n.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(62,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(63,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(64,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"]],null,null,null,m.Yc,m.fb)),l.Ib(65,4440064,[[8,4],["selectedCcy",4]],0,n.vb,[l.r,n.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),l.Jb(66,0,null,0,14,"GridRow",[["height","26%"]],null,null,null,m.Bc,m.J)),l.Ib(67,4440064,[[28,4],["gridRowCalculateGroup",4]],0,n.B,[l.r,n.i],{height:[0,"height"]},null),(t()(),l.Jb(68,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(69,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(70,0,null,0,1,"SwtLabel",[],null,null,null,m.Yc,m.fb)),l.Ib(71,4440064,[[12,4],["calculateGroupLabel",4]],0,n.vb,[l.r,n.i],null,null),(t()(),l.Jb(72,0,null,0,8,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),l.Ib(73,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(74,0,null,0,6,"SwtRadioButtonGroup",[["align","vertical"],["id","calculateGroup"],["width","100%"]],null,[[null,"change"]],function(t,e,i){var l=!0,a=t.component;"change"===e&&(l=!1!==a.changeProcessOption()&&l);return l},m.ed,m.lb)),l.Ib(75,4440064,[[14,4],["calculateGroup",4]],1,n.Hb,[y.c,l.r,n.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),l.Zb(603979776,31,{radioItems:1}),(t()(),l.Jb(77,0,null,0,1,"SwtRadioItem",[["groupName","calculateGroup"],["id","screens"],["marginBottom","5"],["value","M"],["width","100%"]],null,null,null,m.fd,m.mb)),l.Ib(78,4440064,[[31,4],[16,4],["screens",4]],0,n.Ib,[l.r,n.i],{id:[0,"id"],width:[1,"width"],marginBottom:[2,"marginBottom"],groupName:[3,"groupName"],value:[4,"value"]},null),(t()(),l.Jb(79,0,null,0,1,"SwtRadioItem",[["groupName","calculateGroup"],["id","screenAndReports"],["selected","true"],["value","A"],["width","100%"]],null,null,null,m.fd,m.mb)),l.Ib(80,4440064,[[31,4],[17,4],["screenAndReports",4]],0,n.Ib,[l.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),l.Jb(81,0,null,0,14,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(82,4440064,[[29,4],["gridRowDateGroup",4]],0,n.B,[l.r,n.i],null,null),(t()(),l.Jb(83,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(84,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(85,0,null,0,1,"SwtLabel",[],null,null,null,m.Yc,m.fb)),l.Ib(86,4440064,[[13,4],["dateGroupLabel",4]],0,n.vb,[l.r,n.i],null,null),(t()(),l.Jb(87,0,null,0,8,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),l.Ib(88,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(89,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","dateGroup"],["width","100%"]],null,[[null,"change"]],function(t,e,i){var l=!0,a=t.component;"change"===e&&(l=!1!==a.changeDateGroup()&&l);return l},m.ed,m.lb)),l.Ib(90,4440064,[[15,4],["dateGroup",4]],1,n.Hb,[y.c,l.r,n.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),l.Zb(603979776,32,{radioItems:1}),(t()(),l.Jb(92,0,null,0,1,"SwtRadioItem",[["groupName","dateGroup"],["id","single"],["selected","true"],["value","S"],["width","145"]],null,null,null,m.fd,m.mb)),l.Ib(93,4440064,[[32,4],[18,4],["single",4]],0,n.Ib,[l.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),l.Jb(94,0,null,0,1,"SwtRadioItem",[["groupName","dateGroup"],["id","range"],["value","R"],["width","120"]],null,null,null,m.fd,m.mb)),l.Ib(95,4440064,[[32,4],[19,4],["range",4]],0,n.Ib,[l.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),l.Jb(96,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(97,4440064,[[30,4],["gridRowDates",4]],0,n.B,[l.r,n.i],null,null),(t()(),l.Jb(98,0,null,0,1,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(99,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(100,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,m.Ac,m.I)),l.Ib(101,4440064,null,0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(102,0,null,0,1,"SwtDateField",[["id","startDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var a=!0,n=t.component;"change"===e&&(a=!1!==n.ChangeDate(l.Tb(t,103),"fromDateAsString")&&a);return a},m.Tc,m.ab)),l.Ib(103,4308992,[[20,4],["startDate",4]],0,n.lb,[l.r,n.i,l.T],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{changeEventOutPut:"change"}),(t()(),l.Jb(104,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),l.Ib(105,4440064,[[25,4],["toHbox",4]],0,n.A,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(106,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","toDateLabel"],["width","30"]],null,null,null,m.Yc,m.fb)),l.Ib(107,4440064,[[9,4],["toDateLabel",4]],0,n.vb,[l.r,n.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),l.Jb(108,0,null,0,1,"SwtDateField",[["id","toDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var a=!0,n=t.component;"change"===e&&(a=!1!==n.ChangeDate(l.Tb(t,109),"toDateAsString")&&a);return a},m.Tc,m.ab)),l.Ib(109,4308992,[[21,4],["toDate",4]],0,n.lb,[l.r,n.i,l.T],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{changeEventOutPut:"change"}),(t()(),l.Jb(110,0,null,0,1,"SwtCanvas",[["height","70%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(111,4440064,[[4,4],["gridCanvas",4]],0,n.db,[l.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(112,0,null,0,21,"SwtCanvas",[["height","5%"],["id","canvasButtons"],["marginTop","5"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(113,4440064,null,0,n.db,[l.r,n.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],marginTop:[3,"marginTop"]},null),(t()(),l.Jb(114,0,null,0,19,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(115,4440064,null,0,n.C,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(116,0,null,0,5,"HBox",[["paddingLeft","5"],["width","70%"]],null,null,null,m.Dc,m.K)),l.Ib(117,4440064,null,0,n.C,[l.r,n.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(118,0,null,0,1,"SwtButton",[["id","processButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,a=t.component;"click"===e&&(l=!1!==a.launchCalculationProcess()&&l);return l},m.Mc,m.T)),l.Ib(119,4440064,[[22,4],["processButton",4]],0,n.cb,[l.r,n.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(120,0,null,0,1,"SwtButton",[["id","cancelButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,a=t.component;"click"===e&&(l=!1!==a.cancelCalculation()&&l);return l},m.Mc,m.T)),l.Ib(121,4440064,[[23,4],["cancelButton",4]],0,n.cb,[l.r,n.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(122,0,null,0,11,"HBox",[["paddingRight","5"],["width","30%"]],null,null,null,m.Dc,m.K)),l.Ib(123,4440064,null,0,n.C,[l.r,n.i],{width:[0,"width"],paddingRight:[1,"paddingRight"]},null),(t()(),l.Jb(124,0,null,0,5,"HBox",[["width","90%"]],null,null,null,m.Dc,m.K)),l.Ib(125,4440064,null,0,n.C,[l.r,n.i],{width:[0,"width"]},null),(t()(),l.Jb(126,0,null,0,1,"SwtLabel",[["fontSize","12"],["fontWeight","bold"],["id","labelStatus"]],null,null,null,m.Yc,m.fb)),l.Ib(127,4440064,[[3,4],["labelStatus",4]],0,n.vb,[l.r,n.i],{id:[0,"id"],fontSize:[1,"fontSize"],fontWeight:[2,"fontWeight"]},null),(t()(),l.Jb(128,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),l.Ib(129,114688,[[2,4],["loadingImage",4]],0,n.xb,[l.r],null,null),(t()(),l.Jb(130,0,null,0,3,"HBox",[["paddingTop","2"],["width","10%"]],null,null,null,m.Dc,m.K)),l.Ib(131,4440064,null,0,n.C,[l.r,n.i],{width:[0,"width"],paddingTop:[1,"paddingTop"]},null),(t()(),l.Jb(132,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var l=!0,a=t.component;"click"===e&&(l=!1!==a.doHelp()&&l);return l},m.Wc,m.db)),l.Ib(133,4440064,[[24,4],["helpIcon",4]],0,n.rb,[l.r,n.i],{id:[0,"id"]},{onClick_:"click"})],function(t,e){t(e,31,0,"100%","100%");t(e,33,0,"100%","100%","5","5","5","5");t(e,35,0,"100%","25%");t(e,37,0,"2","100%","100%"),t(e,39,0);t(e,41,0,"10%");t(e,43,0,"entityLabel","bold");t(e,45,0,"15%");t(e,47,0,"entity","135","entityCombo");t(e,49,0,"10%");t(e,51,0,"selectedEntity","left","normal"),t(e,53,0);t(e,55,0,"10%");t(e,57,0,"currencyLabel","bold");t(e,59,0,"15%");t(e,61,0,"currency","85","ccyCombo");t(e,63,0,"10%");t(e,65,0,"selectedCcy","normal");t(e,67,0,"26%");t(e,69,0,"10%"),t(e,71,0);t(e,73,0,"40%");t(e,75,0,"calculateGroup","100%","vertical");t(e,78,0,"screens","100%","5","calculateGroup","M");t(e,80,0,"screenAndReports","100%","calculateGroup","A","true"),t(e,82,0);t(e,84,0,"10%"),t(e,86,0);t(e,88,0,"40%");t(e,90,0,"dateGroup","100%","horizontal");t(e,93,0,"single","145","dateGroup","S","true");t(e,95,0,"range","120","dateGroup","R"),t(e,97,0);t(e,99,0,"10%");t(e,101,0,"10%");t(e,103,0,"0-9/","startDate","70");t(e,105,0,"50%");t(e,107,0,"toDateLabel","30","normal");t(e,109,0,"0-9/","toDate","70");t(e,111,0,"100%","70%");t(e,113,0,"canvasButtons","100%","5%","5");t(e,115,0,"100%");t(e,117,0,"70%","5");t(e,119,0,"processButton",!0);t(e,121,0,"cancelButton",!0);t(e,123,0,"30%","5");t(e,125,0,"90%");t(e,127,0,"labelStatus","12","bold"),t(e,129,0);t(e,131,0,"10%","2");t(e,133,0,"helpIcon")},null)}function _(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-ilm-currency-calculation",[],null,null,null,j,k)),l.Ib(1,4440064,null,0,c,[n.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var Z=l.Fb("app-ilm-currency-calculation",c,_,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);