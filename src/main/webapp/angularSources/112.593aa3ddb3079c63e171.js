(window.webpackJsonp=window.webpackJsonp||[]).push([[112],{IIe6:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),o=i("mrSG"),l=i("ZYCi"),a=i("447K"),s=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.actionMethod="",n.jsonReader=new a.L,n.inputData=new a.G(n.commonService),n.baseURL=a.Wb.getBaseURL(),n.moduleId="Predict",n.errorLocation=0,n.menuAccess="",n.deleteData=new a.G(n.commonService),n.screenVersion=new a.V(n.commonService),n.screenName="Message Formats Maintenance",n.versionDate="08/08/2023",n.versionNumber="1",n.showJsonPopup=null,n.swtAlert=new a.bb(e),n}return o.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.mainGrid=this.dataGridContainer.addChild(a.hb),this.mainGrid.allowMultipleSelection=!0,this.entityLabel.text=a.Wb.getPredictMessage("sweep.entity",null),this.entityCombo.toolTip=a.Wb.getPredictMessage("tooltip.selectEntityid",null),this.addButton.label=a.Wb.getPredictMessage("button.add",null),this.addButton.toolTip=a.Wb.getPredictMessage("tooltip.add",null),this.changeButton.label=a.Wb.getPredictMessage("button.change",null),this.changeButton.toolTip=a.Wb.getPredictMessage("tooltip.change",null),this.viewButton.label=a.Wb.getPredictMessage("button.view",null),this.viewButton.toolTip=a.Wb.getPredictMessage("tooltip.view",null),this.deleteButton.label=a.Wb.getPredictMessage("button.delete",null),this.deleteButton.toolTip=a.Wb.getPredictMessage("tooltip.delete",null),this.closeButton.label=a.Wb.getPredictMessage("sweep.close",null),this.closeButton.toolTip=a.Wb.getPredictMessage("tooltip.close",null),this.menuAccessId=a.x.call("eval","menuAccessId")},e.prototype.onLoad=function(){var t=this;this.requestParams=[],this.loadingImage.setVisible(!1),this.menuAccess&&""!==this.menuAccess&&(this.menuAccessId=Number(this.menuAccess)),this.initializeMenus(),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="messageformats.do?",this.actionMethod="method=displayAngular",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.mainGrid.onRowClick=function(e){t.cellClickEventHandler(e)}},e.prototype.cellClickEventHandler=function(t){this.mainGrid.selectedIndex>=0?(this.changeButton.enabled=!0,this.changeButton.buttonMode=!0,this.viewButton.enabled=!0,this.viewButton.buttonMode=!0,this.deleteButton.enabled=!0,this.deleteButton.buttonMode=!0):(this.changeButton.enabled=!1,this.changeButton.buttonMode=!1,this.viewButton.enabled=!1,this.viewButton.buttonMode=!1,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1)},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&(this.entityCombo.setComboData(this.jsonReader.getSelects()),this.defaultEntity=this.jsonReader.getSingletons().defaultEntity,this.entityCombo.selectedLabel=this.defaultEntity,this.defaultAcctType=this.jsonReader.getSingletons().accounttype,null!=this.defaultEntity&&(this.entityCombo.selectedLabel=this.defaultEntity),this.selectedEntity.text=this.entityCombo.selectedValue,!this.jsonReader.isDataBuilding())){var e={columns:this.lastRecievedJSON.messageformatsList.grid.metadata.columns};this.mainGrid.CustomGrid(e);var i=this.lastRecievedJSON.messageformatsList.grid.rows;i.size>0?(this.mainGrid.gridData=i,this.mainGrid.setRowSize=this.jsonReader.getRowSize()):this.mainGrid.gridData={size:0,row:[]},this.prevRecievedJSON=this.lastRecievedJSON}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error")},e.prototype.updateData=function(t){var e=this;this.requestParams=[],this.menuAccessId=a.x.call("eval","menuAccessId"),this.queueName=a.x.call("eval","queueName"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="messageformats.do?",this.actionMethod="method=displayAngular",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.queueName=this.queueName,this.requestParams.entityId=this.entityCombo.selectedLabel,this.requestParams.parentScreen="",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.mainGrid.onRowClick=function(t){e.cellClickEventHandler(t)}},e.prototype.getSelectedList=function(){for(var t=this.mainGrid.selectedItems,e="",i=0;i<t.length;i++)t[i]&&(e=e+t[i].sweepId.content+",");return e},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.doOpenChildWindow=function(t){"add"==t?a.x.call("openChildWindow",t,"",this.jsonReader.getSingletons().defaultEntity,"","","","","","",this.entityCombo.selectedValue):a.x.call("openChildWindow",t,this.mainGrid.selectedItem.formatId.content,this.jsonReader.getSingletons().defaultEntity,this.mainGrid.selectedItem.formatName.content,this.mainGrid.selectedItem.formatType1.content,this.mainGrid.selectedItem.fieldDelimeter.content,this.mainGrid.selectedItem.hexaFldDelimeter.content,this.mainGrid.selectedItem.msgSeparator.content,this.mainGrid.selectedItem.hexaMsgSeparator.content,this.entityCombo.selectedValue)},e.prototype.doDeleteAccount=function(){try{a.c.yesLabel="Yes",a.c.noLabel="No";var t=a.Wb.getPredictMessage("confirm.delete");this.swtAlert.confirm(t,"Alert",a.c.OK|a.c.CANCEL,null,this.deleteAccount.bind(this))}catch(e){a.Wb.logError(e,this.moduleId,"MessageFormat","doDeleteAccount",this.errorLocation)}},e.prototype.deleteAccount=function(t){var e=this;try{t.detail===a.c.OK&&(this.deleteData.cbResult=function(t){e.deleteDataResult(t)},this.deleteCategory())}catch(i){a.Wb.logError(i,this.moduleId,"MessageFormat","deleteAccount",this.errorLocation)}},e.prototype.deleteCategory=function(){try{this.requestParams=[],this.actionPath="messageformats.do?",this.actionMethod="method=deleteAngular",this.requestParams.selectedEntityId=this.jsonReader.getSingletons().defaultEntity,this.requestParams.selectedFormatId=this.mainGrid.selectedItem.formatId.content,this.deleteData.url=this.baseURL+this.actionPath+this.actionMethod,this.deleteData.send(this.requestParams)}catch(t){a.Wb.logError(t,this.moduleId,"MessageFormat","deleteCategory",this.errorLocation)}},e.prototype.deleteDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.jsonReader.setInputJSON(t),this.jsonReader.getRequestReplyStatus()?this.updateData(t):"DataIntegrityViolationExceptioninDelete"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.error(a.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninDelete")+a.Wb.getPredictMessage("alert.ContactSysAdm"),"Error"):this.swtAlert.error("Error occurred, Please contact your System Administrator: \n"+this.jsonReader.getRequestReplyMessage(),"Error"))},e.prototype.doHelp=function(){a.x.call("help")},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.enablePrintButton=function(t){this.printButton.enabled=t,this.printButton.buttonMode=t},e.prototype.printPage=function(){try{a.x.call("printPage")}catch(t){a.Wb.logError(t,this.moduleId,"className","printPage",0)}},e.prototype.keyDownEventHandler=function(t){},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.versionDate);var t=new a.n("Show JSON");t.MenuItemSelect=this.showJSONSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showJSONSelect=function(t){this.showJsonPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastReceivedJSON}),this.showJsonPopup.width="700",this.showJsonPopup.title="Last Received JSON",this.showJsonPopup.height="500",this.showJsonPopup.enableResize=!1,this.showJsonPopup.showControls=!0,this.showJsonPopup.display()},e}(a.yb),d=[{path:"",component:s}],u=(l.l.forChild(d),function(){return function(){}}()),r=i("pMnS"),c=i("RChO"),h=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),m=i("Ip0R"),g=i("gIcY"),w=i("t/Na"),f=i("sE5F"),R=i("OzfB"),y=i("T7CS"),v=i("S7LP"),C=i("6aHO"),B=i("WzUx"),I=i("A7o+"),S=i("zCE2"),D=i("Jg5P"),M=i("3R0m"),k=i("hhbb"),L=i("5rxC"),P=i("Fzqc"),J=i("21Lb"),N=i("hUWP"),T=i("3pJQ"),A=i("V9q+"),x=i("VDKW"),O=i("kXfT"),G=i("BGbe");i.d(e,"MessageFormatsModuleNgFactory",function(){return E}),i.d(e,"RenderType_MessageFormats",function(){return _}),i.d(e,"View_MessageFormats_0",function(){return F}),i.d(e,"View_MessageFormats_Host_0",function(){return q}),i.d(e,"MessageFormatsNgFactory",function(){return H});var E=n.Gb(u,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[r.a,c.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,H]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,g.c,g.c,[]),n.Rb(4608,g.p,g.p,[]),n.Rb(4608,w.j,w.p,[m.c,n.O,w.n]),n.Rb(4608,w.q,w.q,[w.j,w.o]),n.Rb(5120,w.a,function(t){return[t,new a.tb]},[w.q]),n.Rb(4608,w.m,w.m,[]),n.Rb(6144,w.k,null,[w.m]),n.Rb(4608,w.i,w.i,[w.k]),n.Rb(6144,w.b,null,[w.i]),n.Rb(4608,w.f,w.l,[w.b,n.B]),n.Rb(4608,w.c,w.c,[w.f]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.g,f.b,[]),n.Rb(5120,f.i,f.j,[]),n.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),n.Rb(4608,f.f,f.a,[]),n.Rb(5120,f.d,f.k,[f.h,f.f]),n.Rb(5120,n.b,function(t,e){return[R.j(t,e)]},[m.c,n.O]),n.Rb(4608,y.a,y.a,[]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,C.a,C.a,[n.n,n.L,n.B,v.a,n.g]),n.Rb(4608,B.c,B.c,[n.n,n.g,n.B]),n.Rb(4608,B.e,B.e,[B.c]),n.Rb(4608,I.l,I.l,[]),n.Rb(4608,I.h,I.g,[]),n.Rb(4608,I.c,I.f,[]),n.Rb(4608,I.j,I.d,[]),n.Rb(4608,I.b,I.a,[]),n.Rb(4608,I.k,I.k,[I.l,I.h,I.c,I.j,I.b,I.m,I.n]),n.Rb(4608,B.i,B.i,[[2,I.k]]),n.Rb(4608,B.r,B.r,[B.L,[2,I.k],B.i]),n.Rb(4608,B.t,B.t,[]),n.Rb(4608,B.w,B.w,[]),n.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,g.n,g.n,[]),n.Rb(1073742336,g.l,g.l,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,D.a,D.a,[]),n.Rb(1073742336,g.e,g.e,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,I.i,I.i,[]),n.Rb(1073742336,B.b,B.b,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,w.d,w.d,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,k.b,k.b,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,R.c,R.c,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,J.d,J.d,[]),n.Rb(1073742336,N.c,N.c,[]),n.Rb(1073742336,T.a,T.a,[]),n.Rb(1073742336,A.a,A.a,[[2,R.g],n.O]),n.Rb(1073742336,x.b,x.b,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,G.b,G.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,u,u,[]),n.Rb(256,w.n,"XSRF-TOKEN",[]),n.Rb(256,w.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,I.m,void 0,[]),n.Rb(256,I.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,l.i,function(){return[[{path:"",component:s}]]},[])])}),W=[[""]],_=n.Hb({encapsulation:0,styles:W,data:{}});function F(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{entityLabel:0}),n.Zb(402653184,3,{selectedEntity:0}),n.Zb(402653184,4,{entityCombo:0}),n.Zb(402653184,5,{dataGridContainer:0}),n.Zb(402653184,6,{addButton:0}),n.Zb(402653184,7,{changeButton:0}),n.Zb(402653184,8,{deleteButton:0}),n.Zb(402653184,9,{viewButton:0}),n.Zb(402653184,10,{closeButton:0}),n.Zb(402653184,11,{printButton:0}),n.Zb(402653184,12,{loadingImage:0}),(t()(),n.Jb(12,0,null,null,47,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,o=t.component;"creationComplete"===e&&(n=!1!==o.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(13,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(14,0,null,0,45,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(15,4440064,null,0,a.ec,[n.r,a.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),n.Jb(16,0,null,0,13,"SwtCanvas",[["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(17,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(18,0,null,0,11,"HBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(19,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"],paddingRight:[3,"paddingRight"]},null),(t()(),n.Jb(20,0,null,0,9,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(21,4440064,null,0,a.ec,[n.r,a.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(22,0,null,0,7,"HBox",[["height","25"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(23,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(24,0,null,0,1,"SwtLabel",[["id","entityLabel"],["width","100"]],null,null,null,p.Yc,p.fb)),n.Ib(25,4440064,[[2,4],["entityLabel",4]],0,a.vb,[n.r,a.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),n.Jb(26,0,null,0,1,"SwtComboBox",[["dataLabel","entityList"],["id","entityCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var o=!0,l=t.component;"window:mousewheel"===e&&(o=!1!==n.Tb(t,27).mouseWeelEventHandler(i.target)&&o);"change"===e&&(o=!1!==l.updateData(i)&&o);return o},p.Pc,p.W)),n.Ib(27,4440064,[[4,4],["entityCombo",4]],0,a.gb,[n.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(28,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"]],null,null,null,p.Yc,p.fb)),n.Ib(29,4440064,[[3,4],["selectedEntity",4]],0,a.vb,[n.r,a.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(30,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","dataGridContainer"],["marginTop","10"],["minHeight","100"],["paddingBottom","5"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(31,4440064,[[5,4],["dataGridContainer",4]],0,a.db,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minHeight:[4,"minHeight"],paddingBottom:[5,"paddingBottom"],marginTop:[6,"marginTop"],border:[7,"border"]},null),(t()(),n.Jb(32,0,null,0,27,"SwtCanvas",[["height","35"],["id","canvasButtons"],["marginTop","5"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(33,4440064,null,0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],marginTop:[3,"marginTop"]},null),(t()(),n.Jb(34,0,null,0,25,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(35,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(36,0,null,0,11,"HBox",[["paddingLeft","5"],["width","50%"]],null,null,null,p.Dc,p.K)),n.Ib(37,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(38,0,null,0,1,"SwtButton",[["id","addButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doOpenChildWindow("add")&&n);return n},p.Mc,p.T)),n.Ib(39,4440064,[[6,4],["addButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(40,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doOpenChildWindow("change")&&n);return n},p.Mc,p.T)),n.Ib(41,4440064,[[7,4],["changeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(42,0,null,0,1,"SwtButton",[["enabled","false"],["id","viewButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doOpenChildWindow("view")&&n);return n},p.Mc,p.T)),n.Ib(43,4440064,[[9,4],["viewButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(44,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doDeleteAccount()&&n);return n},p.Mc,p.T)),n.Ib(45,4440064,[[8,4],["deleteButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(46,0,null,0,1,"SwtButton",[["id","closeButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.closeHandler()&&n);return n},p.Mc,p.T)),n.Ib(47,4440064,[[10,4],["closeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(48,0,null,0,11,"HBox",[["horizontalAlign","right"],["paddingTop","5"],["width","50%"]],null,null,null,p.Dc,p.K)),n.Ib(49,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),n.Jb(50,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,p.Yc,p.fb)),n.Ib(51,4440064,[["dataBuildingText",4]],0,a.vb,[n.r,a.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),n.Jb(52,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,p.Yc,p.fb)),n.Ib(53,4440064,[["lostConnectionText",4]],0,a.vb,[n.r,a.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),n.Jb(54,0,null,0,1,"SwtButton",[["id","printButton"],["styleName","printIcon"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.printPage()&&n);"keyDown"===e&&(n=!1!==o.keyDownEventHandler(i)&&n);return n},p.Mc,p.T)),n.Ib(55,4440064,[[11,4],["printButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(56,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","spread-profile"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(57,4440064,null,0,a.rb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"}),(t()(),n.Jb(58,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),n.Ib(59,114688,[[12,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,13,0,"100%","100%");t(e,15,0,"vBox1","100%","100%","5","5","5","5");t(e,17,0,"100%");t(e,19,0,"100%","100%","5","5");t(e,21,0,"0","100%","100%");t(e,23,0,"100%","25");t(e,25,0,"entityLabel","100");t(e,27,0,"entityList","135","entityCombo");t(e,29,0,"selectedEntity","10","normal");t(e,31,0,"dataGridContainer","canvasWithGreyBorder","100%","100%","100","5","10","false");t(e,33,0,"canvasButtons","100%","35","5");t(e,35,0,"100%");t(e,37,0,"50%","5");t(e,39,0,"addButton","70");t(e,41,0,"changeButton","70","false");t(e,43,0,"viewButton","70","false");t(e,45,0,"deleteButton","70","false");t(e,47,0,"closeButton","70");t(e,49,0,"right","50%","5");t(e,51,0,"false","red");t(e,53,0,"false","red");t(e,55,0,"printButton","printIcon",!0);t(e,57,0,"helpIcon","true",!0,"spread-profile"),t(e,59,0)},null)}function q(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-message-formats",[],null,null,null,F,_)),n.Ib(1,4440064,null,0,s,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var H=n.Fb("app-message-formats",s,q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);