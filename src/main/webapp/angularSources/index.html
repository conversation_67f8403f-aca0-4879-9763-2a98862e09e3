<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>PFCAngular7</title>
  <base href="./">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <script>

    function getBundle(type, key, defaultValue)
    {
      try{
        var labelValue = new String(label[type][key]);
        if(labelValue=="undefined"|| labelValue==null){
          return defaultValue;
        }else{
          return ""+labelValue.replace(/&nbsp;/g, ' ').replace(/'/g, "'");
        }
      }catch(err){
        return defaultValue;
      }
    }
  </script>
<link rel="stylesheet" href="angularSources/styles.ac9086e019f60e6cc2d8.css"></head>
<body>
  <app-root></app-root>
<script>
  function getBundle(type, key, defaultValue)
  {
    try{
      var labelValue = new String(label[type][key]);
      if(labelValue=="undefined"|| labelValue==null){
        return defaultValue;
      }else{
        return ""+labelValue.replace(/&nbsp;/g, ' ').replace(/'/g, "'");
      }
    }catch(err){
      return defaultValue;
    }
  }

  function getMessage(label, lang){
	return	getMenuWindow().getMessage(label, lang);
}


  
// Auto-generated translation mock from C:\GitWorkspace\Predict1073Jacarta\java-migration\src\main\resources\dictionary_en.properties
window.getMenuWindow = function() {
  return {
    getMessage: function(label, lang) {
      const translations = {
        "ilmreport.keyword.label.runDateMinus7": "RunDate minus 7 days",
        "ilmreport.keyword.label.runDateMinus6": "RunDate minus 6 days",
        "alert.recordAlreadyExists": "Record already exists",
        "tooltip.openMovementFlag": "Include open movements",
        "movement.senderCorrespondent": "Sender's Correspondent",
        "tooltip.jobAccessList": "Indicate which users/roles will have access to reports in the Scheduled Report History screen, plus receive reports as email attachments",
        "screenheader.alertsummary": "There are a number of alerts to be actioned",
        "tooltip.details": "Details",
        "instancerecord.lastRaisedDatetime_tooltip": "Last Raised Date",
        "ilmreport.keyword.label.runDateMinus1": "RunDate minus 1 day",
        "systemGmtOffset": "System GMT Offset",
        "status.lastLogin": "LastLogin",
        "ilmreport.keyword.label.runDateMinus5": "RunDate minus 5 days",
        "ilmreport.keyword.label.runDateMinus4": "RunDate minus 4 days",
        "tooltip.jobOutputFileType": "Choose the output file type from the available options",
        "ilmreport.keyword.label.runDateMinus3": "RunDate minus 3 days",
        "label.accountschedulesweepdetails.title.window": "Account Schedule Sweep Details",
        "corrscode": "Correspondent Code",
        "ilmreport.keyword.label.runDateMinus2": "RunDate minus 2 days",
        "movement.uetr": "UETR",
        "sweepsearch.credited": "Account Credited",
        "label.ilmcalculationfailed": "ILM calculation completed with errors",
        "scenario.events.instAttr": "Instance Attribute",
        "label.forecastMonitorTemplateAdd.userId": "User ID",
        "ilmExcelReport.otherCriticalPaymentsOutflows": "Other Critical Payments - Outflows",
        "tooltip.forecastMonitor.selectEntity": "Select Entity",
        "tooltip.viewQAcclist": "View selected queue access",
        "tooltip.reset": "Reset configuration to defaults",
        "role.workQueueAccess.currency1": "Currency",
        "ilmReport.3ndMin": "3rd min",
        "tooltip.selectOther": "Select Other",
        "label.schedReportHist.outputLocation": "Output Location",
        "msd.heading.addColumns.value": "Value",
        "tooltip.changeSelAcName": "Change selected account name",
        "usermaintenance.userId": "User",
        "sweepAuthQueue.title.window": "Sweep Authorise Queue - SMART-Predict",
        "tooltip.sortValueDate": "Sort by value date",
        "preAdviceInput.column.date": "Value Date;",
        "tooltip.scenarioType": "Sort by Scenario Type",
        "scenario.scenarioId": "Scenario ID",
        "relogin.warning": "Do you want to refresh the menu window?",
        "acctMaintenance.name": "Name",
        "roleBasedControl.addScreen": "Add Role Based Control - SMART-Predict",
        "role.restrictLocations": "Restrict locations",
        "ilmScenario.label.throughputMonitoring": "Throughput Monitoring",
        "tooltip.sortDrAccID": "Sort by DR account ID",
        "ilmAccountGroupDetails.accntNotInGrp": "Accounts not in this group",
        "acctMaintenance.and": "and",
        "tooltip.reportButton": "Generate the report",
        "ilmAccountGroupDetails.ilmAccountgrpdetailsTitle": "Account Group Details Screen",
        "linked.account": "Account",
        "ilmExcelReport.standard": "Standard",
        "tooltip.selectBalanceDate": "Select balance date",
        "batchScheduler.confirm.removeJob": "Are you sure you want to remove the job?",
        "alert.entityMonitor.accessNotAvl": "Access not available for",
        "tooltip.sortLastExeStatus": "Sort by last execution status",
        "label.interfaceMonitor.title": "Interface Monitor",
        "label.entity.indicator": "Select indicator",
        "tooltip.centralMonitorOptions.selectFontSizeSmall": "Select Small Font Size",
        "ilmReport.singleDay": "Single Day",
        "errors.logon.licenseCode": "Licensing Error: Security Code is invalid.",
        "interfacemonitor.header.last": "Last",
        "changeBalance.preditInternal": "Predicted(internal)",
        "addjob.Tuesday": "Tuesday",
        "tooltip.selectTagged": "Select tagged",
        "tooltip.addEntity": "Add entity",
        "movementsearch.credit/debit": "Credit/Debit",
        "tooltip.scenarioSystem": "Sort by System Flag",
        "account.schedulesweep.tooltip.settleMethodDRCombo": "Select the Settlement Method for Debit Sweep",
        "ilmExcelReport.partyInfo": "Party Information",
        "alert.interfaceSettings.requiresChannelName": "Please provide MQ Channel Name",
        "centralMonitor.title.window": "Central Bank Monitor - SMART-Predict",
        "ilmReport.UsePartyCashflowData": "Use party cashflow data",
        "label.accountspecificsweepformat.alert.warningExistAccountData": "Data exists for this account. If you change the definition of the account <br>you must ensure that the existing data is consistent with the new settings",
        "confirm.unopen": "Are you sure you want to set the selected movement to Unopen?",
        "SweepFromBalance": "Sweep From Balance",
        "button.tooltip.schedReportHist.singleDate": "Choose Single Date",
        "tooltip.entityMonitor.entityOffset": "Use Entity Offset Time for each entity ",
        "account.schedulesweep.label.sumAccountLabel": "Sum Accounts",
        "alert.interestCharges.dateRange": "INVALID: The selected date range must not exceed 90 days",
        "label.accountattributehdr.tooltiptext": "Tooltip Text",
        "tooltip.acctBic": "Bank Identifier Code",
        "tooltip.totalBalance": "Total Balance",
        "ilmTransactionSet.description": "Description",
        "alertstage": "Alert Stage",
        "ilmReport.centralBankNotFound": "Warning: Central bank group is not defined for one or more selected currencies",
        "auditLog.auditLog": "Audit Log - SMART-Predict",
        "tooltip.sortApplyAccountCountry": "Sort by apply account country",
        "tooltip.selectMsgFormat": "Select a message format",
        "tooltip.enterPostingDateTo": "Enter posting date to",
        "ilmAccountGroupDetails.first": "First",
        "tooltip.movRetParam": "Movement retention parameter",
        "sweepsearch.id.sweepId": "Sweep ID is not proper",
        "errors.effectiveDateRequired": "Please select a value for effective date",
        "interfacemonitor.header.busy": "Busy",
        "main.alert.enable": "Do you want to enable alerts?",
        "acctMaintenance.tooltip.includeLoro": "include Loro",
        "metaGroup.category": "Category",
        "defaultaccountmaintenance.alert.currencyCode": "Currency Code cannot be set to 'All'",
        "manual.doesnotmatch": "You don't have access",
        "inputAuth.title.window": "Input Authorise - SMART-Predict",
        "errors.maxlength": "{0} cannot be greater than {1} characters",
        "manualInput.reference": "References",
        "tooltip.SelectCurrency": "Select currency",
        "tooltip.movement.amount1": "Amount",
        "ilmReport.CentralBankCollateral": "Central bank collateral",
        "maintenanceLog.logDate_Date": "Date",
        "label.interfaceMonitor.engineActionStop": "STOP",
        "ilmScenario.label.creditlineAvlbl": "Credit-line Availability",
        "tooltip.scenarioEndTime": "Enter End Time (hh:mm)",
        "internalmessage.role": "Role",
        "tooltip.refreshUserLog": "Refresh user log",
        "tooltip.sortModule": "Sort by module ID",
        "tooltip.notReqConOffered": "N - Not Required, C - Confirmed, M - Offered",
        "ilmExcelReport.titleIntradayLiquidityManagementReport": "Intraday Liquidity Management Report",
        "tooltip.schedreporthist.fileId": "Unique file identifier",
        "tooltip.addSecondaryForecast": "Select Secondary Forecast",
        "alert.enterValidFromDate": "Please enter from date.",
        "tooltip.signField": "Sort by Sign",
        "msdAdditionalColumns.reloadProfileTooltip": "Reload profile",
        "tooltip.SelectReportType": "Select report type",
        "account.schedSweep.sweepDirection.Both": "Both (fund or defund)",
        "tooltip.changeSelCurrencyInterest": "Change selected currency interest",
        "movementsearch.cash/sec": "Type",
        "role.workQueueAccessAdd.currCode": "Currency",
        "label.interfaceMonitor.bottomGrid.header.lastExecution": "Last Execution",
        "preAdviceInput.showLabel": "Show:",
        "sweepId.alert.sweepIdamended": "Sweep ID field has been amended - Please choose action again after screen refreshes",
        "ilmExcelReport.ccyMultiplierDisabled": "None",
        "acctSweepBalGrp.entity": "Entity ID",
        "maintenanceLog.userId": "User",
        "location.locationId": "Location ID",
        "tooltip.changeIlmParam": "Change ILM General Parameters",
        "ilmAccountGroupDetails.correspBank": "Correspondent Bank",
        "ilmanalysismonitor.balances.title": "Balances",
        "user.id": "User*",
        "sweepSearchList.entityCr": "Entity CR",
        "tooltip.account.loroToPredicted": "Include Account Class Loro in Prediction",
        "tooltip.selectAllUsers": "Select All Users",
        "bookMonitor.currency": "Currency",
        "genericDisplayMonitor.labelOf": "of",
        "alert.changeScreen": "Change Alert Message - SMART-Predict",
        "currencyMonitor.options.title": "Monitor Options",
        "tooltip.maintainSchedulerReportHist": "Allow the user to delete reports and re-send Emails from scheduled report history",
        "msd.tooltip.addColumns.value": "Value",
        "errors.float": "{0} must be a float",
        "tooltip.archiveId": "Enter Archive ID",
        "movement.currency": "Ccy",
        "tooltip.sortAlertStage": "Sort by alert stage",
        "sweepSearchList.entityDr": "Entity DR",
        "button.refresh": "Refresh",
        "maintenanceLogView.action": "Action",
        "movement.matchChanged": "This match has been changed",
        "scenario.tooltip.treeBreakDown1Combo": "Enter Scenario TreeBreakDown1 column",
        "tooltip.msgSelMvm": "Messages on selected movement",
        "tooltip.partyAlias": "Alias",
        "tooltip.BookBrkdown": "Select Book to view Book Monitor",
        "usermaintenance.userdetails": "User Details",
        "role.roleSweeping": "Sweeping",
        "multipleMvtActions.tooltip.dataSrcCombo": "Please choose a data source",
        "addjob.Weekly": "Weekly",
        "scenario.addScreen.title.window": "Add Scenario - SMART-Predict",
        "tooltip.sortByBalanceSource": "Sort by Balance Source",
        "label.personalCurrencyList.button.ok": "OK",
        "tooltip.sortOpenUnexpectedBalance": "Sort by Open Unexpected balance",
        "ilmExcelReport.unmonitoredTypes": "Unmonitored types",
        "acctSweepBalGrp.alert.changeAccount": "Record with same Sweep Account ID is already exist",
        "maintEvent.nextId": "Next Id",
        "movement.valueDate": "Value",
        "button.NAK": "NAK",
        "tooltip.oldPwd": "Old password",
        "ilmExcelReport.criticalPaymentsInflows": "Critical Payments - Inflows",
        "balMaintenance.user": "User ID",
        "tooltip.copyFrom": "Copy from",
        "interfacemonitor.header.filemanagers": "File Managers",
        "tooltip.changeSubmitSweepAmount": "Change submit sweep amount",
        "label.accountattributehdr.effectivedate": "Effective Date",
        "scenario.valueDateColumn": "Value Date Column",
        "tooltip.entity.crrLimit": "CRR Limit",
        "label.accountattributehdr.type": "Type",
        "tooltip.entityMonitor.viewMetagroupMonitor": "Select Metagroup to view Metagroup Monitor",
        "accountmonitor.today": "Today",
        "ilmReport.correspondentCollateral": "Correspondent collateral ",
        "role.entAccessList.name": "Name",
        "tootltip.cancelCcyProcessStatus": "Cancel calculation of the selected currency process",
        "entity.predict.thresholdParam.sweepCutoffLeadTime": "Sweep cut-off lead time",
        "ilm.ccyFilter": "Currency Filter",
        "tooltip.ClicktoSelectA/cId": "Click here to select account ID",
        "label.refreshRateSelectedMonimum": "Refresh rate selected was below minimum.\\nSet to 5 seconds",
        "ilmSummary.inc": "Inc %",
        "inputException.reject": "Reject",
        "button.tooltip.schedReportHist.endDate": "Select an End Date",
        "criticalMvtUpdate.set": "SET",
        "workflowmonitor.sweeps.title": "Sweeps",
        "tooltip.delayTime": "Enter Delay time in minutes (+/- 0-999) (positive/negative integer to indicate late/early delay)",
        "ilmSummary.tooltip.currencyEntityGroupAccount": "Currency, Entity, ILM Group, Account Hierarchy. Use Options button to configure content and default sorting. Right click tree for context menu.",
        "tooltip.RateWindow": "Change refresh rate",
        "label.schedreporthist.column.reportName": "Report Name",
        "tip.schedReportHist.exportStatus": "'Success' if the report was properly generated to the output location or otherwise 'Fail'",
        "account.schedulesweep.tooltip.otherBookDrCombo": "Select the book for other account Debit Sweep",
        "account.tooltip.iban": "Sort by IBAN",
        "tooltip.AcuntBrkdown": "Select Account to view Account Monitor",
        "usermaintenance.extAuthId": "External Auth ID",
        "ilmScenario.extraTransaction": "Extra Transaction Set",
        "centralMonitorOptions.fontSize": "Font Size",
        "errors.entity.reportingCurr.required": "Reporting currency is required.<BR>",
        "defineEditableFields.addScreen": "Add Editable Fields",
        "ilmccyparamsAdd.toolip.primaryAccountId": "This field should normally be the main nostro account for the currency.",
        "addjob.label.noAccessinILMScenario": "\\nThe ILM scenario {0} does not exists",
        "sweep.bookcode": "Book",
        "alert.mail.sendFail": "Mail sending failure",
        "tooltip.MoveSummaryDisplay": "Select Movement to view movement summary display",
        "login.notification.lastFailedLoginIp": "Last Failed Login IP",
        "tooltip.externalSOD": "Enter external SOD",
        "workflowmonitor.auth.title": "Authorise (All days)",
        "scenario.tooltip.accountId": "Check ACCOUNT ID",
        "tooltip.referenceField": "Sort by reference 1",
        "label.forecastAssumptionsAdd.title.window": "Forecast Monitor Assumptions (Add) - SMART-Predict",
        "ilmreport.dateGreaterThanCcyTimeframeDate": "Please enter a correct date, it must be less than the currency timeframe date",
        "instancerecord.valueDate_header": "Value Date",
        "currMonitor.group": "Group",
        "maintenanceevent.details.button.viewinfacility.label": "View In Facility",
        "sweepDetails.alert.microsoft": "Microsoft Internet Explorer",
        "defineEditableFields.viewScreen": "Editable Fields",
        "aliasTooltip.CurrencyAliasAdd": "Add currency alias",
        "criticalMvtUpdate.unit": "minutes",
        "tooltip.creditIntMsg": "Credit Internal Message Format",
        "group.groupId": "Group",
        "alert.currencyAccess": "Invalid: your role does not specify access to currencies/groups for this entity",
        "tooltip.selectBookcode": "Select a bookcode",
        "user.label.clickHeretoLogIn": "Click here to log in",
        "label.lastRefTime": "Last Refresh:",
        "acctSweepBalGrp.tooltip.entity": "Entity ID",
        "nonworkday.title.changeScreen": "Change Non Workday Maintenance",
        "messagefieldadd.alert.EndPositon": "End Position cannot be equal to 0",
        "ilaapccyparams.altGlobalGrp": "Alternative Global Ccy Group",
        "ilmAccountGroupDetails.target": "Target %",
        "button.entity.delete": "Delete selected position level",
        "accountmaintenance.Sweeping": "Sweeping",
        "ilmSummary.tooltip.external": "Actual balance                                                                                      ",
        "tooltip.movAccount": "Click to select account ID",
        "ilmReport.total": "Total",
        "tooltip.saturday": "Saturday",
        "tooltip.deleteSelCorrespondentAcct": "Delete selected Correspondent Account",
        "label.interfaceMonitor.bottomGrid.header.msgStatus": "Database Status",
        "defaultaccountmaintenance.title.mainWindow": "Default Account Maintenance - SMART-Predict",
        "reports.outputFormat": "Output Format",
        "rsaserver.error": "<b>Exception occurred when connecting to RSA server</b><br>{0}",
        "tooltip.reconMatch": "Reconcile Match",
        "inputconfig.header.interface": "Interface ID",
        "screen.invalid": "Invalid",
        "accountmaintenanceadd.assocForSweepBalnce": "Assoc. Accs for Sweep Balance",
        "group.cutoffOffset": "Offset",
        "scenario.tab.identification": "Identification",
        "scenario.events.value": "Value",
        "addlocationmaintenanace.title.mainWindow": "Add Location definition",
        "movement.external": "External",
        "messageFormats.formatType": "Format Type",
        "connectionPool.connectionId": "Connection ID",
        "centralBankMonitor.currencyCode": "Currency Code",
        "movement.matchingParty": "Matching Party",
        "scenario.signLbl": "SIGN",
        "addjob.Monday": "Monday",
        "tooltip.sortFacility": "Sort by facility",
        "currency.cutOffTime": "Cut Off",
        "scenario.events.tooltip.executeWhen": "Select execute when value",
        "throuputmonitor.actinf": "Inflows",
        "label.mail.from": "From",
        "CriticalPay.enabled": "Enabled?",
        "centralMonitor.fromDate": "Week Commencing",
        "tooltip.sortPartyAlias": "Sort by party alias",
        "button.genericdisplaymonitor.details": "Details",
        "connectionPool.connectionSQLInActive": "Inactive",
        "label.incomplete": "Incomplete",
        "userLog.date": "Date",
        "pwd.days": "days",
        "tooltip.sortDrAccMsgType": "Sort by DR account msg type",
        "role.fieldset.mvtInputUpdate": "Movement input/update",
        "tooltip.unopenSelectMvm": "Unopen selected movement",
        "tooltip.entityOffSetCentralServer": "Entity offset time to central server",
        "currencyGroupAccess.changeScreen": "Change Currency Group Access - SMART-Predict",
        "scenario.tooltip.entityId": "Check ENTITY ID",
        "ilmAccountGroupDetails.recordExistsAlert": "Record already exists",
        "movement.notoutstanding": "Movement is not outstanding",
        "movementDisplay.included": "Included",
        "tooltip.allowMultiMvtUpdatesFromMsd": "Allow the user to update up to <the specified Number> movements selected from the Movement Summary Display",
        "tooltip.copySelFormat": "Copy selected format",
        "tooltip.sortByCurrencyCode": "Sort by currency code",
        "logBalance.SODChange": "SOD Change",
        "tooltip.enterCounterId": "Enter a counter ID",
        "ilmReport.totalValueofCredit": "1. Total value of credit lines extended to  customers",
        "addjob.label.noAccessinILMGroup": "\\nThe ILM group {0} does not exists",
        "dstStartDateAsString": "Start Date",
        "locationaccess.access": "Access",
        "tooltip.sortUserName": "Sort by user name",
        "ccyAccMaintPeriod.tooltip.to": "To",
        "ilmExcelReport.actualSettlement": "Actual Settlement",
        "accountMonitor.label.incSum": "Inc 'Always Sum'",
        "messageFormats.outputParameter": "Output",
        "ilmReport.time": "Time",
        "userprofile.default": "Default profile",
        "tooltip.accountattributehdr.type": "Select a type",
        "connectionPool.alertConnectionKilled": "This connection appears to have been killed. The screen will be refreshed for more updated data.",
        "ilmAccountGroupDetails.labelId": "ID",
        "login.html.internalerror": "HTML internal error occurred: ",
        "tooltip.viewSelectedJob": "View selected job",
        "cashRsvrBal.heading.balanceTarget": "Balance/Target ",
        "tooltip.viewMatchSelMvm": "View match of selected movements",
        "tooltip.sortLastRun": "Sort by last run",
        "login.user.passwordExpired1": "Your password will expire in",
        "login.user.passwordExpired2": "&nbsp;days. Do you want to change it now?",
        "ilmanalysismonitor.interval": "Interval",
        "internalMesgs.alert.logoffUsers1": "Message cannot be sent to following users ",
        "tooltip.changeSweepTimeHM": "Change sweep time (hh:mm)",
        "internalMesgs.alert.logoffUsers2": "because they are not logged on",
        "ccyAccMaintPeriod.tooltip.time": "Time",
        "workqueueaccess.addScreen": "Add Work Queue Access - SMART-Predict",
        "tooltip.enterValueDate": "Enter value date (DD/MM/YYYY)",
        "button.menu": "Menu*",
        "button.details": "Details",
        "account.correspondentBIC": "Correspondent BIC",
        "throuputmonitor.forcout": "Outflows",
        "tooltip.acctSpecBtn": "Click to view account specific",
        "tooltip.ViewSelectedNote": "View selected note",
        "ilmReport.3ndMax": "3rd max",
        "scenarioSummary.currencyCode": "Ccy",
        "accountmonitor.month": "1 Month",
        "title.viewFormats": "View Format Details - SMART-Predict",
        "ilmReport.criticaltransactions": "Critical Transactions",
        "balanceSource": "Source",
        "criticalMvtUpdate.and": "and",
        "ilmSummary.preAdvice": "Pre-advice",
        "ilmExcelReport.summaryNetInflowsOutflows": "Net (Inflow - Outflow)",
        "title.user.password": "Please enter your password",
        "scenarioSummary.pending": "Pending",
        "org.apache.struts.taglib.bean.format.sql.date": "EEE, MMM d, ''yy",
        "ilmExcelReport.intradayThroughputOutflows": "Intraday Throughput - Outflows",
        "interfaceSettings.summaryDetails": "Show XML - Summary Details",
        "movementsearch.account.current": "Current",
        "sweep.saveError": "Error occurred in {1} sweep ID(s) <b>{0}</b>.<br>Please contact your System Administrator!",
        "ilmanalysismonitor.grid.scenario.tooltip": "Scenario ID",
        "ShowErrMsgWindowWithBtn.errorMessage1": "",
        "ShowErrMsgWindowWithBtn.errorMessage2": "Sweep has already been generated for the selected accounts",
        "ShowErrMsgWindowWithBtn.errorMessage3": "Do you want to continue?",
        "tooltip.addinterest": "Add new account interest",
        "tooltip.manSweep": "Manual sweeping",
        "ilmExcelReport.summaryMaximumbalance": "Maximum balance",
        "tooltip.canddext": "Enter cancel debit external",
        "addJob.title.jobType": "Job Type",
        "scheduler.rangeNotCovered": "Based on the configured schedule, the job will never run.<br>Please check the details.",
        "movementDisplay.securities": "Securities",
        "messageFields.lineNoDisplay": "Line Number",
        "movSearch.alert.time": "From time must be earlier than To time",
        "button.schedReportHist.reportjob": "Report Job",
        "activationflg": "Activation Flag",
        "startBalBefore": "Change",
        "format.fieldset": "New",
        "interfacemonitor.details.text.storedprocrunning": "Running",
        "label.interfaceExceptions.messageType": "Interface:",
        "alert.forecastMonitorOption.lesserThanNext": "The value should be lesser than the next value",
        "entitymaintenance.changeScreen": "Change Entity - SMART-Predict",
        "role.maintainAnyIlmScenario": "Maintain any ILM scenario",
        "tooltip.showdays": "Number of days to show",
        "ilmExcelReport.abc": "ABC",
        "interfacerulesmaintenance.title.mainWindow": "Interface Rule Maintenance - SMART-Predict",
        "connectionPool.tooltip.sqlExecStartTime": "Last SQL Start Statement Time",
        "alert.validation.dateRange": "Start Date must be lower than End Date",
        "ilmccyparamsAdd.alert.dbLinkFailed": "Error: Archive database link is incorrectly set",
        "tooltip.accountGroup.ilmGroupName": "Sort by Name",
        "sweep.saveSuccessfullyID": "Sweep(s) <b>{0}</b> successfully {1}",
        "title.exportErrors": "Export Error Log - SMART-Predict",
        "tooltip.enterTimeTo": "Enter time to",
        "fomat.cdExtInt": "Ext via Intermediary",
        "ilm.options.combinedChart_toolTip": "Check box to include Combined View chart in entity/currency tab",
        "accountmonitorbutton.Rate": "Rate",
        "ilmthroughputbreakdown.unsettledOutflows": "Unsettled Outflows",
        "ilmExcelReport.unencumberedLiquidAssets": "Unencumbered Liquid Assets",
        "movSearch.alert.dateComparison1": "'",
        "sweep.manual": "Manual",
        "reports.amountThresholdTypeAbs": "Absolute",
        "tooltip.HideAccountsAfterCutoff": "Hide Accounts After Cut-off",
        "scenario.events.mandatoryFiels": "Please fill all mandatory fields",
        "tooltip.ValueDateMMDDYY": "Enter value date (MM/DD/YYYY)",
        "mvmDisplay.tooltip.custText": "Custodian text",
        "ilmAccountGrpAdd.title.window.addScreen": "Add Account Group Details - SMART-Predict",
        "tooltip.deleteSelectedLocation": "Delete selected location",
        "movementsearch.finance": "Finance",
        "sweepDetail.additionalRef": "Additional Reference ",
        "scenario.events.ignore": "Ignore",
        "alert.ccyAccMaintPeriod.FieldIsEmpty": "Account ID, Fill Days and Fill Balance should not be empty",
        "movementDisplay.originalMessage": "Original Message",
        "tooltip.selectthreshold": "Select Currency Threshold",
        "ccyAccMaintPeriod.tooltip.delete": "Delete",
        "matchQuality.posLeveladd": "Position Level",
        "ilmExcelReport.cpType1": "CP_Type1",
        "tooltip.processButton": "Process the report",
        "maintenanceevent.details.button.reject.tooltip": "Reject the change",
        "tooltip.removeJob": "Remove job",
        "tooltip.viewSecondaryExternal": "View Secondary External",
        "qualityTab.tomorr": "Today+1",
        "usermaintenance.user": "User",
        "movement.orderingCustomer": "Ordering Customer",
        "accountmonitor.confirm.move.testMessageN": "Move balance to Predicted column?",
        "tooltip.sortSweepUser": "Sort by sweep user",
        "accountmonitor.prbalin": "Predicted Bal(In)",
        "tooltip.systemDateTesting": "System date for testing",
        "button.forecastMonitor.delete": "Delete",
        "exportPages.title": "Pages to export",
        "ilmScenario.scenarioName": "Name",
        "acc.SOD": "SOD",
        "tooltip.EnterBookcodeID": "Enter bookcode ID",
        "correspondentaccountmaintenance.add.entityId": "Entity",
        "accountmonitor.confirm.move.testMessageY": "Move balance to Loro/Curr column?",
        "criticalMvtUpdate.activeBetween": "Active Between",
        "metaGroup.id.mgroupId1": "Metagroup ID",
        "tooltip.minPwdlength": "Minimum password length",
        "ilmthroughput.currency": "Currency",
        "tooltip.notincluded": "Not included",
        "accountGroup.createdByUser": "Creator",
        "alert.interfaceMonitor.rateNAN": "Not a Number",
        "tooltip.4positionLevel": "Fourth position level",
        "ilmReport.reportDate": "Report Date:",
        "label.mail.popUpTitle": "Distribution list",
        "ilmExcelReport.unsettledNet": "Unsettled: Net (Credits - Debits)",
        "tooltip.movement.pred": "Predict Status",
        "tooltip.entity.dateFrom": "Date From",
        "mvmmatqsel.title.suspend": "Suspended Match Queue - SMART-Predict",
        "alert.schedreporthist.noAccessToFeature": "your role does not provide access to this feature, please contact your administrator for more details",
        "multiMvtActions.ccy": "Ccy",
        "ilmanalysismonitor.recalculateConfirm": "Are you sure? This may take some time ",
        "book.bookCode": "Book",
        "turnoverReport.showMain": "Show Main",
        "button.tooltip.msd.addColumn": "Add MSD additional column",
        "tip.schedReportHist.runDate": "The Date where the report was executed",
        "sweepSearchList.valueDate": "Value",
        "tooltip.addScenario": "Add new scenario",
        "alert.refreshRate.confirmMinRate": "Refresh rate selected was below minimum.\\nSet it to 5 seconds.",
        "messagefieldFormats.viewScreen": "View Message Field - SMART-Predict",
        "accountmaintenance.changeaccountdetails.addWindow": "Change Account Details - SMART-Predict",
        "tooltip.maintainScnButton": "Maintain Selected Scenario",
        "usermaintenance.Personal": "Personal",
        "movementsearch.allmatched": "All Matched",
        "tooltip.sortMetaGroupId": "Sort by metagroup ID",
        "tooltip.enterGroupIdentifier": "Enter group identifier",
        "tooltip.changeinterest": "Change selected account interest",
        "group.addgroupLvlCode": "Group Level",
        "matchQuality.paramDesc": "Parameter",
        "screen.connectionError": "CONNECTION ERROR",
        "label.entityMonitorOptions.defaultDays": "Default Days",
        "alert.currencyMonitor.confirmRefreshRate": "Refresh rate selected was below minimum.\\nSet it to 5 seconds.",
        "workflowmonitor.system.title": "System",
        "messageFormats.hexaMsgSeparator1": "Message Separator (Hex)",
        "fomat.ddExtInt": "Debit Ext via intermediary",
        "alert.ilmanalysis.nonValidValue": "Please enter a valid value (minutes)",
        "balmaintenance.startOfDayBalance": "Start of day balance",
        "multiMvtActions.amount": "Amount",
        "account.monitor": "Monitor",
        "tooltip.deleteselectedmsgformat": "Delete selected message format",
        "messageFields.sequenceNo": "Sequence Number",
        "tooltip.interfaceMonitor.total": "Total number of messages",
        "tooltip.addCentralAccountGroup": "Click to create a new dynamic group containing all ILM liquidity contributor accounts flagged as central bank members",
        "tooltip.sortByBvAdjustChange": "Sort by BV Adjust Change",
        "book.bookNameadd": "Book Name",
        "pwdchange.html.internalerror": "HTML internal error occurred: ",
        "label.setStyle": "Set Style",
        "scenario.matchColumn": "MATCH_ID Column",
        "label.acctBreakdown.balance": "Balance",
        "tooltip.glCode": "General Ledger Code",
        "tooltip.deleteSelGroup": "Delete selected group",
        "ilmReport.errorMissingData": "An error occurred while checking missing data, please see logs.",
        "tooltip.GrpLvlName1": "Sort by ",
        "tooltip.GrpLvlName2": "Sort by ",
        "metagroup.groupName": "Name",
        "label.matchChangedByanotherUser": "Match has been changed by another user ",
        "account.iban": "IBAN",
        "scenarioSummary.title": "Scenario Summary",
        "label.forecasttemplate.column.userid": "User ID",
        "MovMsgDisplay.title.mainWindow": "Movement Message Summary",
        "batchScheduler.header.scheduledId": "Id",
        "currency.preFlag": "Predict",
        "multiMvtActions.extraRef": "ExtraRef",
        "label.entityMonitorOptions.rate": "Auto-refresh rate",
        "cashRsvrBal.tooltip.balanceTarget": "Balance Value",
        "tooltip.clickNewValueDate": "Click to select new value date",
        "movementSummaryDisplay.drilldownTitleExternal": "External",
        "label.schedReportHist.exportError": "Export Error",
        "msd.tooltip.addColumns.column": "Column",
        "ilmExcelReport.firstLargestCounterParty": "Largest Counterparty",
        "ilm.options.global_toolTip": "Global group status: Y=Main global group; A=Alternative global group",
        "tooltip.noAccess": "No access",
        "tooltip.GrpLvlName3": "Sort by ",
        "accountmonitor.fnbalout": "Final Bal(Out)",
        "alert.scenarioSummary.noData": "No data to display",
        "instancerecord.otherIdType_header": "Other Id Type",
        "movementSummaryDisplay.drilldownTitleUnexpected": "Unexpected",
        "account.schedulesweep.tooltip.othersettleMethodDRCombo": "Select the Settlement Method for other account Credit Sweep",
        "button.send": "Send",
        "addSection.title.window": "Add Section - SMART-Predict",
        "alert.interfaceSettings.alertThresholdRange": "Please enter threshold greater than or equal to one minute",
        "alert.comboBox.invalidValue": "Please enter a valid value",
        "ilmanalysismonitor.tree.accForeCD": "Accum. Forecast C/D",
        "corporateAccount.fillMondatoryFields": "Please fill all Mandatory Fields (*)",
        "tooltip.refreshMainLog": "Refresh maintenance log",
        "ilmanalysismonitor.grid.use.tooltip": "Use",
        "matchQuality.difference": "Difference:",
        "ilmAccountGroupDetails.type": "Type",
        "alert.interfaceSettings.password": "Please enter a valid string",
        "ilmReport.dailyIntradayLiquidityUsageAll": "Daily Intraday Liquidity Usage (Not available for entity 'All')",
        "bookMonitor.title.window": "Book Monitor - SMART-Predict",
        "connectionPool.sqlId": "SQL ID",
        "passwordRules.confirm.close": "You have chosen to close the window. All unsaved changes will be lost - are you sure?",
        "tooltip.selectBalType": "Select balance type",
        "format.Debit": "Debit",
        "ilmanalysismonitor.legend.forecIncludeAct.Title": "FC*.",
        "ilmReport.correspondentBankGroup": "Correspondent Bank Group",
        "pwd.unsuccLogin": "Invalid attempts",
        "ilmanalysismonitor.grid.extsod": "Ext.SOD",
        "turnoverReport.showSub": "Show Sub",
        "tooltip.ILMgroupId": "Enter a group ID",
        "instancerecord.lastRaisedUser_tooltip": "Last Raised User",
        "metaGroup.addpopup": "Metagroup Add Popup",
        "label.entityMonitorOptions.entity": "Entity",
        "auditLog.mainLog": "Maintenance Log",
        "tooltip.accInstit": "Enter Account with Institution ID",
        "copyFromFormatId.title.mainWindow": "Copy From Format ID - SMART-Predict",
        "ilmSummary.external": "Actual  ",
        "alert.serverTime": "Server time",
        "tooltip.reportHostId": "User Host ID",
        "ilmReport.showonlypayments": "Show Only Payments (Debits)",
        "alert.ccyAccMaintPeriod.minTargetFillBalance": "Fill balance cannot be lower than the Minimum Target Balance;",
        "button.alias": "Alias",
        "ilmanalysismonitor.alertNoCcyEntity": "There are no entity/currencies appropriately configured for your role",
        "label.schedreporthist.column.elapsedTime": "Elapsed",
        "crossReferenceDisplay.title.window": "Cross Reference Display - SMART-Predict",
        "errors.sweep.account.changes": "Account sweep configuration is missing required parameters",
        "format.legend.cancel": "Cancel",
        "matchQuality.pos1": "1st",
        "sweep.accountType": "Account Type",
        "tooltip.crdExtMsg": "Credit external message",
        "subacctim.tooltip": "When checked, only determine whether sweeping is possible using parameters of this account. Otherwise, also use parameters on other account involved in the sweep",
        "reasonMaintenance.changeScreen": "Change Reason Maintenance",
        "ccyAccMaintPeriod.tooltip.reference": "Reference",
        "matchQuality.pos9": "Fin",
        "corporateAccount.addCorporateEntries": "Add Corporate Entries",
        "ilmScenario.percentBy": "% by",
        "balance": "Balance",
        "tooltip.forecastMonitorTemplateAddDetail.description": "Enter description",
        "tooltip.editReason": "Edit Reason",
        "positionlevel.total": "Total",
        "acc.importMT950": "Import MT950",
        "tooltip.selectCancelMvm": "Select cancel movement",
        "scenario.events.null": "Null",
        "movementSummaryDisplay.drilldownTitlePredicted": "Predicted",
        "tooltip.accountGroup.createdByUser": "Sort by Creator",
        "tooltip.AddNewReasonCode": "Add new reason",
        "workflowmonitor.suspended.title": "Suspended",
        "tooltip.sortByAccountId": "Sort by Account ID",
        "label.accountspecificsweepformat.text.currency": "Currency",
        "subacctim": "Use Timing of Only This Account",
        "tooltip.confirmPassword": "Enter confirm password",
        "tooltip.viewLogDetails": "View log details",
        "criticalMvtUpdate.toolTip.change": "Change",
        "scenario.amountLbl": "AMOUNT",
        "ilmAccountGroupDetails.dynamic": "Dynamic",
        "label.accountattribute.title.window": "Account Attribute Maintenance - SMART-Predict",
        "tooltip.entity.accountId": "Central Bank Account ID",
        "ilmccyparamsAdd.toolip.clearingStartTime": "Indicates the beginning of the business day for the currency. Specify as HH:MM in currency timeframe.",
        "tooltip.db_Link": "Enter DB_LINK",
        "emailTemplateMaintenance.subjectContent": "Subject Content",
        "tooltip.accountID": "Account ID",
        "main.alerts": "Alerts",
        "login.notification.tooltip.lastFailedLoginIp": "IP address of last failed login attempt",
        "ilmSummary.currencyEntityGroupAccount": "Ccy-Entity-Group-Acc",
        "sweep.refresh": "Refresh",
        "tooltip.sortByMT950Sod": "Sort by MT950 SOD Balance",
        "account.schedulesweep.label.targetBalanceLabel": "Target Balance",
        "tooltip.sortHolidayDate": "Sort by holiday date",
        "alert.interfaceExceptions.noMessage": "No Message Selected",
        "positionlevel.interim": "Intermed",
        "label.entityMonitorOptions.font": "Font Size",
        "tooltip.accountId": "Select Account ID",
        "ilmExcelReport.secondLargestCounterParty": "2nd Largest Counterparty",
        "tooltip.sortRoleName": "Sort by role name",
        "logBalance.suppliedSOD": "Supplied SOD",
        "tooltip.viewBookSelGroup": "View books for selected group",
        "multiMvtActions.critPayType": "Crit Pay Type",
        "movement.id.movement": "Movement",
        "inputexceptions.messages.header.currency": "Currency",
        "main": "Main",
        "currencygroup.group": "Group",
        "ilmExcelReport.outgoingCriticalPaymentsComparedToDailyTotalOutflow": "Outgoing Critical Payments Compared to Daily Total Outflow",
        "label.entityMonitorOptions.hideWeekends": "Hide Weekends",
        "tooltip.schedreporthist.runDate": "Run Date/Time",
        "accIntRate.Credit": "Credit Margin",
        "account.tooltip.accountClass": "Sort by Account Class",
        "preAdviceInput.invalidRowsAlert": "The dataset contains invalid rows.     Continue and ignore invalid rows?",
        "title.archive": "Archive Setup - SMART-Predict",
        "scenario.description": "Description",
        "connectionPool.connectionJDBCOpen": "Open",
        "balMaintenance.forecastSODAsString": "Effective Forecast SOD",
        "shortcuts.menuItemId": "Menu Option",
        "maintenanceevent.details.alert.areyousuretoaccept": "Are you sure you want to accept the changes?",
        "account.schedulesweep.label.directionLabel": "Direction",
        "tooltip.selectUserId": "Select a user ID",
        "tooltip.clickSelMsgId": "Click to select message type",
        "acctSweepBalGrp.alert.sameAccount": "Account ID and Sweep Account ID should not be the same",
        "tooltip.confirmNewPwd": "Confirm new password",
        "scenarioTooltipSummary.closeButton": "Close",
        "acctMaintenance.entityId": "Entity",
        "interfacerulesmaintenance.ruleKey": "Rule Key",
        "tooltip.systemLoggingLevel": "Specify level of logging required by ILM system processes",
        "mainaccountId.doesnotmatch": "Not a valid main account ID",
        "button.options": "Options",
        "scenarioAdvanced.alertInstanceColumns": "Alert Instance Cols",
        "label.roleBasedControl.ReqAuth": "Req.Auth?",
        "party.custodian": "Custodian",
        "movementSummaryDisplay.drilldownTitleLoro": "Loro",
        "messageFormats.msgSeparator": "Msg Separator",
        "tooltip.deleteSelMAtchQuality": "Delete selected match quality",
        "tooltip.enterMatchId": "Enter match ID",
        "scenario.otherIdColumn": "OTHER_ID Column",
        "exportPages.label.current": "Current Page",
        "tooltip.fullInstanceAccess": "Sort by Full Instance Access",
        "label.forecastMonitorOption.userBuckets": "User Buckets",
        "role.accountaccess.changeTitle": "Change Account Access Control",
        "tooltip.status": "User Status",
        "alert.interfaceMonitor.fromDate": "From date should be less than to date",
        "tooltip.enterScenarioTitle": "Enter Scenario Title",
        "acctMaintenance.tooltip.and": "and ",
        "tooltip.generateReportAll": "Generate report for all users",
        "criticalMvtUpdate.add": "Add",
        "button.genericdisplaymonitor.refresh": "Refresh",
        "tooltip.sortStartPosition": "Sort by start position",
        "ilmScenario.label.activeScen": "Active Scenario",
        "bookMonitor.group": "Group",
        "tooltip.enteraccIntRateOverDraft": "Enter Debit Margin",
        "tooltip.sortNextExeStatus": "Sort by next execution time",
        "tooltip.currencyFundingDate": "Enter value date",
        "tooltip.enterAccount": "Enter Account",
        "ilmanalysismonitor.grid.fcasteod": "Fcast.EOD",
        "reports.pdf": "PDF",
        "alert.movementSearch.compareTwoTime": "To Time must be greater than From Time",
        "alert.DateAmountFormatChanged": "Your date or amount format has been changed. Please log off and logon again",
        "button.setRun": "SetRun",
        "movSearch.alert.dateComparison": "should be greater than or equal to",
        "acctmaintenance.useinternal": "Use Internal",
        "balanceType": "Balance Type",
        "alert.closeScreen": "Please close the opened screen!",
        "tooltip.option": "Change options",
        "movement.inputBy": "Input By",
        "tooltip.setRun": "Run pending job",
        "button.queue": "Queue",
        "tooltip.changeILMCcy": "Change selected ILM currency",
        "tooltip.intermediaryInstitutionText3": "Enter Intermediary Institution text 3",
        "tooltip.intermediaryInstitutionText4": "Enter Intermediary Institution text 4",
        "account.schedulesweep.label.allowMultipleN": "No",
        "tooltip.intermediaryInstitutionText5": "Enter Intermediary Institution text 5",
        "account.schedSweep.heading.entityId": "Entity<br>",
        "ilmanalysismonitor.grid.openunsett": "Open Unsett.",
        "button.report": "Report",
        "tooltip.earliestSweepTime": "Earliest time at which sweeping can take place. Used in auto sweeping when \"Use Timings of Only This Account\" is not checked.",
        "sweepDetails.alert.amount": "Sweep Amount should be greater than 0",
        "tooltip.intermediaryInstitutionText1": "Enter Intermediary Institution text 1",
        "label.entityprocess.specTimeZone": "(specified in Entity time zone)",
        "tooltip.intermediaryInstitutionText2": "Enter Intermediary Institution text 2",
        "alert.forecastMonitor.validNumber": "Please enter a valid number",
        "tooltip.sortCategoryTitle": "Sort by Category title ",
        "bookMaintenance.title.mainWindow": "Book Maintenance - SMART-Predict",
        "label.selectedOnlyMatchedItems": "INVALID: You may only select matched items that are all in the SAME match",
        "role.allEntityOption": "Workflow / Currency Monitor: 'All' entity option",
        "tooltip.sortUserId": "Sort by user ID",
        "turnoverReport.enddayTooltip": "Enter report End date",
        "tooltip.viewGroupMonitor": "Select Group to view Group Monitor",
        "account.schedulesweep.label.allowMultipleY": "Yes",
        "user.label.closeWindow": "Close window",
        "tooltip.sortLockTime": "Sort by lock time",
        "currency.cutOffTime1": "Cut Off Time",
        "tooltip.viewDetailsSelUser": "View details for selected user",
        "ccyMonitorOptions.hideWeekends": "Hide Weekends",
        "title.changeFormats": "Change Format Details - SMART-Predict",
        "label.totalInPages": "Total in this page:",
        "corporateAccount.title.corporateEntries": "Corporate Entries",
        "tooltip.attribute": "Select an attribute",
        "tooltip.killSelectedUser": "Kill selected user",
        "tooltip.sortAmount": "Sort by amount",
        "matchQuality.posTotalExternal": "External",
        "tooltip.selectAllSweeping": "Select All Sweeping",
        "account.tooltip.accountPartyId": "Sort by Account Party ID",
        "button.option": "Options",
        "changepasswordRules.title.window": "Change Password Rules - SMART-Predict",
        "connectionPool.alertDBGrantsNeeded": "The screen cannot function for this chosen module until access is granted to the necessary views",
        "messageFormats.addScreen": "Add Sweep Message Format - SMART-Predict",
        "label.unexpected": "Unexpected",
        "sweepDetail.exalignToTarget": "Re-aligned External Balance",
        "tooltip.enterNotes": "Enter notes",
        "button.tooltip.schedreporthist.resendMail": "Resend mail to the distribution list",
        "label.findoraddpopup.type": "Type",
        "label.lastRefTime2": "Last Refresh:",
        "entity.general.reportingCurrency": "Reporting Currency",
        "acctmaintenance.forecast": "Forecast",
        "sweepInter.targetBIC": "Target BIC",
        "label.accountattributehdr.regexvalidation": "Regex Validation",
        "tooltip.close": "Close window",
        "label.accountattributedefinition.column.attributename": "Attribute Name",
        "acountMonitorNew.MovementSummaryScreen": "Movement",
        "auditLog.userLog": "User Log - SMART-Predict",
        "ilmSummary.tooltip.available": "Available balance",
        "sweep.drIntMsg": "DR INT MSG",
        "instancerecord.sweepId_tooltip": "Sweep Id",
        "alert.interfaceExceptions.sure": "Are you sure?",
        "entity.ilmRetain": "ILM Data",
        "contactDetails.alert.twoperiodntadjacentemailadd": "Two periods must not be adjacent in email address",
        "tooltip.enterMGID": "Enter new metagroup ID",
        "title.rates.change": "Change Account Interest Rate",
        "scenario.events.tooltip.allowRepeat": "Allow repeat on re raise",
        "sweepInter.changeChildScreen": "Change Sweep Intermediary - SMART-Predict",
        "ilmanalysismonitor.noglobalgroup": "No global group exists for the selected entity and currency.",
        "userLog.user": "User",
        "tooltip.personalCurrencyList.button.ok": "Save changes List and Exit",
        "scenario.matchLbl": "MATCH_ID",
        "tooltip.bookId": "Book ID",
        "tooltip.viewSelAc": "View selected account",
        "tooltip.daily": "Daily",
        "tooltip.viewMetagroupMonitor": "Select Metagroup to view Metagroup Monitor",
        "errors.password.maxLength": "Invalid password: must be less than {0} characters in length",
        "sweepsearch.accType": "Account Type",
        "tooltip.changeSelExtraID": "Change selected Extra ID",
        "currencyMonitor.locations": "Location",
        "messageFormats.formatType.fixed": "Fixed",
        "manualMatch.warning.messageForPredictStatus": "There are included items at multiple position levels. Do you want to continue?",
        "ccyAccMaintPeriod.tooltip.chargeThreshold": "Balance above which charges may be incurred",
        "acctCcyPeriodMaint.addScreen": "Account Currency Maintenance Period ADD - SMART-Predict",
        "balmaintenance.balanceSource": "Balance Source",
        "tooltip.systemFlag": "System Flag",
        "messageFormats.hexaMsgSeparator": "Msg Separator (Hex)",
        "ilmExcelReport.thirdLow": "Third Low",
        "tooltip.refreshWindow": "Refresh window",
        "tooltip.viewSelectedUser": "View selected user",
        "inputexceptions.messages.header.amount": "Amount",
        "button.copyFrom": "CpyFrom",
        "criticalMvtUpdate.toolTip.runSqlUpdate": "Specify how frequently (in minutes) the SQL update should run",
        "scenario.checkinterval": "Check Interval",
        "label.multipleMessageSelected": "Multiple message selected",
        "alert.interfaceSettings.emaillog": "Enter a valid Email",
        "alert.MvmUnlockSysAdm": "Movement cannot be unlocked. Please contact your System Administrator.",
        "tooltip.currencyCode": "Currency Code",
        "alert.change.existingattribute.data": "Data exists for this attribute. If you change the definition of the attribute you must ensure that the existing data is consistent with the new settings",
        "scenario.guiHighlight.critGuiHighLbl.text": "Use Red icon to indicate critical importance",
        "cannotdelete.admin": "Cannot delete ADMIN ID",
        "tooltip.forecastMonitorTemplateAdd.moveDown": "To move down the selected record",
        "ilmanalysismonitor.tree.thresholds": "Thresholds",
        "messageFields.sequenceNoDisplay": "Seq No",
        "tooltip.inputRetParam": "Input retention parameter",
        "tooltip.changeScenario": "Change selected scenario",
        "title.failPage": "Fail",
        "ilmccyparamsAdd.alert.clearingTime": "Clearing end time must be later than start time",
        "tooltip.scenarioFacilityReferenceColumn": "Enter Facility Reference Columns",
        "tooltip.format": "Add format",
        "button.cancel": "Cancel",
        "tooltip.exlMvmDealerMons": "Exclude movement from dealer monitors",
        "tooltip.selectCrMsgCancel": "Select credit message cancel",
        "alert.invalidColumnPosition": "Invalid column position",
        "movementsearch.sortorder": "Sort Order",
        "label.entityMonitorOptions.autoSizeColumns": "Auto Size Columns",
        "currency.gmtOffset": "Currency GMT Offset",
        "rejectedSuppressedInput": "Rejected/Suppressed Input",
        "tooltip.cancel": "Cancel",
        "tooltip.sortCurrency": "Sort by currency",
        "tooltip.lastpass": "Last Password Change",
        "scenario.minAfter": "mins after expiry",
        "label.format.Ext": "External",
        "multipleMvtActions.externalFieldSet": "External Status",
        "role.workQueueAccess.matchStatus": "Work Queue",
        "ilmScenario.label.dynamicQuery": "Dynamic Query",
        "tooltip.turnoverReportDate": "Enter report date",
        "pwd.minPasswordLen": "Minimum length",
        "button.format": "Format",
        "messageFields.fieldType.text": "Text",
        "ilaapgeneral.currency": "Currency",
        "label.entityMonitor.group": "Group",
        "ccyMonitorOptions.fontSizeNormal": "Normal",
        "ilmScenario.credits": "Credits",
        "alert.dateShouldBeGreater": "To Date should be greater than or equal to From Date",
        "acctMaintenance.tooltip.selectAcctType": "Select account type",
        "centralBankMonitor.showValue": "'Show' value must be between 2 and 14. ",
        "ccyMonitorOptions.hideLoro": "Hide Loro",
        "sweepDetail.subSweepAmt": "Submitted Sweep Amount",
        "label.entityprocess.sysTime": "System Time",
        "label.mail.subject": "Subject",
        "user.copyright": "&#169; 2006 - 2024 SwallowTech",
        "multipleMvtActions.label.selected": "Selected",
        "tooltip.selectKeyword": "Select keyword",
        "movement.amount": "Amount*",
        "criticalMvtUpdate.validateQuery": "Validate Query",
        "country.title.changeScreen": "Change Country Maintenance",
        "tooltip.addNewCurr": "Add new currency",
        "account.schedSweep.thisAccount": "This Account",
        "messageFormatAdd.alert.fieldsRemoved": "All the fields for the previous format type will be removed. Do you want to continue?",
        "table.account": "Account",
        "account.schedSweep.heading.minAmount": "Min<br>&nbsp;&nbsp;&nbsp;&nbsp;Amount",
        "maintenanceevent.details.button.amend.tooltip": "Amend the change",
        "criticalMvtUpdate.reportable": "Reportable",
        "ccyAccMaintPeriod.tooltip.from": "From",
        "tooltip.selectGroupId": "Select group ID",
        "errors.valueDateRequired": "Please select a value date",
        "account.debitLeg": "Debit Leg",
        "tooltip.roleId": "Select the role ID",
        "tooltip.sortByTime": "Sort by time",
        "errors.invalid": "{0} is invalid",
        "ilmAccountGroupDetails.titleScreen": "Account Group Details Screen",
        "ilmScenario.private": "Private",
        "account.loroToPredicted": "Include Loro in Prediction",
        "sweep.Submitted": "Sweep already submitted",
        "tooltip.sortCategorySystemFlag": "Sort by System Flag",
        "criticalMvtUpdate.expectedTime": "Default Expected Time",
        "tooltip.selectCurrencyCode": "Select currency code",
        "match.addMvmt.mvmtIdempty": "Please enter movement ID",
        "alert.noValidParent": "This grid does not have a valid parent",
        "tooltip.sortByBalanceSourceId": "Sort by Balance Source ID",
        "matchQuality.posLevel": "Position Level",
        "maintenanceLogView.fullDetails": "Full Details",
        "maintenanceLogView.oldVal": "Old Value",
        "ccyAccMaintPeriod.tooltip.log": "Log",
        "stp1": "STP",
        "stp2": "New",
        "scenarioNotification.button.distributionList": "Dist. List",
        "ccyAccMaintPeriod.tooltip.changeButton": "Change Account Currency Maintenance Period",
        "tooltip.sortRuleValue": "Sort by Rule Value",
        "ilmExcelReport.date": "Date",
        "acctSweepBalGrp.sweepAccount": "Sweep Account ID",
        "tooltip.changeTarBal": "Change target balance sign",
        "userStatus.header.IPAddress": "IP Address",
        "ilmanalysismonitor.collapseall": "Collapse All",
        "tooltip.sunday": "Sunday",
        "tooltip.toDateDDMMYY": "Select To date (DD/MM/YYYY)",
        "spreadProfilesMaintenance.processPoint.ExceptAll": "All Except",
        "tooltip.EnterBookcodeName": "Enter bookcode name",
        "tooltip.exportErrors": "Export",
        "matchQuality.posTotal": "Position Totals",
        "throuputmonitor.tooltip.current": "Current Throughput Ratio",
        "tooltip.minutesAfterCutOff": "Minutes After Cut-Off",
        "contactDetails.alert.emailaddcontain@": "Email address must contain an @",
        "addjob.Date": "Date",
        "archive.type": "Type",
        "workflowmonitor.option.applied": "Applied - Checkbox checked",
        "tooltip.nonworkday.changeButton": "Change selected facility",
        "ilmReport.reportingPeriod": "Reporting Period",
        "ccyAccMaintPeriod.tooltip.ipAddress": "IP Address",
        "connectionPool.tooltip.status": "Java connection status",
        "scenarioSummary.activeInstance": "Active instances",
        "tooltip.enterCategoryId": "Enter Category ID",
        "label.entityMonitorOptions.save": "Save",
        "button.tooltip.msd.configure": "Configure profile",
        "tooltip.copyExMvm": "Copy details from an existing movement",
        "tooltip.manualMessageType": "Message Type (Search texts containing % as wild card are supported)",
        "movementDisplaly.source": "Source",
        "inputexceptions.tooltip.button_rej": "Move messages to rejected queue",
        "auditLog.viewScreen": "View User Audit Log Details - SMART-Predict",
        "account.sweepfrmbal": "Choose to calculate sweep on Predicted or external balance. External will cause sweep movement to be count towards external balance.",
        "sweep.postCut": "Post Cut Off",
        "alert.accountattributehdr.checkminmax": "Maximum value must be greater than minimum",
        "label.entityMonitor.date": "Date",
        "ccyAccMaintPeriod.endDate": "End Date",
        "inputexceptions.tooltip.button_rep": "Reprocess the selected Message",
        "alert.acctBreakdown.exceedDate": "Selected date should be within 30 days from current date",
        "tooltip.DeleteSelectedReasonCode": "Delete selected reason",
        "groupmaintenance.title.bookMain": "Book Maintenance - SMART-Predict",
        "tooltip.entityu.indicator": "Sort by indicator",
        "sweep.cancelPanel": "Cancel Panel",
        "interfacerulesmaintenance.add.ruleKey": "Rule Key",
        "tooltip.enterBeneId": "Enter a beneficiary ID",
        "ilmAccountGroupDetails.time": "Time",
        "acctSweepBalGrp.tooltip.accountStatus": "Account Status",
        "scenario.events.never": "Never",
        "tooltip.interfaceMonitor.messageType": "Message Type",
        "currencyFunding.threshold": "Threshold",
        "movementDisplay.reference": "Referred",
        "ilmSummary.startOfDay": "Start of Day                   ",
        "tooltip.acctBreakdown.move": "Move Loro Balance to Predicted Balance",
        "tooltip.movMetagroup": "Select metagroup category",
        "scenario.payIdColumn": "PAYMENT_ID Column",
        "label.personalEntityList.button.addsum": "Add Sum",
        "cashRsvrBal.tooltip.currency": "Currency Code",
        "ilmReport.Other": "Other",
        "tooltip.addNewRole": "Add new role",
        "label.personalCurrencyList.title.window": "Personal Currency List - SMART-Predict",
        "tooltip.scenarioCategory": "Sort by Scenario Category",
        "tooltip.1positionLevel": "First position level",
        "label.interfaceExceptions.movement.message": "Movement Message",
        "corporateAccount.tooltip.save": "Save",
        "sweep.subTime": "Submit Time",
        "inputexceptions.title.window": "Input Exception - SMART-Predict",
        "currMonitor.crrGrp": "Currency Group",
        "title.failMessagePage": "You have entered wrong userid/password",
        "tooltip.enterFieldDelimiter": "Enter field delimiter",
        "metaGroup.mgrpLvlCode1": "Metagroup Level",
        "acctMaintenance.tooltip.inServEntity": "In this servicing entity",
        "bookMonitor.today1": "Today+1",
        "bookMonitor.today2": "Today+2",
        "role.roleName": "Name",
        "account.schedSweep.show": "Show",
        "accountmaintenance.DeletionWithoutArchivingIsRequired": "Flagging accounts not to be archived may result in data loss for ILM functionality. Please ensure this accountâs data is not required in the archive when unchecking this checkbox",
        "tooltip.interestRateRetention": "Ccy Interest Rate retention period",
        "tooltip.deleteselectedmsgfield": "Delete selected message field",
        "ilm.options.ccyEntityGrp": "Ccy-Entity-Group",
        "preAdviceInput.headerLabel": "My data has headers",
        "tooltip.selectAccessRequired": "Select Access Required",
        "movement.matchId": "MatchId",
        "sweep.entity": "Entity",
        "instancerecord.userLog_header": "User",
        "title.rates.add": "Add Account Interest Rate",
        "ilmReport.onDayIncompletedData": "Report data is incomplete. Records are missing for ",
        "workflowmonitor.submit.title": "Submit",
        "ilm.options.ccyEntityGrp_toolTip": "Currency-Entity-Account Group Id",
        "tooltip.addMov": "Add movement",
        "inputexceptions.messages.header.valuedate": "Value Date",
        "instancemessage.messageId_header": "Message ID",
        "sweep.drExtMsg": "DR EXT MSG",
        "workflowmonitor.errors.title": "Errors",
        "changeBalance.legend.backValAdj": "Back Value Adjustments",
        "button.msd.saveAs": "Save as",
        "multipleMvtActions.unmatchRadio": "Unmatch",
        "tooltip.reportEntity": "Select the entity",
        "archive.typeD": "DB Link",
        "label.currencyMonitor": "Currency Monitor",
        "scenarioAdvanced.baseQuery": "Base Query",
        "addJob.alert.selectDays": "Please select day(s)",
        "button.ManSwp": "ManSwp",
        "ilmccyparamsAdd.toolip.centralBankGrpId": "ILM Account group relating to central bank account(s)",
        "tooltip.all": "All",
        "tooltip.sortByBalanceAfter": "Sort by balance after",
        "movementsearch.included": "Included",
        "tooltip.interfaceExceptions.description": "Description",
        "connectionPool.audsid": "AUD SID",
        "tooltip.help": "Help Screen Content",
        "movement.payChannek": "Pay Channel",
        "scenarioCategory.categoryDisplayorder": "Order",
        "scenarioAdvanced.display": "Display",
        "throuputmonitor.tooltip.actout": "Actuals Outflows",
        "button.reconcile": "Recon",
        "alert.interfaceSettings.emails": "You cannot enter more than 20 email addresses",
        "reasonCodeTooltip.sortreasonCode": "Sort by reason code",
        "alert.error.creationWithAllRole": "INVALID: ALL is not a valid Role ID. Please specify a different ID ",
        "sweep.accounttype": "Account Type",
        "archive.typeS": "Schema",
        "screen.releaseDate": "Release date: ",
        "workflowmonitor.authorise.title": "Authorise",
        "tip.schedReportHist.elapsedTime": "Time took by the report to finish",
        "scenario.allLbl": "All attributes'name-value pairs",
        "sweeppriorcutoff.accountClass": "Account Class",
        "tooltip.CancelChanges": "Cancel changes",
        "tooltip.selectClick": "Click to select account ID",
        "tooltip.cancelMvm": "Cancel movement",
        "tooltip.primaryForecast": "Primary Forecast",
        "tooltip.selectDomCCY": "Select domestic ccy/ccy",
        "ccyAccMaintPeriod.start": "Start",
        "groupMonitor.location": "Location",
        "ilmAccountGroupDetails.acctGrpRequired": "Account Group ID is required",
        "label.alertSummaryTooltip.parameters": "Parameters",
        "ilmReport.title": "ILM Report Request",
        "tooltip.enterRefIncl": "Include movements having references found with this value",
        "preAdviceInput.fieldSet2.legendText": "Pre-Advices Input in Last 24 hours",
        "tooltip.currencytimeframe": "Click to show scale in entity timeframe",
        "sweepDetails.alert.sweeperror1": "Cut off time violation.<BR>",
        "alert.noArchiveDefined": "No archive defined",
        "ilmExcelReport.availableIntradayLiquidity": "Available Intraday Liquidity",
        "tooltip.credit": "Sort by credit",
        "ilmanalysismonitor.expandall": "Expand All",
        "tooltip.exportErrors_excel": "Export to Excel",
        "party.branch": "Branch",
        "tooltip.reference1": "Sort by reference 1",
        "account.tooltip.entityId": "Entity Id",
        "main.alert.loggedOff": "Do you really want to log off?",
        "msdAdditionalColumns.saveProfileImageTooltip": "Save profile",
        "instancerecord.hostId_tooltip": "Host",
        "scenario.cyclic": "Cyclic",
        "label.totalSelected": "Selected:",
        "party.entityId": "Entity",
        "corporate.amount": "Amount",
        "alert.entityprocess.unSaved": "All unsaved changes will be lost. Do you want to continue?",
        "currencyMonitor.title.window": "Currency Monitor - SMART-Predict",
        "label.accountspecificsweepformat.column.hostId": "Host Id",
        "scenarioTooltipSummary.linkToSpecificButton": "Link to specific",
        "label.accountspecificsweepformat.text.external": "External",
        "throuputmonitor.ilm_group": "ILM Group",
        "sweep.debitedmessage": "Msg Type DR",
        "tooltip.changeAlignTarget": "Change align to target",
        "login.newUSer.continue": "New User! Please change your password.",
        "accountGroup.publicPrivate": "Public?",
        "tooltip.movPredict": "Select Predict status",
        "tooltip.updatedatetime": "Update Date Time",
        "label.schedReportHist.errorDescription": "Error Description",
        "label.matchHasBeenChanged": "This match has been changed ",
        "movement.productType": "Product Type",
        "tooltip.refreshevery": "Enter Refresh Every ",
        "label.unableSave": "Unable to save to server\\nPossible loss of connection",
        "label.ilmccyprocess.lastexecutestatus.canceled": "Cancelled",
        "Jobmaintenance.JobName": "Job Name",
        "tooltip.outputRetParam": "Output retention parameter",
        "ilmScenario.alert.allowReporting": "Specifying âAllow ILM Reportingâ for this  \\n group/scenario will consume more server \\n resources. Do you wish to continue?",
        "userOptions.confirm.close": "You have chosen to close the window. All unsaved changes will be lost. Are you sure?",
        "tooltip.listFromGrp": "Select a group from list",
        "label.forecastMonitorOptions.title.window": "Forecast Monitor Options - SMART-Predict",
        "ilmAccountGroupDetails.showXMLLeftGrid": "Show Left Grid XML",
        "accountmonitor.selected": "Selected",
        "logBalance.workingSODType": "Working SOD Type",
        "ilmScenario.currency": "Currency",
        "tooltip.debit": "Debit",
        "addjob.alert.invalidRetainFiles": "Please enter a valid number of days for Retains Files",
        "format.cdInt": "Credit Internal",
        "entity.general.retentionFlag": "Retention flag",
        "screen.lastRefresh": "Last Refresh:",
        "sweepsearch.entity": "Entity",
        "label.accountattribute.currency": "Currency",
        "acctMaintenance.tab.general": "General",
        "tooltip.movement.sign": "Sign",
        "sweep.type": "Type",
        "sweepsearch.generatedby": "Generated By",
        "tooltip.LVPSGrid": "Sort by LVPS Name",
        "tooltip.privatePublic": "Specify if scenario is public and available to all users or private and only available to the creator of the scenario",
        "access_restrictions": "Access restrictions : User not allowed",
        "ilmExcelReport.monitoredCriticalpaymentTypes": "Monitored Critical Payment Types",
        "alertInstance.sweepId": "SWEEP_ID",
        "tooltip.enterHexaFieldDelimiter": "Enter hex field delimiter",
        "label.accountattributedefinition.column.attributeid": "Attribute ID",
        "interfaceSettings.title": "Input Configuration Screen",
        "title.acctBreakdown.window": "Account Breakdown Monitor - SMART-Predict",
        "tooltip.sortPredictBalance": "Sort by Predict balance",
        "role.alerttype": "Alert Type*",
        "sweep.msgformat": "Message Format",
        "ilmSummary.tooltip.sum": "Monitor Summation: 'N' - Never; 'C' - by Cut-off",
        "currencyalias.addScreen": "Currency Alias Add - SMART-Predict",
        "tooltip.add": "Add",
        "errors.date": "{0} is not a date",
        "tooltip.delete.TransactionSet": "Delete Selected Transaction",
        "alert.forecastMonitor.contactSysAdm": "Please contact your System Administrator",
        "tooltip.currencyFundingThreshold": "Enter threshold value",
        "label.ilmcalculationsuccess": "ILM calculation completed successfully",
        "tooltip.forecastMonitorTemplateAdd.userId": "Select User ID",
        "label.forecasttemplate.column.displayname": "Display Name",
        "tooltip.msgID": "Enter message ID",
        "scenario.alert.uncheckingRecordInstances1": "Warning. Unchecking Record Instances will cause configuration parameters to be lost in the General, Instances and Events tabs. Are you sure?",
        "tooltip.ConfMatch": "Confirm match",
        "criticalMvtUpdate.toolTip.timeFrameGroup": "Specify the timeframe to be used when scheduling the SQL update",
        "sweepsearch.sweepdetails": "Sweep Details",
        "movementRecovery.updateUser": "User",
        "movementDisplay.originalValueDate": "Original",
        "workflowmonitor.exclOut.title": "Excluded Outstandings: Today",
        "errors.invalidLogin": "User ID or password is incorrect. Please try again.",
        "multipleMvtActions.label.counterPartyLbl": "Counterparty ID",
        "holiday.countryId": "Country",
        "ilmExcelReport.asOfDailyTotalInflow": "as % of daily total inflow",
        "addJob.title.accessList": "Access List",
        "balMaintenance.accountId": "Account ID",
        "tooltip.MvmntBrkdown": "Select Movement to view Movement Summary Detail",
        "error.errorDateTime": "Date and time of error:",
        "tooltip.SortByPreflag": "Sort by Predict flag",
        "label.accountattributedefinition.column.type": "Type",
        "tooltip.schedreporthist.reportName": "Report Name",
        "alert.warning.emptyAcctAttribute": "Please select an Account Attribute",
        "instancerecord.resolvedByUser_tooltip": "Resolved By",
        "tooltip.mail.recipientsListTo": "Select originals recipients",
        "errorLog.errorDate_Date": "Date",
        "userOptions.Label.enabled": "Enabled",
        "sweepSearchList.sweepId": "Sweep",
        "tooltip.SortByEnableDst": "Sort by Enable Daylight Saving",
        "mvmt.applyCurrencyThreshold": "Apply Currency Threshold",
        "ext": "EXT",
        "alert.interfaceSettings.startendtime": "Enter the Alert start and end time for the following interface(s):",
        "tooltip.enterBenText5": "Enter Beneficiary Institution text 5",
        "schedReportHist.detailsScreen.title": "Scheduled Reports History Details",
        "tooltip.enterBenText3": "Enter Beneficiary Institution text 3",
        "tooltip.enterBenText4": "Enter Beneficiary Institution text 4",
        "ccyAccMaintPeriod.tooltip.changedTo": "Changed To",
        "tooltip.enterBenText1": "Enter Beneficiary Institution text 1",
        "tooltip.enterBenText2": "Enter Beneficiary Institution text 2",
        "account.cutOff": "Cut Off",
        "tooltip.movement.update_date": "Updated Date",
        "tooltip.currency": "Select the Currency",
        "criticalMvtUpdate.toolTip.validateQuery": "Click to validate the SQL is valid",
        "alert.microsoftInternetExplorer": "Microsoft Internet Explorer: Warning",
        "changeSection.title.window": "Change Section - SMART-Predict",
        "label.schedReportHist.runDate": "Run Date",
        "preadvice.alert.notfound": "Pre-Advice not found in file",
        "tooltip.movement.input": "Input Date",
        "msd.noneFilter": "<None>",
        "contactDetails.alert.emailaddntprecede@": "Period must not immediately precede @ in email address",
        "label.ilmccyprocess.lastended": "Last Ended",
        "tooltip.Critical.Mvt.delete": "Delete",
        "linked.tooltip.name": "Name",
        "alertMessage.alertstageadd": "Alert Event",
        "tooltip.9positionLevel": "Final position level",
        "tooltip.statusActive": "Select status as Active",
        "scenario.events.changeFormat": "Change message Format",
        "emailTemplateMaintenance.description": "Description",
        "alert.interfaceMonitor.internal": "Show XML - Internal",
        "role.alerttype.popup": "Pop-up",
        "login.password.modified": "Password Expired today",
        "addjob.Manual": "Manual",
        "movementroll.dateValidateOriginal": "The Value date should be greater than original value date",
        "alert.forecasttemplate.validnumber": "Please enter a valid number",
        "connection.dbError": "Cannot change the current DB",
        "tooltip.datasetUpdateInterval": "Time in minutes representing how frequently the standard dataset (and associated scenarios) are to be updated",
        "tooltip.aggAcct": "Aggregate Account",
        "tooltip.forecastMonitorTemplateAddDetail.contributeText": "Enter multiplier to contribute total",
        "button.auth": "Auth",
        "ilmReport.showall.tooltip": "Show All (Debits and Credits)",
        "tooltip.selectBookLocation": "Select Book Location",
        "account.schedulesweep.tooltip.bookCrCombo": "Select the book for Credit Sweep",
        "ilmaccountgroup.title.window": "ILM Account Group Maintenance - SMART-Predict",
        "accountMonitor.label.notSum": "Inc 'Do not Sum'",
        "label.accountspecificsweepformat.text.credit": "Credit",
        "movementDisplay.cash": "Cash",
        "tooltip.selectSectionId": "Select section ID",
        "currencymonitor.currId": "Currency",
        "scenario.ccyLbl": "CURRENCY_CODE",
        "movementValueDate.alert.Validate": "The value date entered falls on defined holiday or weekend. Please change",
        "tooltip.AddNewCurrency": "Add new currency",
        "tooltip.interfaceMonitor.messageStatus": "Database Status",
        "button.add": "Add",
        "criticalMvtUpdate.toolTip.radioEntity": "Evaluate scheduling in entity timeframe'",
        "scenario.emailWhenDiff": "Email When Diff.",
        "addjob.alert.paramConfigNotCorrectBeforeSave": "Report parameter configuration values are not correct please verify before saving.\\nCause: ",
        "ilmreport.keyword.label.endOfPreviousMonth": "End of the previous month",
        "tooltip.sortOutFinalBalance": "Sort by out final balance",
        "Account.generalTab": "General",
        "label.amountSelectedMovementsDiffer": "The amounts of the selected movements differ. Do you want to continue?",
        "tooltip.disableCurrPredict": "Disable this currency in Predict",
        "movement.bookCode": "Bookcode",
        "account.schedulesweep.label.bookCrLabel": "Book CR",
        "alert.entityProcess.save": "Change have been made. Are you sure you want to save?",
        "tooltip.metaLevel3": "Metagroup level 3 name",
        "tooltip.metaLevel2": "Metagroup level 2 name",
        "tooltip.metaLevel1": "Metagroup level 1 name",
        "submit": "Submit",
        "tooltip.sort.dststartdate": "Sort by Start Date",
        "scenarioRoleAssignment.title.window": "Scenario - Role Assignment",
        "accountMonitorNew.openUnexpectedTotal": "Open Unexpected",
        "multipleMvtActions.mvtTotalFieldSet": "Movement Totals",
        "label.entityMonitor.entityOffset": "Today (Entity)",
        "sweep.errorInsertingAlert": "Error inserting alerts.<br>Refer to the error log for details!",
        "tooltip.alertInstanceScenario": "Scenario ID",
        "tooltip.forecastMonitor.close": "Close window",
        "instancerecord.movementId_header": "Movement Id",
        "scenarioCategory.title": "Title",
        "label.interfaceMonitor.header.lastMessageRecieved": "Last Message",
        "tooltip.DeleteSelectedCurrencyGroup": "Delete selected currency group",
        "ilmReport.titleBaselBReportDateRange": "Date Range Liquidity Management Report - Basel B: Correspondent",
        "throuputmonitor.unsetout": "Unsettled Out.",
        "ilmthroughputActTvsActT": "Actuals[t] vs Actuals [latest]",
        "tooltip.balance.extBalEodDate": "Business day on which the supplied external balance was evaluated",
        "label.calculate": "Calculate",
        "cashRsvrBal.tooltip.fillDays": "Fill Days",
        "ilmScenario.legend.excludedMovements": "Excluded Movements",
        "confirm.open": "Are you sure you want to set the selected movement to Open?",
        "msd.saveFilterImageTooltip": "Save Filter",
        "tooltip.scenarioRunEvery": "Enter Run Every (hh:mm:ss)",
        "tooltip.enterMinSweepAmount": "Enter minimum sweep amount",
        "tooltip.enterMvmExtraText": "Enter movement extra text",
        "label.sweepPriorCuttoff.entityTime": "Entity Time",
        "tooltip.interfaceMonitor.processed": "Number of messages in processed state",
        "cashRsvrBal.tooltip.startDate": "Click to choose start date",
        "scenario.events.emailLbl": "Email",
        "sweepcancelQ.title.window": "Sweep Cancel Queue - SMART-Predict",
        "tooltip.entityMonitor.selectCcyGroup": "Select Currency Group",
        "label.forecastTemplateOptions.title.window": "Forecast Monitor User Template Options - SMART-Predict",
        "tooltip.InterBIC": "Sort by Intermediary BIC",
        "ilmtransSetDetailAdd.title.window.addScreen": "Add ILM Transaction Detail Display - SMART-Predict",
        "entity.GroupLevel2Name": "Group level 2 name",
        "interfacerulesmaintenance.alert.ruleId": "Rule ID is empty",
        "sweep.postcutoff1": "Post cut off",
        "timeslotSize": "Timeslot Size (min)",
        "correspondentaccountmaintenance.messageType": "Message Type",
        "tooltip.AcctBreakDown": "Select Account Breakdown to view Account Breakdown Monitor",
        "tooltip.movement.source": "Movement Source",
        "generalsystem.retentionParameters": "Retention",
        "label.forecastMonitorOptions.cumulativetotal": "Cumulative Bucket totals",
        "tooltip.archiveData": "Enable Data Archiving process to move this account's data to the archive database instead of deleting it only",
        "ilaapccyparams.currencyGrid": "Ccy",
        "movementsearch.swiftmessagetype": "SWIFT Message Type",
        "tooltip.enterNewOverdraft": "Enter new overdraft rates",
        "interest.tooltip.sortByName": "Currency Name",
        "label.acctBreakdown.ccy": "Ccy",
        "login.password.noaccess.to.facility": "ERROR: Password change is required but user's role does not allow access to the facility",
        "errorLog.dateRange": "Error Log",
        "tooltip.sortIpAddress": "Sort by IP address",
        "accountMonitor.alert.label.refreshRateSelected": "Refresh rate selected was below minimum.\\nSet to 5 seconds",
        "label.mail.attachments": "Attachments",
        "sweepsearch.matchId": "Match",
        "ilmanalysismonitor.sourcesOfLiquidity": "Sources of Liquidity",
        "connectionPool.tooltip.duration": "Time passed since opening connection",
        "tooltip.schedreporthist.elapsedTime": "Time Taken",
        "tooltip.selectRestrictLocation": "Select a Restrict Location",
        "tooltip.viewSelectedLog": "View selected log entry",
        "movement.movementType": "Type",
        "entity.AddEntity": "Add entity",
        "account.schedSweep.heading.otherSweepFromBalType": "Sweep<br>&nbsp;&nbsp;&nbsp;&nbsp;Balance",
        "scenarioCategory.title.window": "Scenario Category",
        "instancemessage.updateDate_header": "Update Date",
        "tooltip.forecastMonitorOptions.hidezerovalue": "Hide Zero Value Columns",
        "ilmanalysismonitor.maintain": "Maintain",
        "tooltip.changeSelectedArchive": "Change Selected Archive",
        "account.autoopenunexpected": "Auto Open Unexpected",
        "alert.criticalPaymentType.ccyAlreadyExists": "Record with same currency code is already exists",
        "tooltip.sort.transactionSetId": "Sort by Transaction Set ID",
        "connectionPool.tooltip.lastActionTime": "Time connection opened",
        "tooltip.changeOriginalSweepAmount": "Change original sweep amount",
        "instancerecord.valueDate_tooltip": "Value Date",
        "account.tooltip.linkAccountId": "Sort by Link Account ID",
        "tooltip.movintermediaryInstitution": "Click to select an Intermediary Institution",
        "alert.SmartPredict": "SMART-Predict",
        "tooltip.CurrentOverdraftInterestRates": "Current Overdraft Interest Rate",
        "tooltip.personalCurrency": "Sort by currency code",
        "alert.refreshRateSelectedMonimum": "Refresh rate selected was below minimum.<br>Set to 5 seconds",
        "tooltip.SortByBookCode": "Sort by bookcode name",
        "sweep.sweepDateTimeUser": "Date/Time/Original User",
        "tooltip.sortByInput": "Input",
        "tooltip.SortByDstEndDate": "Sort by End Date",
        "currencyalias.name": "Name",
        "tooltip.defaultDb": "Current Database",
        "entity.dateFrom": "Date From",
        "ilmAccountGroupDetails.mainAgentText": "Main Agent ",
        "tooltip.options": "Change Options",
        "sweepDetail.additionalRefCut": "Additional Ref.",
        "tooltip.canceelSelSweep": "Cancel selected sweep(s)",
        "criticalMvtUpdate.toolTip.reportIndivPayCheck": "Check to include individual payments (used in ILM reporting) ",
        "usermaintenance.select": "Select",
        "tooltip.schedreporthist.exportStatus": "Export Status",
        "tip.accountattributeadd.type": "Select a type",
        "role.copyFrom.roleId": "Role",
        "acclevel": "Account Level",
        "tooltip.viewSelCountry": "View selected country",
        "workflowmonitor.entity.title": "Entity",
        "sweep.otherPanel": "View Panel",
        "scenario.tooltip.instExpTxt": "Enter instance expiry value (mins)",
        "message.EndPosition": "End Position",
        "throuputmonitor.ccy": "Ccy",
        "tooltip.reqconoffered": "N - Not Required, C - Confirmed, M - Offered",
        "alert.criticalPaymentType.whereClauseIsEmpty": "Where clause should not be empty",
        "accountMonitor.label.hideZeroBalances": "Hide Zero Balance",
        "tooltip.SortByDstStartDate": "Sort by Start Date",
        "addjob.Hours": "Hrs",
        "tooltip.inputNotification": "Notify if any interfaces report problems or there are delays in messages arriving",
        "button.mvmnt": "Mvmnt",
        "accountmaintenance.AccountLevelCannotChanged": "There are sub a/c defined for this main account, so the account level cannot be changed",
        "label.forecastAssumptions.title.window": "Forecast Monitor Assumptions - SMART-Predict",
        "entity.manualonly": "Manual Only",
        "tooltip.mail.recipientsListCc": "Select recipients in Cc",
        "ilmReport.LoroClient": "LORO Client ",
        "workqueueaccess.recordPresent": "The record is already present",
        "corporateAccount.corporateName": "Corporate Name*",
        "label.mail.cc": "Cc",
        "jobSetup.title.window": "Job Setup - SMART-Predict",
        "corporateAccount.label.save": "Save",
        "alertInstance.status": "Status",
        "contactDetails.alert.emailaddntfollow@": "Period must not immediately follow @ in email address",
        "intermediary": "Intermediary",
        "tooltip.saveExit": "Save changes and Exit",
        "tooltip.alertTypePopUp": "Alert type popup",
        "ilmAccountGroupDetails.accntInGrp": "Accounts in this group",
        "inputexceptions.header.suppressed": "Suppressed",
        "label.personalCurrencyList.currency": "Currency",
        "instancerecord.paymentId_header": "Payment Id",
        "ccyAccMaintPeriod.fillBalance": "Fill Balance",
        "MovementRecovery.confirm.unlock": "Are you sure you want to unlock?",
        "tooltip.stp": "STP",
        "linked.level": "Level",
        "button.notes": "Notes",
        "sweepDetail.sweepSetting.warning.bookandsettReverted": "WARNING: Manual changes to Book and Settlement Method fields will be reverted.  Are you sure?",
        "tooltip.sortScheduledId": "Sort by scheduled id",
        "tooltip.sub": "Sub",
        "ilmanalysismonitor.scenarioComboLabel": "Scenario",
        "tooltip.enterNewExtraID": "Enter new Extra ID",
        "changePassword.alert.confirmPass": "Password and confirmed password do not match",
        "usermaintenance.info": "Info",
        "defaultaccountmaintenance.title.addWindow": "Add Default Account - SMART-Predict",
        "role.button.showList": "Show List",
        "instancerecord.otherId_tooltip": "Other Id",
        "tooltip.enterEndTime": "Enter end time",
        "acc.netting": "Netting",
        "changePassword.userId": "User Id",
        "tooltip.sweepOk": "OK",
        "positionlevel.addScreen": "Add Position Level - SMART-Predict",
        "tooltip.button.linked": "Show Linked Accounts",
        "tooltip.excludedCondition": "Define an SQL &#34;where clause&#34; condition to specify which movements would not be affected by the scenario.\\nColumns may be referenced from tables P_MOVEMENT, P_MOVEMENT_EXT, S_ENTITY and P_ACCOUNT.",
        "tip.accountattributeadd.time": "Select time",
        "accountmaintenace.alert.PositiveSweepDays": "Invalid Sweep Days, positive Sweep Days is not supported",
        "ilmReport.ofWhichCommitted": "1b. Of which committed",
        "pwdchange.reenterNewPassword": "Please re-enter your new password",
        "ilmExcelReport.thirdHigh": "Third High",
        "currencyAliasChild.alert.IdasAll": "Currency Alias 'All' not permitted",
        "tooltip.clickSelGroupId": "Click to select group ID",
        "menuaccessoptions.warning.changePassword": "WARNING: This role does not grant access to the password change facility",
        "movementsearch.metagroup": "Metagroup",
        "ilmExcelReport.creditLineSecured": "Secured",
        "connectionPool.connectionJDBCClosed": "Closed",
        "toolTip.scenarioDelete": "Delete",
        "tooltip.sortById": "Sort by ID",
        "account.schedSweep.heading.account": "Account<br>",
        "ilmanalysismonitor.grid.group.tooltip": "Group",
        "scenario.no": "No",
        "movementroll.dateValidatePreviousDate": "The Value date should be greater than previous value date",
        "correspondentaccountmaintenance.predefinedMessageType": "Predefined",
        "sweepSearch.alert.amount": "Amount Under should be greater than Amount Over",
        "button.rates": "Rates",
        "label.entity.externalIndicator": "External",
        "tooltip.systemGmtOffset": "Define the database server time offset relative to GMT. Used in liquidity analysis functionality for determining currency timeframe",
        "interfacerules.alert.ruleIdLengthValidation": "Please enter 100 characters only for Rule ID",
        "tooltip.movement.extraRef": "Extra Reference",
        "interfacemonitor.date": "Date",
        "ilmExcelReport.tabNameCriticalInflowsDetail": "Critical Inflows Detail",
        "workflowmonitor.reconciled": "Reconciled",
        "ilmanalysismonitor.groups.title": "Groups",
        "currency.multiplier": "Multiplier",
        "label.accountattributedefinition.column.systemFlag": "System",
        "scenario.id": "ID",
        "label.entityMonitorOptions.currency": "Ccy",
        "tooltip.sortByInputDate": "Sort by input date",
        "acctmaintenance.usepredicted": "Use Predicted",
        "acc.predicted": "Predicted",
        "matchQuality.matchId": "Match",
        "movementsearch.timefrom": "Input Time From",
        "archive.id.archiveId": "Archive ID",
        "ilmExcelReport.correspondingTime": "Corresponding Time",
        "account.schedSweep.tooltip.thisAccSettleMethodCr": "Settle Method CR",
        "accountmonitorNew.Loro": "Loro/Curr",
        "label.ilmccyprocess.lastexecutestatus.failed": "Failed",
        "currency.dstEndDateAsString": "End Date",
        "entity.startDay": "Start Day",
        "tooltip.enterSweepCode": "Enter Sweep Code",
        "sweep.authorize": "Auth",
        "msdAdditionalColumns.profileComboTooltip": "Choose a profile",
        "sweep.authorized": "Authorised",
        "sweepDetail.alignToTarget": "Align to Target",
        "addjob.Startdate": "Start date",
        "account.schedSweep.tooltip.thisAccSettleMethodDr": "Settle Method DR",
        "tooltip.saveAdvanced": "Apply advanced details",
        "accountmaintenance.title.mainWindow": "Account Maintenance - SMART-Predict",
        "tooltip.7positionName": "Seventh position name",
        "roleId": "Role ID",
        "ilmReport.balances": "Balances",
        "ccyAccMaintPeriod.current": "Current",
        "ilmAccountGroupDetails.listFromGrp": "List from group",
        "tooltip.accountattributehdr.regexvalidation": "Enter regex validation",
        "tooltip.maintainDST": "maintain the daylight saving periods.",
        "label.accounts": "&nbsp;accounts.",
        "tooltip.1positionName": "First position name",
        "account.schedulesweep.tooltip.otherBookCrCombo": "Select the book for other account Credit Sweep",
        "acctbiccode": "BIC",
        "tooltip.ChangeCrInterestRates": "Change credit interest rates",
        "passwordRules.label.0to9": "[0-9]",
        "tooltip.preadvice.currentUser": "Current User",
        "ilmReport.sumInOutflows": "Sum Inflows/Outflows by",
        "rolemaintenance.changeScreen": "Change Role - SMART-Predict",
        "tooltip.accountGroup.groupType": "Sort by Dynamic",
        "tooltip.addNewSweepIntermediary": "Add New Sweep Intermediary",
        "movementsearch.reference1": "Reference 1",
        "movementsearch.reference3": "Reference 3",
        "movementsearch.reference2": "Reference 2",
        "tooltip.refreshProcessStatus": "Refresh currency process status details",
        "ilmReport.collateral": "Collateral",
        "menuaccessoptions.viewScreen": "View Menu Access Options - SMART-Predict",
        "bookCode.entity": "Entity",
        "mainacctId": "Main Account",
        "entity.balance": "Balance",
        "cashRsvrBal.fillDays": "Fill Days",
        "tooltip.enterMvmId": "Enter movement ID",
        "account.schedSweep.tooltip.entityId": "Entity ID",
        "PreAdviceInput.title.window": "Pre Advice Input - SMART-Predict",
        "monitor.dataInsertionJobRunning": "Data build in progress, please wait...",
        "groupMonitor.dateMMDDYY": "MM/dd/yyyy",
        "movement.senderToReceiverInfo": "Sender/Receiver Info",
        "sweep.CcyName": "Ccy Name",
        "tooltip.selectNewCountry": "Select new country",
        "ilm.options.globalChart_toolTip": "Check box to include Global View chart in entity/currency tab",
        "intradayBalances.entity": "Entity",
        "tooltip.sortAlertMsgs": "Sort by alert messages",
        "tooltip.changeSelectMvm": "Change selected movement",
        "additionalColumns.alertOverwriteProfile": "Are you sure you want to overwrite this profile? ",
        "tooltip.applyCurrencyThreshold": "Apply Currency Threshold",
        "accountMonitorNew.alert.exceedDate": "Selected date should be within 30 days from current date",
        "tooltip.refreshDesktop": "Refresh desktop",
        "acctiban": "IBAN",
        "tooltip.enterOrderInstText2": "Enter Ordering Institution text 2",
        "tooltip.enterOrderInstText3": "Enter Ordering Institution text 3",
        "tooltip.enterOrderInstText1": "Enter Ordering Institution text 1",
        "tooltip.selectlevel": "Select Group Level",
        "tooltip.selectSign": "Select sign",
        "ccyMonitorOptions.fontSize": "Font Size",
        "msdAdditionalColumns.useAddColsCheckTooltip": "Use additional columns to update MSD main grid",
        "tooltip.scenTotals": "Total Scenarios Alerts Count",
        "fomat.canddInt": "Cancel Debit Internal",
        "tooltip.changeSelectedShortcut": "Change selected shortcut",
        "tooltip.sortSeqNo": "Sort by sequence no.",
        "label.forecasttemplate.column.public": "Public",
        "org.apache.struts.taglib.bean.format.date": "hh 'o''clock' a, zzzz",
        "addJob.title.days": "Days",
        "label.updateResponse": "Update response",
        "label.personalCurrencyList.ccyName": "Name",
        "label.forecastMonitorTemplateAddDetail.contributeCheck": "Contributes to Total with Multiplier",
        "alert.columndelete": "Are you sure you want to remove this record?",
        "criticalMvtUpdate.toolTip.category": "Select a Category",
        "sweepIntermediaries.currencyCode": "Currency Code",
        "ilmExcelReport.creditLinesCommitted": "Credit lines committed",
        "tooltip.movement.value": "Value Date",
        "tooltip.sweepMsgFormat.selectInterfaceId": "Select Interface ID",
        "sweepSearchList.status1": "Status",
        "ilmthroughputbreakdown.currentFilter": "Current Filter",
        "ilmExcelReport.critical": "Critical",
        "scenarioCategoryAdd.title.window.changeScreen": "Change Scenario Category",
        "errorLog.userId": "User",
        "ilmanalysismonitor.saveProfileImageTooltip": "Save profile",
        "tooltip.forecastMonitorTemplateAdd.templateId": "Enter Template ID",
        "label.ilmccyprocess.currentstatus.skipped": "Skipped",
        "tooltip.enterOrderInstText4": "Enter Ordering Institution text 4",
        "tooltip.enterOrderInstText5": "Enter Ordering Institution text 5",
        "bookmaintenance.changeScreen": "Change Book",
        "ilmExcelReport.ccyMultiplierEnabled": "Enabled",
        "multipleMvtActions.noRadio": "No",
        "tooltip.sortByBalance": "Sort by balance",
        "rolemaintenance.mainScreen": "Role Setup - SMART-Predict",
        "addJob.title.evaluateRunDateAs": "Evaluate RUN_DATE as",
        "matchAuditLog.movement": "Movement",
        "account.schedulesweep.tooltip.otherBalanceTypeP": "Predicted balance type from which sweeping will be performed",
        "movementsearch.both": "Both",
        "tooltip.entity.movementRetentionParameter": "Large Movements retention period",
        "label.entityprocess.defaultRunTime": "Default Run Time",
        "label.sweepSuccessfullySubmitted": "successfully submitted",
        "scenario.events.tooltip.delete": "Delete event",
        "positionLevel.title.mainWindow": "Position Level Maintenance - SMART-Predict",
        "multiMvtActions.importFailed": "Import failed",
        "passwordRules.legend.passParams": "Password parameters",
        "tooltip.forecastMonitorTemplateAdd.public": "Select Public Status",
        "label.accountattributehdr.minlength": "Minimum Length",
        "connectionPool.sid": "SID",
        "entity.CountryCode": "Country code",
        "alertInstance.lastRaised": "Last Raised Date",
        "sweep.postlevel": "Position Level",
        "defaultaccountmaintenance.accountId": "Account",
        "tooltip.movOrderingInst": "Click to select an ordering institution",
        "account.schedSweep.heading.sweepAccountId": "Account ID<br>",
        "label.forecastAssumptionsChange.title.window": "Forecast Monitor Assumptions (Change) - SMART-Predict",
        "account.schedulesweep.tooltip.otherBalanceTypeE": "External balance type from which sweeping will be performed",
        "entitymaintenance.addScreen": "Add Entity - SMART-Predict",
        "role.roleName1": "Role Name*",
        "ilmSummary.tooltip.unsettled": "Unsettled movements",
        "role.roleInput": "Input",
        "balmaintenance.forecastSOD": "Forecast SOD",
        "alert.forecastMonitorOption.greaterThanPrevious": "The value should be greater than the previous value",
        "ilmReport.endOfReport": "*** End of Report ***",
        "tooltip.thresholds": "Thresholds",
        "role.roleAccounts": "Accounts",
        "maintenanceevent.summary.seearchbutton.label": "Search",
        "label.schedreporthist.column.fileName": "File Name",
        "toolTip.scenarioConfig": "Configure",
        "movementsearch.cash": "Cash",
        "ilmtransaction.title.window": "ILM Transaction Set Maintenance - SMART-Predict",
        "scenario.changeLabel": "Change",
        "tooltip.entity.smallMovementRetain": "Small Movements Retention",
        "correspondentaccountmaintenance.title.changeWindow": "Change Correspondent Account Alias - SMART-Predict",
        "tooltip.deletemvmntdetails": "Delete selected movement",
        "tooltip.deleteinterest": "Delete selected account interest",
        "tooltip.maxSweepAmount": "Maximum Sweep Amount",
        "tooltip.sortTotalNoBooks": "Sort by total no. of books",
        "scenarioSummary.Entity": "Entity",
        "aliasTooltip.sortalias": "Sort by currency alias",
        "ilmScenario.legend.scenarioMovements": "Scenario Movements",
        "tooltip.4positionName": "Fourth position name",
        "instancerecord.raisedUser_header": "Raised User",
        "instancerecord.sign_header": "Sign",
        "ilmTransactionSet.entityId": "Entity ID",
        "alert.interestCharges.toDate": "To Date should be greater than or equal to From Date",
        "sweepInter.mainScreen": "Sweep Intermediary Maintenance - SMART-Predict",
        "systemlog": "System Log",
        "account.schedSweep.tooltip.targetBalanceType": "Target Type",
        "tooltip.movement.productType": "Product Type",
        "label.forecastMonitorTemplateAdd.public": "Public",
        "alertInstance.entityId": "ENTITY_ID",
        "multiMvtActions.ext": "Ext",
        "tooltip.changePwdRules": "Change password rules",
        "addjob.title.distlist": "Distribution List",
        "label.personalEntityList.entityName": "Name",
        "correspondentaccountmaintenance.accountName": "Account Name",
        "connectionPool.duration": "Open For (s)",
        "ilmScenario.percent": "%",
        "tooltip.primaryAccId": "Sort by Primary Account ID",
        "bookMonitor.monitor": "Monitor Type",
        "toolTip.scenarioAdd": "Add",
        "tooltip.enterDaysRetainErrorLog": "Enter number of days to retain error log",
        "tooltip.counterPartId": "Enter Counterparty ID",
        "ilmScenario.label.AllowReporting": "Allow ILM Reporting",
        "entity.InputRetentionParameter": "Input retention parameter",
        "scenario.tooltip.apiType": "Select API Type",
        "tooltip.sortByReasonDesc": "Sort by Reason Description",
        "group.noOfBookCode": "Total Book",
        "sweep.drMsg": "DR A/C Msg",
        "tooltip.accountParty": "Predict Party ID of the party which holds or services this account",
        "entity.CloseConfirm": "You have chosen to close the window. All unsaved changes will be lost. Are you sure?",
        "errors.authorization.attack": "As a security measure, your request is blocked, an authorization bypass is detected!",
        "errors.databaseexp": "A database problem occurred",
        "workflowmonitor.date.title": "Date",
        "tooltip.enterLocationId": "Enter Location ID",
        "ilmScenario.legend.mainDetails": "Main Details",
        "label.schedReportHist.jobId": "Job Id",
        "tooltip.entityMonitor.viewGroupMonitor": "Select Group to view Group Monitor",
        "label.entityprocess.lastStarted": "Last Started",
        "role.workQueueAccessAdd.suspended": "Suspended",
        "tooltip.manualSweep": "Manual sweep",
        "tooltip.pwdExpDays": "Number of days before password expires",
        "error.killSession": "Unable to kill session, please contact your Administrator",
        "tooltip.selectNewAcStatus": "Select a new account status",
        "scenarioSummary.title.listemails": "List Emails",
        "label.schedreporthist.column.exportStatus": "Status",
        "tip.schedReportHist.jobId": "Selected Job Id",
        "alert.acctbreakdownmonitor.date": "Please enter a valid date",
        "preAdviceInput.importInProgress": "Import in progress",
        "tooltip.timeslotSize": "Specify a timeslot size (time interval between data points) that will be used for aggregation of movement data",
        "errors.logon.invalid": "Invalid login",
        "tooltip.AddNewCurrencyGroup": "Add new currency group",
        "errors.logon.disableUser": "This user ID has been locked. Please contact your System Administrator.",
        "tooltip.findoraddpopup.Search": "Execute Search",
        "tooltip.sortGrName": "Sort by group name",
        "monitor.nomovements": "No movements available",
        "entity.ChangeEntity": "Change entity",
        "entity.general.retentionFlag.yes": "Yes",
        "tooltip.addNewBookcode": "Add new bookcode",
        "movement.diffcurrency": "Movement is not for the selected currency",
        "tooltip.sortByExternalWorking": "Sort by External SOD Type",
        "interfacerulesmaintenance.add.ruleID": "Rule ID",
        "scenario.defParamsLabel": "Define Parameters",
        "messageFormats.outputType1": "Output",
        "alert.passwordExpired": "&nbsp;Your password has expired, please change it",
        "alert.forecastMonitor.accessNotAvl": "Access not available for",
        "movementroll.rollNotAllowed": "This movement cannot be Rolled Over",
        "tooltip.amountUnder": "Enter amount under",
        "screan.screanVersion": "Screen Version",
        "multipleMvtActions.addNoteRadio": "Add a movement note only",
        "ilmReport.reportType.multiCurrencyILMReportExcel": "Multi-Currency ILM Report - Excel",
        "tooltip.groupName": "Enter currency group name",
        "tooltip.addPrimaryForecast": "Select Primary Forecast",
        "instancerecord.raisedDatetime_tooltip": "Raised Date",
        "accountmaintenanceadd.defaulttargetBalance": "Default Target Balance",
        "id.currencyCode": "Currency",
        "tooltip.AliasName": "Enter currency alias",
        "currencymaintenance.alert.validDecimal": "Please enter a valid decimal number",
        "tooltip.canceloption": "Cancel changes and exit",
        "tooltip.findoraddpopup.name": "Enter Name",
        "acctMaintenance.tooltip.inThisEntity": "In this entity",
        "ilmSummary.confD": "Pay % Conf. ",
        "multiMvtActions.movementId": "MovementID",
        "alert.ContactSysAdm": ". Please contact your System Administrator.",
        "tooltip.ccyMonitorOptions.selectFontSizeNormal": "Select Normal Font Size",
        "ilmExcelReport.centralBankCollateral": "Central Bank Collateral",
        "label.personalEntityList.button.ok": "OK",
        "tooltip.changeAuthSweepAmount": "Change authorise sweep amount",
        "type.numeric": "Numeric",
        "tooltip.sortCrAccID": "Sort by CR account ID",
        "sweepSearchList.accountIdDr": "DR Account",
        "tooltip.deleteCategory": "Delete Selected Scenario Category",
        "sweep.cutOffnotDefined": "Invalid cut-off time defined",
        "tooltip.centralMonitorOptions.selectFontSizeNormal": "Select Normal Font Size",
        "interfacemonitor.header.health": "Health",
        "button.currency": "Ccy",
        "alert.changeScenarioId": "Please change the scenario ID, since 'STANDARD' will be reserved for the standard dataset",
        "metagrpmaintenance.addScreen": "Add Metagroup - SMART-Predict",
        "sweepSearchList.accountIdCr": "CR Account",
        "ilmExcelReport.threshold": "Threshold",
        "button.positionLevel": "Pos Lvl",
        "tooltip.cutOffTimeHM": "Cut off time (hh:mm)",
        "button.Unopen": "Unopen",
        "bookCode.bookLocation": "Location",
        "maintenanceevent.summary.dispalybutton.label": "Display",
        "scenarioAdvanced.facilityDetail": "Facility Detail",
        "sessionInactiveMessage": "Your Session has been logged off due to inactivity",
        "tooltip.acctId": "Account ID",
        "ilmSummary.unexpected": "Unexpected",
        "tooltip.movement.movementId": "Movement ID",
        "message.Value": "Value",
        "tooltip.enterLanguage": "Enter language",
        "alert.accountattributehdr.checkregex": "Please enter a valid Regex",
        "sweepInter.childScreen": "Add Sweep Intermediary - SMART-Predict",
        "tooltip.deleteSelCurr": "Delete selected currency",
        "tooltip.sweepTime": "Sweep Time (hh:mm)",
        "tip.accountattributeadd.value": "Specify a value",
        "label.forecastMonitorTemplateAddDetail.columnType": "Column Type",
        "movementsearch.uetr": "UETR",
        "alert.startMonitor": "Please define Central Bank parameters in Entity Maintenance before starting this monitor.",
        "tooltip.sortJobId": "Sort by job ID",
        "connectionPool.idleConnections": "Idle Connections",
        "ilmExcelReport.tabNameCriticalOutflowsDetail": "Critical Outflows Detail",
        "tooltip.deleteQAcclist": "Delete selected queue access",
        "type": "Type",
        "ilmReport.include": "Include:",
        "tooltip.messageTypeSearchCheckbox": "Select Check box to enter message type Search String",
        "account.schedulesweep.label.sumAccountsN": "No",
        "tooltip.scenarioScheduled": "Select Scheduled",
        "tooltip.selectMatching": "Select Matching",
        "cashRsvrBal.currency": "Currency",
        "account.schedulesweep.label.sumAccountsY": "Yes",
        "preAdviceInput.invalidImportedData": "Some fields values are missing.    Please check if you respect the provided header.",
        "enableDst": "Enable Daylight Saving Adjustment",
        "sweepCode": "Sweep Code",
        "tooltip.selectArchiveId": "Select the Archive ID",
        "tooltip.selectToDateMMDDYY": "Enter To date (MM/DD/YYYY)",
        "label.roleBasedControl.facility.tooltip": "Facility Screen",
        "tooltip.entityMonitor.mvmntBrkdown": "Select Movement to view Movement Summary Detail",
        "sweep.amount": "Amount",
        "correspondentaccountmaintenance.add.accountID": "Account ID*",
        "ilmScenario.label.otherSourcesAvlbl": "Other Liquid Assets Availability",
        "button.cpyFrom": "CpyFrom",
        "tooltip.fromDate": "Enter from date",
        "changeBalance.lastMov": "Last Movement",
        "tooltip.addNewCorrespondentAccount": "Add new Correspondent Account",
        "instancerecord.lastRaisedUser_header": "Last Raised User",
        "label.sweepSavedWithID": "Sweep saved with ID: ",
        "groupMonitor.refreshRateSelected": "Refresh rate selected was below minimum.\\nSet to 5 seconds",
        "label.forecasttemplate.column.entity": "Entity",
        "errors.DataAccessException": "DataAccessException",
        "button.forecastMonitor.change": "Change",
        "ilmanalysismonitor.grid.fcasteod.tooltip": "Forecast End Of Day",
        "changeBalance.legend.userDetail": "User Details",
        "sweepinglimits.addScreen": "Add Sweeping Limits - SMART-Predict",
        "acctmain.balances": "Balance Calculation",
        "tooltip.sortLoroBalance": "Sort by Loro balance",
        "tooltip.addSwpLmtCurr": "Add new sweep limits by currency",
        "criticalMvtUpdate.toolTip.orderInCategory": "Order of display within the category (used in ILM reporting)",
        "tooltip.enterGroupId": "Enter a group ID",
        "pwdchange.password.bothBoxes": "The passwords you typed do not match. Type the new passwords in both text boxes.",
        "account.schedulesweep.tooltip.minAountInput": "Sweeping is only required when the balance is above target and consequently needs to be debited",
        "tooltip.excMvmInDataExternal": "Exclude movement in data external",
        "tooltip.facility": "Select facility",
        "tooltip.time": "Enter Time",
        "tooltip.executeSearch": "Execute Search",
        "msd.filterComboTooltip": "Select Filter",
        "sweepinglimits.mainScreen": "Sweeping Limits - SMART-Predict",
        "tooltip.selSortOrder": "Select sort order",
        "role.inputInterruption": "Interface Interruption",
        "addjob.distlist.alert": "Based on selection, the mail will not be sent to any user. Please select at least one user/role.",
        "accountmonitor.confirm.testMessageP": "Include/Exclude this account and all linked accounts from the totals?",
        "tooltip.errorDesc": "Sort by error description",
        "accountmonitor.confirm.testMessageN": "Include this account and all linked accounts in the totals?",
        "accountmonitor.confirm.testMessageY": "Exclude this account and all linked accounts from the totals?",
        "tooltip.enterNewAcName": "Enter new account name",
        "tooltip.delSelectedUser": "Delete selected user",
        "label.accountattributehdr.title.window": "Define Attribute - SMART-Predict",
        "errors.logon.verify.role": "Your user is missing a Role, Please contact your System Administrator.",
        "tooltip.SortByTolerance": "Sort by tolerance",
        "scenarioSummary.Count": "Count",
        "tooltip.MvmID": "Movement ID",
        "tooltip.changeMsg": "Change selected alert message",
        "tooltip.autoOpenUnsettled": "Auto Open Unsettled",
        "button.unmatch": "Unmatch",
        "ilm.options.TabDate_toolTip": "Select Date to be used in entity/currency tab",
        "label.accountspecificsweepformat.column.accountId": "Account ID",
        "tooltip.sortbyreference3": "Sort by reference 3",
        "errors.content.notAllowed": "WARNING: Malicious content detected in request!",
        "changeBalance.source": "(Source)",
        "tooltip.sortbyreference2": "Sort by reference 2",
        "button.suspend": "Suspend",
        "errors.DataRetrievalFailureException": "DataRetrievalFailureException",
        "scenarioSummary.active": "Active",
        "userStatus.header.logonDtTime": "LogOn Date & Time",
        "auditLog.to": "To",
        "interest.tooltip.updateDate": "Update date & time",
        "tooltip.entityMonitorOptions.usePersonalCcyList": "Personal Currency List",
        "tooltip.secondMin": "Enter Second Minimum",
        "tooltip.sortFieldType": "Sort by field type",
        "tooltip.enterPostingDate": "Enter posting date",
        "instancerecord.expand_header": "Expand",
        "maintenanceevent.details.alert.actionperfermored": "The action is successfully performed",
        "CriticalPay.category": "Category",
        "tooltip.movement.bookcode": "Bookcode",
        "internalMesgsFrame.title.window": "Error",
        "acctmaintenance.predict": "Prediction",
        "button.ccyGrp": "Ccy Group ",
        "tooltip.extAuthId": "ID of user in external authenticating system",
        "label.personalEntityList.display": "Display",
        "scenarioAdvanced.facilityID": "Facility ID",
        "multipleMvtActions.label.importButton": "Import",
        "scenario.schedParamsDesc": "(Note: Parameters in the query needs to be in \"P{param}\" format)",
        "tooltip.EnterGroupIDLvl1": "Enter group ID level 1",
        "tooltip.postionLevel": "Select the position level",
        "tooltip.EnterGroupIDLvl2": "Enter group ID level 2",
        "tooltip.EnterGroupIDLvl3": "Enter group ID level 3",
        "accountmaintenance.AccountIsIlmDataMember": "This account is a member of an ILM group and it is therefore likely that its data will be require archiving. Please ensure this accountâs data is not required in the archive when unchecking this checkbox",
        "sweepsearch.authorizedamt": "Authorised Amount",
        "movementrollover.title.window": "Movement Rollover - SMART-Predict",
        "ilmExcelReport.tabNameAvailableLiquidity": "Available Liquidity",
        "sweep.messageGenerationError": "Error in message generation.<br>Refer to the error log for details!",
        "button.Open": "Open",
        "scenario.addLabel": "Add",
        "notes.alert.recordNotFound": "Record does not exist",
        "tooltip.ChangeSelectedReasonCode": "Change selected reason",
        "alert.mail.sendSuccess": "Mail sent successfully",
        "tooltip.interfaceMonitor.stopInterface": "Click to stop interface",
        "alertInstance.tooltip.msgBody": "Message Body",
        "maintEvent.action": "Type",
        "dateFormat": "Date Format",
        "tooltip.sortMatchStatus": "Sort by match status",
        "inputexceptions.header.repair": "Repair",
        "tooltip.sortGrLevelName1": "Sort by ",
        "tooltip.sortGrLevelName2": "Sort by ",
        "alert.selectDate": "Please select a date",
        "tooltip.unMatch": "Unmatch all movements and delete this match",
        "maintenanceLogView.date": "Date",
        "tooltip.sortGrLevelName3": "Sort by ",
        "drmsgcancel": "Debit Msg Cancel",
        "centralMonitorOptions.fontSizeSmall": "Small",
        "tooltip.scenarioCurrencyColmun": "Enter Scenario Currency Column",
        "role.workQueueAccessAdd.confirmed": "Confirmed",
        "ccyAccMaintPeriod.alert.updateValue": "Tier/Minimum Reserve has changed.     Do you wish to update Target Avg Balance to the new Charge Threshold?",
        "movementsearch.productType": "Product Type",
        "tooltip.entityMonitor.close": "Close window",
        "acctMaintenance.acctname": "Account Name",
        "reports.process": "Process",
        "tooltip.scenarioDescription": "Enter Scenario Description",
        "balMaintenance.name": "Name",
        "ccyAccMaintPeriod.tooltip.change": "Change",
        "tooltip.forecastMonitorTemplateAddDetail.contributeCheck": "Select to Contribute to total",
        "ilmanalysismonitor.grid.thresholds": "Thresholds",
        "ccyAccMaintPeriod.entity.id": "Entity",
        "ilmReport.titleBaselAReportDaily": "Daily Liquidity Management Report - Basel A: Direct Participant",
        "tooltip.creditExtMsg": "Credit External Message Format",
        "qualityTab.today": "Today",
        "tooltip.sort.transactionEntityId": "Sort by Entity ID",
        "ilmanalysismonitor.grid.exteod": "Ext.EOD",
        "instancemessage.inputDate_tooltip": "Input Date",
        "scheduledReportHist.status.notAvailable": "N/A",
        "alert.forecastMonitorOption.bucketExist": "Selected bucket already exists",
        "label.personalEntityList.defaultDays": "Display Days",
        "label.entity.positionName": "Enter position name",
        "batchScheduler.scheduledJobType": "Scheduled Job Type",
        "tooltip.closeWindow": "Close window",
        "tooltip.selectMultiplier": "Select Multiplier",
        "inputtime": "Input Time",
        "ccyMonitorOptions.fontSizeSmall": "Small",
        "role.Name": "Name",
        "alert.dateFromYesterday": "Please enter a date from yesterday to last 7 days",
        "alertInstance.tab.message": "Message",
        "multipleMvtActions.reconcileRadio": "Reconcile",
        "tooltip.copyMatchQualityProfile": "Copy this match quality profile",
        "tooltip.enable": "Enable",
        "tooltip.noRecentlyUsedPwds": "Number of recently used passwords to be disallowed",
        "ilmExcelReport.firstLargestCustomer": "Largest Customer",
        "throuputmonitor.tooltip.actinf": "Actuals Inflows",
        "movementDisplay.status": "Status",
        "scenario.events.msgFormat": "Maintain",
        "scenario.treeBreakDown2": "Tree Breakdown 2",
        "scenario.treeBreakDown1": "Tree Breakdown 1",
        "accountmonitor.acctId": "Account",
        "tooltip.personalEntityList.button.deletesum": "Delete Sum Entity",
        "ilmReport.endOfDay": "End of Day",
        "tooltip.sortSweeptype": "Sort by sweep type",
        "tooltip.sortbysign": "Sort by sign",
        "tooltip.reportingCurr": "Reporting currency",
        "label.adjcumulativebalance": "Adj. Cumulative",
        "instancerecord.currencyCode_tooltip": "Ccy",
        "tooltip.forecastMonitor.bookBrkdown": "Select Book to view Book Monitor",
        "sweepDetails.unlockAll": "Unlock All",
        "ilmScenario.label.unencumberedLiqAssetAvlbl": "Unencumbered Liquid Assets Availability",
        "label.from": "From",
        "additionalColumns.alertProfileSaved": "The profile was successfully saved",
        "exchangeRate": "Ccy Exchange Rate",
        "sweepSearch.alert.datecomparison": "From date must be earlier than To date",
        "addjob.Seconds": "Sec",
        "sweepinglimits.changeScreen": "Change Sweeping Limits - SMART-Predict",
        "matchQuality.changeScreen": "Change Match Quality - SMART-Predict",
        "inputconfig.header.threshold": "Alerts Threshold (min)",
        "tooltip.helpScreen": "Help screen content",
        "party.alert.pagination": "Please enter a valid page no",
        "MovMsgDisplay.Message": "Message",
        "userprofile.name": "Profile Name",
        "tooltip.accountField": "Sort by account",
        "criticalMvtUpdate.updateMvt": "UPDATE P_MOVEMENT",
        "label.personalCurrencyList.applyToAllCurrency": "Apply to All Currencies:",
        "label.dataFor": "Data For",
        "tooltip.enterHolidayDate": "Enter holiday date",
        "corporateAccount.title.window": "Corporate Entries - SMART-Predict",
        "entity.generalTab": "General",
        "maintEvent.maintEventId": "Maint Event Id",
        "tooltip.sortCategoryDisplayName": "Sort by Display Tab Name",
        "connectionPool.tooltip.stackTrace": "Java stack trace for opening connection",
        "maintenanceLog.tooltip.changedTo": "Changed To",
        "tooltip.sortLogDate": "Sort by log date",
        "alertInstance.alertInstanceFieldSet": "Attributes",
        "tooltip.searchMvmId": "Search movement ID",
        "MaintenanceLog.title.window": "Maintenance Log - SMART-Predict",
        "tooltip.saveProfil": "Save profile",
        "instancemessage.updateDate_tooltip": "Update Date ",
        "tooltip.jobdetail": "Job Detail",
        "balmaintenance.userNotes": "User Notes",
        "scenario.events.emailFormat": "Email Format",
        "title.preadvice.changeWindow": "Change Pre-advice - SMART-Predict",
        "scenarioSummary.flashScen": "Flash",
        "errors.requestURI.log": "Request URI",
        "sweep.cancelled": "Cancelled",
        "tooltip.notesAvailable": "Notes available",
        "exportPages.label.allPages": "All Pages",
        "manSweeping.title.window": "Manual Sweep - SMART-Predict",
        "metaGroup.financeTrade": "Fin/Trade",
        "entity.field": "Field",
        "ilmAccountGroupDetails.firstMax": "First Maximum",
        "alert.interfaceMonitor.toDate": "To date should be greater than From date",
        "tooltip.2positionLevel": "Second position level",
        "label.roleBasedControl.facility": "Facility",
        "title.linkedaccount": "Linked Account Details - SMART-Predict",
        "role.perCurrrency.priority": "Priority",
        "label.schedReportHist.fileId": "File Id",
        "sweepsearch.outgoingmsgs": "Outgoing Message",
        "label.forecastMonitor.bookCode": "Book",
        "movementsearch.valuedate": "Value Date",
        "tooltip.changeMinSweepAmount": "Change minimum sweep amount",
        "tooltip.sortInterimPositionLevel": "Sort by interim position level",
        "systemLog.logDate_Date": "Date",
        "ilmExcelReport.netInflowsOutflows": "Actuals: Net (Inflow - Outflow)",
        "movement.msgformat": "Format",
        "tooltip.movement.alerting": "Alerting",
        "ccyAccMaintPeriod.tier": "Tier",
        "role.authorization.authorizationInput": "Authorisation",
        "button.forecastMonitor.option": "Options",
        "manualInput.title.window": "Manual Input - SMART-Predict",
        "preAdviceInput.importFrom": "Imported from",
        "title.archiveadd": "Archive Details Add - SMART-Predict",
        "intradayBalances.date": "Report Date",
        "tooltip.exlMvmInitMonitors": "Exclude movement from dealer monitors",
        "ccyAccMaintPeriod.excludeFillPeriodFromAvgLabel": "Exclude Fill Period from average calculation",
        "correspondentaccountmaintenance.manualMessageType": "Manual",
        "alert.interfaceSettings.savecancelconfirmation": "You have already selected an interface for modification. Do you want to Save or Cancel the changes?",
        "balmaintenance.effectiveForecastSOD": "Effective Forecast SOD",
        "multiMvtActions.cparty": "Cparty",
        "tooltip.sortbyupdate_date": "Sort by update date",
        "accountmaintenance.currentInterestRates": "Current Interest Rates",
        "messageFields.endPos1": "End Pos",
        "screenTitle.centralBankMonitor": "Central Bank Monitor",
        "status.roleId": "RoleId",
        "workflowmonitor.close.title": "Close",
        "ilmanalysismonitor.legend.forecIncludeAct": "Forecast (inc. actuals)",
        "outstanding.tabs.today1": "Today+1",
        "outstanding.tabs.today2": "Today+2",
        "scenario.tooltip.signColCombo": "Enter Scenario Sign column",
        "movement.balances": "Balance",
        "currencyInterest.inputDate": "Input Date-Time",
        "button.displayList": "Display List",
        "accountMonitor.label.group": "Group",
        "scenario.timeAlreadyExists": "Scheduler with the same time was already configured",
        "contactDetails.alert.notstrtemailaddcontain@": "Email address must not start with @",
        "label.forecastMonitorTemplateChange.title.window": "Change Forecast Monitor Template - SMART-Predict",
        "alert.killSession": "User is already logged in, do you want to kill the existing session and continue?",
        "manualInput.alert.mvmIdamended": "Movement ID field has been amended - please choose action again after screen refreshes",
        "sweep.creditaccountId": "Account ID CR",
        "tooltip.changeQAcclist": "Change selected queue access",
        "groupCode.entity": "Entity",
        "account.status.open": "Open",
        "cannotkill.message": "Cannot kill your own session",
        "tooltip.enterAmount": "Enter amount",
        "sweepinglimits.viewScreen": "View Sweeping Limits - SMART-Predict",
        "reasonMaintenance.description": "Description",
        "tooltip.entity": "Sort by Entity",
        "alertDisplay.ccyNotFound": "<Currency not found>",
        "errors.CouldNotSaveJobReportWithSameNameExceptioninAdd": "Report record with Same name and type already exists",
        "pwd.mixedCase": "Mixed Case",
        "label.warningInternal": "Warning - Internal",
        "alert.deleteSystemScenario": "You can't delete a System Scenario",
        "intradayBalances.accountScreenForGraph": "Intraday Balance Graph",
        "addJob.alert.SchDateTime": "Scheduled date-time should be greater than or equal to current date-time",
        "label.exportMultiPages": "First {0} pages will be exported. Do you want to continue?",
        "scenario.tooltip.mvt": "Check MOVEMENT ID",
        "BookgroupMonitor.title.window": "Book Group Monitor - SMART-Predict",
        "alertInstance.tab.attributes": "Attributes",
        "tooltip.entityMonitorOptions.currency": "Click to open Personal Currency List screen",
        "label.nonworkday.applyEntityCountry": "Apply Entity Country",
        "scenario.schedule.tooltip.scenarioId": "Scenario ID",
        "button.test": "Test",
        "error.unexpectedError": "An unexpected error has occurred.",
        "scenario.events.literal": "Literal",
        "errors.entity.entityId.minlength": "Entity ID cannot be greater than 12 characters.<BR>",
        "criticalMvtUpdate.toolTip.reportableCheck": "Check to be listed in ILM reporting",
        "tooltip.lastlog": "Last Login",
        "scenario.sytemScenarioAlert": "INVALID: Scenario ID should not start with 'SYS_'. \\nPlease specify a different scenario ID",
        "tooltip.isIlmContributer": "Accounts flagged as ILM liquidity contributors can be easily selected to be member of the ILM global currency group for the purpose of Basel reporting",
        "button.submit": "Submit",
        "label.accountspecificsweepformat.tooltip.specAccountID": "Select Specific Account ID",
        "ilmTransactionSet.transactionSetId": "Transaction Set ID",
        "tooltip.deleteScenario": "Delete selected scenario",
        "movementsearch.sec": "Securities",
        "ilmanalysismonitor.grid.name.tooltip": "Name Of Scenario ID ",
        "movementDisplay.cancelled": "Cancelled",
        "ccyAccMaintPeriod.tooltip.deleteButton": "Delete Account Currency Maintenance Period",
        "label.record": "record",
        "balMaintenance.inputDateAsString": "Update Date",
        "button.schedReportHist.stratDate": "From",
        "movement.extraRef": "Extra Ref",
        "type.date": "Date",
        "label.entityMonitorOptions.usePersonalEntityList": "Use Personal Entity List",
        "custodian": "Custodian",
        "messageFormats.overdue": "ACK Overdue",
        "movementsearch.suspended": "Suspended",
        "sessionTimeOutMessage": "Your Session has been timed out",
        "label.internalBalance": "Internal Balance",
        "entity.balancelog": "Balance Log",
        "attributeusagesummary.name": "Name",
        "tooltip.enterRoleName": "Enter role name",
        "label.accountattribute.attribute": "Attribute",
        "personalCurrency.priority": "Priority",
        "tooltip.monday": "Monday",
        "alertDisplay.entityNotFound": "<Entity not found>",
        "button.tooltip.schedReportHist.startDate": "Select a From Date",
        "instancerecord.matchId_tooltip": "Match Id",
        "addjob.label.noAccessinEntityCurrency": "The selected role does not have access in combination {0} entity and {1} currency",
        "ilmExcelReport.ebs": "EBS",
        "sweepSearchList.drIntMsg": "DR INT MSG",
        "movement.targetbalance": "Target Balance",
        "balancetype": "Type",
        "tooltip.refmvmntdetails": "Refer selected movement",
        "ilmExcelReport.thirdLargestCustomer": "3rd Largest Customer",
        "sweepsearch.externalstatus": "External Status",
        "tooltip.entityMonitorOptions.fontsmall": "Select Small Font Size",
        "criticalMvtUpdate.where": "WHERE",
        "alert.cyclicInervalCannotBeZero": "Cyclic Interval cannot be Zero",
        "currencyMonitor.alert.defDays": "Default days should be between 1 and 14",
        "tooltip.sortApplyCurrencyCountry": "Sort by apply currency country",
        "maintenanceevent.summary.dateselection.to": "Show Events To Date",
        "ilmReport.ilmGroup.title": "ILM Group",
        "tooltip.forecastMonitorOptions.hideweekend": "Hide Weekend",
        "sweep.alreadySubmitted": "Sweep ID(s) <b>{0}</b> already {1} by another user",
        "tooltip.debitInterMsg": "Debit Intermediary Message Format",
        "messageFormats.usage.other": "Other",
        "instancerecord.entityId_header": "Entity",
        "button.load": "Process",
        "criticalMvtUpdate.toolTip.entity": "; Select entity ID",
        "centralBankMonitor.fromToOutsideRange": "From date and To date are outside the defined range",
        "pwdchange.welcomePredict": "Welcome to SMART-Predict",
        "title.archivechange": "Archive Details Update - SMART-Predict",
        "scenario.guiHighlight.facilityId": "GUI Facility ID",
        "alert.criticalPaymentType.activeBetween": "Please fill both Start/End time fields",
        "preAdviceInput.column.sign": "Sign;",
        "crossReference.movement": "Movement",
        "tooltip.selectCuurencyGrp": "Select currency group",
        "tooltip.isCentralBank": "Accounts flagged as central bank members can be easily selected to be member of the ILM central bank group for the purpose of Basel reporting",
        "roleBasedControl.column.authOther.tooltip": "Specify whether this role user can authorise other use",
        "tooltip.button.multiMvtUpdate": "Perform multiple movement action with 2 or more selected movements",
        "alert.delayTimeInterval": "Delay time must be in (+/- 0-999) interval",
        "label.accountspecificsweepformat.tooltip.internalDebit": "Select Internal Debit",
        "instancerecord.resolvedByUser_header": "Resolved By",
        "reports.submitR": "Report",
        "sweepCancel.colValue.Auto": "Auto",
        "alert.ilmanalysis.enableToMaintain": "Invalid: your role does not specify access to scenario/groups for this entity or for this screen",
        "ilm.options.summary_toolTip": "Check box fro group to be included in Summary tab",
        "groupmaintenance.alert.validString": "Please enter a valid string",
        "holiday.holidayDate_Date": "Date",
        "groupMonitor.today": "Today",
        "tooltip.sortGroupLevel": "Sort by group level",
        "tooltip.account.autoopenunsettled": "Automatically hold open unsettled movements at end of day?",
        "tooltip.durationHours": "Enter duration (Hrs)",
        "tooltip.forecastMonitor.rateButton": "Click to open Refresh Rate Window",
        "movementsearch.inputdate": "Input Date",
        "alert.fromDateShouldBeLess": "From Date should be less than or equal to To Date",
        "alert.nonZeroExchangeRate": "Please enter a non-zero value for exchange rate",
        "scenarioSummary.zeroTotals": "Hide Zero Totals",
        "ilmtransSetAdd.title.window.changeScreen": "Change ILM Transaction Set Display - SMART-Predict",
        "label.accountspecificsweepformat.text.accountId": "Account ID",
        "ilmanalysismonitor.nodata.forexport": "No data is available for the export",
        "currency.threshold": "Threshold ( millions )",
        "tooltip.changeEntity": "Change entity",
        "tooltip.selectDrMsg": "Select debit message format",
        "accountMonitorNew.screenSelectorText": "Breakdown",
        "ilmScenario.label.collateralAvlbl": "Collateral Availability",
        "button.tooltip.schedReportHist.dateRange": "Choose Date Range",
        "label.forecastMonitorTemplateAddDetail.title.window": "Add a Column to Forecast Monitor Template - SMART-Predict",
        "label.labelTotalOverPages": "Total over all pages:",
        "ilmExcelReport.scenario": "Scenario",
        "alert.acctBreakdown.noMovements": "No movements available",
        "scenario.tab.guiHighlight": "GUI Highlighting",
        "sweepDetails.alert.sweeptarget": "Align account to target?",
        "archive.defaultDb": "Current?",
        "label.dataTo": "Data To",
        "updated": "Updated",
        "personalCurrency.personalCurrencyName": "Name",
        "maintenanceevent.summary.checkbox.rejected": "Show rejected maintenance events",
        "ilmExcelReport.intradayThroughputInflows": "Intraday Throughput - Inflows",
        "addJob.tab.user": "Users",
        "account.sweep.scheduleused.title": "Account Used in other sweep schedules",
        "tooltip.preadvice.allUsers": "All Users",
        "addJob.title.evaluateRunDateAs.value2": "Entity Date",
        "tooltip.enterAccountWithInstitutionText5": "Enter Account with Institution Text 5",
        "addJob.title.evaluateRunDateAs.value1": "System Date",
        "tooltip.enterAccountWithInstitutionText4": "Enter Account with Institution Text 4",
        "attributeusagesummaryadd.title.window": "Define Attribute Usage",
        "tooltip.enterAccountWithInstitutionText3": "Enter Account with Institution Text 3",
        "tooltip.enterAccountWithInstitutionText2": "Enter Account with Institution Text 2",
        "tooltip.enterAccountWithInstitutionText1": "Enter Account with Institution Text 1",
        "alert.ccyAccMaintPeriod.overlapOfExistingRecord": "you will overlap existing record with the same Host, Entity and account",
        "movement.postingDateMSD": "Post Date",
        "interfacerulesmaintenance.alert.ruleValidation": "Invalid characters entered. Only alphanumeric characters and the symbols (.,%&<>/|\\\\-_+=^;:[]*()?#) are accepted.",
        "addJob.title.outputFileType": "Output File Type",
        "tooltip.clearingStartGrid": "Sort by Clearing Start Time",
        "tooltip.sortInFinalBalance": "Sort by in final balance",
        "CriticalPay.status": "Description",
        "ilaapccyparams.CBGroupId": "CB Group ID",
        "tooltip.entityName": "Enter entity name",
        "interfacemonitor.details.text.storedprocedure": "Stored Procedure",
        "sweepDetail.sweepSetting.useAccountDefault": "Use account default settings ",
        "instancerecord.amount_tooltip": "Amount",
        "tooltip.sortNAks": "Sort by NAKs",
        "alertInstance.instanceId": "Instance ID",
        "turnoverReport.reportingperiod": "Reporting Period",
        "tooltip.totalOpenUnexpectedBalance": "Total Open Unexpected Balance",
        "usermaintenance.entity": "Default Entity",
        "tooltip.changeSelectedSection": "Change selected section",
        "tooltip.selectLink": "Select Link Account",
        "label.movementCannotBeUnlocked": "Movement cannot be unlocked. Please contact your system administrator",
        "drmsgfrmt": "Debit Msg Format",
        "metagrpmaintenance.changeScreen": "Change Metagroup - SMART-Predict",
        "tooltip.jobDescription": "Enter a description and/or notes relating to the scheduled report",
        "cpyFromManualInput.alert.mvmNotOnFile": "Movement not on file",
        "nonworkday.title.mainWindow": "Non Workday Maintenance",
        "multiMvtActions.entity": "Entity",
        "tooltip.enterCPartytext5": "Enter Counterparty text 5",
        "ilmanalysismonitor.alertProfileSaved": "The profile was successfully saved",
        "tooltip.ilmRetain": "Enter number of days to retain ILM datasets",
        "tooltip.enterCPartytext1": "Enter Counterparty text 1",
        "tooltip.enterCPartytext2": "Enter Counterparty text 2",
        "tooltip.enterCPartytext3": "Enter Counterparty text 3",
        "tooltip.enterCPartytext4": "Enter Counterparty text 4 ",
        "ilmExcelReport.actualTime": "Actual time",
        "tooltip.assAcct": "Define additional accounts to be summed when calculating a sweep based on an aggregated balance",
        "crossReference.updateDate": "Update Date",
        "scenario.signColumn": "SIGN Column",
        "messageFormats.outputType.mqInterface": "MQ interface",
        "functGrp.id": "Functional Group",
        "tooltip.forecastMonitor.ok": "Save changes and exit",
        "SweepMsgDisplay.GeneratedOnDate": "Date",
        "account.schedulesweep.label.leftaccountIdLabel": "Account Id",
        "alert.entityMonitorOptions.refreshRate": "Refresh rate selected was below minimum.\\nSet to 5 seconds.",
        "correspondentaccountmaintenance.currencyCode": "Ccy",
        "tooltip.selectLine": "Enter Line",
        "button.ratesAdd": "Add Rate",
        "tooltip.includeMvmInDataEx": "Include movement in data extract",
        "sweepDetails.alert.sweepamount": "Sweep amount less than minimum sweep amount of account",
        "scenario.existingConfigAlert": "Existing schedule entries will lose their configuration.    Do you want to continue?",
        "startBalAfter": "New Balance",
        "ErrorLog.title.window": "Error Log - SMART-Predict",
        "tooltip.statusInactive": "Select status as Inactive",
        "notincl": "Not Included",
        "msd.tooltip.addColumns.label": "Label",
        "contact.phone": "Phone",
        "pwdchange.userId": "User Id",
        "addjob.label.configParamStatusCorrectConfig": "Configuration is correctly supplied",
        "instancemessage.updateUser_tooltip": "Update User",
        "userSetup.changeScreen": "Change User - SMART-Predict",
        "multipleMvtActions.notUpdateRadio": "Do not update",
        "ilmReport.CorporateClient": "Corporate Client ",
        "tooltip.entityMonitor.bookBrkdown": "Select Book to view Book Monitor",
        "ilmReport.UsePaymentTypeData.tooltip": "Display payments made on behalf of correspondent banking customers using movement payment type data (summing payments in currency global group)",
        "accountGroup.currencyCode": "Ccy",
        "scenario.events.tooltip.msgCombo": "Choose a scenario message format",
        "ilmReport.costumerPayments": "Customer Payments",
        "ilmScenario.legend.sourcesOfFunds": "Sources of Funds Availability",
        "tooltip.accountMonitorNew.accumulatedSODBalAsString": "Start of Day Balance",
        "tooltip.selectMultiline": "Select multi-line",
        "shortcut.changeScreen": "Change Shortcut - SMART-Predict",
        "connectionPool.alertDetailsChanged": "This connection appears to have updated since the grid was last refreshed.<br>Are you sure you wish to Kill this connection?",
        "button.Refresh": "Refresh",
        "tooltip.selectDate": "Select date",
        "tooltip.delete.dst": "Delete the selected daylight saving period ",
        "tooltip.menuLevel2Desc": "Menu Item Level2 description",
        "alert.htmlInternalErrorOccurred": "HTML internal error occurred: ",
        "account.schedSweep.heading.otherAccSweepBookcodeCr": "Book CR<br>",
        "acctMaintenance.tab.sweeping": "Sweeping",
        "multipleMvtActions.includedRadio": "Included",
        "instancerecord.count_header": "Count",
        "alert.forecastMonitor.noMovements": "No movements available",
        "genericdisplaymonitor.title.window": "Generic Display - SMART-Predict",
        "label.deleteMessages": "Delete messages",
        "movementsearch.postingDateFrom": "Posting Date From",
        "account.fieldset": "Monitor",
        "tooltip.selectPostcutoff": "Select post cut off",
        "confirm.refreshMainWindow": "Menu level changes will be applicable after re-login",
        "tooltip.selectSweep": "Select Sweep",
        "tooltip.suspMatch": "Suspend match",
        "attributeusagesummaryadd.grandTotal.tooltip": "Specify if and how the attribute will contribute to a grand total (where relevant)",
        "tooltip.entityMonitor.rateButton": "Click to open Refresh Rate Window",
        "account.schedSweep.heading.otherAccSweepBookcodeDr": "Book DR<br>",
        "tooltip.alertTypeEmail": "Alert type email",
        "book.bookName": "Name",
        "ilmanalysismonitor.tree.actBalance": "Actual Balance",
        "ilmAccountGroupDetails.tootlip.allowReporting": "When checked this group will have ILM report data built on a daily basis and will be selectable for ILM reporting",
        "scenario.alert.emptyRowAlreadyAdded": "Please fill the empty row first.",
        "label.interfaceMonitor.header.totalMessagesProcessed": "Processed",
        "alert.forecasttemplate.mandatory": "Please enter mandatory field marked with *",
        "notes.date": "Date",
        "workflowmonitor.unsYestday.title": "Unsettled yesterday",
        "tooltip.shortCutId": "Shortcut ID",
        "errors.header": "",
        "interfacerules.alert.ruleValueLengthValidation": "Please enter 250 characters only",
        "balmaintenance.updateUser": "User",
        "ilmExcelReport.secondLargestCustomer": "2nd Largest Customer",
        "accountmonitor.fnbalance": "Final Bal",
        "tooltip.account.bookCode": "Click here to select Book Code",
        "scenario.tooltip.hostId": "Check HOST ID",
        "label.entityMonitorOptions.title.window": "Monitor Options",
        "errors.archive.add": "Please Select the Current Database",
        "tooltip.enterSenderCorrText2": "Enter Sender's Correspondent text 2",
        "tooltip.enterSenderCorrText1": "Enter Sender's Correspondent text 1 ",
        "copyFromRole.alert.deleteWorkQ2": "before setting No Access",
        "tooltip.open": "Select Open Status",
        "ccyMonitorOptions.enterDefaultDays": "Enter default days",
        "copyFromRole.alert.deleteWorkQ1": "Please delete Work Queue Access records for entity ",
        "tooltip.enterSenderCorrText4": "Enter Sender's Correspondent text 4",
        "tooltip.enterSenderCorrText3": "Enter Sender's Correspondent text 3",
        "notes.note": "Notes",
        "tooltip.enterSenderCorrText5": "Enter Sender's Correspondent text 5",
        "corporateAccount.validAmount": "Please enter a valid amount",
        "restriction.id": "Restriction",
        "tooltip.entitytimeframe": "Click to show scale in currency timeframe",
        "maintenanceLogView.newVal": "New Value",
        "userLog.itemId": "Item ID",
        "instancemessage.inputDate_header": "Input Date",
        "genericDisplayMonitor.errorServerSide": "SERVER SIDE ERROR",
        "title.addContacts": "Add Contact Details - SMART-Predict",
        "tooltip.qualityA": "Quality A",
        "tip.accountattributeadd.effectivedate": "Select the effective date",
        "sweepDetail.realignBal": "Re-aligned Predicted Balance",
        "tooltip.qualityC": "Quality C",
        "tooltip.qualityB": "Quality B",
        "tooltip.interfaceSettings.emaillogs": "Email Logs",
        "ilmReport.netCumulativeBalance": "Net Cumulative Balance",
        "scenarioSummary.selectedScen": "Selected Scenario Summary	",
        "currency.preFlag.InActive": "Inactive",
        "ilmAccountGrpAdd.title.window.changeScreen": "Change Account Group Details - SMART-Predict",
        "button.disable": "Disable",
        "label.dailysavingperiodsystem.title.window": "Daylight Saving Period Summary - System",
        "genericDisplayMonitor.validPageNumber": "Please enter a valid page number",
        "instancerecord.status_tooltip": "Status",
        "tooltip.qualityE": "Quality E",
        "interfacerulesmaintenance.partialRuleId": "Partial Rule ID",
        "tooltip.qualityD": "Quality D",
        "active": "Active",
        "scenario.events.pending": "Set Instance as Pending Resolution",
        "button.interfaceMonitor.startInterface": "Start",
        "role.roleId": "Role",
        "tooltip.selectEntity": "Select entity",
        "inputconfig.header.emaillogs": "Email Logs",
        "tooltip.acctStatus": "Account Status",
        "scheduledReportHist.status.success": "Success",
        "linked.tooltip.account": "Account",
        "ilmScenario.tab.movements": "Movements",
        "interfacerulesmaintenance.alert.messageType": "Message Type is empty",
        "errors.csrf.attack": "As a security measure, your request is blocked, a CSRF Attack detected!",
        "ilmReport.LVPSName": "LVPS Name",
        "mvmDisplay.title.changewindow": "Change Movement - SMART-Predict",
        "tooltip.changeSelGroup": "Change selected group",
        "tooltip.scenarioId": "Enter Scenario ID",
        "inputException.rateSelected": "Refresh rate selected was below minimum.\\nSet to 5 seconds",
        "criticalMvtUpdate.tooltip.expectedTime": "Please enter default expected time",
        "instancerecord.eventsLaunchStatus_tooltip": "Events Status",
        "title.AddaccContacts": "Add Contact Details - SMART-Predict",
        "location.locationName": "Name",
        "spreadProfilesMaintenance.processPoint.Only": "Only",
        "maintEvent.requestUser": "Requester",
        "accountGroup.global": "Global",
        "msd.dateradio.option2.text": "Relative",
        "ilaapccyparams.defaultMapTimeGrid": "Default",
        "intradayBalances.mainScreen": "Intraday Balance Report - SMART-Predict",
        "errors.csrf.attack.RefererError": "Reason: Referer header tag is Empty or does not fit server URL",
        "label.interfaceExceptions.header.messageId": "Message ID",
        "tooltip.user": "User",
        "tooltip.viewPrimaryForecast": "View Primary Forecast",
        "entity.general.exchangeRateFormat.ccy.domestic": "ccy/Domestic ccy",
        "tooltip.qualityZ": "Quality Z",
        "ilmAccountGroupDetails.secondMax": "Second Maximum",
        "currency.decimalPlaces1": "Number of Decimals",
        "tooltip.distributionList": "Distribution List",
        "tooltip.selectScenario": "Select Scenario",
        "sweepSubmit.colValue.Man": "Man",
        "corporateAccount.recordExists": "Record already exists",
        "ilmthroughputbreakdown.foreOutlflowsCheckbox": "Forecasted Outflows",
        "sweep.drAccount": "DR Account",
        "ilmSummary.tooltip.predicted": "Forecast balance                                                                                 ",
        "label.entityprocess.process": "Process",
        "messageFormats.viewScreen": "View Sweep Message Format - SMART-Predict",
        "addNotes.title.window": "Add Notes - SMART-Predict",
        "ilmExcelReport.ccytimeframe": "CCY timeframe",
        "account.schedulesweep.tooltip.targetBalanceTypeR": "Indicates that the target balance field will contain the name of a pre-defined rule which will be evaluated at run time according to relevant configuration parameters in place for that rule",
        "sweepNotes.title.window": "Sweep Notes - SMART-Predict",
        "ilmReport.Branch": "Branch",
        "msd.alertOverwriteProfile": "Are you sure you want to overwrite this filter",
        "tooltip.sortbyParty": "Sort by party ID",
        "label.country.countryCode": "Country",
        "tip.accountattribute.startdate": "Select start Date",
        "account.schedulesweep.tooltip.targetBalanceTypeD": "Debit target balance type",
        "sweepsearch.status": "Status",
        "account.schedulesweep.tooltip.targetBalanceTypeC": "Credit target balance type",
        "tooltip.sortField": "Sort by field",
        "alert.criticalPaymentType.runSqlUpdateEvery": "Run sql update every field should be filled when Enable Process is checked",
        "account.schedulesweep.tooltip.targetBalanceTypeA": "Indicates that the target balance field will contain the name of a numeric account attribute whose value will be used at run time",
        "tooltip.scenarioID": "Sort by Scenario ID",
        "balmaintenance.effectiveExternalSOD": "Effective External SOD",
        "inputconfig.header.engineactive": "Active",
        "party.type": "Select a Party Type",
        "ilmTransactionSet.currencyCode": "Ccy",
        "ilmTransactionSet.transactionSetName": "Name",
        "tooltip.refFlag": "Check to consider the reference in the search",
        "personalCurrency.personalCurrency": "Currency",
        "role.workQueueAccessAdd.outstanding": "Outstanding",
        "label.isInBusyBy": "is in use by",
        "usermaintenance.language": "Language",
        "tooltip.clickSelGrLevel1": "Click to select group ID level 1",
        "tooltip.clickSelGrLevel3": "Click to select group ID level 3",
        "button.message": "Message",
        "tooltip.clickSelGrLevel2": "Click to select group ID level 2",
        "tooltip.overdue": "Enter ACK overdue time as HH:MM:SS",
        "instancerecord.sweepId_header": "Sweep Id",
        "tooltip.refreshUserStatus": "Refresh user status",
        "tooltip.selectEntityid": "Select an entity ID",
        "label.forecasttemplate.column.templateid": "Template ID",
        "ilmScenario.tooltip.creditlineAvlbl": "Enter percentage of Credit-line Availability (0-100%)",
        "tooltip.deleteSelectedPartyAlias": "Delete selected party alias",
        "sweepSearchList.currentAmt": "Original Amount ",
        "ilmccyparamsAdd.toolip.globalCcyAcctGrpSelect": "Select Global Currency Account Group",
        "errors.DataIntegrityViolationExceptioninAdd": "Record already exists",
        "ilmccyparamsAdd.toolip.LVPSName": "Name of the large value payment system for this currency",
        "tooltip.9positionName": "Final position name",
        "errors.password.mixedCase": "Invalid password: both upper case and lower case characters are required",
        "limit.to": "To",
        "tooltip.newddint": "Enter new debit internal",
        "account.schedulesweep.label.allowMultipleLabel": "Allow Multiple",
        "label.entityprocess.lastEnded": "Last Ended",
        "ilmExcelReport.unsettledDebits": "Debits",
        "account.tooltip.accountLevel": "Sort by Account Level",
        "tooltip.enterIntermediary": "Enter intermediary BIC",
        "label.acctBreakdown.sum": "Sum",
        "tooltip.saveMG": "Save metagroup",
        "tooltip.preadvice.cash": "Select cash movement type",
        "shortcuts.id.shortcutId": "Shortcut",
        "instancerecord.sign_tooltip": "Sign",
        "alert.criticalPaymentType.valueHigherThanMax": "No more than 1440 should be set",
        "ilmScenario.label.systemScen": "System Scenario",
        "movementDisplay.id.movementId": "Movement",
        "tooltip.accIntCredit": "Account Interest Rate Credit Margin",
        "tooltip.sweepPosition": "Sweep position level",
        "sweepSearchList.Name": "Name",
        "messageFormats.formatName": "Name",
        "scenario.tooltip.mvtColCombo": "Enter Scenario Movement ID column",
        "tooltip.findoraddpopup.type": "Select Type",
        "button.schedReportHist.endDate": "To",
        "errors.DataIntegrityViolationException": "A data integrity violation occurred",
        "tooltip.enterAlertMsg": "Enter alert message",
        "button.tooltip.hidecutoffcutoff": "Do not display accounts that have past the cut-off time",
        "entity.predict.metagroupLevelNames": "Metagroup Level Names",
        "hidezerobalances": "Hide Zero Balances",
        "tooltip.entityMonitor.refreshWindow": "Refresh window",
        "accountmaintenace.alert.pcmAccount": "WARNING: If this account is to be used in the Payment Control module, it will need to be associated with a PCM account group.",
        "acctMaintenance.fmi": "FMI",
        "tooltip.sortSortCutName": "Sort by shortcut name",
        "acctmaintenance.primary": "Primary Source",
        "userprofile.option": "Profile Option",
        "groupMonitor.notANumber": "Not a number",
        "overdraftrate": "Overdraft Rate",
        "acctMaintenance.autoSwpfFlgAlert": "account does not have automatic sweeping enabled.",
        "tooltip.criteria": "Open additional search criteria screen",
        "menuaccessoptions.changeScreen": "Change Menu Access Options - SMART-Predict",
        "confirm.acctBreakdown.includeInTotal": "Include this account in the totals?",
        "tooltip.delSwpLmtCurr": "Delete selected sweep limits by currency",
        "button.Refer": "Refer",
        "tooltip.canddint": "Enter cancel debit internal",
        "ilmReport.titleGroupReportDateRange": "Date Range Liquidity Management Report - ILM Group",
        "linked.ccy": "Ccy",
        "ilmExcelReport.asOfAvailableLiquidityExclIncomingPaymentsTitle": "as % of Available Liquidity (excl. incoming payments)",
        "alert.mail.connectionProblem": "Unable to save server, Possible loss of connection! Please contact your administrator  ",
        "cashRsvrBal.tooltip.account": "Account ID",
        "sweep.postauthorizeCut": "Post Authorise Cut Off",
        "ilmExcelReport.otherCriticalPaymentsInflows": "Other Critical Payments - Inflows",
        "tooltip.addNewShortCut": "Add new shortcut",
        "secureid.timeout": "The connection to the RSA server has timed out",
        "manualMatch.warning.messageForAmtTotals": "This match will have different amount totals across position levels. Do you want to continue?",
        "ilmanalysismonitor.grid.creator": "Creator",
        "ilmanalysismonitor.legend.accForeC.Title": "FCIn.",
        "interfacemonitor.details.text.interfacemanager": "Interface Manager",
        "ccyAccMaintPeriod.minTargetBalanceLabel": "Minimum Account Balance",
        "tooltip.enterMsgSeparator": "Enter message separator",
        "account.schedulesweep.label.bookDrLabel": "Book DR",
        "label.schedReportHist.fileName": "File Name",
        "label.crrlimit": "CRR Limit",
        "preAdviceInput.column.amount": "Amount;",
        "tooltip.printScreen": "Print screen content",
        "movsearch.timefrom": "Time From",
        "interfacemonitor.details.text.generaldetails": "General Details",
        "tooltip.viewNotificationMessages": "Left click to view notification messages",
        "tooltip.ChangeOverdraftRates": "Change overdraft rates",
        "section.title.window": "Section Setup - SMART-Predict",
        "role.accountaccess.addTitle": "Add Account Access Control",
        "movSearch.title.mainWindow": "Movement Search - SMART-Predict",
        "button.include": "Include",
        "tooltip.selectFile": "Select file",
        "ilmReport.ccyMultiplierB": "Billions",
        "account.schedSweep.usedInOther": "Used in other schedules:",
        "pwd.numericChar": "Numeric Char",
        "entity.general.exchangeRateFormat": "Exchange Rate Format",
        "tooltip.matchQuality.paramDesc": "Sort by parameter",
        "scenarioNotification.email": "Email",
        "acctSweepBalGrp.accountStatus": "Account Status",
        "ilmReport.warningIncompletedData": "Report data is incomplete. Records are missing in the range",
        "tooltip.alertMatchAnotherProcess": "Match is in use by another process",
        "menuaccessoptions.addScreen": "Add Menu Access Options - SMART-Predict",
        "label.accountattribute.entity": "Entity",
        "label.currenctFilter": "Current Filter",
        "ilmScenarioAdd.title.window.viewScreen": "View ILM Scenario Detail - SMART-Predict",
        "inputconfig.tooltip.change": "Change Input Configuration",
        "ilmSummary.available": "Available ",
        "label.interfaceMonitor.header.interfaceId": "Interface ID",
        "tooltip.scenarioAlertInstanceColumn": "Alert Instance Columns",
        "contactDetails.alert.phonenocontain**********": "Phone number should contain **********-",
        "alert.scenarioQueryTested": "The scenario query was tested successfully, identifying",
        "ilmReport.ccyMultiplierNone": "None",
        "movement.diffvaluedate": "The value date should be greater than or equal to today",
        "sweepPrior.minutesAfterCoutOff": "Minutes After Cut-Off",
        "scheduledReportParams.runDateWorkdays": "Working days only",
        "label.ilmcalculationsuccesswitherrors": "ILM calculation completed successfully with errors",
        "maintEvent.recordId": "Record",
        "button.roll": "Roll",
        "ccyAccMaintPeriod.tooltip.fillBalance": "Balance to use in the 'Fill Days' period",
        "button.role": "Role",
        "personalCurrencyList.numberBetweenBigRange": "Please enter a number between 1 - 999",
        "tooltip.party.selectParentParty": "Select a parent party",
        "ilmReport.ccyMultiplierT": "Thousands",
        "tooltip.viewSweepDetails": "Sweep Display",
        "entity.positionLevel": "Add position level",
        "tooltip.CopySelectedFormat": "Copy selected format",
        "movement.isValueDateAchievable": "Is Value Date Achievable",
        "ilmReport.ccyMultiplierM": "Millions",
        "queue.applyCurrencyThreshold": "Apply Currency Threshold",
        "tooltip.sortbyformattype": "Sort by format type",
        "interfaceNotificationAlertPCM": "[PCM] Error when attempting to contact the SmartInput engine",
        "scenarioCategory.description": "Description",
        "sweepNAKSummary.title.window": "Sweep Exceptions Summary - SMART-Predict",
        "account.tooltip.bic": "Sort by BIC",
        "errors.csrf.attack.emptyToken": "Reason : Empty CSRF token",
        "ilmAccountGroupDetails.fixed": "Fixed",
        "tooltip.enterSectionId": "Enter section ID",
        "label.selectAccountAtt.prompt": "Please select ...",
        "movement.displayLevel": "Display Level",
        "sweep.amountunder": "Amount To",
        "alert.sweepDetail.saved": "Sweep saved with ID: ",
        "bookCode.bookName": "Name",
        "ilmthroughputActTvsForecL": "Actuals[t] vs Forecast [latest]",
        "preAdviceInput.column.ccy": "Ccy;",
        "title.subaccounts": "Sub Account Details - SMART-Predict",
        "interfacemonitor.header.proc_running": "Proc running",
        "label.accountattribute.value": "Value",
        "sweep.limitExceeded": "Sweep amount exceeds your limit",
        "corporateAccount.label.change": "Change",
        "tooltip.deleteSelectedArchive": "Delete Selected Archive",
        "positionLevel.alert.mandatoryFields": "Please fill all mandatory fields (marked with *)",
        "balparameter": "Name",
        "tooltip.CopyMatchQualityProfile": "Copy this match quality",
        "location.mainScreen": "Location Maintenance - SMART-Predict",
        "tooltip.deleteSelectedSection": "Delete selected section",
        "aliasTooltip.CurrencyName": "Delete currency alias",
        "tooltip.report": "Generate the report",
        "ilmthroughputActTvsForecT": "Actuals[t] vs Forecast [t]",
        "entity.general.serverTomeOffSet": "Server Time Offset",
        "batchScheduler.header.currentStatus": "Current Status",
        "groupMonitor.level": "Level",
        "addjob.Enddate": "End date",
        "tooltip.accountGroup.currencyCode": "Sort by Currency",
        "label.forecastMonitor.entity": "Entity",
        "ilmExcelReport.gold": "Gold (+)",
        "multiMvtActions.ref2": "Ref2",
        "tooltip.amountFrom": "Enter amount from",
        "multiMvtActions.ref1": "Ref1",
        "tooltip.enterDate": "Enter date",
        "ilmExcelReport.creditLineCommitted": "Committed",
        "batchScheduler.title.window": "Scheduler - SMART-Predict",
        "tooltip.ChangePersonalCurrency": "Change selected personal currency",
        "ilaapgeneralsysparam.changescreen": "Change ILM General Parameters- SMART-Predict",
        "metaGroup.mgrpLvlID1": "Metagroup Level",
        "scenario.events.refColumn": "Ref Columns <br>(Bind variables)",
        "alert.delayRateBetween": "Delay Rate must be between 0 and 100",
        "tooltip.SortByGmtOffset": "Sort by GMT offset",
        "interfacerules.alert.ruleValueLengthValidation2": "Please enter 50 characters only.",
        "account.schedSweep.tooltip.sweepFromBalanceType": "Sweep Balance",
        "ilmReport.ofWhichLargestCustomer": "Largest Customer",
        "emailTemplateMaintenance.keywordsCombo": "Keywords",
        "tooltip.accountGroup.publicPrivate": "Sort by Public",
        "matchOriginalNoteText": "Match rolled over to match ?",
        "format.Acc.Attribute": "Acc. Attribute",
        "ilmSummary.cutOff": "Cut-off   ",
        "ilmReport.oneDayMissingData": "Warning: Report data is incomplete. Records are missing for {0}, do you wish to continue?",
        "tooltip.enterpath": "Enter path",
        "label.forecastMonitorOptions.hidetotal": "Hide Total",
        "scenario.events.addFormat": "Add message Format",
        "tooltip.sendMessage": "Send message",
        "groupMonitor.total": "Total",
        "label.externalbal": "External Balance",
        "button.attributes": "Attributes",
        "errors.long": "{0} must be a long",
        "intradayBalances.accountScreen": "Intraday Balance Account",
        "tooltip.accountSpecific": "Account Specific",
        "entityMonitor.title": "Entity Monitor screen",
        "ilmanalysismonitor.grid.scenario": "Scenario ID",
        "movement.screen": "Movement Audit Log - SMART-Predict",
        "ilm.options.groupAnalysis_toolTip": "Check box to include Group Analysis chart in entity/currency tab",
        "sweeppriorcutoff.time": "Time",
        "label.errorContactSystemAdmin": "Error occurred, Please contact your System Administrator:",
        "errors.valueoutsideRange": "The given amount is outside of range",
        "tooltip.UpdatedDate": "Updated Date",
        "tooltip.sortname": "Sort by name",
        "currencyInterest.interestRateDate": "Date",
        "ilmAccountGroupDetails.netThresholds": "Net Cumulative Position Thresholds",
        "label.matchingMovementWillChange": "Matching these movements will change composition and status of an existing match. Are you sure that you wish to continue?",
        "button.save": "Save",
        "instancerecord.uniqueIdentifier_header": "Unique ID",
        "centralBankMonitor.fromOutsideRange": "From date is outside the defined range",
        "turnoverReport.excel": "Excel",
        "tooltip.groupMonitor.options": "Change Options",
        "tooltip.selectDelimite": "Select delimited",
        "addjob.alert.invalidFileNamePrefix": "Please enter a valid File Name Prefix",
        "preAdviceInput.fieldSetImport2.legendText": "Data Preview",
        "movement.CcyName": "Name",
        "tooltip.sweep": "Sweep",
        "balMaintenance.externalSODNegative": "External SOD Negative",
        "inputexceptions.messages.header.sign": "Sign",
        "tooltip.ccyMonitorOptions.selectFontSizeSmall": "Select Small Font Size",
        "tab.users": "Users",
        "connectionPool.activeConnections": "Active Connections",
        "ccyAccMaintPeriod.minimumReserve": "Minimum Reserve",
        "ilmccyparamsAdd.title.window.viewScreen": "View ILM Currency Parameter Maintenance",
        "ilmAccountGroupDetails.defaultDynamic.descriptionCentralBankGroup": "Automatically created CB group based on accts having IS_ILM_LIQ_CONTRIB and S_ILM_CENTRAL_BANK_MEMBE",
        "ilmAccountGroupDetails.defaultDynamic.descriptionGlobalCurrencyGroup": "Automatically created global currency group based on accounts having IS_ILM_LIQ_CONTRIBUTOR='Y'",
        "tooltip.4EyesAccess": "4 eyes access",
        "tooltip.generateReport": "Generate report",
        "role.printallbutton": "PrintAll",
        "ilmExcelReport.balancesOtherBanks": "Balances Other Banks",
        "messagefieldadd.alert.isStartPosExisting": "Record already existing",
        "tooltip.countryCode": "Country code",
        "scenario.title.window": "Scenario Maintenance - SMART-Predict",
        "ilmreport.keyword.label.startOfPreviousQuarter": "Start of the previous quarter",
        "tooltip.enterProfileName": "Enter profile name",
        "alert.movementSearch.reference": "Reference searches will have no effect where no reference fields have been specified. Do you want to continue?",
        "tooltip.advancedUser": "Implements Show advanced user configuration",
        "label.acctBreakdown.acctClass": "Account Class",
        "alert.templateOption.templateLock": "Template ID is locked by",
        "tooltip.sortRoleId": "Sort by role ID",
        "addJob.title.reportDecription": "Report Description",
        "tooltip.addNewHoliday": "Add new holiday",
        "instancerecord.eventsLaunchStatus_header": "Events Status",
        "label.forecastMonitorOptions.userbucket": "User Buckets",
        "messageFormats.outputType": "O/P Type",
        "ilm.options.groupAnalysis": "Group Analysis",
        "tooltip.changeselectedmsgformat": "Change selected message format",
        "tooltip.systemEnableDst": "Apply daylight saving adjustments when calculating ILM time series data",
        "tooltip.interfaceSettings.enabled": "Enabled",
        "text.day": "day",
        "ccyAccMaintPeriod.tooltip.logButton": "Log Account Currency Maintenance Period",
        "accountMonitor.label.entity": "Entity",
        "alertInstance.tab.logGrid": "Log",
        "ilmanalysismonitor.noneProfile": "<None>",
        "ilmAccountGroupDetails.secondMin": "Second Minimum",
        "tab.roles": "Roles",
        "sweep.messgeFormatNotDefined": "Message format not defined.<br>Refer to the error log for details!",
        "metaGroup.id.mgroupId1add": "Metagroup",
        "generalsysparam.mainScreen": "System Parameter Maintenance - SMART-Predict",
        "contact.ccy.exchange": "FX Rate",
        "tooltip.sortFirstPositionLevel": "Sort by first position level",
        "locationaccess.location": "Location",
        "label.ilmccyprocess.calculationlaunchfailed": "Error occurred when launching the processing",
        "maintEvent.tooltip.requestUser": "Request User",
        "format.cancel": "Cancel",
        "ccyAccMaintPeriod.tooltip.fillDays": "The number of days at end of the period where a specific target balance is required.",
        "tooltip.delSelCurrencyInterest": "Delete selected currency interest",
        "tooltip.accName": "Account Name",
        "logBalance.workingSOD": "Working SOD",
        "errors.DataIntegrityViolationExceptioninDelete": "Record cannot be deleted as other transactions depend on it",
        "tooltip.ilmReport.selectGroupILM": "Select an ILM Group",
        "errors.entity.selectEntity": "Please select a entity",
        "account.corresAccId": "Corres. Code",
        "tooltip.deliverPopup": "Sort by Deliver Popup",
        "movementSummaryDisplay.drilldownTitleOpenUnexpected": "OpenUnexpected",
        "interfacerulesmaintenance.ruleId": "Rule ID",
        "tooltip.sortByBvAdj": "Sort by BV Adjust",
        "role.entAccessList.readOnly": "View Access",
        "connectionPool.Connections": "Connections",
        "tooltip.sortGrId": "Sort by group ID",
        "label.dailysavingperiodsystemdetail.title.window": "Daylight Saving Period Detail - System",
        "errors.logon.MFAOnlyIsEnabled": "You can login to the application only using Multi-Factor Authentication authentication, please contact your Administrator",
        "tooltip.externalPosLvlName": "External Balance position level",
        "acctmain.usezero": "Use Zero",
        "party.alert.criteriaNotMatch": "Search Criteria Does not Match",
        "tooltip.Manual": "Select Manual",
        "alert.validationmsg": "Validation Message",
        "tooltip.expectedSettlement": "Enter Expected Settlement",
        "scenario.category": "Category",
        "alert.noMovemensAvailable": "No Movements Available",
        "tooltip.enterPartyName": "Enter party name",
        "sweepDisplay.title.window": "Sweep Display - SMART-Predict",
        "ilmAccountGroupDetails.class": "Class",
        "otherEmail.emailAddress": "Email Address",
        "scenario.active": "Active",
        "userLog.item": "Item",
        "tooltip.sortUpdateUser": "Sort by update user",
        "movement.input": "Input",
        "ilmanalysismonitor.grid.use": "Use",
        "button.schedreporthist.download": "Download",
        "tooltip.creditInterMsg": "Credit Intermediary Message Format",
        "msdAdditionalColumns.noneProfile": "<None>",
        "int": "INT",
        "acctmain.isIlmContributer": "Is ILM Liquidity Contributor",
        "tooltip.acc.zero": "Select Zero",
        "tooltip.2positionName": "Second position name",
        "ilmReport.largestDailyTotalInflow": "Largest daily total inflow",
        "balmaintenance.updateDate": "Input Date",
        "crmsgfrmt": "Credit Msg Format",
        "tooltip.scenarioEntity": "Sort by Entity",
        "addjob.alert.invalidOutputFileLocation": "Please enter a valid Output File Location",
        "additionalColumns.alert.emptyLabel": "Please enter a label",
        "tooltip.selectCCyDom": "Select ccy/domestic ccy",
        "multipleMvtActions.label.closeReturnButton": "Close and return",
        "ccyAccMaintPeriod.show": "Show",
        "status.ipAddress": "IPAddress",
        "cashRsvrBal.tooltip.entity": "Entity ID",
        "tooltip.deleteSelMetaGroup": "Delete selected metagroup",
        "tooltip.refreshErrorLog": "Refresh error log",
        "tooltip.enterStartTime": "Enter start time",
        "alert.forecasttemplate.columndelete": "Are you sure you want to remove this record?",
        "scenario.CreateInsDesc": "Requires parameters: Not yet configured",
        "alert.templateOption.duplicateTemplate": "Template already defined for the same entity & currency",
        "emailTemplateMaintenance.bodyContent": "Body Content",
        "title.preadvice.displayWindow": "Pre-advice Display - SMART-Predict",
        "tooltip.movementSearch.uetr": "Please enter UETR",
        "ilmScenario.tab.general": "General",
        "tooltip.positionLevel": "Select position level",
        "entity.predict.thresholdParam.cashFilter": "Cash Filter",
        "workflowmonitor.refreshRate": "Refresh Rate",
        "reasonMaintenance.mainTitle": "Reason Maintenance - SMART-Predict",
        "tooltip.viewNAKmessage": "View NAK message",
        "label.accountspecificsweepformat.column.newExternalDrFormatINt": "External D(Int)",
        "button.next": "Next",
        "tooltip.schemaName": "Enter Schema Name",
        "ilmAccountGroupDetails.priv": "Private",
        "ilmAccountGroupDetails.nbOfRecords": "No of Records:",
        "movement.counterParty": "Counterparty",
        "alert.missingBodyOrSubject": "Do you want to send this email without a body or subject?",
        "inputconfig.header.spinterval": "Proc Cycle",
        "cashRsvrBal.rowTotalLabel": "Total",
        "workflowmonitor.timet.title": "Time",
        "tooltip.ChangeSelectedCurrencyAlias": "Change selected currency alias",
        "ilmAccountGroupDetails.level": "Level",
        "batchScheduler.header.LastExe": "Last Executed",
        "ilmReport.ofWhich": "Of which:",
        "metagroup.groupId": "Group",
        "criticalMvtUpdate.radioSystem": "System",
        "throuputmonitor.throughputratios": "Throughput",
        "tooltip.selectCurrencyId": "Select currency code",
        "scenarioNotification.access": "Access",
        "tooltip.mixedCase": "Mixed Case",
        "acctSweepBalGrp.account": "Account ID",
        "exchange.delete": "Delete selected exchange rate",
        "tooltip.sumButton": "Sum window",
        "ilmanalysismonitor.errorOnServerSide": "An error occurred on server side, please see logs",
        "ilmExcelReport.secondHigh": "Second High",
        "tooltip.sortMvmLevel": "Sort by movement level",
        "label.accountspecificsweepformat.text.debit": "Debit",
        "scenario.afterInterval": "After an interval of",
        "tooltip.changeSelectedLocation": "Change selected location",
        "entity.SelectCCY/DomesticCCY": "Select ccy/domestic ccy",
        "scenario.tooltip.sign": "Check SIGN",
        "criticalMvtUpdate.enableUpdateProcessing": "Enable Update processing",
        "account.entityId": "Entity Id",
        "group.mgroupId": "Metagroup",
        "spreadProfilesMaintenance.processPoint.All": "All",
        "tab.otherEmail": "Other Email",
        "tooltip.sortCategoryDisplayorder": "Sort by Display Order",
        "ilmanalysismonitor.legend.accActualC.Title": "ActIn.",
        "tooltip.enterEntityId": "Enter entity ID",
        "role.workQueueAccess.qualityC": "C",
        "role.workQueueAccess.qualityD": "D",
        "connection.passed": "Connection successful",
        "tooltip.sortByNote": "Sort by note",
        "role.workQueueAccess.qualityA": "A",
        "sweepId.alert.noAccess": "Invalid: your role does not provide access to this sweep",
        "role.workQueueAccess.qualityB": "B",
        "ilmExcelReport.cls": "CLS",
        "role.workQueueAccess.qualityE": "E",
        "workflowmonitor.option.notApplied": "Not Applied - Checkbox unchecked",
        "ilmReport.totalCreditLineAvailable": "Total credit lines available",
        "tooltip.changeSelBookcode": "Change selected bookcode",
        "Account.monitorTab": "Balances",
        "label.records": "records",
        "org.apache.struts.taglib.bean.format.int": "######",
        "addjob.All": "All",
        "account.schedulesweep.label.balanceTypeE": "External",
        "tooltip.enterSectionName": "Enter section name",
        "interfacerulesmaintenance.messageType": "Message Type",
        "acctstatus": "Account Status",
        "role.workQueueAccess.qualityZ": "Z",
        "balanceTypeId": "Identifiers",
        "account.schedulesweep.label.balanceTypeP": "Predicted",
        "ilaapccyparams.lvpsName": "LVPS Name",
        "interfaceSettings.bottomDetails": "Show XML - Bottom grid Details",
        "account.schedulesweep.label.settleMethodCRLabel": "Settle Method CR",
        "correspondentaccountmaintenance.add.messageType": "Message Type",
        "ilmExcelReport.dailyTotalInflow": "Actuals: Daily Total Inflow",
        "tooltip.sortSweepAmount": "Sort by sweep amount",
        "scenario.events.allowRepeat": "Allow repeat on Re-raise",
        "tooltip.refreshJobDetail": "Refresh job details",
        "messageFields.id.serialNo": "Sequence No",
        "tooltip.addNewMatchQuality": "Add new match quality",
        "errors.DataAccessResourceFailureException": "DataAccessResourceFailureException",
        "tooltip.Currencies": "Currencies",
        "label.accountspecificsweepformat.alert.specEntityIdRequired": "Specific Entity ID is required",
        "tooltip.sortByDate": "Sort by date",
        "addjob.Saturday": "Saturday",
        "alert.forecastMonitor.totalValues": "Total values cannot be updated",
        "tooltip.entity.output": "Output Retention Period",
        "tooltip.enableCurrPredict": "Enable this currency in Predict",
        "workflowmonitor.refresh.title": "Refresh",
        "balMaintenance.reasonDesc": "Reason Description",
        "bookmaintenance.addScreen": "Add Book",
        "contactDetails.alert.fillnamefield": "Please fill in the name field",
        "role.sweepLimits.sweepLimits": "Sweep Limits",
        "maintenanceevent.details.button.accept.tooltip": "Accept the change",
        "inputException.enterFromDate": "Enter from date",
        "autologgedoff.msg.time": "seconds.",
        "personalCurrency.childscreenName": "Personal Currency Definition - SMART-Predict",
        "instancerecord.attributesJson_tooltip": "Attribute JSON",
        "metagroupMonitor.metagroup": "Metagroup",
        "alert.interfaceSettings.save": "Your changes will not take effect until the input engine is restarted",
        "ilmExcelReport.throughputAt": "Throughput at",
        "label.forecastTemplateOption.template": "Template",
        "label.ilmccyprocess.currentstatus.notRunning": "Not Running",
        "msg.title.alertAvailable": "Alert available",
        "tooltip.accountClass": "Select Account Class",
        "currency.name": "Name",
        "cannotdelete.message": "Cannot delete your own User ID",
        "label.difference": "Difference:",
        "tooltip.sortLogonTime": "Sort by logon date and time",
        "ilmAccountGroupDetails.tootlip.netCum": "Generates net cumulative position data for use in 'All' Entity ILM reports and for display in ILM report charts.  This data could grow to be large so only specify it on groups where the feature is necessary.",
        "label.personalEntityList.priorityOrder": "Order of Display",
        "inputconfig.header.enabled": "Enabled",
        "button.tooltip.schedReportHist.reporttype": "Select a Report Type",
        "movementsearch.archive": "Archive",
        "ccyAccMaintPeriod.internal": "Internal ",
        "turnoverReport.debit": "Debit",
        "messageFormats.hexaFldDelimeter1": "Field Delimiter (Hex)",
        "ccyAccMaintPeriod.tooltip.eodBalanceSrc": "Specify to use supplied internal balance or supplied external balance when determining EOD balances for days in the past",
        "role.sweepLimits.sweepLimit1": "Sweep Limit*",
        "tooltip.extBalEodDate": "Select External Balance EOD Date ",
        "currencyMonitor.alert.dateRange": "The data for this date range selection may not be available in the cache and will take time to be calculated next time. \\n Do you want to continue?",
        "cashRsvrBal.heading.runningAvg": "Running Avg.",
        "tooltip.forecastMonitor.add": "Add new record",
        "screen.breakdown": "Breakdown",
        "scenarioSummary.resolved": "Resolved",
        "scenario.sweepLbl": "SWEEP_ID",
        "preAdviceInput.invalidHeaderAlert": "There is something wrong with the imported data.     Please check if you don't forget to copy movement attributes columns .",
        "label.interfaceMonitor.header.totalFilteredMessages": "Filtered",
        "tooltip.defaultCcyGrp": "Select a default Currency Group",
        "tooltip.selectMetagroupCat": "Select metagroup category",
        "tooltip.GeneratedOnDate": "Date",
        "ilmSummary.tooltip.accountName": "Account Name",
        "ilmReport.availableIL": "Available Intraday Liquidity at SOD",
        "passwordRules.label.spChars": "[~!@#$%^&*()-_=+;:'\",<.>/?]",
        "tooltip.sortCurrencyGroupName": "Sort by Name",
        "ilmReport.reportType.intradayRisk.netCumulative": "Intraday Risk: Net Cumulative Positions",
        "ilmScenario.delayRate": "Delay Rate",
        "multiMvtActions.matchId": "MatchID",
        "ccyAccMaintPeriod.currency.id": "Currency Code",
        "msd.heading.addColumns.operator": "Operator",
        "tooltip.movSenderCorres": "Click to select an Sender's Correspondent",
        "ilmAccountGroupDetails.showXMLRightGrid": "Show Right Grid XML",
        "instancerecord.otherId_header": "Other Id",
        "emailTemplateMaintenance.templateId": "Template Id",
        "ilmReport.accumulatedCR": "Accumulated CR",
        "format.Credit": "Credit",
        "label.roleBasedControl.ReqOthers": "Auth. Others?",
        "entity.allmovement": "All Movements",
        "partyAlias.alias": "Alias",
        "ilmanalysismonitor.alertProfileDeleted": "The profile was successfully deleted",
        "role.roleAccountsLabel": "Apply account access controls",
        "ilmReport.accumulatedDR": "Accumulated DR",
        "maintEvent.requestDate": "Requested On",
        "scenario.eventTab.alert.facilityExists": "Event facility already exists",
        "account.schedulesweep.tooltip.allowMultipleN": "Indicate whether to allow multiple ",
        "account.schedSweep.tooltip.otherSweepFromBalType": "Sweep Balance",
        "tip.accountattribute.enddate": "Select end Date",
        "tooltip.changeSelectedRole": "Change selected role",
        "mvmDisplay.tooltip.enterAccountId": "Enter Account",
        "locationAccess.title.mainWindow": "Change Location Access",
        "tooltip.maintainAnyIlmScenario": "Allow user to change any defined scenarios and make scenarios public",
        "entity.MetagroupLevel3Name": "Metagroup level 3 name",
        "label.ilmccyprocess.processid": "Process ID",
        "sweep.swpFrm": "Sweep From",
        "ilmScenario.createdByUser": "Creator",
        "tooltip.sortLevel": "Sort by level",
        "inputconfig.header.value": "Value",
        "sweep.debitaccountId": "Account ID DR",
        "currency.thresholdMainScreen": "Threshold (M)",
        "targetbalance": "Target Balance*",
        "entity.predict.retentionParam.movement": "Large Movements",
        "label.accountspecificsweepformat.tooltip.externalDebit": "Select External Debit",
        "tooltip.enterCategoryDisplayorder": "Enter Display Order",
        "inputException.notANumber": "Not a number",
        "maintEvent.tooltip.status": "Status",
        "account.schedulesweep.tooltip.allowMultipleY": "Indicate whether to allow multiple ",
        "matchQueue.currency": "Currency",
        "interfaceSettings.save": "Save",
        "criticalMvtUpdate.toolTip.endTime": "Specify the time period in which the SQL update should run",
        "confirm.addMovementsTomatch": "The amounts of the selected movements differ. Do you want to continue?",
        "tooltip.entityMonitorOptions.autoSizeColumns": "Auto Size Columns",
        "auditLog.logDate_Time": "Time",
        "balMaintenance.forecastSODTypeAsString": "Forecast SOD Type as String",
        "instancerecord.hostId_header": "Host",
        "changeBalance.updateUser": "Update User",
        "alert.accountattributehdr.acctattridrequired": "Account Attribute ID is required",
        "tooltip.enteraccIntRateCredit": "Enter Credit Margin",
        "tooltip.CurrentCrInterestRates": "Current Credit Interest Rate",
        "sweep.genTime": "Sweep Time",
        "minswpamnt": "Min Amount",
        "tooltip.scenarioStartTime": "Enter Start Time (hh:mm)",
        "section.sectionName": "Name",
        "movement.messageId": "Message ID",
        "ilmExcelReport.netCreditDebit": "Net (credits - debits)",
        "ccyAccMaintPeriod.ccyCode": "Currency",
        "tooltip.selectedScen": "Selected Scenario Alerts",
        "passwordRules.title.window": "Password Rule - SMART-Predict",
        "tooltip.Predefined": "Select Predefined",
        "tooltip.sortTotal": "Sort by total",
        "alert.forecasttemplate.totalmultiplier": "Please enter Total Multiplier",
        "preAdviceInput.column.accountId": "AccountID;",
        "alert.FacilityMissingValues": "Facility cannot be launched - missing values",
        "tooltip.SaveChanges": "Save changes",
        "linked.name": "Name",
        "ilmReport.dataState": "ILM data state",
        "generalsysparam.changescreen": "Change General System Parameters - SMART-Predict",
        "errors.logon.invalidPassword": "Invalid login",
        "tooltip.cancelbutton": "Cancel changes and exit",
        "usermaintenance.password": "Password",
        "tooltip.overwriteExistingProfile": "Select to overwrite existing profile",
        "label.accountspecificsweepformat.alert.specAccountIdRequired": "Specific Account ID is required",
        "tooltip.sortBookId": "Sort by book ID",
        "turnoverReport.templateSheetName": "Turnover Report",
        "recovery.title.window": "Matching Recovery",
        "alert.criticalPaymentType.updateQuery": "An update query must be specified when Enable Process is checked",
        "scenario.tooltip.amount": "Check AMOUNT",
        "ilmanalysismonitor.groupComboTooltip": "Select a group Id",
        "tooltip.user.name": "Change User Name",
        "ilaapccyparams.primaryAccountId": "Primary Account ID",
        "ilmExcelReport.totalCriticalPaymentsOutflows": "Total Critical Payments - Outflows",
        "sweep.sweepType": "Type",
        "ilmReport.otherSystemsCollateral": "Other systems collateral",
        "contact.fieldet": "Details",
        "movement.accountId": "Account",
        "tooltip.Message": "Format",
        "ilmccyparamsAdd.alert.noCcyOffset": "GMT offset is not set for the selected currency",
        "tooltip.sortAccountId": "Sort by Account ID",
        "bookCode.groupIdLevel3": "Grp Lev 3",
        "bookCode.groupIdLevel2": "Grp Lev 2",
        "movement.party": "Party",
        "exchange.rate": "Enter FX Rate",
        "button.rate": "Rate",
        "ilmthroughputbreakdown.actInlflowsCheckbox": "Actuals Inflows",
        "tooltip.drIntMsg": "Debit internal message",
        "usermaintenance.lastLogin": "Last Login",
        "sweep.currencyCode": "Ccy",
        "entity.nochange": "No Change",
        "scenario.payLbl": "PAYMENT_ID",
        "bookCode.groupIdLevel1": "Grp Lev 1",
        "fomat.ddInt": "Debit Internal",
        "label.attributeusagesummaryadd.grandTotal": "Grand Total",
        "label.refreshRate.seconds": "&nbsp;seconds",
        "tooltip.successRateDebitPct": "Enter percentage of debits success rate (0-999%)",
        "cashRsvrBal.entity": "Entity",
        "tooltip.workQueueAccess": "Work queue access",
        "tooltip.manual": "Manual",
        "tooltip.enterHexMsgSeparator": "Enter hex message separator",
        "label.accountattribute.startdate": "Start Date",
        "tooltip.user.id": "User ID",
        "instancerecord.amount_header": "Amount",
        "workflowmonitor.curGrp.title": "Currency Group",
        "tooltip.changeSecondaryForecast": "Change Secondary Forecast",
        "tooltip.currencyGroupName": "Enter currency group name",
        "tip.schedReportHist.mailStatus": "'Success' : Mail successfully sent, 'N/A' : No send mail required, 'Fail' : Error occurred when sending the mail",
        "balmaintenance.externalSOD": "External SOD",
        "tooltip.rateButton": "Rate window",
        "entity.predict.groupLevelNames.level2": "Level 2",
        "entity.predict.groupLevelNames.level1": "Level 1",
        "entity.predict.groupLevelNames.level3": "Level 3",
        "tooltip.movBeneficiary": "Click to select beneficiary ID",
        "tooltip.selectJobType": "Select Job Type",
        "sweep.postcutoff": "Post cut off",
        "label.getAdobeFlashPlayer": "Get Adobe Flash Player",
        "maintEvent.tooltip.maintFacilityId": "Maint Facility Id",
        "group.entityId": "Entity",
        "ilmreport.keyword.label.runDate": "the date that report is being run",
        "label.inconsistent": "Inconsistent",
        "ilmScenario.tooltip.allowReporting": "Allow ILM Reporting",
        "tooltip.sortAcLevel": "Sort by account level",
        "correspondentaccountmaintenance.add.currencyCode": "Currency",
        "ilmanalysismonitor.tooltip.allEntityGlobalAlternSummation": "Summation of the entities using the alternative global groups, or main global group when no alternative is defined",
        "tooltip.interfaceExceptions.exception": "Exception",
        "tooltip.userpassword": "Password",
        "entity.output": "Output",
        "ilmsummary.title.window": "ILM Summary",
        "addJob.title.reportType": "Report Type",
        "refreshRate.title.window": "Auto-refresh Rate",
        "movementsearch.debit": "Debit",
        "centralBankMonitor.amountGreaterThanZero": "Amount should be greater than zero",
        "preAdviceInput.column.inputBy": "Input by;",
        "tooltip.includeMvmInIlmFcast": "Include in ILM forecast",
        "role.workQueueAccess.currency": "Ccy",
        "changeBalance.legend.suppSods": "Supplied SODs",
        "alert.forecasttemplate.templatedelete": "Are you sure you want to delete this template?",
        "instancerecord.currencyCode_header": "Ccy",
        "alertMessages.title.window": "Alert Message - SMART-Predict",
        "addjob.Once": "Once",
        "acctMaintenance.archiveData": "Archive data",
        "inputexceptions.header.accepted": "Accepted",
        "tooltip.viewSelBal": "View selected balance",
        "tooltip.entityMonitorOptions.reportingCcy": "Reporting Currency",
        "ccyAccMaintPeriod.tooltip.accountId": "Account Id",
        "ilmExcelReport.thirdLargestCounterParty": "3rd Largest Counterparty",
        "tooltip.groupId": "Group ID",
        "tooltip.sort.debits": "Sort by Debits",
        "tooltip.sortBookcodeName": "Sort by bookcode name",
        "scenario.events.tooltip.remainactive": "Instance will remain Active",
        "metaGroup.title.mainWindow": "Metagroup Maintenance - SMART-Predict",
        "tooltip.forecastMonitor.save": "Save changes and exit",
        "scenarioSummary.overdue": "Overdue",
        "acc.zero": "Zero",
        "preAdviceInput.column.inputAt": "Input at;",
        "currencyFunding.mainScreen": "Currency Funding Report - SMART-Predict",
        "interfaceMonitor.title.window": "Interface Monitor - SMART-Predict",
        "tooltip.scenarioCreator": "Sort by Creator",
        "movement.movementId": "Movement",
        "outstanding.tabs.all": "All",
        "tooltip.enterInterestRate": "Enter Interest rate",
        "maintEvent.authDate": "Authorised On",
        "errors.range": "{0} is not in the range {1} through {2}",
        "account.schedSweep.heading.scheduleTo": "To<br>",
        "scenarioSummary.scenTotals": "Scenario Totals",
        "workflowmonitor.confirmed.title": "Confirmed",
        "label.country.overrideWeekend1": "Override <br> Weekend 1",
        "label.country.overrideWeekend2": "Override <br> Weekend 2",
        "msd.reloadFilterImageTooltip": "Reload Filter",
        "status.logOnTime": "LogOnTime",
        "errors.logon.reg": "Product is not registered",
        "scenario.sweepIdColumn": "SWEEP_ID Column",
        "button.chooseFile": "Choose file",
        "messageFormats.fileName": "File Name",
        "tooltip.forecastMonitor.refreshWindow": "Refresh window",
        "ilmScenario.tooltip.joinMvt": "Allows use of P_MOVEMENT_EXT columns in scenario filter and exclusion expressions. This can incur a performance cost.",
        "ilmExcelReport.multiplier": "Multiplier",
        "tooltip.sortMetagroupId": "Sort by Metagroup ID",
        "ilmExcelReport.largestPositiveNetCumulativePosition": "Largest Positive Net Cumulative Position",
        "msdAdditionalColumns.deleteProfileImageTooltip": "Delete profile",
        "bookCode.changeScreen": "Bookcode Change Pop up",
        "currency.interestBasis1": "Interest Basis",
        "ilmAccountGroupDetails.labelName": "Name",
        "tooltip.sortCurrName": "Sort by currency name",
        "messagefieldadd.alert.space": "Space between Start and End position is less than the length of the text in Value field",
        "archive.schemaName": "Schema Name",
        "criticalMvtUpdate.currencyId": "Currency",
        "button.advanced": "Advanced",
        "ilmanalysismonitor.grid.type.tooltip": "Type",
        "tooltip.once": "Once",
        "errors.authorization.attack.log": "SECURITY WARNING - Client request blocked: An authorization bypass was detected",
        "turnoverReport.outputFormat": "Output Format",
        "addJob.tab.scheduling": "Scheduling",
        "tooltip.durationMins": "Enter duration (Min)",
        "ilmSummary.tooltip.unexpected": "Unexpected movements",
        "scenarioSummary.title.xml": "Summary Details XML",
        "email.configRecipients.title": "Configure Recipients  - SMART-Predict",
        "movementDisplay.ilmFcast": "ILM Fcast Status",
        "acctmaintenance.backvalue": "Back-Value Adjustments",
        "maintenanceLog.tooltip.field": "Field",
        "interfacemonitor.details.text.storedproclastexectime": "Last Execution Time",
        "acctmain.isCentralBank": "Is Central Bank Member Account",
        "tooltip.enterMvmRef1": "Enter movement reference 1",
        "turnoverReport.sub": "Sub",
        "tooltip.enterMvmRef3": "Enter movement reference 3",
        "tooltip.enterMvmRef2": "Enter movement reference 2",
        "interfacemonitor.details.text.commandsocket": "Command Socket Listener",
        "tooltip.orderCus": "Enter Ordering Customer ID",
        "viewJob.title.Window": "View Job Detail - SMART-Predict",
        "label.entityprocess.lastHeartbeat": "Last Heartbeat",
        "balmaintenance.MT950Sod": "MT950 SOD",
        "org.apache.struts.taglib.bean.format.float": "######,####",
        "account.schedulesweep.label.otherBalTypeLabel": "Balance Type",
        "errors.logon.inactiveDisable": "Your user ID has been disabled due to inactivity. Please contact your System Administrator.",
        "scenario.deleteLabel": "Delete",
        "button.tooltip.clearAccounts": "Clear Selected Accounts",
        "movement.newreference2": "Ref 2",
        "tooltip.sortByAccount": "Sort by Account",
        "ilmExcelReport.dailyTotalOutflow": "Actuals: Daily Total Outflow",
        "title.user.userId": "Please enter your user ID",
        "currencyGroupAccess.addScreen": "Add Currency Group Access - SMART-Predict",
        "ilaapgeneral.account": "Account",
        "inputexceptions.label.autoFormatXML": "Auto-format XML",
        "movement.newreference1": "Ref 1",
        "ilmAccountGroupDetails.maximum": "Maximum",
        "tooltip.scenarioQueryText": "Enter Query Text",
        "tooltip.selectUseGeneric": "Select Use Generic Display",
        "ilmExcelReport.toDate": "To Date",
        "tooltip.access": "Access",
        "alert.balanceLogRententionperiod": "Balance Log Retention period should not be higher than Balance Retention period",
        "alert.interfaceSettings.nothingToSave": "Nothing to save",
        "ilmReport.reportType.ilmGrpReport": "ILM Group Report",
        "ilmReport.correspondent": "Correspondent",
        "messageFormats.id.formatId1": "Format ID*",
        "tooltip.acc.SOD": "Start of Day",
        "tooltip.deleteSelectedJob": "Delete selected job",
        "tooltip.viewSelectedRole": "View selected role",
        "label.accountattributeadd.value": "Value",
        "alert.scenarioSummary.noData.title": "Warning - Scenario Summary Details",
        "tooltip.movement.extra_text1": "Extra Text 1",
        "msd.alertFilterSaved": "The filter was successfully saved",
        "sweepSearchList.sweepUser": "User",
        "exchange.tooltip.user": "Sort by user",
        "userLog.tooltip.time": "Time",
        "metaGroup.entityId": "Entity",
        "ilmanalysismonitor.tree.accActualCD": "Accum. Actual C/D",
        "tooltip.acc.Zero": "Zero",
        "label.personalCurrencyList.priorityOrder": "Order of Display",
        "tooltip.forecastMonitorTemplateAdd.templateName": "Enter Template Name",
        "movement.level": "Level",
        "ilmanalysismonitor.tooltip.allEntityAlternSummation": "Summation of entities using ONLY alternative global groups",
        "ilmanalysismonitor.accumDC": "Accumulated D/C",
        "movement.type": "MT",
        "tip.accountattribute.attribute": "Select attribute ID",
        "movSearch.alert.valueFrom": "Value From Date should be equal to or greater than system date",
        "movementsearch.entity": "Entity",
        "ilmanalysismonitor.balance": "Balance",
        "turnoverReport.turnoverdic": "Turnover",
        "movementsearch.extra": "Extra",
        "tooltip.enterTarBal": "Enter target balance",
        "tooltip.changeJob": "Change job",
        "ccyAccMaintPeriod.tooltip.user": "User",
        "tooltip.addNewGroup": "Add new group",
        "main.showPreviousLogin": "Show Previous Login Details",
        "tooltip.changeSelCorrCode": "Change selected correspondent code",
        "account.schedulesweep.tooltip.targetBalanceInput": "Target Balance",
        "errors.csrf.attack.wrongToken.log": "SECURITY WARNING - Client request blocked: A CSRF attack detected -  wrong CSRF token",
        "alert.groupSpecifiedAsCurrencyGlobal": "This group is specified as a currency global group and cannot be removed at this time",
        "alertInstance.hostId": "HOST_ID",
        "errors.csrf.attack.invalidSession": "Reason: User Session in invalid",
        "auditLog.dateRange": "User Log",
        "tooltip.quickSearch": "Quick Search an account ID",
        "maintenanceLogView.user": "User",
        "balmaintenance.externalWorkingSod": "External SOD",
        "criticalMvtUpdate.lastExec": "Last Executed (system time):",
        "tooltip.forecastTemplateOption.template": "Template",
        "ilmanalysismonitor.profileComboTooltip": "Select profile",
        "tooltip.archiveName": "Enter Archive Name",
        "tooltip.enterInterfaceRuleValue": "Enter Interface Rule Value",
        "tooltip.changeOverdraft": "Change overdraft rates",
        "ilmExcelReport.totalCriticalPayment": "All Critical Payments (Monitored and Unmonitored)",
        "crossReference.businessSource": "Business Source",
        "sweepSubmitQueue.title.window": "Sweep Submit Queue - SMART-Predict",
        "button.enable": "Enable",
        "ilmExcelReport.other": "Other",
        "currencyMonitor.alert.defDayschanged": "changed default days will not be applied \\n until you restart the screen",
        "tooltip.Min": "Minimum net cumulative position threshold (must be negative)",
        "errors.valuetoosmall": "Value is lower than the allowed minimum of ",
        "movementDisplay.predictStatus": "Predict Status",
        "sweep.accountCr": "CR A/C Msg",
        "scenarioNotification.role": "Role",
        "swpdays": "Sweep Days",
        "label.allowMultiMvtUpdatesFromMsd": "Allow multi-movement updates from Movement Summary",
        "scenarioSummary.resolvedOn": "Resolved On",
        "ilmReport.dailyIntradayLiquidityUsage": "Daily Intraday Liquidity Usage",
        "accountmonitorNew.Total": "Total",
        "tooltip.selectExcMvmInDataEx": "Select exclude movement from data extract",
        "account.schedulesweep.label.balanceTypeLabel": "Balance Type",
        "Jobmaintenance.Name": "Name",
        "tooltip.selectAcStatus": "Select an account status",
        "tooltip.clickBack": "Back to first screen",
        "shortcut.title.window": "Shortcut Maintenance - SMART-Predict",
        "reportsmatch.title.window": "Match Statistics - SMART-Predict",
        "multipleMvtActions.label.total": "Total",
        "messageFormats.lineNoDisplay": "Line No",
        "preAdviceInput.column.postDate": "Post Date;",
        "button.schedReportHist.reporttype": "Report Type",
        "ilmReport.average": "Average",
        "user.titleBtnLogin": "Click here to log in",
        "tooltip.enterNewSweepDay": "Enter new sweep day",
        "scenario.tooltip.radioAfter": "After an interval of",
        "acctMaintenance.applyBetween": "Apply between",
        "sweepSearchResults.title.window": "Sweep Search Results - SMART-Predict",
        "movement.doesnotexist": "Movement is not for the selected entity",
        "centralBankMonitor.validNumber": "Not Valid Number",
        "groupMonitor.selected": "Selected",
        "movementDisplay.entity.id": "Entity",
        "tooltip.defaultWeekend2": "Default Weekend 2",
        "tooltip.defaultWeekend1": "Default Weekend 1",
        "additionalColumns.alertDeleteProfile": "Are you sure you want to delete the profile?",
        "scenarioAdvanced.currencyColumn": "CURRENCY Column",
        "ilmTransactionSet.debits": "Debits",
        "tooltip.nextMatch": "Next match",
        "warn.calculationstatus": "These calculations may take a long time. Do you wish to continue?",
        "movement.predict": "Predict",
        "tooltip.changeSelectedJob": "Change selected job",
        "cashRsvrBal.account": "Account",
        "acctMaintenance.autoSwpfFlgAlertSave": "not having automatic sweeping enabled.    Do you want to continue? ",
        "sweep.submit": "Submit",
        "tooltip.selectFacility": "Select Facility",
        "matchAuditLog.screen": "Match Audit Log - SMART-Predict",
        "balmaintenance.balanceParamter": "Name",
        "account.currencyCode": "Ccy",
        "tooltip.enterScenario": "Enter Scenario ID",
        "account.schedSweep.heading.allowMultiple": "Allow<br>&nbsp;&nbsp;&nbsp;&nbsp;Multiple",
        "account.schedulesweep.label.sweepAccountLabel": "Sweep Account",
        "tooltip.forecastMonitor.change": "Change selected record",
        "tooltip.viewCurrAc": "View current account",
        "ccyAccMaintPeriod.all": "All",
        "messageFormats.formatName1": "Format Name*",
        "groupMonitor.title.window": "Group Monitor - SMART-Predict",
        "tooltip.partyField": "Sort by party",
        "sweepIntermediaries.targetBic": "Target BIC",
        "entity.general.retentionFlag.no": "No",
        "turnoverReport.mainScreen": "Turnover Report - SMART-Predict",
        "alert.currencyExchangeRate.invalidCreditRate": "Invalid credit margin: Positive value is not allowed.",
        "tooltip.tarBalSign": "Target Balance Sign",
        "tooltip.sort.transactionName": "Sort by Name",
        "screen.cancel": "Cancel",
        "tooltip.firstDay": "First Day",
        "sweep.amountover": "Amount From",
        "tooltip.senderToReceiverText2": "Enter Sender/Receiver Info 3",
        "tooltip.senderToReceiverText1": "Enter Sender/Receiver Info 2",
        "changeUserDetails.title.window": "Change User Details - SMART-Predict",
        "ilmTransactionSet.accountId": "Account ID",
        "label.forecastTemplateOption.expand": "Expand",
        "tooltip.senderToReceiverText5": "Enter Sender/Receiver Info 6",
        "tooltip.senderToReceiverText4": "Enter Sender/Receiver Info 5",
        "accountmonitorbutton.Sum": "Sum",
        "tooltip.senderToReceiverText3": "Enter Sender/Receiver Info 4",
        "ilmExcelReport.expectedSettlement": "Expected Settlement",
        "tooltip.nonworkday.addButton": "Add new facility",
        "label.schedReportHist.mailStatus": "Mail Status",
        "user.login": "Login",
        "account.schedSweep.tooltip.thisAccSweepBookcodeCr": "Book CR",
        "otherEmail.description": "Description",
        "movement.accountWithInstitution": "Account with Institution",
        "ilmAccountGroupDetails.accountIdName": "Account ID - Name",
        "currencyalias.currency": "Currency",
        "label.ilmdataandreportscreens": "ILM data for screens and reports",
        "tooltip.accIntRateDate": "Account Interest Rate Date",
        "tooltip.clickSelAccountParty": "Click to select account party",
        "tooltip.3positionName": "Third position name",
        "account.schedSweep.tooltip.thisAccSweepBookcodeDr": "Book DR",
        "tooltip.scenarioDisplayOrder": "Enter Display Order",
        "tooltip.selectInputDate": "Select input date",
        "entity.crrLimit": "CRR Limit",
        "personalEntityList.title": "Personal Entity List Screen",
        "acctMaintenance.inThisEntity": "In this entity...",
        "alertInstance.valueDate": "VALUE DATE",
        "account.tooltip.accountId": "Sort by Account ID",
        "tooltip.forecastAssumptionAdd.date": "Date",
        "inactive": "Inactive",
        "tooltip.saveRefresh": "Save refresh rate",
        "tooltip.reportbutton": "Generate the report",
        "label.accountspecificsweepformat.column.newExternalDrFormat": "External D",
        "alert.toTimeGreaterThanFromTimethe": "To time should be greater than From time",
        "party.parentParty": "Parent ID",
        "account.tooltip.accountName": "Sort by Account Name",
        "tooltip.entityMonitorOptions.usePersonalEntityList": "Personal Entity List",
        "account.fieldsetBalance": "Start of Day Balance",
        "tooltip.drExtMsg": "Debit external message",
        "login.notification.lastFailedLogin": "Last Failed Login",
        "ilmthroughputbreakdown.actOutlflowsCheckbox": "Actuals Outflows",
        "tooltip.rollSelectMvm": "Roll selected movement",
        "additionalColumns.alert.alreadyOpened": "Please close opened additional columns screen first",
        "tooltip.manualOnly": "Manual Only",
        "errors.password.numbers": "Invalid password: {0} or more numeric characters are required",
        "tooltip.entityMonitor.date": "Enter/Select date",
        "tooltip.Max": "Maximum net cumulative position threshold (must be positive)",
        "accountmaintenance.EODSweeping": "Sweep Schedule",
        "messagefieldadd.alert.duplicateRecord": "A record with this sequence number already exists",
        "multipleMvtActions.ilmFieldSet": "ILM Fcast Status",
        "alert.forecastMonitor.smartPredict": "SMART-Predict",
        "alert.interfaceMonitor.showStrdProc": "Show XML - Stored Procedure Details",
        "tooltip.MatchID": "Match ID",
        "tooltip.deleteRole": "Delete Selected Role",
        "amountcomaafterdecimal": "999.999,99",
        "ilmTransactionSet.currency": "Currency",
        "addjob.label.configParamStatusNoConfig": "No configuration is supplied",
        "sweepIntermediaries.intermediary": "Intermediary BIC",
        "tooltip.jobtype": "Job Type",
        "alertDateShouldNotBeEarlierEhanFromDate": "To Date should not be earlier than From Date",
        "pwd.specialChar": "Special Char",
        "tooltip.selectFromDate": "Select from date",
        "tooltip.matchQuality.matchQuaA": "Sort by quality A",
        "tooltip.matchQuality.matchQuaB": "Sort by quality B",
        "tooltip.matchQuality.matchQuaC": "Sort by quality C",
        "tooltip.changeSelAcBic": "Change selected account BIC",
        "tooltip.testconnection": "Test Connection",
        "tooltip.matchQuality.matchQuaD": "Sort by quality D",
        "connectionPool.tooltip.audsid": "Database audit session ID",
        "tooltip.matchQuality.matchQuaE": "Sort by quality E",
        "tooltip.sweepingLimitsCurr": "Sweeping limits by currency",
        "ilmScenario.entity": "Entity",
        "tooltip.calendarturnoverReportdate": "Select report date",
        "text.showdays": "show",
        "internalmessage.user": "User",
        "sweep.accountId": "Account ID",
        "account.status": "Status",
        "ilmExcelReport.veryCriticalPaymentsOutflows": "Very Critical Payments - Outflows",
        "openQueue.screen": "Excluded Outstanding Queue - SMART-Predict",
        "tooltip.entityMonitorOptions.hideWeekends": "Hide Weekends",
        "label.accountspecificsweepformat.column.newInternalCrFormat": "Internal C",
        "throuputmonitor.tooltip.ccy": "Currency Code",
        "manswp": "Manual Sweep",
        "ilmanalysismonitor.grid.group": "Group",
        "tooltip.allMovements": "All Movements",
        "tooltip.CreditAct": "Sort by CR account",
        "tooltip.enterNewCrInt": "Enter new credit interest rates",
        "label.accountattributehdr.validationmsg": "Validation Message",
        "instancerecord.uniqueIdentifier_tooltip": "Unique ID",
        "ilmExcelReport.summaryDailyTotalInflow": "Daily Total Inflow",
        "alert.Summary.noData.title": "Warning - Summary Details",
        "alert.interfaceMonitor.showSummary": "Show XML - Interface Summary Details",
        "scenario.tab.events": "Events",
        "entity.predict.sweepposition": "Positions",
        "label.validMatchId": "Please enter a valid Match ID",
        "label.accountattributeadd.effectivedate": "Effective Date",
        "tooltip.SelectFormatID": "Select format ID",
        "centralMonitorOptions.fontSizeNormal": "Normal",
        "ilmccyparamsAdd.toolip.altGlobalGroup": "Alternative global currency group. This will be selectable in the Global View tab of the ILM Monitor",
        "tooltip.selectDateFormatMDY": "Select date format (MM/DD/YYYY)",
        "messageFormats.formatType1": "Type",
        "ilmExcelReport.at": "at",
        "accountmonitorNew.Unexpected": "Unexpected",
        "currMonitor.alert.dateRangeValidation": "Date range should not be greater than 30 days",
        "ilmanalysismonitor.scenarioComboTooltip": "Select a Scenario",
        "acctMaintenance.oneNostro": "One-Nostro",
        "button.entity": "Entity*",
        "addJob.title.outputFileLocation": "Output File Location",
        "entity.predict.retentionParam.output": "Output",
        "tooltip.interfaceSettings.class": "Class",
        "label.schedReportHist.mailRsult": "Mail Distribution List",
        "movementsearch.finance/trade": "Finance/Trade",
        "addjob.Friday": "Friday",
        "scenario.distributionlist.tilte": "Users",
        "label.interfaceExceptions.header.description": "Description",
        "ilmccyparamsAdd.alert.defaultMapTime": "Default map time, when specified must be between clearing start and end times",
        "groupMonitor.name": "Name",
        "tooltip.movReceiverCorres": "Click to select an Receiver's Correspondent",
        "tooltip.changeSelCountry": "Change selected country",
        "accountmaintenace.alert.partyId": "The Account Party does not refer to an existing record in the Party table. Click OK to continue or Cancel",
        "scenario.tooltip.sweep": "Check SWEEP ID",
        "tooltip.exportErrors_pdf": "Export to PDF",
        "sweep.accountDr": "DR A/C Msg",
        "sweep.userLimitExceeded": "Sweep amount for sweep ID(s) <b>{0}</b> exceeds your limit for this currency",
        "label.entityMonitor.breakdown": "Breakdown",
        "inputexceptions.messages.header.prid": "PR ID",
        "tooltip.changeSelCorrespondentAcct": "Change selected Correspondent Account",
        "tooltip.entityMonitorOptions.useCurrencyMultiplier": "Currency Multiplier",
        "tooltip.securities": "Securities",
        "addJob.title.retainFilesFor": "Retain Files For",
        "ilmanalysismonitor.entity.title": "Entity",
        "entityaccesslist.addScreen": "Add Entity Access - SMART-Predict",
        "alert.toDateNotOnRange": "To Date is not on default days range",
        "ilmExcelReport.centralBankBalance": "Central Bank Balance",
        "entity.entityName": "Entity Name",
        "account.schedSweep.heading.scheduleFrom": "From<br>",
        "alert.forecastMonitor.notNumber": "Not a Number",
        "ccyAccMaintPeriod.chargeThresDesc": "(Tier+1) * Minimum Reserve ",
        "screenProfile.alert.profileChanged": "Profile has been changed",
        "errors.logon.expirydate": "Licensing Error: Date expired",
        "entity.input": "Input",
        "label.interfaceMonitor.header.interfaceStatus": "Engine Status",
        "tooltip.ChangeSweep": "Change selected sweep",
        "tooltip.deleteAccountGroup": "Delete selected account group",
        "movement.position": "Pos",
        "tooltip.currencyIdentifier": "Currency identifier",
        "partymaintenance.addScreen": "Add Party - SMART-Predict",
        "ilmReport.accumulatedTotal": "Accumulated Total",
        "tooltip.8positionLevel": "Eighth position level",
        "maintEvent.prevId": "Prev Id",
        "ilmAccountGroupDetails.alert.netMinimum": "The minimum net cumulative position threshold must be negative ",
        "PCMReport.BlockedPayments": "Blocked Payments",
        "tooltip.scenarioDescriptions": "Sort by Description",
        "alertInstance.scenarioId": "Scenario ID",
        "ilmreport.keyword.label.startOfCurrentYear": "Start of the current year",
        "cashRsvrBal.startDate": "Start",
        "role.accountaccess.title": "Account Access Control",
        "tooltip.sortEndPosition": "Sort by end position",
        "tooltip.readOnlyAccess": "Read only access",
        "tooltip.user.lastpass": "Last Password Change",
        "sweep.auto": "Auto",
        "account.schedulesweep.tooltip.balanceTypeP": "Predicted balance type from which sweeping will be performed",
        "ilmanalysismonitor.tree.forebasic": "Forecast (basic)",
        "tooltip.selectFullInstAccess": "Allow full access to alert instances",
        "account.schedulesweep.tooltip.balanceTypeE": "External balance type from which sweeping will be performed",
        "screen.alert.warning": "Warning",
        "tooltip.enterCustText5": "Enter Custodian text 5",
        "menuaccessoptions.error.changePassword": "ERROR: Password change is required but user's role does not allow access to the facility",
        "inputexceptions.header.submitted": "Submitted",
        "ilmReport.warningMissingData": "Warning: Report data is incomplete. Records are missing in the range {0} to {1}, do you wish to continue?",
        "ilmccyparamsAdd.title.window.changeScreen": "Change ILM Currency Parameter Maintenance",
        "scenarioSummary.alertableScen": "Show Alertable Scenarios Only",
        "archive.archiveName": "Archive Name",
        "tooltip.enterCustText1": "Enter Custodian text 1",
        "tooltip.enterCustText2": "Enter Custodian text 2",
        "tooltip.enterCustText3": "Enter Custodian text 3",
        "tooltip.enterCustText4": "Enter Custodian text 4",
        "tooptip.personalCurrencyList.button.cancel": "Cancel changes and Exit",
        "user.label.cancel": "Cancel",
        "tooltip.credits": "Enter Credits",
        "instancerecord.attributesJson_header": "Attribute JSON",
        "tooltip.sortParty": "Sort by party",
        "tooltip.interfaceExceptions.inputDate": "Input Date",
        "movementsearch.outstanding": "Outstanding",
        "roleBasedControl.column.facility.tooltip": "Facility or screen",
        "button.category": "Category",
        "ilmanalysismonitor.tooltip.allEntityGlobalSummation": "Summation of the entities using the main currency global account groups",
        "tooltip.sortByReasonCode": "Sort by Reason Code",
        "group.groupLvlCode": "Level",
        "tooltip.roleAccountsButton": "Maintain account based controls",
        "ilmScenario.alert.SourcesValue": "Value must be between 0 and 100",
        "tooltip.AddNewBookcode": "Add new bookcode",
        "scenarioSummary.applyCcy": "Apply Currency Threshold",
        "throuputmonitor.tooltip.forcout": "Forecasted Outflows",
        "scenarioSummary.allOpen": "All open",
        "metaGroup.noofGroups": "# Groups",
        "button.forecastMonitor.ok": "OK",
        "attributeusagesummaryadd.noAttribute": "There are no attributes associated with this functional group. Please add an attribute to define its usage",
        "scenarioNotification.entityID": "Entity ID",
        "cashRsvrBal.fillBal": "Fill Balance",
        "alert.forecasttemplate.templatelocked": "Template ID is locked by ",
        "alertMessage.roleId": "Role",
        "tooltip.sysTime": "Select system time",
        "errors.logon.alreadyLoggedIn": "This user is already logged in",
        "alertDisplay.hostNotFound": "<Host not found>",
        "ilmExcelReport.incomingCriticalPaymentsComparedToDailyTotalOutflow": "Incoming Critical Payments Compared to Daily Total Inflow",
        "movement.bookcode": "Book",
        "label.accountspecificsweepformat.column.newExternalCrFormatInt": "External C(Int)",
        "status.emailId": "EmailId",
        "tooltip.primaryExternal": "Primary External",
        "ilm.options.TabDate": "Tab Date",
        "changePassword.alert.newpass": "New password and confirmed password do not match",
        "multipleMvtActions.colNumberRadio": "Column Number",
        "tooltip.sortAccountName": "Sort by Account Name",
        "tooltip.selectMGCategory": "Select metagroup category",
        "workflowmonitor.logon.title": "Logged on",
        "ilmanalysismonitor.showActual": "Show Actual Datasets Only",
        "corporateAccount.labelDelete": "Delete",
        "linked.tooltip.type": "Type",
        "tooltip.enterNewValueDate": "Enter new value date",
        "workflowmonitor.title.xml": "Workflow Details XML",
        "label.forecastMonitorOptions.hidescenario": "Hide Scenario",
        "addjob.jobStatus": "Status",
        "ilmExcelReport.asOfDailyTotalOutflow": "as % of daily total outflow",
        "tooltip.recordCheck": "Check record instance",
        "addjob.alert.configChanged": "A configuration change has been made, Do you wish for this report to be immediately triggered?",
        "sweepDetail.sweepSetting.noUseSchedule": "No sweep schedule found ",
        "tooltip.debitIntMsg": "Debit Internal Message Format",
        "instancerecord.userLog_tooltip": "User",
        "addjob.label.noAccessinAllEntity": "The selected role does not have access in 'All' entity",
        "tooltip.sortByMatching": "Matching",
        "screen.showJSON": "Show JSON",
        "multiMvtActions.actSettlement": "Act Settlement",
        "scenarioSummary.status": "Status",
        "partySearch.title.mainWindow": "Party Search - SMART-Predict",
        "ilmanalysismonitor.legend.accForeD": "Accum. Forecast Debit",
        "ilmExcelReport.sourcesOfIntradayLiquidityExclIncomingPayments": "Sources of Intraday Liquidity (excl. incoming payments)",
        "tooltip.enterDayMonth": "Enter day of month",
        "ilmanalysismonitor.legend.accForeC": "Accum. Forecast Credit",
        "label.interfaceMonitor.header.activeInterface": "Enabled",
        "accountMonitor.label.accountClass": "Account Class",
        "alert.checkParty": "Invalid: This party cannot be deleted because other parties refer to it as a parent",
        "movementSummaryDisplay.drilldownTitleUnsettled": "Unsettled",
        "group.groupName": "Name",
        "label.forecastMonitorTemplateAddDetail.description": "Description *",
        "label.bothAccountSelectedAre": "Both accounts selected are ",
        "cashRsvrBal.tooltip.fillBal": "Fill Balance",
        "ccyAccMaintPeriod.eodBalSrc": "EOD Balance Source",
        "usermaintenance.userId*": "User",
        "scenarioCategory.categoryid": "Category ID",
        "interfacerules.alert.ruleKeyLengthValidation": "Please enter 100 characters only for Rule Key",
        "accountmonitor.prbalance": "Predicted Balance",
        "tooltip.movement.ccy": "Currency Code",
        "maintEvent.authUser": "Authorisor",
        "ilmanalysismonitor.alertFillMandatoryFields": "Please fill the profile name before saving",
        "scenarioAmendAssignment.title.window": "Amend Role Assignment",
        "label.personalEntityList.button.modifysum": "Change",
        "instancerecord.paymentId_tooltip": "Payment Id",
        "party.partyName": "Name",
        "turnoverReport.actuals": "Actuals",
        "positionlevel.first": "First",
        "ilmReport.largestPositivePosition": "Largest positive net cumulative position",
        "mvmDisplay.tooltip.currencyCode": "Currency code",
        "ilmreport.keyword.label.startOfPreviousWeek": "Start of the previous week",
        "tooltip.tuesday": "Tuesday",
        "errors.archive.delete": "Cannot delete the Current Database",
        "contact.dom.exchange": "FX Rate",
        "maintenanceLog.ipAddress": "IP Address",
        "systemLog.logTime": "Time",
        "tooltip.interfaceSettings.interfaceId": "Interface ID",
        "status.userName": "UserName",
        "tooltip.5positionLevel": "Fifth position level",
        "bookMonitor.bookName": "Book Name",
        "criticalMvtUpdate.cancel": "Cancel",
        "movementsearch.all": "All",
        "interfacemonitor.details.text.directorymanager": "Directory Manager",
        "sweep.cutoffExceeded": "The cut-off time for one or more selected sweeps <b>{0}</b> has been breached.<br>Do you want to go back and review these sweeps?",
        "tooltip.setStyleButton": "Change the style of visible series",
        "ccyAccMaintPeriod.tooltip.date": "Date",
        "button.editreason": "Reason",
        "movement.custodian": "Custodian",
        "ccyAccMaintPeriod.tooltip.excludeFillDays": "Exclude Fill Period from average calculation",
        "ccyAccMaintPeriod.chargeThreshold": "Charge Threshold",
        "alert.interfaceMonitor.rateBelowMin": "Refresh rate selected was below minimum.<br>Set to 5 seconds.",
        "tooltip.selectShowCR": "Select show CR option",
        "ilmtransSetAdd.title.window.viewScreen": "View ILM Transaction Set Display - SMART-Predict",
        "positionlevel.changeScreen": "Change Position Level - SMART-Predict",
        "tooltip.clickSelBookId": "Click to select book ID",
        "account.autoopenunsettled": "Auto Open Unsettled",
        "excludedMovements.mainScreen": "Excluded Movements Report -SMART-Predict",
        "accountmaintenance.alreadyLinked": "Other account is already linked to current account",
        "tooltip.selectShowDR": "Select show DR option",
        "inputreffered.title.window": "Input Referred Queue - SMART-Predict",
        "ilmccyparamsAdd.toolip.defaultMapTime": "Default time to use for credit movements not mentioning a settlement date-time. Specify as HH:MM in currency timeframe.",
        "ccyAccMaintPeriod.tooltip.changedFrom": "Changed From",
        "datasetUpdateInterval": "Update Interval",
        "movementsearch.credit": "Credit",
        "alert.MovementInUse": "Movement is in use by ",
        "alert.accountattributehdr.checknumbervalue": "is wrong value. Please enter a valid Number",
        "sweep.amountChanged": "The proposed amount (based on EOD target balance) of one or more selected sweeps <b>{0}</b> has changed. <br> Do you want to go back and review these sweeps?",
        "centralBankMonitor.toDate": "To Date",
        "alert.currencyMonitor.refreshRate": "Enter a valid refresh rate",
        "tooltip.Usesubacc": "When selected, auto-sweeping between sub and main account will only use sweep time and cut-off from sub-account",
        "messageFormat.title.MainWindow": "Sweep Message Format Maintenance - SMART-Predict",
        "tooltip.addRole": "Add Role",
        "button.log": "Log",
        "tooltip.previousMatch": "Previous match",
        "locationAccess.title.changeWindow": "Change Location Access - SMART-Predict",
        "notes.user": "User",
        "scenario.startTime": "Start Time",
        "maintenanceevent.details.button.amendwinfacility.tooltip": "Launch Maintenance facility relevant to the maintenance event,  in 'Change' mode",
        "account.schedSweep.tooltip.scheduleFrom": "From",
        "tooltip.acc.importinternal": "Select Import Internal",
        "msd.heading.addColumns.sequence": "Sequence",
        "tooltip.toDateMMDDYY": "Select To date (MM/DD/YYYY)",
        "ilmanalysismonitor.title.screenName": "Intra-Day Liquidity Monitor - Main Screen ",
        "scenarioSummary.selectedScenLastRan": "Selected scenario last ran:	",
        "tooltip.changeSweepCode": "Change Sweep Code",
        "movementsearch.status": "Status",
        "label.entityprocess.lastStatus": "Last Status",
        "account.tooltip.cutOff": "Sort by Cut Off",
        "errors.stringistoolarge": "The text is longer than the allowed maximum of !!! characters",
        "scenario.events.tooltip.literal": "Literal",
        "role.workQueueAccess.outStanding": "Outstanding",
        "ilmanalysismonitor.title.window": "Liquidity Monitor - SMART-Predict",
        "tooltip.ccyGrp": "Currency Group",
        "tooltip.internalPosLvlName": "Internal Balance position level",
        "party.alert.partyname": "Please enter a valid party name",
        "menuaccessoptions.alert.changePassword": "Password maintenance is not granted",
        "scenario.events.tooltip.null": "Null",
        "tooltip.enterCustText": "Enter Custodian text",
        "changeBalance.external": "External",
        "sweep.accountTypeNotFound": "Account type not found.<br>Refer to the error log for details!",
        "account.schedulesweep.tooltip.entityIdAgainstAccountCombo": "Please select the other entity Id",
        "multipleMvtActions.label.closeExitButton": "Close and exit",
        "tooltip.forecastMonitor.mvmntBrkdown": "Select Movement to view Movement Summary Detail",
        "party.other": "Other",
        "attributeusagesummary.title.window": "Attribute Usage Summary - SMART-Predict",
        "sweep.id": "Sweep",
        "inputexceptions.messages.header.reference": "Reference",
        "label.entityprocess.timeDisplay": "Time Display",
        "tooltip.exlMvmFromIlmFcast": "Exclude from ILM forecast",
        "tooltip.submitmvmntdetails": "Refer selected movement",
        "party.partyAlias": "Alias",
        "scenarioCategory.categorySystemFlag": "System",
        "connectionPool.lastActionTime": "Opened at",
        "label.country.countryName": "Country Name",
        "tooltip.movement.externalBalanceStatus": "External Status",
        "accountmaintenance.alert.subAcctMainAcct": "There are sub a/c defined for this main account, so the account level cannot be changed",
        "scenario.tooltip.otherIdTypeCombo": "Enter Scenario Other ID type column",
        "scenarioAdvanced.reqRefCols": "Required Ref Cols",
        "multipleMvtActions.importFile?": "Import movements from a file?",
        "sweep.submittedby": "Submitted By",
        "schedReportHist.mainScreen.title": "Scheduled Reports History",
        "tooltip.movement.postingDate": "Posting Date",
        "multiMvtActions.Source": "Source",
        "label.accountspecificsweepformat.column.newInternalDrFormat": "Internal D",
        "throuputmonitor.entity": "Entity",
        "account.schedulesweep.tooltip.fromInput": "Time which sweep schedule window starts (hh24:mi)",
        "errors.short": "{0} must be a short",
        "tooltip.sortRuleKey": "Sort by Rule Key",
        "entity.defineEditableFields": "Define",
        "movementsearch.open": "Open",
        "label.accountattributeadd.title.window": "Account Attribute Maintenance details - SMART-Predict",
        "maintenanceevent.summary.dateselection.from": "Show Events From Date",
        "scenario.tooltip.uniqueExp": "Enter unqiue expression",
        "tooltip.movement.account": "Account ID",
        "tooltip.sort.accountId": "Sort by Account ID",
        "groupmaintenance.title.MainWindow": "Group Maintenance - SMART-Predict",
        "inputException.rate": "Rate",
        "account.schedSweep.heading.sweepFromBalanceType": "Sweep<br>&nbsp;&nbsp;&nbsp;Balance",
        "label.roleBasedControl.ReqOthers.tooltip": "User can authorise another's change?",
        "accountmonitorNew.External": "External",
        "tooltip.sortCrAccMsgType": "Sort by CR account msg type",
        "label.preadviceUpdated": "Pre-advice updated successfully",
        "instancerecord.lastRaisedDatetime_header": "Last Raised Date",
        "workflowmonitor.input.title": "Input",
        "secureid.challenged": "The SecurID has been challenged, please re-enter",
        "maintenanceLog.reference": "Reference",
        "pwd.alphaChar": "Alpha Char",
        "sweepNAKQueue.overduetime": "Overdue",
        "label.ilmccyprocess.currentstatus": "Current Status",
        "entity.predict.groupLevelNames": "Group Level Names",
        "movementsearch.matchstatus": "Match Status",
        "account.sum": "Sum",
        "tooltip.sortSwiftInOutDirection": "Sort by SWIFT input/output direction",
        "label.accountattributedefinition.title.window": "Account Attribute Definitions - SMART-Predict",
        "logBalance.dateandtime": "Date/Time",
        "ilmAccountGroupDetails.tootlip.correspBank": "When checked, this group will be selectable as a correspondent bank for the purpose of Basel reporting",
        "interfacerulesmaintenance.predefinedMessageType": "Predefined",
        "scenario.runAtDesc": "(Specify in system time frame)",
        "contact.email": "Email",
        "scenario.recordInsLbl": "Record Instances",
        "archive.db_link": "DB Link",
        "addJob.title.parameters": "Parameters",
        "alert.forecastMonitor.refreshRate": "Refresh rate selected was below minimum.\\nSet to 5 seconds",
        "tooltip.SortByCutofftime": "Sort by cut off time",
        "maintEvent.maintFacilityId": "Facility",
        "tooltip.scenarioEntityColumn": "Enter Scenario Entity Column",
        "tooltip.addGlobalAccountGroup": "Click to create a new dynamic group containing all accounts flagged as ILM liquidity contributors",
        "instancerecord.otherIdType_tooltip": "Other Id Type",
        "account.schedulesweep.label.directionB": "Both",
        "tooltip.selectRole": "Select role(s)",
        "account.schedulesweep.label.directionD": "Defund",
        "nonworkday.title.addScreen": "Add Non workday Maintenance",
        "account.schedulesweep.label.directionF": "Fund",
        "sweepPrior.extendDisplay": "Extend Display to",
        "account.schedSweep.tooltip.allowMultiple": "Allow Multiple",
        "displayOrder.id": "Display Order",
        "tip.schedReportHist.fileName": "Report File Name",
        "positionLevel.id.accountId": "A/C ID",
        "messageFormats.msgSeparator1": "Message Separator",
        "acctMaintenance.includeLoro": "Include Loro/Predict Options",
        "accountmonitor.erBalance": "External Balance",
        "inputconfig.header.emaillogsto": "Email Logs To",
        "tooltip.selectReference": "Select Like Condition for Reference",
        "addjob.Daily": "Daily",
        "tooltip.sortMsgId": "Sort by message ID",
        "ilmReport.noData": "No Data",
        "tooltip.currencyLimit": "Enter the currency limit",
        "role.perCurrrency.currency": "Currency",
        "acc.Zero": "Zero",
        "tooltip.selectAccountClass": "Select account class",
        "tooltip.sortMessageType": "Sort by Message Type",
        "userStatus.title.window": "User Status - SMART-Predict",
        "ilmanalysismonitor.exportLabelWaiting": "Export in progress, please wait...",
        "alert.sweepDetail.submitted": "&nbsp;successfully submitted",
        "rolemaintenance.copyFromScreen": "Copy Role Details - SMART-Predict",
        "movement.pos1": "Pos",
        "tooltip.selectBalanceType": "Select balance Type",
        "role.menuaccess.access": "Access",
        "linked.tooltip.class": "Class",
        "tooltip.addSection": "Add new section",
        "tooltip.decimalplaces": "Decimal places",
        "tooltip.user.lastlog": "Last Login",
        "tooltip.sortCategoryId": "Sort by Category ID ",
        "role.notification.inputNotification": "Notifications",
        "tooltip.selectCustFlag": "Select for custodian flag",
        "label.entityMonitorOptions.useCurrencyMultiplier": "Use Currency Multiplier",
        "shortcut.addScreen": "Add Shortcut - SMART-Predict",
        "sweepSearchList.drExtMsg": "DR EXT MSG",
        "aliasTooltip.sortcurrency": "Sort by currency code",
        "tooltip.movType": "Select movement type",
        "ilmanalysismonitor.legend.actBalance": "Actual Balance",
        "inputException.inputDate": "Input Date",
        "scenario.immediately": "Immediately",
        "tooltip.generateReportforSingleRole": "Generate report for a Selected role",
        "SweepMsgDisplay.title.mainWindow": "Sweep Message Summary",
        "interfaceNotificationAlert": "[Predict] Error when attempting to contact the SmartInput engine",
        "scenarioCategory.categoryDescription": "Description",
        "tooltip.scenarioAmountColumn": "Enter Scenario Amount Column",
        "usermaintenance.section": "Section",
        "messageFormats.formatType.delimited": "Delimited",
        "changePassword.tooltip.userId": "User ID",
        "ilmExcelReport.riskAppetite": "Risk Appetite",
        "tooltip.selectDelimited": "Select delimited",
        "tooltip.extendDisplayTo": "Change Extend Display to",
        "bookCode.add.groupIdLevel3": "Group Level 3",
        "changeBalance.internal": "Internal",
        "alertInstance.eventStatus": "Event Status",
        "bookCode.add.groupIdLevel2": "Group Level 2",
        "bookCode.add.groupIdLevel1": "Group Level 1",
        "acctMaintenance.accountId": "Account",
        "sweepSearchList.currencyCode": "Ccy",
        "myUserDetails.title.window": "My User Detail - SMART-Predict",
        "tip.schedReportHist.exportError": "If any error was occurred during the report export",
        "ccyAccMaintPeriod.tooltip.entityId": "Entity Id",
        "balMaintenance.forecastSODNegative": "Forecast SOD Negative",
        "tooltip.wednesday": "Wednesday",
        "entity.predictTab": "Predict",
        "label.entityprocess.entityLocalTime": "Entity Local Time",
        "scenario.tooltip.payment": "Check PAYMENT ID",
        "ilmAccountGroupDetails.currency": "Currency",
        "criticalMvtUpdate.toolTip.add": "Add",
        "scenarioNotification.entity": "Entity",
        "tooltip.senderToReceiverText": "Enter Sender/Receiver Info 1",
        "tooltip.enterNewAcId": "Enter new account ID",
        "tooltip.personalEntityList.button.cancel": "Cancel changes and Exit",
        "correspondentaccountmaintenance.alert.corresAccId": "Correspondent Acc ID is empty",
        "label.newDataExistFor": "New data exist for",
        "label.movement.externalBalanceStatus": "Ext",
        "inputException.refresh": "Refresh",
        "label.operationTakeTime": "Note: this operation may take several minutes",
        "tooltip.enterNewAcGL": "Enter new account GL code",
        "processScheduler.title.window": "Process Scheduler - SMART-Predict",
        "CriticalPay.type": "critical Payment Type",
        "button.schedReportHist.date": "Date",
        "tooltip.changeRole": "Change Selected Role",
        "ilmExcelReport.totalCriticalPaymentsInflows": "Total Critical Payments - Inflows",
        "ilmExcelReport.outgoingCriticalPaymentsAsOfAvailableLiquidityExclIncomingPayments": "Outgoing Critical Payments as % of Available Liquidity (excl. incoming payments)",
        "label.accountspecificsweepformat.column.accountName": "Name",
        "alert.entityMonitor.contactSysAdm": "Please contact your System Administrator.",
        "tooltip.sortBalance": "Sort by Balance",
        "message.StartPosition": "Start Position",
        "status.pwdChangeDate": "LastPasswordChange",
        "throuputmonitor.tooltip.unsetout": "Unsettled Outflows",
        "tooltip.sortByInputTime": "Sort by update Date",
        "alertInstance.resolved": "Resolved Date/User",
        "tooltip.sortScreenName": "Sort by screen name",
        "tooltip.isCustomerAccount": "Accounts flagged as customer accounts can be easily selected for the purpose of Basel reporting",
        "label.forecastMonitorOption.userTemplates": "User Templates",
        "tooltip.addPrimaryExternal": "Select Primary External",
        "usermaintenance.phoneNo": "Phone Number",
        "ilmSummary.predicted": "Forecast ",
        "button.print": "Print",
        "amountThresholdTypeAbs": "Domestic Currency",
        "role.alerttype.both": "Both",
        "systemLog.ipAddress": "IPAddress",
        "ilmExcelReport.centralBankCollateralBlockedForCriticalPayments": "Central bank collateral blocked for critical payments (-)",
        "sweepsearch.debited": "Account Debited",
        "user.titleBtnCancel2": "Close window",
        "movementroll.dateValidateSystemDate": "The Value date cannot be earlier than current system date",
        "ilmScenario.minutes": "mins",
        "user.titleBtnCancel1": "Back to first screen",
        "mvmDisplay.tooltip.ref2": "Movement reference 2",
        "mvmDisplay.tooltip.ref3": "Movement reference 4",
        "accountmaintenance.alert.subAccount": "No sub a/c are defined for this main account",
        "mvmDisplay.tooltip.ref1": "Movement reference 1",
        "usermaintenance.currGrp": "Default Ccy Group",
        "mvmDisplay.tooltip.ref4": "Movement extra text",
        "tooltip.sortWorkQueue": "Sort by work queue",
        "tooltip.description": "Enter Description",
        "scenario.events.afterLaunch": "After Launching Events",
        "tooltip.add.dst": "Add new daylight saving period ",
        "criticalMvtUpdate.orderInCategory": "Order in Category",
        "userStatus.header.name": "Name",
        "accountmaintenanceadd.earliestSweepTime": "Earliest Sweep Time",
        "label.accountspecificsweepformat.tooltip.extViaIntermediaryDebit": "Select Ext Via Intermediary Debit",
        "alert.atlestAnyOneOption": "Please select at least one option (Show DR/Show CR)",
        "label.dailysavingperiodcurrency.title.window": "Daylight Saving Period Summary - Currency",
        "button.change": "Change",
        "tooltip.sortCutOff": "Offset=Sort by cut off offset",
        "button.maintain": "Maintain",
        "messageScenarioFormats.changeScreen": "Change Scenario Message Format - SMART-Predict",
        "tooltip.partialRuleId": "Check Partial ID check box to search on Partial Rule ID",
        "logBalance.id": "ID",
        "criticalMvtUpdate.tooltip.currencyId": "Please choose a currency",
        "scenario.events.eventFacilityDesc": "Description",
        "addjob.Weekly2": "Weekly",
        "tooltip.change": "Change",
        "label.accountspecificsweepformat.tooltip.internalCredit": "Select Internal Credit",
        "tooltip.sort": "InternalFlag=Sort by internal flag",
        "sweepSearchList.crdExtMsg": "CR EXT MSG",
        "sweep.valudatefor": "Value Date From",
        "messageFormats.formatType.tagged": "Multi-line",
        "ilaapccyparams.globalGrp": "Global Group",
        "currency.preFlag.active": "Active",
        "tooltip.SweepStatus": "Sort by sweep status",
        "instancerecord.raisedDatetime_header": "Raised Date",
        "scenarioAdvanced.parameterValues": "Parameter values",
        "tooltip.accounId": "Sort by Account ID",
        "addJob.button.configure": "Configure",
        "movementsearch.timeto": "To",
        "tooltip.file": "Enter file name",
        "tooltip.selectLanguage": "Select a language",
        "amountDelimiter": "Amount Format",
        "tooltip.newddext": "Enter new debit external",
        "tooltip.sortRuleId": "Sort by Rule ID",
        "account.bic": "BIC",
        "tooltip.ReasonCode": "Enter reason code",
        "tooltip.sortBIC": "Sort by BIC",
        "tooltip.defaultMapTime": "Sort by Default Map Time",
        "button.display": "Display",
        "preAdviceInput.column.productType": "Product Type;",
        "ilaapccyparams.LVPSGrid": "LVPS",
        "errors.entity.entityName.required": "Entity name is required.<BR>",
        "ilmreport.keyword.label.endOfPreviousYear": "End of the previous year",
        "tooltip.schedreporthist.mailStatus": "Mail Status",
        "tooltip.viewAccountGroup": "View selected account group",
        "ExchangeRate.confirm.IntoNew": "The Exchange Rate setting is designed to be set once and not changed. All current exchange rates are stored in the originally specified format. If you do want to change this setting, please contact your support team. Continue with change (not recommended)?",
        "account.schedSweep.heading.targetBalanceType": "Target<br>&nbsp;&nbsp;&nbsp;Type",
        "currencyGroupChild.alert.IdasAll": "Currency Group 'All' not permitted",
        "role.sweepLimits.sweep": "Sweeping",
        "preAdviceInput.invalidRows": "Invalid rows",
        "status.phoneNumber": "Phone No.",
        "tooltip.sortByTimeZoneOffset": "Sort by Time Zone Offset",
        "maintEvent.status": "Status",
        "tooltip.sortArchiveId": "Sort by Archive ID",
        "alert.forecastMonitor.connectionLost": "Unable to save server \\nPossible loss of connection",
        "instancerecord.id_tooltip": "ID",
        "messageFormats.alert.overdueFormat": "ACK Overdue field must be formatted as HH:MM:SS",
        "tooltip.selectNewCrMsgCancel": "Select new credit message cancel",
        "label.accountspecificsweepformat.text.specAccountId": "Specific Account ID*",
        "ccyAccMaintPeriod.fillDays": "Fill Days",
        "tooltip.enterEndPosition": "Enter end position",
        "matchNotes.title.window": "Match Notes - SMART-Predict",
        "ilmReport.reportType": "Report Type",
        "tooltip.selectExlMvmDealerMons": "Select exclude movement from dealer monitors",
        "account.schedSweep.heading.thisAccSweepBookcodeDr": "Book DR<br>",
        "sweepInter.InterBIC": "Intermediary BIC",
        "entityaccesslist.changeScreen": "Change Entity Access - SMART-Predict",
        "alert.ibanFormatInvalid": "The IBAN format is invalid, please (re)check",
        "bookMonitor.balance": "Predicted Balance",
        "ilmSummary.unsettled": "Unsettled ",
        "entity.GroupLevel1Name": "Group level 1 name",
        "scenario.events.eventFacility": "Event Facility ID",
        "userLog.tooltip.user": "User",
        "addjob.Thursday": "Thursday",
        "account.schedSweep.heading.thisAccSweepBookcodeCr": "Book CR<br>",
        "custodian.custodianFlag": "Custodian Flag",
        "tooltip.exchangeRateRetention": "Ccy Exchange Rate retention period",
        "sweeppriorcutoff.valueDate": "Value Date",
        "sweepsearch.mvmntId": "Movement",
        "label.entityMonitorOptions.usePersonalCcyList": "Use Personal Currency List",
        "tooltip.sortSweepDTU": "Sort by sweep date/time/user",
        "centralBankMonitor.refreshRateSelected": "Refresh Rate Selected",
        "alert.enterValidDate": "Please enter a valid date.",
        "label.accountattributehdr.norequireddate": "Not Required",
        "addjob.Monthly": "Monthly",
        "userSetup.title.window": "User Setup - SMART-Predict",
        "messageFormats.usage.sweep": "Sweep",
        "ilmanalysismonitor.requestRecalculation": "Request recalculation of ILM data",
        "bookCode.addScreen": "Bookcode Add Pop up",
        "inputexceptions.messages.header.reference1": "Reference 1",
        "sweepsearch.currency": "Currency",
        "ilm.options.order": "Order",
        "turnoverReport.forecasted": "Forecasted",
        "label.forecastAssumptionAdd.amount": "Amount",
        "tooltip.forecastMonitorOptions.hidetotal": "Hide Total",
        "errors.password.alphaChars": "Invalid password: {0} or more alpha characters are required",
        "tooltip.srchmvmnt": "Search movements",
        "tooltip.category": "Open scenario category screen",
        "tooltip.enterCorrespondentAcctId": "Enter Correspondent Account ID",
        "ilmAccountGroupDetails.pub": "Public",
        "tooltip.sortUnsettledBalance": "Sort by Unsettled balance",
        "movementDisplay.alert.noAccess": "Invalid: your role does not provide access to this movement",
        "tooltip.secondWeekendDay": "Second weekend day",
        "label.schedReportHist.exportStatus": "Export Status",
        "msd.dateradio.option1.text": "Fixed",
        "ilmanalysismonitor.errorAllOptionCcyNotAvailable": "This currency cannot be shown with Entity 'All'. Currencies must share the same ccy-GMT offset in each entity to enable aggregation over all entities",
        "tooltip.sortStatus": "Sort by status",
        "alert.throuputbreakdown.atleastOneFilter": "at least one filter needs to be selected",
        "pwd.recentuserdPwd": "Recently used",
        "alertInstance.currencyCode": "CURRENCY_CODE",
        "group.groupmaintenanceScreen": "Group Maintenance - SMART-Predict",
        "movementsearch.debit/credit": "Sign",
        "tooltip.threshold": "Sort by threshold",
        "queryBuilderScreen.title.window": "Query Builder",
        "turnoverReport.entity": "Entity",
        "scenarioCategory.categoryId": "Category ID",
        "balMaintenance.title.MainWindow": "Start of Day Balance Maintenance - SMART-Predict",
        "tooltip.schedSweep.usedInOtherSched": "Number of other sweep schedules that refer to this account",
        "tooltip.selectMetagroupId": "Select metagroup ID",
        "ilmScenario.tooltip.otherSourcesAvlbl": "Enter percentage of Other Liquid Assets Availability (0-100%)",
        "throuputmonitor.tooltip.throughputratios": "",
        "ilmScenario.on": "On",
        "button.testcon": "TestCon",
        "turnoverReport.levelBreakdown": "Level Breakdown",
        "balmaintenance.SODType": "SOD Type",
        "ilmanalysismonitor.ccy.title": "Currency",
        "maintenanceevent.details.button.viewinfacility.tooltip": "Launch Maintenance facility relevant to the maintenance event,  in 'View'/'Display-only' mode",
        "toolTip.import": "Import",
        "sweepCancel.colValue.Man": "Man",
        "account.schedulesweep.tooltip.toInput": "Time at which sweep schedule window ends (hh24:mi)",
        "ccyAccMaintPeriod.external": "External ",
        "ccyAccMaintPeriod.tooltip.minTargetBalance": "Minimum Target Balance",
        "title.preadvice.inputWindow": "Pre-advice Input - SMART-Predict",
        "movementsearch.account.loro": "Loro",
        "tooltip.debitExtMsg": "Debit External Message Format",
        "matchQuality.title.mainWindow": "Copy From Match Quality - SMART-Predict",
        "accountmonitorBrkDown.title.window": "Account Breakdown Monitor - SMART-Predict",
        "accIntRate.UpdateDate": "Update Date/Time",
        "tooltip.linkAc": "Link-account",
        "tooltip.screenName": "Screen name",
        "button.groups": "Groups",
        "personalCurrency.title": "Personal Currency List Screen",
        "tooltip.addNewLocation": "Add New Location",
        "tooltip.forecastMonitor.selectCurrency": "Select Currency",
        "bookMonitor.selected": "Selected",
        "ilmanalysismonitor.alertRevertProfile": "Are you sure you want to reload this profile?",
        "manualMatch.warning.messageforOnePositionLevel": "Only 1 position level is available. Do you want to continue?",
        "scenario.tooltip.afterMinTxt": "Enter interval value (mins)",
        "alert.entityMonitorOptions.notANumber": "Not a Number",
        "tooltip.addSecondaryExternal": "Select Secondary External",
        "entity.general.domesticCurrency": "Domestic Currency",
        "ilmanalysismonitor.grid.fcastsod.tooltip": "Forecast Start Of Day",
        "tooltip.sortByInterestRateDate": "Sort by date of interest rate",
        "movement.positionLevel": "Position Level",
        "tooltip.corrCode": "Correspondent Code",
        "ilmAccountGroupDetails.grpId": "Group ID",
        "tooltip.viewWrkQAcclist": "View selected work queue access list",
        "ilmanalysismonitor.grid.openunsett.tooltip": "Open Unsettled Movement Adjustment ",
        "login.user.alert.passwordExpired": "Your password has expired, please change it",
        "tooltip.movement.status": "Movement Status",
        "msd.addCols.title": "Additional Columns  - SMART-Predict",
        "interestCharges.currency": "Currency",
        "additionalColumns.label.column": "Column",
        "entity.general.exchangeRateFormat.domestic.ccy": "Domestic ccy/ccy",
        "messageFields.startPos1": "Start Pos",
        "bookCode.bookCode": "Book",
        "scenarioAdvanced.useGenericDisplay": "Use Generic Display",
        "tooltip.forecastMonitorOptions.hidezerosum": "Hide Zero Sum Columns",
        "label.entityMonitor.movementId": "Movement",
        "tooltip.sortByUserId": "Sort by user ID",
        "viewUserStatus.title.window": "View User Status - SMART-Predict",
        "workflowmonitor.incMovmnts.title": "Included Movements: Today",
        "label.accountattributeadd.time": "Time",
        "tooltip.changeGenSysParam": "Change general system parameters",
        "minutes": "minutes",
        "multipleMvtActions.confirmProcess1": "> movements and is not reversible. Are you sure?",
        "connectionPool.poolStats": "Pool Stats",
        "label.ilmccyprocess.calculinprogress": "Data build in progress, please wait",
        "ilm.options.ilmTab_toolTip": "Check box for including an Entity/Currency tab",
        "unsettledMovements.mainScreen": "Unsettled Movements Report - SMART-Predict",
        "ilmScenario.id": "Scenario ID",
        "invalid_instance_id": "Enter a valid Instance ID",
        "currency.decimalPlaces": "Dec",
        "label.ilmccyprocess.currentstatus.toRun": "To Run",
        "workflowmonitor.rate.title": "Rate",
        "tooltip.overrideWeekend2": "Select override weekend 2",
        "groupMonitor.metagroup": "Metagroup",
        "tooltip.overrideWeekend1": "Select override weekend 1",
        "bookMonitor.location": "Location",
        "turnoverReport.date": "Date",
        "button.forecastMonitor.save": "Save",
        "movement.intermediaryInstitution": "Intermediary Institution ",
        "ilmScenario.publicPrivate": "Public?",
        "label.forecastMonitorTemplateAdd.title.window": "Add Forecast Monitor Template - SMART-Predict",
        "roleBasedControl.column.reqAuth.tooltip": "Specify whether this role user requires authorisation by another",
        "tooltip.clickSelPosId": "Click to select position level",
        "accountmonitor.title.window": "Account Monitor - SMART-Predict",
        "ccyAccMaintPeriod.forDate": "For Date:",
        "message.alert.accountAccess": "Account access settings will be saved subject to currency group access settings",
        "tooltip.sortAcType": "Sort by account type",
        "tooltip.interfaceSettings.property": "Property",
        "label.mvmtAreBusy": "Movement(s) are in use by",
        "criticalMvtUpdate.toolTip.cancel": "Cancel",
        "title.rates": "Account Interest Rate",
        "tooltip.mainAcctID": "Main Account ID",
        "crintrates": "Current Interest Rates",
        "tooltip.firstWeekendDay": "First weekend day",
        "workflowmonitor.option.optionLabel": "On entry, the amount currency threshold will be",
        "ilmExcelReport.group": "Group",
        "account.schedulesweep.label.targetBalanceTypeD": "Debit",
        "account.schedulesweep.label.targetBalanceTypeC": "Credit",
        "schedulerError": "Control --> Scheduler --> Matching process",
        "ilmSummary.accountName": "Account Name",
        "account.schedulesweep.label.targetBalanceTypeA": "Acc. Attribute",
        "alert.personalEntityList.sumEntityNotSelected": "Please, select at least one entity to be summed before saving",
        "matchRolledNoteText": "Match rolled over from match ?",
        "ilmanalysismonitor.tree.accTotals": "Accumulated Totals",
        "tooltip.accountMonitorNew.openUnexpectedBalTotalAsString": "Show open unexpected balance",
        "button.clear": "Clear",
        "addjob.StartTime": "Start Time",
        "link.closePage": "Close page",
        "tooltip.enterPartyId": "Enter party ID",
        "tooltip.sortbybeneficiaryid": "Sort by beneficiary ID",
        "otherEmail.description.tooltip": "Sort by description",
        "ilmReport.unencumberedLiquidAssets": "Unencumbered liquid assets",
        "ilaapccyparams.defaultMapTime": "Default Map Time",
        "scenario.configFieldSet.legendText": "Required parameters",
        "tooltip.benCustomer": "Enter Beneficiary Customer ID",
        "archive.module": "Module",
        "tooltip.ChangeSelectedBookcode": "Change selected bookcode",
        "tooltip.sortItemNum": "Sort by movement ID",
        "errors.invalidNumber": "Please enter a valid number",
        "label.entityMonitor.bookCode": "Book",
        "scenario.events.mins": "mins",
        "label.diff": "Diff",
        "sweep.currentAmt": "Original Amount",
        "tooltip.sortCurrentArchive": "Sort by Current Archive",
        "ilmExcelReport.ofWhich": "Of which:",
        "label.accountattributeadd.type": "Type",
        "entity.editablefield": "Editable Field",
        "errors.InvalidDataAccessResourceUsageException": "InvalidDataAccessResourceUsageException",
        "alert.transactionSetSpecified": "This transaction set specified as 'Extra Transaction Set' for an ILM scenario and cannot be removed at this time",
        "tooltip.forecastMonitorOptions.hideassumption": "Hide Assumption",
        "tooltip.DeleteSelectedCurrency": "Delete selected currency if unused",
        "tooltip.sortValue": "Sort by value",
        "tooltip.CBGroupId": "Sort by Central Bank Group ID",
        "defaultaccountmaintenance.alert.account": "Account field is empty",
        "alertInstance.accountId": "ACCOUNT_ID",
        "ilmreport.keyword.label.endOfCurrentWeek": "End of the current week",
        "ilmExcelReport.tabNameAverageSummary": "Average Summary",
        "ilmExcelReport.totalCreditLines": "Total Credit Lines",
        "tooltip.move": "Move",
        "tooltip.enterMsgType": "Enter a message type",
        "account.allpreadviceentity": "Allow Pre-Advice Entry",
        "account.schedulesweep.label.targetBalanceTypeR": "Rule",
        "acctextraid": "Extra ID",
        "tooltip.numberOfAliases": "Number of Aliases",
        "turnoverReport.startdayTooltip": "Enter report Start date",
        "tooltip.typeMessage": "Type message",
        "tooltip.sortTargetBalance": "Sort by target balance",
        "entity.sweepPoslvl": "Sweep",
        "corporateAccount.tooltip.close": "Close",
        "throuputmonitor.tooltip.actuals": "",
        "ilmReport.ccyMultiplierLabel": "Currency Multiplier",
        "tooltip.cash": "Cash",
        "account.status.closed": "Closed",
        "tooltip.Description": "Enter description",
        "poslevel.indicator": "Int/Ext",
        "label.entityMonitorOptions.reportingCcy": "Reporting Currency",
        "messageFormatAdd.alert.defineFields": "Please define fields for the message",
        "acctMaintenance.accttype": "Type",
        "label.accountattributedefinition.column.updated": "Updated",
        "metaGroup.mgroupName1": "Metagroup Name",
        "tooltip.movOrderingCustomer": "Click to select ordering Customer",
        "scenario.events.remainactive": "Instance will remain Active",
        "turnoverReport.dataSource": "Data Source",
        "accountGroup.ilmGroupName": "Name",
        "attributeusagesummary.type": "Type",
        "tooltip.domesticCurr": "Domestic currency",
        "movementsearch.criteria.fielset": "Additional Search Criteria",
        "corporateAccount.tooltip.change": "Change",
        "entity.EnterEntityName": "Enter entity name",
        "tooltip.enterDaysRetainMaintenanceLog": "Enter number of days to retain maintenance log",
        "rolemaintenance.viewScreen": "View Role - SMART-Predict",
        "tooltip.CurrencyIdentifier": "Currency identifier",
        "sweepsearch.predictstatus": "Predict Status",
        "label.maxNumberPages": ", maximum number of pages:",
        "alertInstance.firstRaised": "First Raised Date",
        "label.copyforecasttemplate.title.window": "Copy From Forecast Monitor Template - SMART-Predict",
        "interfacerulesmaintenance.alert.ruleKey": "Rule Key is empty",
        "alert.acctBreakdown.date": "Please enter a valid date",
        "criticalMvtUpdate.toolTip.startTime": "Specify the time period in which the SQL update should run",
        "tooltip.Sweep": "Sweep",
        "criticalMvtUpdate.desc": "Description",
        "role.maintainAnyIlmGroup": "Maintain any ILM account group",
        "button.copy": "Copy",
        "tooltip.selectMultilineVariablesFields": "Select Multi-Line - Variable Fields",
        "tooltip.entity.input": "Input Retention Period",
        "label.entityprocess.databaseSession": "Database<br>Session",
        "scenario.Advanced.window": "Scenario - Advanced Details - SMART-Predict",
        "holidays.addScreen": "Add Holiday - SMART-Predict",
        "button.ratesChange": "Change Rate",
        "account.schedSweep.tooltip.sweepAccountId": "Account ID",
        "account.nostrotype": "Nostro",
        "shortcuts.id.shortcutIdadd": "Shortcut",
        "alert.forecasttemplate.templateexist": "Template already exists",
        "ilaapccyparams.clearingStartTime": "Clearing Start Time",
        "balMaintenance.scenarioHighlighted": "Scenario Highlighted",
        "tooltip.ok": "OK",
        "ilmReport.alertSelectPayment": "At least one of the four checkBoxes must be selected",
        "auditLog.errorLog": "Error Log",
        "MovementRecovery.title.window": "Movement Recovery - SMART-Predict",
        "otherEmail.send.tooltip": "Send",
        "addjob.Time": "Time",
        "contactDetails.alert.invaildprimrydomain": "Invalid primary domain in email address",
        "ilmSummary.minBalT": "Min Bal / T                    ",
        "usermaintenance.roleId": "Role Profile",
        "tooltip.sortbyextraText1": "Sort by EXTRA_TEXT1",
        "addJob.title.fileNamePrefix": "File Name Prefix",
        "tooltip.defaultRunTime": "Enter default run time",
        "scenario.events.tooltip.add": "Add event",
        "tooltip.printDetails": "Print details",
        "account.schedulesweep.label.tolabel": "To",
        "tooltip.enterProfileOption": "Select profile option",
        "tooltip.maintainGrpButton": "Maintain Selected Group",
        "criticalMvtUpdateMaint.title": "Critical Movement Update Maintenance",
        "scenario.events.tooltip.instAttr": "Instance Attribute",
        "interest.tooltip.sortByInterestRateDate": "Sort by interest rate date",
        "scenario.events.tooltip.view": "View event",
        "criticalMvtUpdate.toolTip.set": "Enter the SET clause of an UPATE P_MOVEMENT SQL",
        "ilmanalysismonitor.time": "Time",
        "CriticalPay.order.in.categ": "Order in Categ",
        "accttype": "Account Type",
        "messagefieldadd.alert.LineNumber": "Line Number cannot be equal to 0",
        "tooltip.viewSecondaryForecast": "View Secondary Forecast",
        "tooltip.accountMonitorNew.date": "Select date",
        "user.titletext": "Welcome to SMART-Predict",
        "tooltip.forecastMonitor.delete": "Delete selected record",
        "linked.tooltip.ccy": "Ccy",
        "ilmScenario.debits": "Debits",
        "personalCurrencyList.numberBetweenRange": "Please enter a number between 1 - 14 ",
        "tooltip.selectMain": "Select Main Account",
        "tooltip.fullAccess": "Full access",
        "format.canddExt": "Cancel Debit External",
        "ilmExcelReport.tabNameCriticalInflows": "Critical Inflows",
        "dstEndDateAsString": "End Date",
        "sweepSearchList.NewAmt": "New Amount",
        "movSearch.alert.amountoverLess": "Amount Over must be less than Amount Under",
        "tooltip.addArchive": "Add new Archive",
        "messagefieldadd.alert.sequenceNo": "Sequence Number cannot be equal to 0",
        "tooltip.entity.largeSmallMovementThresholdAsString": "Large/Small Movements Threshold",
        "msd.dateradio.label": "Search Date behaviour",
        "tooltip.enterSeqNo": "Enter sequence number",
        "label.ilmccyprocess.currentstatus.running": "Running",
        "tooltip.selectNewCrMsg": "Select new credit message format",
        "tooltip.selectMail": "Notify by Mail",
        "manualInput.entity.id": "Entity",
        "tooltip.securitiesFilter": "Securities filter",
        "criticalMvtUpdate.sqlUpdateQuery": "SQL Update Query:",
        "intradayBalances.currency": "Currency",
        "archive.databaseLink": "Database Link",
        "tooltip.accountattributehdr.validationmsg": "Enter validation message",
        "label.personalCurrencyList.entity": "Entity",
        "criticalMvtUpdate.criticalTypeId": "Critical Type",
        "ilm.options.order_toolTip": "Numeric values indicate sorting of currencies and entities",
        "tooltip.multiplier": "Sort by multiplier",
        "label.forecastTemplateOption.bucketNo": "Bucket No",
        "criticalMvtUpdate.delete": "Delete",
        "fomat.cdExt": "Credit External",
        "label.exportPDF": "Export to PDF",
        "label.accountattribute.enddate": "End Date",
        "connectionPool.sqlStatement": "SQL Statement",
        "correspondentaccountmaintenance.add.correspondentAccID": "Correspondent Acc ID",
        "currMonitor.breakdown": "Breakdown",
        "format.accountSpecific": "Account Specific",
        "ilmanalysismonitor.includeMvnts": "Include Open Movements",
        "auditLog.userId": "User",
        "alert.entityprocess.defTime": "Please enter a valid time",
        "changeBalance.updateDate": "Update Date",
        "alert.interfaceSettings.text": "Please enter a text",
        "inputconfig.header.inputdir": "Input Directory",
        "acc.overdraft": "Debit",
        "tooltip.sortFile": "Sort by file",
        "ilmReport.intradayThroughput": "Intraday Throughput",
        "ilmReport.centralBank": "Central Bank",
        "tooltip.sortCountryCode": "Sort by country code",
        "scenario.distributionlist": "Distribution List",
        "batchScheduler.confirm.removeJobInfo": "reports will be deleted too",
        "autoRefreshRate": "Auto-refresh rate",
        "ilmReport.startOfDay": "Start of Day",
        "additionalColumns.label.table": "Table",
        "movementsearch.positionlevel": "Position Level",
        "accountmonitorNew.Sum": "Sum",
        "dynamicJasper.noDataFound": "\"No data for this report\";",
        "preAdviceInput.column.partyId": "Counterparty ID;",
        "sweep.messageType": "Message Type",
        "ilmScenario.tooltip.systemScen": "System Scenario",
        "alert.priorityNotBeZero": "Priority should not be zero",
        "opportunityCostReport.mainScreen": "Opportunity Cost Report - SMART-Predict",
        "multiMvtActions.format": "Format",
        "logBalance.suppliedSODSource": "Supplied SOD Source",
        "ccyAccMaintPeriod.startDate": "Start Date",
        "tooltip.changeCutOffTime": "Change cut off time (hh:mm)",
        "tooltip.globalCcyAcctGrp": "Sort by Global Currency Account Group",
        "ilmanalysismonitor.combinedview.title": "Combined View",
        "tooltip.8positionName": "Eighth position name",
        "tooltip.addnewmsgformat": "Add new message format",
        "tooltip.DeleteSelectedBookcode": "Delete selected bookcode",
        "ilmAccountGroupDetails.accountInGrp": "Accounts in this group",
        "movement.beneficiaryInstitution": "Beneficiary Institution",
        "messagefieldadd.alert.EPSP": "End Position cannot be less than Start Position",
        "scenario.events.info": "Info",
        "tooltip.selectUserStatus": "Select user status",
        "tooltip.enterNewAcBic": "Enter new account BIC",
        "ilmScenario.legend.remainingMovements": "Remaining Movements",
        "accountmonitorNew.Predicted": "Predicted",
        "tooltip.change.TransactionSet": "Change Selected Transaction",
        "tooltip.entityId": "Entity ID",
        "interest.tooltip.sortByUserId": "User ID",
        "label.interfaceMonitor.buttonStart": "Start button",
        "scenario.accountIdColumn": "ACCOUNT_ID Column",
        "screen.error": "Error",
        "tooltip.selectText": "Select text",
        "tooltip.menuAccess": "Menu access",
        "preAdviceInput.importStatusLbl": "Import status:",
        "cpyFromManualInput.copyFrm": "copyFrm",
        "currencyFunding.date": "Value Date",
        "ilmReport.other": "Other",
        "addjob.label.configParamStatusIncorrectConfig": "Configuration is NOT correctly supplied",
        "tooltip.accountGroup.accs": "Sort by Accounts",
        "errors.content.notAllowed.urlDescription": "As a security measure, your request is blocked for security reasons",
        "movement.pos": "Position",
        "acctmaintenance.externalsod": "External SOD",
        "movement.expectedSettlement": "Expected Settlement",
        "tooltip.sortDefaultWeekend1": "Sort by default weekend 1",
        "tooltip.sortDefaultWeekend2": "Sort by default weekend 2",
        "defineEditableFields.changeScreen": "Change Editable Fields",
        "scenario.mvtIdColumn": "MOVEMENT_ID Column",
        "sweepsearch.sweephistory": "Sweep History",
        "tooltip.addNewInterfaceRule": "Add new Interface Rule",
        "multipleMvtActions.label.expSettlLbl": "Expected Settlement",
        "tooltip.addNewWrkQAcclist": "Add new work queue access list",
        "tooltip.sortNoGroups": "Sort by no. of groups",
        "corporateAccount.tooltipDelete": "Delete",
        "tooltip.acctGrpFilter": "Specify a filter condition to identify the accounts which are to be members of a dynamic group",
        "tooltip.calendarintradayBalancesdate": "Select report date",
        "manualSweep.tabs.all": "1 Week",
        "batchScheduler.header.jobName": "Job Name",
        "tooltip.contactDt": "Contact Details",
        "tooltip.sortBookcodeId": "Sort by bookcode ID",
        "tooltip.SortByInterestBasis": "Sort by interest basis",
        "accountmaintenance.Format": "Formats",
        "tooltip.cashFilter": "Cash filter",
        "reports.submit": "Report",
        "tooltip.selectSweepFromBalance": "Select Sweep from Balance",
        "tip.schedReportHist.mailRsult": "The list of users to whom an email is actually sent ",
        "instancerecord.scenarioId_header": "Scenario ID",
        "msd.alertFillMandatoryFields": "Please fill the filter name before saving",
        "tooltip.enterMGName": "Enter metagroup name",
        "stopRuleMaintenanceDetails.title.window": "Stop Rule Details",
        "scenario.colType.error": "Please fill Base Query field before clicking on Test button",
        "tooltip.defineEditableFields": "Define Editable Fields",
        "tooltip.roleAccountsCheck": "Implement account-based controls on manual input, matching and sweeping",
        "multiMvtActions.account": "Account",
        "tooltip.int": "Int",
        "label.forecastMonitor.templateId": "Template ID:",
        "tooltip.entityMonitor.option": "Change Options",
        "interfacerulesmaintenance.title.changeWindow": "Change Interface Rules Maintenance - SMART-Predict",
        "tooltip.interfaceMonitor.bad": "Number of messages in bad state",
        "bookMonitor.name": "Name",
        "ccyAccMaintPeriod.minTargetBalance": "Minimum Account Balance",
        "label.forecastMonitorTemplateAdd.templateId": "Template ID",
        "tooltip.enterMaxSweepAmount": "Enter maximum sweep amount",
        "password.new": "New Password",
        "ilmreport.keyword.label.endOfCurrentMonth": "End of the current month",
        "scenario.tooltip.checkedAll": "Check all attributes",
        "defaultaccountmaintenance.currencyCode": "Ccy",
        "accountmaintenance.intradaySweeping": "Intra-Day Sweeping",
        "sweep.sweepuetr1": "UETR",
        "sweep.sweepuetr2": "Extra UETR",
        "tooltip.selectFormatReqAuth": "Select if format type requires authorisation",
        "tooltip.enterreceiverCorrText2": "Enter Receiver's Correspondent text 2",
        "tooltip.enterreceiverCorrText1": "Enter Receiver's Correspondent text 1",
        "tooltip.enterreceiverCorrText4": "Enter Receiver's Correspondent text 4",
        "tooltip.enterreceiverCorrText3": "Enter Receiver's Correspondent text 3",
        "label.accountattributelastvalues.title.window": "Account Attribute Latest Values - SMART-Predict",
        "label.personalEntityList.title.window": "Personal Entity List - SMART-Predict",
        "multipleMvtActions.label.closeButton": "Close",
        "tooltip.deleteSelectedRole": "Delete selected role",
        "archiveSearch.title.mainWindow": "Archive Search - SMART-Predict",
        "format.legend.new": "New",
        "alert.interfaceSettings.emaillogsto": "Email logs type is None, you cannot enter an email ",
        "acctmaintenance.forecastsod": "Prediction SOD",
        "label.entityMonitor.metagroup": "Metagroup",
        "tooltip.sortQualityZ": "Sort by quality Z",
        "tooltip.changeUserDetails": "Change your user details",
        "errors.csrf.attack.log": "SECURITY WARNING - Client request blocked: A CSRF attack detected",
        "alertMessage.alertstage": "Alert Event",
        "scenario.tab.instances": "Instances",
        "multipleMvtActions.updateOtherRadio": "Update other settlement details",
        "tooltip.allEntityOption": "Implements Workflow / Currency Monitor: 'All' entity option",
        "addjob.enable": "Enable",
        "tooltip.showSub": "Select Show Sub option",
        "tip.accountattribute.entity": "Select an entity ID",
        "amountcomabeforedecimal": "999,999.99",
        "accgroup": "Account Group",
        "linked.type": "Type",
        "alert.criticalPaymentType.emptyExpectedTime": "Please fill default Expected Time field",
        "addjob.Cyclic": "Cyclic",
        "movementOriginalNoteText": "Movement rolled over to movement ?, new value date ?",
        "tooltip.enterreceiverCorrText5": "Enter Receiver's Correspondent text 5",
        "bookMonitor.book": "Book",
        "role.entAccessList.fullAccess": "Full Access",
        "ilmExcelReport.amount": "Amount",
        "tooltip.selectInclMvmInMonitors": "Select include movement in dealer monitors",
        "ccyAccMaintPeriod.excludeFillDays": "Exclude Fill Days",
        "CriticalPay.entity": "Entity",
        "balmaintenance.reasonCode": "Reason Code",
        "party.current": "Current",
        "alert.AccessNotAvl": "Access not available for ",
        "inputconfig.header.class": "Class",
        "ilmScenario.tab.sources": "Sources",
        "tooltip.sortLocationId": "Sort by location ID",
        "reports.thresholdAmount": "Amount Threshold",
        "confirm.delete": "Are you sure you want to delete?",
        "connectionPool.tooltip.sid": "Database session ID",
        "scenario.accountLbl": "ACCOUNT_ID",
        "ilmanalysismonitor.recalculationError": "Recalculation failed, please see logs",
        "matchquamaintenance.title.MatchQuality": "Match Quality Maintenance - SMART-Predict",
        "tooltip.calendarcurrencyFundingdate": "Select value date",
        "currency.id": "Ccy",
        "interfacerulesmaintenance.title.addWindow": "Add Interface Rules Maintenance - SMART-Predict",
        "label.nonworkday.optionYes": "Yes",
        "tooltip.sort.dstenddate": "Sort by End Date",
        "systemLog.action": "Action",
        "tooltip.viewGroup": "View group",
        "acctMaintenance.tooltip.accNameInSvcEntity": "Specify the account name to use when this account appears in monitors under the servicing entity",
        "tooltip.BeneficiaryID": "Beneficiary ID",
        "scenario.alert.uncheckingRecordInstances": "Warning. Unchecking Record Instances will cause configuration parameters to be lost in the Instances and Events tabs. Are you sure?",
        "tooltip.view": "View",
        "accountGroup.accs": "Accs",
        "queue.authorizePanel": "Authorise Panel",
        "addJobDetails.title.Window": "Add Job Details - SMART-Predict",
        "ilmanalysismonitor.legend.actBalance.Title": "Act.",
        "button.interfaceMonitor.stopInterface": "Stop",
        "scenarioCategory.categoryTitle": "Title",
        "ilmreport.keyword.label.startOfPreviousYear": "Start of the previous year",
        "label.forecastAssumptionAdd.assumption": "Assumption",
        "text.days": "days",
        "movement.status": "Status",
        "ilmExcelReport.asOfTotalAvailableLiquidity": "as % of total available liquidity",
        "label.movementsearch.externalBalanceStatus": "Ext Bal Status",
        "tooltip.account.mainAcctID": "Click here to select Main Account ID",
        "entity.predict.retentionParam.input": "Input",
        "maintenanceLog.logDate_Time": "Time",
        "ilmAccountGroupDetails.absoluteThresholds": "Absolute Thresholds",
        "tooltip.name": "Enter group name",
        "ilmAccountGroupDetails.alert.netMaximum": "The maximum net cumulative position threshold must be positive ",
        "tooltip.change.dst": "Change the selected daylight saving period ",
        "group.add.cutoffOffset": "Cut off offset",
        "maintenanceLogView.ipAddress": "IP Address",
        "criticalMvtUpdate.reportIndivPay": "Report Individual Payments",
        "movement.secondEntity": "Second Entity",
        "alert.entityProcess.recover": "Are you sure that you want to set the status of this process to Disabled? You may want to verify with your database team that the database process is no longer running.",
        "acctMaintenance.accNameInSvcEntity": "Name in Svc Entity",
        "errors.valuetoolarge": "Value is higher than the allowed maximum of ",
        "label.milliseconds": "milliseconds",
        "account.tooltip.status": "Sort by Account Status",
        "tooltip.changeNewSweepIntermediary": "Change Sweep Intermediary",
        "multiMvtActions.status": "Match Status",
        "ilmExcelReport.totalPayments": "Payments",
        "tooltip.sortAuthFlag": "Sort by authorisation flag",
        "maintEvent.tooltip.prevId": "Prev Id",
        "ilmReport.titleGroupReportDaily": "Daily Liquidity Management Report - ILM Group",
        "mvmmatqsel.title.authorised": "Offered Match Queue - SMART-Predict",
        "tooltip.restriction": "restriction of the selected functional group",
        "addjob.Wednesday": "Wednesday",
        "usermaintenance.status": "Status",
        "accountMonitor.label.sumCutOff": "Inc 'Sum by Cutoff'",
        "account.isIlm": "isILM",
        "scenario.eventTab.alert.missingMsgFormat": "Please choose a valid message format",
        "ilmScenario.scenarioDescription": "Description",
        "alert.passwordExpiresInFewDays": "Your password will expire in ${changepassworddays} days. Do you want to change it now?",
        "tooltip.selectBalDate": "Enter balance date",
        "tooltip.sortQualityA": "Sort by quality A",
        "inputException.close": "Close",
        "tooltip.sortQualityC": "Sort by quality C",
        "message.FieldType": "Field Type",
        "tooltip.alertMnmtDislay": "This match has been changed",
        "tooltip.sortQualityB": "Sort by quality B",
        "throuputmonitor.tooltip.ilm_group": "ILM Account Group",
        "tooltip.sortQualityE": "Sort by quality E",
        "table.movement": "Movement",
        "tooltip.sortQualityD": "Sort by quality D",
        "tooltip.sortByFrequency": "Sort by Frequency",
        "userLog.tooltip.itemId": "Item ID",
        "ilmReport.ownEntity": "Own Entity",
        "button.setRunProcess": "Set Run",
        "multipleMvtActions.yesRadio": "Yes",
        "movement.actualSettlement": "Actual Settlement",
        "tooltip.enterInputDate": "Enter input date",
        "workflowmonitor.excluded": "Excluded",
        "scenario.events.resolutionAfter": "Resolution Overdue after",
        "tooltip.jobFileLocation": "Specify the output file location (beneath the system configured ScheduledReportLocation)",
        "ilmanalysismonitor.deleteProfileImageTooltip": "Delete profile",
        "user.hostId": "Host",
        "interfacemonitor.details.text.storedprocdetails": "Stored Procedure Details",
        "holiday.holidayDay": "Day",
        "acct.currency.id": "Currency",
        "tooltip.sortAccName": "Sort by account name",
        "button.groupMonitor.options": "Options",
        "tooltip.enterInterfaceRuleId": "Enter Interface Rule ID",
        "label.accountspecificsweepformat.column.specifiedAccountId": "Account ID",
        "addJob.tab.role": "Roles",
        "movementRecovery.lockTime": "Lock Time",
        "preAdviceInput.allRows": "All rows",
        "scenario.tooltip.sweepColCombo": "Enter Scenario Sweep ID column",
        "reports.thresholdTime": "Time Threshold",
        "account.accountClass": "Class",
        "currencygroup.ccy": "# Ccy",
        "tooltip.entityMonitor.accountMonitor": "Select Account to view Account Monitor",
        "sweepInter.curName": "Name",
        "tooltip.sortBySODChange": "Sort by SOD Change",
        "changePassword.title.window": "Change Password - SMART-Predict",
        "tooltip.submitbutton": "Generate the report",
        "accIntRate.UpdateUser": "User",
        "label.forecasttemplate.column.columnno": "Column No",
        "scenario.CreateInsDescFull": "Requires parameters",
        "user.secureId": "SecurID*",
        "interfacemonitor.sub.threadname": "Name",
        "movement.currency1": "Currency",
        "role.accountaccess.viewTitle": "View Account Access Control",
        "maintenanceEvent.alert.cannotBeAmended": "This record cannot be amended because a pending change requires authorisation",
        "label.refreshevery": "Refresh Every",
        "tooltip.predictField": "Sort by Predict status",
        "status.pwdChangeDT": "Password History",
        "tooltip.SaveUpdates": "Save updates",
        "status.sectionId": "SectionId",
        "acctbreakdown.alert.datechange": "Server date changed at midnight. Your date selection will be updated.",
        "label.report.totalInPages": "Amount total in this report",
        "tooltip.entityProcess": "Launch Entity Process Information screen",
        "userStatus.header.role": "Role",
        "sweep.to": "To",
        "ilmReport.TotalCreditLines": "Total credit lines",
        "emailTemplateMaintenance.tooltip.templateId": "Template Id",
        "addJob.title.mail": "Mail (Attachment)",
        "ccyAccMaintPeriod.tooltip.endDate": "End date of the maintenance period",
        "alert.validExchangeRate": "Please enter a valid exchange rate",
        "userSetup.Label.Disabled": "Disabled",
        "label.acctBreakdown.date": "Date",
        "ilmReport.centralbankcreditlines": "Central bank credit lines",
        "sweep.submitted": "Submitted",
        "tooltip.sendCorrs": "Enter Sender's Correspondent ID",
        "sweep.submitPanel": "Submit Panel",
        "ilmReport.mainCorrespondentBank": "Main Correspondent Banks",
        "tooltip.date": "Date",
        "tooltip.enterRoleId": "Enter role ID",
        "addJob.title.executeAsRole": "Execute As Role",
        "centralBankMonitor.entity": "Entity",
        "acctmain.usetodaysod": "Use Today's SOD",
        "ilmanalysismonitor.legend.forebasic": "Forecast (basic)",
        "errors.email": "{0} is an invalid email address",
        "tooltip.sortJobName": "Sort by job name",
        "throuputmonitor.forecasted": "Forecasted",
        "tooltip.sortCounterPartyId": "Sort by counterparty ID",
        "label.ilmcalculation.title.window": "ILM Calculation Launcher ",
        "tooltip.schedSweep.show": "Click to view other sweep schedules that refer to this account",
        "entity.accountId": "Account ID",
        "tooltip.copy": "Copy",
        "preadviceInput.reference": "Reference",
        "replacebalanceDate": "Date",
        "instancerecord.matchId_header": "Match Id",
        "multipleMvtActions.confirmProcess": "This action will affect <",
        "tooltip.enterCustodianId": "Enter a custodian ID",
        "tip.schedReportHist.outputLocation": "File location on the server",
        "ilmExcelReport.xyz": "XYZ",
        "tooltip.DeleteSeletedNote": "Delete selected note",
        "reportsturnover.title.window": "Turnover Statistics - SMART-Predict",
        "alert.interfaceSettings.time": "Please enter a valid time",
        "additionalColumns.alert.emptyField": "Please choose a valid",
        "otherEmail.send": "Send",
        "matchId.alreadyexist": "Unable to generate match.<br>Please try again.<br>If the problem persists contact your System Administrator.",
        "tooltip.sortLineNo": "Sort by line no.",
        "workflowmonitor.total": "Total",
        "confirm.acctBreakdown.moveFromLoro": "Move balance to Predicted column?",
        "scenario.tooltip.match": "Check MATCH ID",
        "acctmain.futurebalances": "Future Balances",
        "usermaintenance.role": "Role",
        "ilmExcelReport.average": "Average",
        "entity.DeleteEntity": "Delete entity",
        "tooltip.accountattributehdr.minvalue": "Enter minimum value",
        "button.schedReportHist.dateRange": "Date Range",
        "ilmReport.timeSpecific": "Time Specific / Critical Payments",
        "msd.generalProfile.label": "General Profile",
        "label.preadviceCreatedWithMovement": "Pre-advice created with Movement ID",
        "label.forecastMonitorTemplateChangeDetail.title.window": "Change a Column of Forecast Monitor Template - SMART-Predict",
        "correspondentaccountmaintenance.alert.currencyCode": "Currency Code cannot be set to 'All'",
        "ccyMonitorOptions.defaultDays": "Default Days",
        "groupMonitor.date": "Date",
        "sweepDetail.sweepSetting.useSchedule": "Use settings from schedule",
        "tooltip.scenarioPublicPrivate": "Sort by Public?",
        "attributeusagesummaryadd.grandTotal.noContribution": "No contribution",
        "autoswpswitch": "Auto Sweep",
        "title.timeZoneOffset": "Time Zone Offset",
        "label.matchUserByAnotherProcess": "Match is in use by another process ",
        "tooltip.transactionSetName": "Enter Transaction Set Name",
        "tooltip.allPreAdviceEntity": "Allow Pre Advice Entry",
        "tooltip.authmvmntdetails": "Authorise selected movement",
        "title.accContacts": "Contact Details - SMART-Predict",
        "tooltip.InsertNewField": "Insert new message field",
        "movementRolledNoteText": "Movement rolled over from movement ?, original value date ?",
        "tooltip.changePrimaryForecast": "Change Primary Forecast",
        "tooltip.selectEntityId": "Select an entity ID",
        "ccyAccMaintPeriod.eodBalanceSrc": "EOD Balance Source",
        "title.addFormats": "Add Format Details - SMART-Predict",
        "tooltip.interfaceMonitor.filtered": "Number of messages in filtered state",
        "sweep.confirm.cancelMsg": "Are you sure you want to cancel this sweep?",
        "sweep.accountIdCr": "CR Account",
        "corporate.name": "Corporate Name",
        "button.findoraddpopup.Search": "Search",
        "tooltip.priority": "Sort by priority",
        "inputException.title": "Input Exception Monitor",
        "alert.interfaceMonitor.noData": "No data to display",
        "main.warningMsg": "WARNING: Test date set to ",
        "button.genericdisplaymonitor.goTo": "Go to ",
        "tooltip.selectFlashIcon": "Notify by Flash Icon",
        "ilmSummary.alert.dataFromArchiveNotFound": "Warning: Retrieval of data from archive will be derived from ILM account-based time-series data. This may take a little longer than usual.",
        "tooltip.selectSweeping": "Select Sweeping",
        "ilmanalysismonitor.grid.lastupdate.tooltip": "Last Update",
        "ilmReport.InflowOutflowSummation": "Inflow/Outflow Summation",
        "sweep.accountIdDr": "DR Account",
        "entity.MetagroupLevel1Name": "Metagroup level 1 name",
        "tooltip.enterUserName": "Enter user name",
        "message.SequenceNumber": "Sequence Number",
        "label.forecastMonitorOptions.applyCurrencyMultiplier": "Apply Currency Multiplier",
        "tooltip.enterPhNo": "Enter phone number",
        "tooltip.restrictLocations": "Open Restrict Locations window",
        "tooltip.sortByForecast": "Sort by Forecast SOD",
        "ilmanalysismonitor.timeframe.title": "Ccy Timeframe",
        "ilmanalysismonitor.scenarios.title": "Scenarios",
        "label.findoraddpopup.title.window": "Find / Add pop up (Normal) - SMART-Predict",
        "section.sectionId": "Section",
        "scenarioNotification.roleID": "Role ID",
        "inputconfig.header.mqactive": "Read MQ",
        "button.kill": "Kill",
        "tooltip.none": "None",
        "fomat.cancdExt": "Cancel Credit External",
        "tooltip.search": "Search movement details",
        "outstanding.tabs.today": "Today",
        "multipleMvtActions.tooltip.bookCombo": "Please choose a bookCode",
        "partyalias.title.mainWindow": "Party Alias Summary - SMART-Predict",
        "tooltip.sortGroupId": "Sort by group ID",
        "button.limits": "Limits",
        "connectionPool.tooltip.module": "Module (obtained from database v$session)",
        "label.forecasttemplate.column.id": "ID",
        "ilmAccountGroupDetails.labelLegendText": "Default Legend Text",
        "usermaintenance.emailId": "Email",
        "errors.ipAddress.log": "IP Adress",
        "label.personalEntityList.sumEntityName": "Sum Entity *",
        "ilmReport.correspondentBankGroups": "Correspondent Bank Groups",
        "tooltip.accountattributehdr.attributeid": "Enter attribute id",
        "label.nonworkday.optionNo": "No",
        "manualInput.alert.chkStatus": "WARNING: Item queued for Authorisation",
        "accountmaintenance.MaximumMinimum": "Maximum sweep amount cannot be less than minimum sweep amount",
        "movementsearch.valuefrom": "Value Date From",
        "tooltip.removeSelMov": "Remove selected movement",
        "account.mainAccountId": "Main Account ID",
        "user.label.backtoFirstScreen": "Back to first screen",
        "tooltip.sortAcClass": "Sort by account class",
        "tooltip.cutOffTime": "Cut Off Time (hh:mm)",
        "movSearch.alert.amountunder": "Amount Under should be greater than Amount Over",
        "login.notification.tooltip.lastLoginIp": "IP address of last successful login",
        "accountGroup.groupType": "Dynamic?",
        "label.schedReportHist.elapsedTime": "Elapsed Time",
        "tooltip.newPwd": "New password",
        "batchScheduler.alert.terminateDenied": "Terminate request denied: this process is currently not running",
        "maintenanceLogView.facility": "Facility",
        "tooltip.sortByjobDetail": "Sort by job detail",
        "turnoverReport.currency": "Currency",
        "alert.invalidDataFound": "No valid data found in the excel for the given Movement ID location",
        "tooltip.deleteSelBookcode": "Delete selected bookcode",
        "tooltip.selectAccountType": "Select account type",
        "tooltip.entity.countryCode": "Select Country Code",
        "genericDisplayMonitor.menu.showXML": "Show XML",
        "movementDisplay.newValueDate": "New Value Date",
        "entity.allowRecalculation": "Allow Re-Calculation for past",
        "button.tooltip.msd.save": "Save",
        "ilmExcelReport.criticalOutgoingPayments": "Critical Outgoing Payments",
        "button.move": "Movement",
        "qualityTab.all": "All",
        "sweepDetail.maxSweepAmt": "Max Sweep Amount",
        "label.movementSummaryDisplay": "Movement Summary Display",
        "ilmanalysismonitor.continueWithoutRecalculate": "Continue without recalculation of ILM data",
        "account.options": "Options",
        "multipleMvtActions.label.ordInstLbl": "Ordering Institution",
        "errorLog.errorDate_Time": "Time",
        "currency.exchangeRate": "Exch Rate",
        "CurrencyInterest.changeScreen": "Change Currency Interest - SMART-Predict",
        "ilmExcelReport.expectedTime": "Expected time",
        "role.sweepLimits.sweepLimit": "Sweep Limit",
        "ilmExcelReport.asOfAvailableLiquidityExclIncomingPayments": "as % of available liquidity (excl. incoming payments)",
        "ilmReport.currencyGlobalGroup": "Currency Global Group",
        "tooltip.lastDay": "Last Day",
        "ilmAccountGroupDetails.grpMemberAccount": "Group Member Accounts",
        "tooltip.forecastMonitorTemplateCopy.templateId": "Select a Template ID",
        "ilm.options.globalChart": "Global Chart",
        "changelocationmaintenanace.title.mainWindow": "Change Location definition",
        "tooltip.enterPartyAlias": "Enter party alias",
        "scenario.events.tooltip.resolutionQuery": "Enter resolution query",
        "tooltip.sortCurrencyId": "Sort by currency code",
        "corporateAccount.label.add": "Add",
        "balMaintenance.reasonCode": "Reason Code",
        "tooltip.enterSweepTimeHM": "Enter sweep time (hh:mm)",
        "label.movementChanged": "This movement has been changed",
        "connectionPoolDetails.title.window": "Connection pool details",
        "tooltip.amountTo": "Enter amount to",
        "inputauthorise.title.window": "Input Authorise Queue - SMART-Predict",
        "movementsearch.exclude": "Exclude",
        "button.genericdisplaymonitor.resolve": "Resolve",
        "ilmAccountGroupDetails.accountNotInGrp": "Accounts not in this group",
        "changeBalance.legend.workingBal": "Working SOD Balances",
        "turnoverReport.pdf": "PDF",
        "attributeusagesummary.attributeId": "Attribute ID",
        "exchange.tooltip.currName": "Sort by currency name",
        "metagroupMonitor.level": "Level",
        "tooltip.view.TransactionSet": "View Selected Transaction",
        "interfacerulesmaintenance.add.ruleValue": "Rule Value",
        "sweepDetail.orgSweepAmt": "Original Sweep Amount",
        "message.alert.emptySuppliedExtBalance": "A supplied external must be provided before entering a value for this field",
        "ilmAccountGrpAdd.title.window.viewScreen": "View Account Group Details - SMART-Predict",
        "title.accountInterest": "Account Interest Rate - SMART-Predict",
        "tooltip.entityAccess": "Entity access",
        "account.creditLeg": "Credit Leg",
        "tooltip.disable": "Disable",
        "instancerecord.overThreshold_header": "Threshold",
        "matchQualityNotDefined": "Match quality not defined for ",
        "exchange.tooltip.updateDate": "Sort by update date & time",
        "errors.password.specialChar": "Invalid password: {0} or more symbol characters are required",
        "inputexceptions.header.interface": "Interface",
        "alertInstance.uniqueIdentifier": "Unique Identifier",
        "acctmaintenance.external": "External",
        "tooltip.sortGroupCode": "Sort by group code",
        "tooltip.SortByExchangeRate": "Sort by exchange rate",
        "scenario.distributionlist.selectall": "Select All",
        "tooltip.selectHolidayDate": "Select holiday date",
        "tooltip.enterPriority": "Enter Priority",
        "addjob.Monthly2": "Monthly",
        "tooltip.EntergrIdLevel2": "Enter group ID level 2",
        "tooltip.EntergrIdLevel1": "Enter group ID level 1",
        "tooltip.personalCurrencyName": "Sort by currency name",
        "criticalMvtUpdate.save": "Save",
        "tooltip.EntergrIdLevel3": "Enter group ID level 3",
        "confirm.title": "Microsoft Internet Explorer",
        "userSetup.addScreen": "Add User - SMART-Predict",
        "tooltip.contact": "Add contact",
        "movementsearch.offered": "Offered",
        "connectionPool.connectionPool": "Connection Pool",
        "multiMvtActions.sign": "Sign",
        "accountmonitor.date": "Value Date",
        "tip.accountattribute.account": "Select Account ID",
        "alertInstance.otherId": "OTHER_ID",
        "addJob.combo.process": "Process",
        "account.tooltip.isIlm": "Sort by Is ILM Liquidity Contributor",
        "label.matchDifferentAmountTotalsAcrossPositionLevels": "This match has different amount totals across position levels",
        "scenario.role.selectAll.label": "Select All",
        "button.entityProcess": "Entity",
        "ilmreport.keyword.label.startOfCurrentQuarter": "Start of the current quarter",
        "alertInstance.mvtId": "MOVEMENT_ID",
        "ent.deleteSelectedPosLvl": "Selected position level is defined as sweep/pre-advice position.\\n Are you sure you want to delete?",
        "inputException.startDate": "Start Date",
        "tooltip.selectRoleId": "Select a role ID",
        "tooltip.emailId": "Enter email ID",
        "button.entity.add": "Add new position level",
        "ilmExcelReport.intradayThroughputOutflowsCcyTimeframe": "Intraday throughput - Outflows (CCY timeframe)",
        "tooltip.exportErrors_csv": "Export to CSV",
        "tooltip.debits": "Enter Debits",
        "tooltip.sortPreflag": "Sort by Predict flag",
        "label.accountattributehdr.attributeid": "Attribute ID",
        "status.lang": "Language",
        "preAdviceInput.resetAlert": "This will reset the configuration to default settings.      Are you sure?",
        "confirm.recon": "Are you sure you want to reconcile the selected movement?",
        "tooltip.DeleteSelectedCurrencyAlias": "Delete selected currency alias",
        "matchQuality.entityId": "Entity",
        "tooltip.selectAmountFormat": "Select amount format",
        "button.tooltip.schedreporthist.details": "Show file history details",
        "errors.password.minLength": "Invalid password: must be {0} or more characters in length",
        "cpyFromManualInput.alert.rights": "Rights for the entity of selected movement do not exist. Please select another movement",
        "tooltip.preAdvicePosition": "Pre advice position level",
        "tooltip.entity.general.domesticCurrency": "Select Domestic Currency",
        "tooltip.internalSOD": "Enter internal SOD",
        "systemLog.dateRange": "System Log",
        "tooltip.refreshSystemLog": "Refresh system log",
        "account.schedSweep.heading.sweepDirection": "Direction<br>",
        "auditLog.from": "From",
        "ilmAccountGroupDetails.syntaxAlertTitle": "Filter condition error",
        "ilmReport.CorrespondentTotalCreditLines": "Correspondent credit lines",
        "ilm.options.summary": "Summary",
        "tooltip.deleteWrkQAcclist": "Delete selected work queue access list",
        "tooltip.selectGrouping": "Select Grouping",
        "centralMonitor.currencylimit": "Currency Limit",
        "messageScenarioFormats.viewScreen": "View Scenario Message Format - SMART-Predict",
        "manualInput.alert.mvmNotonFile": "Enter a valid Movement ID",
        "label.includedItemsAtMultiplePositionLevels": "There are included items at multiple position levels.",
        "ccyAccMaintPeriod.tooltip.tableName": "Facility Name",
        "ccyAccMaintPeriod.tooltip.view": "View",
        "button.forecastMonitor.cancel": "Cancel",
        "personalCurrency.alert.priority": "Priority order should be between 1 and 99",
        "ilaapccyparams.clearingEndTime": "Clearing End Time",
        "addjob.CycleDuration": "Cycle Duration",
        "scenario.events.tooltip.pending": "Set instance as Pending Resolution",
        "addJob.combo.report": "Report",
        "tooltip.sortName": "Sort by job name",
        "messagefieldadd.alert.rangeOverlapping": "Range overlapping",
        "entity.preadv": "Pre advice",
        "party.typecust": "Custodian",
        "ilmAccountGroupDetails.second": "Second",
        "cashRsvrBal.tooltip.valueDate": "Value Date ",
        "tooltip.secondaryForecast": "Secondary Forecast",
        "sweep.limitNotDefined": "Sweep limit not defined for this currency",
        "tooltip.excMvmInDataEx": "Exclude movement from data extract",
        "acctSweepBalGrp.tooltip.sweepAccount": "Sweep Account ID",
        "label.mail.messageBody": "Message Body",
        "button.exclude": "Exclude",
        "startBalLog.title.MainWindow": "Starting Balance Audit Log - SMART-Predict",
        "acountMonitorNew.AccountBreakDownScreen": "Account Breakdown",
        "ilmanalysismonitor.nodata": "No ILM data exists for the selected entity, currency and date.",
        "button.4EyesGrp": "4-Eyes",
        "tooltip.currentstatus": "Current status",
        "manualInput.amount": "Amount",
        "accountMonitor.label.screenMode": "Screen Mode",
        "tooltip.deleteSelHoliday": "Delete selected holiday",
        "movementsearch.status.referred": "Referred",
        "tooltip.delayRateCreditPct": "Enter percentage of credits delayed (0-100%)",
        "addJob.title.frequency": "Frequency",
        "tooltip.changeCategory": "Change Selected Scenario Category",
        "scenario.events.tooltip.refColumn": "Select reference columns",
        "tooltip.defineParams.delete": "Delete parameter",
        "tooltip.menuLevel1Desc": "Menu Item Level1 description",
        "sweep.sweepStatus": "Status",
        "locationAccess.title.viewWindow": "View Location Access - SMART-Predict",
        "tooltip.sortOldValue": "Sort by old value",
        "balMaintenance.title.addWindow": "Change Balance - SMART-Predict",
        "label.personalEntityList.title.addsum": "Add Sum Entity",
        "ilmReport.throughputGraph": "Throughput Graph",
        "scheduler.recordExists": "Record already exists",
        "ilaapgeneralsysparam.mainScreen": "ILM General Parameters - SMART-Predict",
        "ilmReport.dailyTotalOutflow": "Daily total outflow",
        "throuputmonitor.forcinf": "Inflows",
        "title.viewContacts": "View Contact Details - SMART-Predict",
        "tooltip.mail.attachment": "Attaching files",
        "ilmthroughput.Today": "Today",
        "pwdchange.enterOldPassword": "Please enter your old password",
        "errors.entity.domesticCurr.required": "Domestic currency is required.<BR>",
        "ilmSummary.turnover": "Turnover  ",
        "tooltip.deleteSelInterfaceRule": "Delete selected Interface Rule",
        "tooltip.sortByLocation": "Sort by Location",
        "account.schedSweep.tooltip.scheduleTo": "To",
        "sweepsearch.originalamt": "Original Amount",
        "tooltip.sortMsgDrAc": "Sort by message DR A/C",
        "tooltip.sortItem": "Sort by item",
        "changeJob.title.Window": "Change Job Detail - SMART-Predict",
        "metaGroup.mgroupName1add": "Metagroup Name",
        "ilmTransactionSet.credits": "Credits",
        "label.accountattributehdr.attributename": "Name",
        "cpyFromManualInput.title.window": "Copy From Manual Input - SMART-Predict",
        "sweep.crAccount": "CR Account",
        "accountmonitorNew.Unsettled": "Unsettled",
        "maintEvent.tooltip.requestDate": "Request Date",
        "tooltip.changeSecondaryExternal": "Change Secondary External",
        "inputexceptions.messages.header.messageid": "Message ID",
        "userLog.tooltip.action": "Action",
        "ilmtransSetAdd.title.window.addScreen": "Add ILM Transaction Set Display - SMART-Predict",
        "alertInstance.sign": "SIGN",
        "label.accountSpecificSweepFormat.title.window": "Account-Specific Sweep Format Display",
        "acctmain.ilmParameters": "ILM Parameters",
        "tooltip.entityMonitorOptions.rate": "Enter refresh rate",
        "tooltip.Status": "Status",
        "instancemessage.updateUser_header": "Update User",
        "screenProfile.title.window": "Screen Profile - SMART-Predict",
        "tooltip.displayList": "Display List",
        "scenario.mvtLbl": "MOVEMENT_ID",
        "label.preadvice.movement": "Movement ID",
        "tooltip.timeframe": "Timeframe of the currency",
        "movementsearch.amountunder": "Amount Under",
        "ilmExcelReport.dailyLiqMinimumBalance": "Minimum Balance",
        "ilm.options.ilmTab": "ILM Tab",
        "label.dataFrom": "Data from",
        "criticalMvtUpdate.category": "Category",
        "tooltip.sortPositionLevel": "Sort by position level",
        "ilmExcelReport.summaryBalance": "Balance",
        "scenario.createInst": "Create Instances using API",
        "scenario.hostLbl": "HOST_ID",
        "tooltip.SaveNotesandExit": "Save notes and exit",
        "account.accclass": "Account Class",
        "ilmExcelReport.otherCriticalPayments": "Other critical Payments",
        "tooltip.countryName": "Country Name",
        "button.remove": "Remove",
        "manualSweeping.warning.messageForNonWorkingDays": "WARNING - Sweeping may not be achievable for value-date for at least one of the selected accounts due to cut-off / non-working days. Do you wish to continue?",
        "ilmanalysismonitor.errorAllOptionNotAvailable": "There are no appropriate currencies available. Currencies must share the same ccy-GMT offset in each entity to enable aggregation over all entities",
        "tooltip.positionlevel": "Sort by position level",
        "tooltip.transactionSetId": "Enter Transaction Set ID",
        "errors.password.passwordRules": "Password rules not satisfied",
        "tooltip.sortByDateandTime": "Sort by Date/Time",
        "errors.footer": "",
        "cashRsvrBal.rowSweepToToTarget": "Sweep To Target",
        "multipleMvtActions.internalSttlmFieldSet": "Internal Settlement",
        "currencyFunding.account": "Account",
        "role.menuaccess.menuItemDesc": "Menu Item Description",
        "label.unsettled": "Unsettled",
        "label.accountattributedefinition.column.updatedTime": "Updated Date/Time",
        "tooltip.sortBookLocation": "Sort by location",
        "account.schedSweep.tooltip.targetBalance": "Target Balance",
        "scenario.events.rolesLbl": "Roles",
        "entityprocess.title.window": "Entity Process Information",
        "errors.logon.verify.exception": "An error occurred on server side, please see logs, or try to login again",
        "ilmanalysismonitor.grid.exteod.tooltip": "External End Of Day",
        "movementsearch.criteria.title": "Additional Search Criteria  - SMART-Predict",
        "ilaapccyparams.entity": "Entity",
        "ilmExcelReport.tabNameThroughputIN": "Throughput IN",
        "partymaintenance.addAliasScreen": "Add Party Alias - SMART-Predict",
        "preAdviceInput.column.preference": "Reference;",
        "ilmAccountGroupDetails.filterCondition": "Filter Condition:",
        "entity.PosLvl": "Pos Lvl",
        "metaGroup.trade": "Trade",
        "title.ChangeaccContacts": "Change Contact Details - SMART-Predict",
        "tooltip.cancdext": "Enter cancel credit external",
        "tooltip.selectFixed": "Select fixed",
        "ccyAccMaintPeriod.tooltip.targetAvgBalance": "Required average balance over the maintenance period",
        "ilmSummary.sum": "Sum",
        "scenario.scheduled": "Scheduled",
        "tooltip.sortCurrencyGroup": "Sort by currency group",
        "tooltip.AddNewPersonalCurrency": "Add new personal currency",
        "movementsearch.counterparty": "Counterparty",
        "label.selected": "Selected",
        "systemLog.userId": "User",
        "sweep.authTime": "Authorised Time",
        "exchange.change": "Change selected exchange rate",
        "ilmanalysismonitor.errorDataNotUpToDate": "Existing ILM data is not entirely up-to-date. Current data will be shown.<br> Hover over the red/orange icon for more information.",
        "genericDisplayMonitor.connectionError": "CONNECTION ERROR",
        "tooltip.location": "Location ID",
        "tooltip.sortSection": "Sort by section",
        "user": "Updated By",
        "tooltip.movement.entityId": "Entity ID",
        "button.update": "Update",
        "tooltip.movExternalBalanceStatus": "Select External Balance status",
        "label.schedreporthist.column.runDate": "Run Date/Time",
        "maintEvent.tooltip.nextId": "Next Id",
        "ilmScenario.title.window": "ILM Scenario Maintenance - SMART-Predict",
        "messageFormats.authorizeFlag": "Authorise",
        "messagefieldadd.alert.StartPosition": "Start Position cannot be equal to 0",
        "errors.authorization.attack.session": "As a security measure, your request is blocked, an session hijacking was detected!",
        "multipleMvtActions.label.status": "Status",
        "tooltip.addnewparty": "Add new party",
        "balmaintenance.forecastSODType": "Forecast SOD Type",
        "error.unexpectedError.duringauthentification": "An unexpected error occurred during authentication.",
        "ilmReport.totalCreditLinesfromCorr": "Total credit lines available from correspondent",
        "movement.Other": "Other",
        "messageFormats.msgStructure": "Message Structure",
        "scenario.scheduledDesc": "Scheduled parameter values will be used at run-time in the scenario base query",
        "maintenanceevent.summary.dispalybutton.tooltip": "Display Details of the selected Maintenenance Event",
        "movement.cutOff": "Cut Off",
        "metagroupmaintenance.alert.validString": "Please enter a valid string",
        "tooltip.generateReportforAllRoles": "Generate report for all roles",
        "systemLoggingLevel": "System Logging",
        "ilmoption.title.window": "ILM Options",
        "tooltip.changeSelectedUser": "Change selected user",
        "tooltip.filterCondition": "Define an SQL &#34;where clause&#34; condition to specify which movements would be affected by the scenario.\\nColumns may be referenced from tables P_MOVEMENT, P_MOVEMENT_EXT, S_ENTITY and P_ACCOUNT.",
        "acctmain.usenearestsod": "Use Nearest SOD",
        "errors.javaRuntimeException": "RuntimeException",
        "acctSweepBalGrp.alert.deleteAcctSweep": "Existing account sweep balance will be removed.    Do you want to continue?",
        "button.sweep": "Sweep",
        "maintEvent.tooltip.authDate": "Auth Date",
        "addjob.LastDate": "Last Day",
        "ccyMonitorOptions.refreshRate": "Auto-refresh rate",
        "Summary.title.xml": "Screen Details XML",
        "tooltip.sweepCheck": "Change align to target",
        "tooltip.sortArchiveName": "Sort by Archive Name",
        "tooltip.addCategory": "Add Scenario Category",
        "account.schedSweep.heading.targetBalance": "Target<br>&nbsp;&nbsp;&nbsp;&nbsp;Balance",
        "alert.emptyMovementIdLocation": "Please specify the movement ID location in your file",
        "tooltip.newcdint": "Enter new credit internal",
        "ilmAccountGroupDetails.privPub": "Public/Private",
        "positionlevel.final": "Final",
        "menulabel.itemid.2": "Work",
        "acctCcyPeriodMaint.changeScreen": "Account Currency Maintenance Period CHANGE - SMART-Predict",
        "label.to": "To",
        "sweep.authorizePanel": "Authorise Panel",
        "menulabel.itemid.1": "Monitors",
        "menulabel.itemid.4": "Match",
        "menulabel.itemid.3": "Input",
        "menulabel.itemid.6": "Account Monitor",
        "menulabel.itemid.5": "Search",
        "party.header": "Type",
        "confi.id": "ID",
        "sweep.accountLimitBreached": "Sweep amount(s) outside account limits for one or more selected sweeps <b>{0}</b>.<br>Do you want to go back and review these sweeps?",
        "errors.creditcard": "{0} is an invalid credit card number",
        "movementAdd.title.Window": "Add Movement - SMART-Predict",
        "menulabel.itemid.8": "Excluded Outstandings",
        "account.tooltip.currencyCode": "Sort by Currency Code",
        "menulabel.itemid.7": "Currency Monitor",
        "menulabel.itemid.9": "Archive",
        "ilmExcelReport.intradayThroughput": "Intraday Throughput",
        "multipleMvtActions.noteFieldSet": "Notes",
        "account.linkAccountId": "Link Account ID",
        "preAdviceDisplay.title.window": "Balance Maintenance - SMART-Predict",
        "accountMonitorNew.currencyTextToday": "Today",
        "systemLog.id.logDate": "Date",
        "tooltip.enterAcName": "Enter account name",
        "entitymaintenance.alert.entitySaved": "Entity saved:  Please note that access to this new entity must now be granted to roles",
        "tooltip.amountOver": "Enter amount over",
        "tooltip.enterGroupName": "Enter group name",
        "scenario.testQuery.error": "The scenario query failed testing. See error details below \\n",
        "alertInstance.tab.json": "Json",
        "ilmanalysismonitor.alertDateOutsideRange": "The selected date is outside the range, please chose another date",
        "alert.atleastAnyOneLevelBreakdown": "Please select at least one Level Breakdown",
        "screen.buildInProgress": "DATA BUILD IN PROGRESS",
        "tooltip.overdraft": "Sort by overdraft",
        "label.accountattributehdr.maxlength": "Maximum Length",
        "ccyAccMaintPeriod.tooltip.cancel": "Cancel",
        "group.addScreen": "Add Group - SMART-Predict",
        "scenario.unresolvedInstancesAlert": "WARNING: Unresolved alert instances exist for this scenario. Some fields will be locked to help avoid introducing incompatibilities. <BR> You may override the locked fields, but are advised to liaise with your dev team to analyse the impact of any change on existing instances.",
        "sweep.user": "User",
        "instancerecord.resolvedDatetime_header": "Resolved Date",
        "tooltip.maintainAnyPCFeature": "Allow user to change any Payment Control Action that required 4 Eyes",
        "accountMonitor.label.currencyThresold": "Currency Threshold",
        "alert.interestCharges.fromDate": "From Date should be less than or equal to To Date",
        "interestCharges.mainScreen": "Interest Charge per Account Report - SMART-Predict",
        "tooltip.enterTomeFrom": "Enter time from",
        "preAdviceInput.headerType": "Mandatory / Optional:",
        "tooltip.currencyname": "Currency Name",
        "interfacemonitor.details.text.interfacedetails": "Interface Details",
        "tooltip.entity.balance": "Account balance retention period",
        "tooltip.forecastMonitor.option": "Click to open Options Window",
        "movementsearch.ref2": "Ref2",
        "sweep.status": "Status",
        "movementsearch.ref3": "Ref3",
        "movementsearch.ref1": "Ref1",
        "errors.valuenotmatchpatteren": "The given value does not match against attribute pattern.",
        "msd.tooltip.addColumns.table": "Table",
        "ilmanalysismonitor.recalculationWindowTitle": "ILM data Recalculation",
        "sweep.invalidDirectoryPath": "Invalid directory path defined.<br>Refer to the error log for details!",
        "tooltip.interfaceMonitor.active": "Enabled",
        "alertInstance.alertInstanceFieldSetLog": "Log",
        "ilmExcelReport.dailyLiqMaximumbalance": "Maximum Balance",
        "alert.mvmQSelectionStMatchIdChange": "The status of the match has changed, the screen will be refreshed",
        "tooltip.AliasIdentifier": "Select currency code",
        "tooltip.calendarreportdate": "Select Report date",
        "acctMaintenance.tooltip.servicingEntityId": "Specify the entity in which this account would appear as a Loro account",
        "corporateAccount.label.close": "Close",
        "account.openunexpected": "Open Unexpected Adjustment",
        "tooltip.movement.uetr": "UETR",
        "tooltip.matchNotes": "Match notes",
        "scenario.events.tooltip.ignore": "Ignore",
        "copyFromRole.alert.oneEntity": "At least one entity is required for full or view only access",
        "otherEmail.emailAddress.tooltip": "Sort by email address",
        "tooltip.enterposLevel": "Enter a position level",
        "userOptions.Label.disabled": "Disabled",
        "criticalMvtUpdate.sumToTotal": "Sum to Total",
        "account.settleMethodSweep": "Settlement Method",
        "password.confirmPassword": "Confirm password",
        "tooltip.movement.position": "Position Level",
        "label.personalCurrencyList.button.cancel": "Cancel",
        "button.schedreporthist.resendMail": "Resend Mail",
        "connectionPool.stackTrace": "StackTrace",
        "tooltip.sortMvmId": "Sort by movement ID",
        "holiday.entityId": "Entity ID",
        "messageFormats.changeScreen": "Change Sweep Message Format - SMART-Predict",
        "connection.failed": "Connection failed! Please contact the DBA to check the database link settings",
        "label.schedreporthist.column.fileId": "File Id",
        "centralBankMonitor.ToOutsideRange": "To date is outside the defined range",
        "menulabel.itemid.1002": "Scenario Message Format",
        "movement.sign": "Sign",
        "correspondentaccountmaintenance.alert.accountId": "Account ID field is empty",
        "button.no": "No",
        "SweepDisplay.msg": "Msgs",
        "usermaintenance.userName": "Name",
        "tooltip.sendMail": "Sort by Email",
        "account.schedulesweep.tooltip.directionF": "Sweeping is only required when the balance is below target and consequently needs to be credited",
        "account.schedulesweep.tooltip.directionB": "Sweeping should align the account to its target balance by applying a credit or debit as necessary, subject to the usual logic relating to the minimum sweep amount",
        "account.schedulesweep.tooltip.directionD": "Sweeping is only required when the balance is above target and consequently needs to be debited",
        "scenario.runAt": "Run at",
        "correspondentaccountmaintenance.correspondentAccId": "Correspondent Acc ID",
        "interfacerulesmaintenance.add.messageType": "Message Type",
        "manualSweep.tabs.selectedDate": "Selected Date",
        "label.in": "in",
        "tooltip.enterValueDateTo": "Enter value date to",
        "ccyAccMaintPeriod.end": "End",
        "tooltip.ilmdatascreen": "Build cash flow data and ILM time series data necessary for ILM monitor screen",
        "tooltip.addNewField": "Add new message field",
        "ilmanalysismonitor.grid.type": "Type",
        "entity.currencyInterstRate": "Ccy interest rate",
        "button.tooltip.msd.deleteColumn": "Delete MSD additional column",
        "accountmaintenace.alert.selectAccount": "Please select Main Account",
        "alertInstance.amount": "AMOUNT",
        "ilmReport.totalPayments": "Total Payments",
        "group.groupNameadd": "Name",
        "button.tooltip.msd.saveAs": "Save as",
        "autologgedoff.msg.stayLoggedIn": "Click OK to stay logged in.",
        "label.forecastMonitor.movementId": "Movement",
        "button.go": "Go",
        "addjob.EndTime": "End Time",
        "tooltip.selectPopUp": "Notify by Pop-Up",
        "accountmaintenance.title.selectAnAccount": "Select an Account - SMART-Predict",
        "messagefieldformat.addScreen": "Add Message Field - SMART-Predict",
        "label.forecasttemplate.column.type": "Type",
        "preAdviceInput.dataSource": "Data source",
        "tooltip.sortbyParentParty": "Sort by parent ID",
        "correspondentaccountmaintenance.title.addWindow": "Add Correspondent Account Alias - SMART-Predict",
        "button.entityMonitor.refresh": "Refresh",
        "updatedBy": "Updated By",
        "acctSweepBalGrp.alert.invalidComboValue": "Please select a valid sweep account",
        "tooltip.defEntity": "Select default entity ID",
        "tooltip.selectCategory": "Select Category",
        "addJob.alert.StartTimeGreater": "Start date-time should be greater than or equal to current date-time",
        "tooltip.accountattributehdr.tooltiptext": "Enter tooltip text",
        "movement.value": "Value",
        "account.accountType": "Type",
        "ilmSummary.tooltip.turnover": "Turnover",
        "scenariosummary.title.window": "Scenario Summary",
        "label.findoraddpopupsubtotal.title.window": "Find / Add pop up (Sub-Total) - SMART-Predict",
        "ccyAccMaintPeriod.tooltip.save": "Save",
        "workflowmonitor.outstandings": "Outstandings",
        "userSetup.viewScreen": "View User Details - SMART-Predict",
        "tooltip.custId": "Enter Custodian ID",
        "sweep.crdExtMsg": "CR EXT MSG",
        "ccurrency.dstStartDateAsString": "Start Date",
        "tooltip.enterNewCountry": "Enter new country",
        "balMaintenance.balCurrencyCode": "Balance Currency Code",
        "confirm.openGeneric": "Do you want display data for the given base query?",
        "alert.scenarioQueryTested2": "\\n Do you want to display data for the given base query?",
        "tooltip.sortbycustodianid": "Sort by custodian ID",
        "ilmExcelReport.criticalPaymentsOutflows": "Critical Payments - Outflows",
        "errors.logon.verify.smartRoleId": "Invalid login: No linked Role was found in the application, please contact your Administrator",
        "balMaintenance.userNotes": "User Notes",
        "workflowmonitor.title.window": "Workflow Monitor - SMART-Predict",
        "ilmanalysismonitor.setStylePopupName": "Change Styles",
        "batchScheduler.confirm.disable": "This will disable the selected process",
        "matchQuality.Int": "Int",
        "sweepdetail.accountattribute.label": "Acct Attr.",
        "tooltip.selectFormatId": "Select format ID",
        "instancemessage.messageId_tooltip": "Message ID",
        "ilmanalysismonitor.sumByCutOff": "Sum by Cut-off",
        "preAdviceInput.sourceFormat": "Source format:",
        "label.forecasttemplate.column.templatename": "Template Name",
        "label.forecastAssumptionAdd.date": "Date",
        "tooltip.sortOrder": "Sort by order",
        "password.old": "Old Password",
        "scenarioAdvanced.defaultGrouping": "Default Grouping",
        "label.amountsDiffer": "The amounts of the selected movements differ. Do you want to continue?",
        "inputexceptionsmessages.title.window": "Input Exceptions Message Details - SMART-Predict",
        "maintenanceevent.details.button.amend.label": "Amend",
        "login.notification.failedLoginAttempts": "Failed Login Attempts",
        "label.entityMonitor.rate": "Rate",
        "msd.heading.addColumns.table": "Table",
        "tooltip.minSpecialChars": "Minimum number of special characters",
        "acctMaintenance.acctClass": "Class",
        "org.apache.struts.taglib.bean.format.sql.time": "h:mm a",
        "movement.ilmFcast": "ILM FCast",
        "maxswpamnt": "Max Amount",
        "corporateAccount.amount": "Amount*",
        "addjob.disable": "Disable",
        "monitor.norecords": "No records available",
        "alert.entityMonitor.smartPredict": "SMART-Predict",
        "crmsgcancel": "Credit Msg Cancel",
        "movement.messageFormat": "Message",
        "ilmExcelReport.standardScenarioNameDescription": "Standard dataset",
        "label.accountspecificsweepformat.text.extViaIntermediary": "Ext Via Intermediary",
        "tooltip.sortFinalBalance": "Sort by final balance",
        "tooltip.scenarioTitle": "Sort by Scenario Title",
        "sweep.mainAcctNotFound": "Main account not found.<br>Refer to the error log for details!",
        "scenario.events.instanceAttribute.info": "Select Values from list of attributes below",
        "tooltip.addCols": "Configure additional columns",
        "label.hideButtonBar": "Hide Button Bar",
        "connectionPool.title.window": "Connection Pool Monitor Screen",
        "scenario.viewScreen.title.window": "View Scenario - SMART-Predict",
        "tooltip.acc.predicted": "Select Predicted",
        "sweepDetail.trbalance": "Target Balance",
        "currency.interestBasis": "Int Bas",
        "currMonitor.metagroup": "Metagroup",
        "ilmReport.TotalCreditLinesAvailable": "Total credit lines available",
        "account.schedSweep.againstAccount": "Against Account",
        "ilmExcelReport.creditLinesSecured": "Credit lines secured",
        "tooltip.clear": "Clear",
        "scenarioAdvanced.summary": "Summary",
        "scenario.reRaiseAfter": "Re-raise after expiry",
        "outstanding.tabs.todayminusone": "Today-1",
        "sweep.value": "Value",
        "maintenanceevent.details.alert.actionneedauthorisation": "This action needs second user authorisation",
        "multipleMvtActions.actionFieldSet": "Action (For all updates, matched movements will first be unmatched)",
        "ilmReport.centralBankGroup": "Central Bank Group",
        "preAdviceInput.column.preStatus": "Pred.Status;",
        "country": "Country",
        "tooltip.delete": "Delete selected position level",
        "tooltip.nonworkday.deleteButton": "Delete selected facility",
        "tooltip.enterDescription": "Enter Scenario Description",
        "ilmExcelReport.largestNegativeNetCumulativePosition": "Largest Negative Net Cumulative Position",
        "tooltip.defaultProfile": "Select to make it default profile",
        "alert_header.error": "Error",
        "label.accountattributehdr.datevalue": "Date Value",
        "userLog.viewScreen": "View User Log Details - SMART-Predict",
        "ilmSummary.tooltip.cutOff": "Cut-off                                                                                                                                     ",
        "changeBalance.acctId": "Account ID",
        "account.accountPartyId": "Party ID",
        "label.accountspecificsweepformat.text.specEntityId": "Specific Entity ID*",
        "SweepMsgDisplay.Status": "Status",
        "ilmReport.ValuePayements": "B(i) Value of payments made on behalf of  correspondent banking customers",
        "scenarioAdvanced.hostColumn": "HOST Column",
        "ilm.options.combinedChart": "Combined Chart",
        "multiMvtActions.pred": "Pred",
        "tooltip.enterUserPwd": "Enter user password",
        "tooltip.forecastAssumptionAdd.assumption": "Enter the Assumption",
        "tooltip.authorizationInput": "Require another user to authorise a manually entered movement",
        "label.schedreporthist.column.mailStatus": "Mail",
        "accountmaintenance.viewaccountdetails.addWindow": "View Account Details - SMART-Predict",
        "metaGroup.mgroupName": "Name",
        "tooltip.jobNamePrefix": "Specify the text that will precede the date and time in the name of the report file generated",
        "scenario.configLabel": "Configure",
        "tooltip.addnewpartyAlias": "Add new party alias",
        "movement.notes": "Notes",
        "role.printbutton": "Print",
        "button.ok": "OK",
        "movementDisplay.movementparam": "Main Details",
        "currencyGroup.access": "No Currency Group defined for full access",
        "ilmScenario.tooltip.collateralAvlbl": "Enter percentage of Collateral Availability (0-100%)",
        "movementsearch.bookcode": "Book",
        "tooltip.selectMetaGroupLevel": "Select metagroup level",
        "label.warningSummary": "Warning - Summary",
        "ilmanalysismonitor.legend.accActualC": "Accum. Actual Credit",
        "sweepSubmit.colValue.Auto": "Auto",
        "ilmthroughput.date": "Date",
        "sweep.cancel": "Cancel",
        "tooltip.clickSelMetaGroupId": "Click to select metagroup ID",
        "ilmanalysismonitor.legend.accActualD": "Accum. Actual Debit",
        "ilmExcelReport.entity": "Entity",
        "ilmAccountGroupDetails.allowReporting": "Allow Reporting",
        "alert.movementsummaryWhileExport": "An Error occurred while exporting, Please contact your System Administrator",
        "alert.assumptionAdd.validAmount": "Please enter a valid amount",
        "ilmScenario.tooltip.throughputMonitoring": "Throughput Monitoring",
        "addJob.alert.EndTimeLesser": "End date-time should be greater than or equal to current date-time",
        "label.limitexcess": "Limit Excess",
        "role.password.noaccess.to.facility": "WARNING: This role does not grant access to the password change facility",
        "tooltip.sortFinTrade": "Sort by Finance/Trade",
        "sweep.currencyGroup": "Currency Group",
        "tooltip.selectLocationId": "Select location ID",
        "user.accessmessage": "Unauthorised Access Prohibited",
        "movementsearch.include": "Include",
        "currMonitor.alert.fromDateValidation": "From date should be within 180 days of system date",
        "sweep.saveSuccessfully": "Sweep ID(s) <b>{0}</b> successfully {1}",
        "reports.threshold": "Threshold",
        "inputexceptions.messages.header.accountId": "Account",
        "ilmExcelReport.tabNameTotalPayments": "Payments",
        "button.linked": "Linked",
        "account.schedulesweep.fieldSet1.legendText": "Sweep Account",
        "account.schedSweep.tooltip.otherAccSweepBookcodeCr": "Book CR",
        "entity.general.BIC": "BIC",
        "title.login.successPage": "Success",
        "tooltip.enterActId": "Enter a account ID",
        "stopRuleMaintenance.title.window": "Stop Rule Maintenance",
        "tooltip.jobRetainFilesFor": "Specify how long files are to be remain on the system before being removed during archiving",
        "tooltip.referenceField2": "Sort by reference 2",
        "scenario.tab.general": "General",
        "tooltip.sortByWorkingSOD": "Sort by Working SOD",
        "tooltip.sortCurrencyCode": "Sort by currency code",
        "account.schedSweep.tooltip.otherAccSweepBookcodeDr": "Book DR",
        "currency.group": "Currency Group",
        "interfacemonitor.details.text.storedproclaststarttime": "Last Start Time",
        "ilmanalysismonitor.includeSOD": "Include SOD",
        "errorLog": "Error Log",
        "alert.templateOption.recordExist": "Record already exists",
        "accountmonitor.today2": "Today+2",
        "tooltip.currency.gmtOffset": "Define the currency's working day relative to GMT - Used in liquidity analysis functionality for determining currency timeframe. No value indicate to use the entity-local timeframe.",
        "tooltip.unlockMovement": "Unlock movement",
        "alert.interfaceSettings.number": "Please enter a number",
        "ilmanalysismonitor.legend.forebasic.Title": "FC.",
        "accountmonitor.today1": "Today+1",
        "tooltip.scenarioHostColumn": "Enter Scenario Host Column",
        "scenarioSummary.all": "All",
        "button.forecastMonitor.find": "Find",
        "entity.predict.centralBank": "Central Bank",
        "workflowmonitor.matches.title": "Matches (Total / No-Included)",
        "ilmReport.average2": "Average",
        "role.roleMatching": "Matching",
        "scenario.changeScreen.title.window": "Change Scenario - SMART-Predict",
        "ilaapccyparams.clearingStartGrid": "Start",
        "scenarioTooltipSummary.displayListButton": "Display List",
        "label.findoraddpopup.entity": "Entity",
        "ilmAccountGroupDetails.tootlipLegendText": "Define default legend text for the liquidity monitor screen",
        "ilmExcelReport.veryCriticalPayments": "Very critical Payments",
        "tooltip.accountattributehdr.maxvalue": "Enter maximum value",
        "criticalMvtUpdate.toolTip.sumToCategCheck": "Check to contribute to category total (used in ILM reporting)",
        "role.entAccessList.Access": "Access",
        "userAuditLog.title.window": "My User Audit Log - SMART-Predict",
        "sweepsearch.mvmntdetails": "Movement Status",
        "label.interfaceMonitor.header.totalBadMessages": "Bad",
        "tooltip.enterOrderCusText3": "Enter Ordering Customer text 3",
        "tooltip.enterOrderCusText4": "Enter Ordering Customer text 4",
        "tooltip.enterOrderCusText1": "Enter Ordering Customer text 1",
        "tooltip.enterOrderCusText2": "Enter Ordering Customer text 2",
        "ilmReport.globalCurrencyGroup": "Global Currency Group",
        "label.ilmccyprocess.calculationalreadyinprogress": "Manual calculation could not be launched for the current <br>selection. Cause: A current calculation for {0} for value date of {1} is actually in progress",
        "button.yes": "Yes",
        "ilmSummary.tooltip.inc": "Confirmed Credits as a percentage.",
        "toolTip.scenarioDefParams": "Define Parameters",
        "maintEvent.tooltip.action": "Action",
        "tooltip.enterOrderCusText5": "Enter Ordering Customer text 5",
        "tooltip.enterBIC": "Enter the BIC/Network",
        "label.country.defaultWeekends2": "Default Weekend 2",
        "label.country.defaultWeekends1": "Default Weekend 1",
        "label.accountspecificsweepformat.column.entityId": "Entity Id",
        "tooltip.movSearchRolled": "Rolled",
        "tooltip.subAc": "Sub-account",
        "movement.extraText1": "Extra Text 1",
        "tooltip.deleteSelAc": "Delete selected account",
        "date": "Date",
        "tooltip.recoverProcess": "Recover",
        "ilmReport.titleBaselCReport": "BCBS 248 Report C: Banks that provide correspondent banking services",
        "label.accountattributedefinition.column.user": "User",
        "tooltip.sortFileName": "Sort by file name",
        "tooltip.accountattributehdr.minlength": "Enter minimum length",
        "sweep.status1": "Status",
        "currencyFunding.currency": "Currency",
        "screen.alert.error": "Error",
        "userLog.action": "Action",
        "scenario.guiHighlight.mapFrom": "Map From",
        "button.view": "View",
        "batchScheduler.confirm.execute": "Are you sure you want to execute?",
        "tooltip.sortItemId": "Sort by item ID",
        "label.entityMonitor.title.window": "Entity Monitor - SMART-Predict",
        "tooltip.accountGroup.global": "Sort by Global",
        "maintEvent.tooltip.maintEventId": "Maint Event Id",
        "tooltip.enterCurrency": "Enter a currency ID",
        "buttton.entity.change": "Change selected position level",
        "multipleMvtActions.label.dataSource": "Data Source",
        "tooltip.AddNewCurrencyAlias": "Add new currency alias",
        "tooltip.reActivateButton": "Re-activate",
        "balanceSourceId": "Source ID",
        "userLog.time": "Time",
        "main.alert.changeProfile": "Are you sure you want to change the screen profile?",
        "label.personalEntityList.button.cancel": "Cancel",
        "sweepDetails.alert.sweeperror": ".<BR> Do you want to continue?",
        "tooltip.custodain": "Custodian",
        "movement.receiverCorrespondent": "Receiver's Correspondent",
        "tooltip.entity.general.reportingCurrency": "Select Reporting Currency",
        "ccyAccMaintPeriod.targetAvgBalance": "Target Avg Balance",
        "tooltip.sortByName": "Sort by Name",
        "label.report.labelTotalOverPages": "Grand total from Movement Summary Display",
        "tooltip.enterRef": "Enter a reference",
        "tooltip.accountattributehdr.attributename": "Enter attribute name",
        "tooltip.addNewJob": "Add new job",
        "tooltip.newValue": "Sort by new value",
        "tooltip.forecastMonitorTemplateAdd.moveUp": "To move up the selected record",
        "scenario.missingRunAtValue": "Please fill run at field",
        "tooltip.user.lastlogout": "Last Logout",
        "aliasTooltip.sortname": "Sort by currency name",
        "tooltip.link.linkAcctId": "Click here to select Link Account ID",
        "tooltip.group": "Enter currency group ID",
        "tooltip.cyclic": "Cyclic",
        "tooltip.changeCrInt": "Change credit interest rates",
        "instancerecord.accountId_tooltip": "Account ID",
        "ilmAccountGroupDetails.firstMin": "First Minimum",
        "turnoverReport.credit": "Credit",
        "ilmanalysismonitor.groupanalysis.title": "Group Analysis",
        "errors.InvalidDataAccessApiUsageException": "InvalidDataAccessApiUsageException",
        "ilmthroughput.report.sheet1.title": "ThroughPut Report",
        "ilmreport.keyword.label.endOfCurrentQuarter": "End of the current quarter",
        "errors.entity.entityIdSelect.required": "Please select a entity.<BR>",
        "sweep.crdIntMsg": "CR INT MSG",
        "movementDisplay.initPredStatus": "Init Pred Status",
        "systemLog.process": "Process",
        "testDateAsString": "System Test Date",
        "tooltip.selectAll": "Select all",
        "tooltip.sortByIdentifier": "Sort by identifier",
        "button.search.criteria": "Criteria",
        "tooltip.sortStartingBalance": "Sort by Starting Balance",
        "metagroup.groupNameTotal": "# Books",
        "tooltip.clickSelAcId": "Click to select account ID",
        "sweep.acctLevelNotFound": "Account level not found.<br>Refer to the error log for details!",
        "ilmReport.min": "Min",
        "tooltip.sortMGId": "Sort by metagroup ID",
        "workflowmonitor.included": "Included",
        "descriptionTooltip.sortdescription": "Sort by description",
        "tooltip.changeSelParty": "Change selected party",
        "alert.forecastMonitor.enterNumber": "Please enter a number",
        "label.showButtonBar": "Show Button Bar",
        "multipleMvtActions.label.processButton": "Process",
        "alert.interfaceSettings.requiresPortNumber": "Please provide MQ Port Number",
        "ilaapgeneral.entity": "Entity",
        "entity.MetagroupLevel2Name": "Metagroup level 2 name",
        "tooltip.displayMatch": "Display Match",
        "tooltip.RateButton": "Rate Window",
        "scenarioNotification.popup": "Pop-up",
        "scenarioAdvanced.entityColumn": "ENTITY Column",
        "tooltip.movement.counterPartyId": "Counterparty ID",
        "userStatus.header.user": "User",
        "alert.forecasttemplate.savetemplate": "Do you want to save the changes for template?",
        "ilmAccountGroupDetails.tootlip.mainAgentText": "Enter Main Agent",
        "tooltip.interestBasis": "Interest basis",
        "movement.entity.id": "Entity ID*",
        "acctMaintenance.servicingEntityId": "Servicing Entity",
        "label.ilmccyprocess.calculationcancelled": "Calculation cancelled",
        "currencyGroupMaintenance.title.mainWindow": "Currency Group Maintenance - SMART-Predict",
        "label.roleBasedControl.ReqAuth.tooltip": "Changes require authorisation by another user?",
        "movNote.title.window": "Movement Notes - SMART-Predict",
        "alert.enterValideReasonDescription": "Please enter Valid Reason Description",
        "movement.beneficiaryCustomer": "Beneficiary Customer",
        "pwd.chars": "chars",
        "maintenanceLog.oldValue": "Changed From",
        "tooltip.forecastMonitor.cancel": "Cancel changes and exit",
        "ilmAccountGroupDetails.syntaxAlert": "Query syntax is incorrect, please verify your query:",
        "tooltip.messageType": "Message Type",
        "tooltip.selectSubmit": "Select submit",
        "ilmExcelReport.monitoredTypes": "Monitored types",
        "user.label.login": "Login",
        "role.workQueueAccess.entity1": "Entity",
        "instancerecord.count_tooltip": "Count",
        "ilmReport.largestNegativePosition": "Largest negative net cumulative position",
        "accountaccess.closeConfirm": "Changes have been made. Are you sure you want to close without saving?",
        "tooltip.movement.matchingParty": "Matching party",
        "tooltip.addNewMetaGroup": "Add new metagroup",
        "tooltip.selectHexa": "Select hexadecimal",
        "scenario.events.resolved": "Set Instance as Resolved",
        "metaGroup.finance": "Finance",
        "tooltip.ClickSelectGroupIDLvl3": "Click to select group ID level 3",
        "ccyAccMaintPeriod.entityId": "Entity",
        "maintenanceevent.details.button.accept.label": "Accept",
        "tooltip.ClickSelectGroupIDLvl2": "Click to select group ID level 2",
        "tooltip.ClickSelectGroupIDLvl1": "Click to select group ID level 1",
        "throuputmonitor.tooltip.forecasted": "",
        "ilmanalysismonitor.recalculationInProgress": "Calculation in progress",
        "msdAdditionalColumns.generalProfileCheckTooltip": "Use general profile",
        "crossReference.sourceOfReference": "Source Of Ref",
        "days": "days",
        "defaultaccountmaintenance.accountName": "Name",
        "instancerecord.textLog_tooltip": "Text",
        "acctSweepBalGrp.alert.chooseSweep": "Please choose the previous Sweep Account ID before adding new record",
        "logBalance.forecast": "Forecast",
        "ilmAccountGroupDetails.invalidAmountAlert": "Please enter a valid amount",
        "login.notification.lastSuccessfulLoginTime": "Last Successful Login",
        "maintEvent.tooltip.authUser": "Auth User",
        "balMaintenance.externalSODAsString": "Effective External SOD",
        "tooltip.viesselectedmsgformat": "View selected message format",
        "sweepsearch.sweepamt": "Amount",
        "tooltip.movSign": "Select sign",
        "button.books": "Books",
        "currencyAliasMaintenance.title.mainWindow": "Currency Alias Maintenance - SMART-Predict",
        "label.entityMonitorOptions.fontsmall": "Small",
        "tooltip.sortOutstanding": "Sort by outstanding",
        "batchScheduler.header.nextExeTime": "Next Executed",
        "tooltip.movCustodian": "Click to select custodian ID",
        "ilmExcelReport.outgoingPaymentsComparedToAvailableIntradayLiquidity": "Outgoing payments compared to available intraday liquidity",
        "tooltip.saveEntityId": "Save entity ID",
        "centralBankMonitor.fromDate": "From Date",
        "sweepId.doesnotmatch": "Please enter a valid sweep ID",
        "acctMaintenance.tooltip.fmi": "Indicate Financial Market Infrastructure relevant to this account",
        "currencyInterest.toDate": "To",
        "button.sum": "Sum",
        "maintenanceevent.summary.seearchbutton.tooltip": "Search the list of event based on the specified criteria",
        "shortcuts.shortcutName": "Name",
        "errors.double": "{0} must be a double",
        "accountmaintenanceadd.defaultSettleMethodTooltip": "Select the default Settlement Method",
        "button.sub": "Sub A/C",
        "ilmScenario.currencyCode": "Ccy",
        "scenarioNotification.fullInstanceAccess": "Full Instance Access",
        "movement.ccy": "Ccy",
        "currencyInterest.interestRate.label": "Rate",
        "workflowmonitor.movMent.title": "Movements",
        "label.currentPage": "Current Page",
        "instancerecord.textLog_header": "Text",
        "tooltip.movement.msgformat": "Message Format",
        "errors.match.unmatched": "Match has been unmatched by other user",
        "inputexceptions.header.received": "Received",
        "alertmsg": "Alert Message",
        "label.forecastMonitorTemplateAddDetail.columnId": "Column No *",
        "batchScheduler.header.LastExeStatus": "Last Status",
        "tooltip.clickSelCounterId": "Click to select counter ID",
        "ilmExcelReport.intradayThroughputInflowsCcyTimeframe": "Intraday throughput - Inflows (CCY timeframe)",
        "linked.tooltip.level": "Level",
        "tooltip.sortTolerance": "Sort by tolerance",
        "ilmExcelReport.totMonitoredPayTypesInf": "Total Monitored Critical Payment Types - Inflows",
        "generalparameters": "General Parameters",
        "movementsearch.beneficiary": "Beneficiary",
        "inputconfig.title.window": "Interface Setting - SMART-Predict",
        "inputException.reprocess": "Reprocess",
        "tooltip.forecastAssumptionAdd.amount": "Enter the Amount",
        "currencymaitenance.addScreen": "Add Currency - SMART-Predict",
        "sweepDetail.sweepSetting.useSchedule.tooltip": "Choose sweep settings from relative sweep schedule (if found) or use account defaults",
        "tooltip.monthly": "Monthly",
        "multipleMvtActions.label.xButton": "X",
        "ilmReport.max": "Max",
        "tooltip.sortEntityId": "Sort by entity ID",
        "currencyGroupAccess.viewScreen": "View Currency Group Access - SMART-Predict",
        "tooltip.interBalEodDate": "Select Internal Balance EOD Date ",
        "ccyAccMaintPeriod.tooltip.minimumReserve": "The minimum reserve requirement for this maintenance period",
        "alert.interfaceSettings.requiresHostName": "Please provide MQ Host Name",
        "instancerecord.dateTimeLog_header": "Date Time",
        "tooltip.includeMvmInDataExternal": "Include movement in data external",
        "label.entity.internalIndicator": "Internal",
        "tooltip.enterEndDate": "Enter end date",
        "ilmReport.dailyTotalInflow": "Daily total inflow",
        "tooltip.add.TransactionSet": "Add New Transaction",
        "acctMaintenance.tab.balances": "Balances",
        "tooltip.movement.ilmFcast": "ILM forecast status",
        "msd.profileId.label": "Profile ID",
        "ilmanalysismonitor.tree.AllGlobalGroups": "All Global Groups",
        "alert.showValue": "Value must be between 2 and 14.",
        "alert.accessToFacility": "You don't have access to this facility",
        "entity.id.entityId": "Entity",
        "ilmSummary.tooltip.confD": "Confirmed Debits as a percentage.",
        "userSetup.Label.Enabled": "Enabled",
        "tooltip.totalStartingBalance": "Total Starting Balance",
        "inputexceptions.header.rejected": "Rejected",
        "label.acctBreakdown.currency": "Currency",
        "tip.schedReportHist.fileId": "Selected File Id",
        "cutofftime": "Cut off Time",
        "tooltip.mvmntnotes": "Movement notes",
        "connectionPool.tooltip.sqlStatement": "Last SQL statement",
        "instancerecord.overThreshold_tooltip": ">Threshold",
        "message.alert.emptySuppliedInterBalance": "A supplied internal must be provided before entering a value for this field",
        "multiMvtActions.ordInst": "Ord Inst",
        "button.previous": "Previous",
        "inputexceptions.tooltip.button_supp": "Move messages to suppressed queue",
        "alert.MovementLocked": "Some of the movements in a match (of the selected movements) are locked by another user.",
        "recovery.confrim.continue": "Do you want to continue?",
        "button.forecastmonitor.suggest": "Suggest",
        "tooltip.durationSecs": "Enter duration (Sec)",
        "label.accountattribute.effectivedateTime": "Effective Date/Time",
        "tooltip.makeOfferedMatch": "Make Offered Match",
        "main.refreshDesktop": "Refresh desktop",
        "addJob.title.reportName": "Report Name",
        "balMaintenance.inputTimeAsString": "Input Time as String",
        "CurrencyInterest.addScreen": "Add Currency Interest - SMART-Predict",
        "movementsearch.matchingParty": "Matching Party",
        "ccyAccMaintPeriod.tooltip.minTargetBalanceTooltip": "Prevent the sweeping process from causing this account's balance to fall below the specified value.",
        "tooltip.preadvice.securities": "Select securities movement type",
        "tooltip.exchangerate": "FX Rate",
        "alert.mail.oneOrManyMailsNotSent": "One, or many mails has not been sent. Please check logs for more details or contact your administrator",
        "movement.criticalPaymentType": "Critical Payment Type",
        "ilmanalysismonitor.grid.fcastsod": "Fcast.SOD",
        "scenario.runEvery": "Run Every",
        "maintenanceLog.columnName": "Field",
        "sweep.cutoffExceed": "The cut off time has passed for this sweep",
        "tooltip.BIC": "Sort by BIC/Network",
        "sweepInter.currency": "Currency",
        "criticalMvtUpdate.toolTip.radioSystem": "Evaluate scheduling in system timeframe",
        "scenarioAdvanced.amountColumn": "AMOUNT Column",
        "opportunityCostReport.currencyLabel": "Currency",
        "changeJob.alert": "Job is running, cannot change job specifications",
        "addjob.DayMonth": "Day of Month",
        "interfaceExceptionsMonitor.title.window": "Interface Exceptions Monitor - SMART-Predict",
        "correspondentaccountmaintenance.alert.messageType": "Message Type is empty",
        "ilmAccountGroupDetails.quickSearch": "Quick Search",
        "scenario.testQueryEmpty.error": "Please fill Base Query field before clicking on Test button",
        "ilmExcelReport.fromDate": "From Date",
        "internalmessage.send": "Send message to",
        "movementsearch.status.reconcilled": "Reconciled",
        "maintEvent.tooltip.recordId": "Record Id",
        "emailTemplateMaintenance.tooltip.bodyContent": "Body Content",
        "scenario.events.executeWhen": "Execute when",
        "multipleMvtActions.dataDefFieldSet": "Data Definition",
        "label.accountattributehdr.defaulttime": "Default Time to 00:00",
        "cashRsvrBal.tooltip.targtAvgBal": "Target AVG Balance",
        "multipleMvtActions.label.critPayTypeLbl": "Critical payment Type",
        "centralMonitor.closingBalances": "Closing Balances",
        "ilmScenarioAdd.title.window.changeScreen": "Change ILM Scenario Detail - SMART-Predict",
        "ilmExcelReport.asOfDailyTotalOutflowTitle": "as % of Daily Total Outflow",
        "pwdchange.tooltip.userId": "User ID",
        "instancerecord.resolvedDatetime_tooltip": "Resolved Date",
        "acctmaintenance.title.cpyfrom": "Copy From Account Maintenance - SMART-Predict",
        "tooltip.enterTime": "Enter time",
        "attributeusagesummaryadd.grandTotal.substract": "Subtract",
        "label.accountspecificsweepformat.tooltip.accountID": "Select Account ID",
        "label.accountattribute.accountId": "Account",
        "tooltip.groupLevel3": "Group level 3 name",
        "movement.reference2": "Ref 2",
        "tooltip.groupLevel2": "Group level 2 name",
        "movement.reference3": "Ref 3",
        "tooltip.groupLevel1": "Group level 1 name",
        "currency.EnableCurrencyForPredict": "Enable this currency for Predict",
        "movement.reference1": "Ref 1",
        "systemLog.title.window": "System Log - SMART-Predict",
        "maintenanceLog.newValue": "Changed To",
        "messageFormats.fieldDelimeter": "Field Del",
        "additionalColumns.label.colLabel": "Label",
        "scenarioNotification.flash": "Flash",
        "inputconfig.header.beginTime": "Alerts Start",
        "entity.SecondWeekendDay": "Second weekend day",
        "msd.heading.addColumns.label": "Label",
        "tooltip.enterValueDateFrom": "Enter value date from",
        "tooltip.scenarioCcy": "Sort by Ccy",
        "msg.title.notesAvailable": "Notes available",
        "ilaapccyparams.clearingEndGrid": "End",
        "sweeppriorcutoff.leadTime": "Cut-Off Lead Time (mins)",
        "tooltip.sortApplyEntityCountry": "Sort by apply entity country",
        "acctMaintenance.acctlevel": "Level",
        "account.schedulesweep.tooltip.sumAccountsN": "Indicate whether to sum associated accounts when calculating the sweep balance",
        "inputexceptions.header.awaiting": "Awaiting",
        "sweep.search": "Search",
        "account.schedulesweep.tooltip.sumAccountsY": "Indicate whether to sum associated accounts when calculating the sweep balance",
        "label.forecastMonitorOptions.hidezerovalue": "Hide Zero Value Columns",
        "interfacemonitor.header.commits": "Commits/hr",
        "message.alert.acctAccessInput": "Access controls prevent you from inputting movements on this account",
        "tooltip.accountattributehdr.maxlength": "Enter maximum length",
        "title.accountformat": "Sweep Messages Format - SMART-Predict",
        "label.country.defaultWeekend1": "Default <br> Weekend 1",
        "additionalColumns.alert.changeValues": "Record with same Label is already exist",
        "label.country.defaultWeekend2": "Default <br> Weekend 2",
        "scenario.schedule.runAt": "The scheduled scenario will Run At (hh:mm)",
        "tooltip.selectDateFormatDMY": "Select date format (DD/MM/YYYY)",
        "tooltip.toDate": "Enter to date",
        "user.askForUserID": "Please enter your User ID",
        "tooltip.changeNewMatchQuality": "Change selected match quality",
        "ilmReport.titleBaselBReportDaily": "Daily Liquidity Management Report - Basel B: Correspondent",
        "ilmReport.largestPositiveNetCumulativePosition": "Largest positive net cumulative position",
        "tooltip.friday": "Friday",
        "preAdvice.alert.noAccess": "Invalid: your role does not provide access to this pre-advice",
        "ilmanalysismonitor.saveAsComboprofilePopupTooltip": "Save As",
        "ilmanalysismonitor.recalculateConfirmAlertTitle": "Recalculate data",
        "genericDisplayMonitor.lastRefresh": "Last Refresh:",
        "ilmTransactionSet.time": "Time",
        "party.title.mainWindow": "Party Maintenance - SMART-Predict",
        "tooltip.sortCurrencyName": "Sort by currency name",
        "menuaccessoptions.alert.menuOption": "At least one menu option must be selected",
        "passwordRules.legend.MinNum": "Minimum number of",
        "scenario.events.usersLbl": "Users",
        "movement.alerting": "alert",
        "tooltip.currentCcyGrpId": "Select the default currency group",
        "recovery.lastRun": "Last Run",
        "retention": "Retention",
        "label.adjexternalbalance": "Adj. External Balance",
        "accountmonitor.startbal": "Starting Bal",
        "scenario.tooltip.acctIdCombo": "Enter Scenario Account ID column",
        "errors.prefix": "",
        "tooltip.sort.credits": "Sort by Credits",
        "tooltip.main": "Main",
        "errors.suffix": "",
        "currencyGroup.addScreen": "Add Currency Group - SMART-Predict",
        "label.entityMonitorOptions.cancel": "Cancel",
        "tooltip.enterCutOffTime": "Enter cut off time (hh:mm)",
        "tooltip.tarBalAmount": "Target Balance Amount",
        "ilmanalysismonitor.legend.accActualD.Title": "ActOut.",
        "movement.sweepAmount": "Sweep Amount",
        "currency.exchangeRate2": "Exch Rate",
        "msd.alertFilterDeleted": "The filter was successfully deleted",
        "ilmReport.titleBaselAReportDateRange": "Date Range Liquidity Management Report - Basel A: Direct Participant",
        "currency.exchangeRate1": "Exchange Rate",
        "maintenanceevent.summary.dateselection.all": "Show All maintenance events without date restriction",
        "messageScenarioFormats.addScreen": "Add Scenario Message Format - SMART-Predict",
        "label.cumulativeext": "Cumulative",
        "tooltip.successRateCreditPct": "Enter percentage of credits success rate (0-999%)",
        "scenario.tooltip.valueDateCombo": "Enter Scenario Value Date column",
        "sweepIntermediaries.currencyName": "Currency Name",
        "label.acctBreakdown.openUnexpected": "Open Unexpected",
        "tooltip.6positionLevel": "Sixth position level",
        "ilmReport.titleIntraDayRisk": "Intra-Day Risk: Net Cumulative Positions",
        "alertInstance.paymentId": "PAYMENT_ID",
        "matchQuality.currencyCode": "Currency",
        "entity.EntityOffsetTimetoCentralServer": "Entity offset time to central server",
        "movementsearch.custodian": "Custodian",
        "addjob.FirstDate": "First Day",
        "label.resourceBusy": "Resource is busy, please wait for current request to complete",
        "tooltip.cpyMatchQuality": "Copy match quality",
        "msd.dateradio.option1.tooltip": "Date parameters will be saved as absolute values",
        "tooltip.enterNewCorrCode": "Enter new correspondent code",
        "tooltip.criticalPaymentType": "Enter Critical Payment Type",
        "tooltip.sortShortCutId": "Sort by shortcut ID",
        "tooltip.enableDisableAlartPopups": "Left click to view alert messages & right click to enable/disable Alert pop-ups",
        "label.matchIsInUseByanotherProcess": "Match is in use by another process",
        "title.alertsummary": "Alert Summary Display",
        "tooltip.sortbyReference": "Sort by reference",
        "scenarioSummary.popupScen": "Pop Up",
        "tooltip.ILMTestButton": "Test a filter condition ",
        "sweepDetail.authQueue": "Auth Queue",
        "tooltip.delSelShortcut": "Delete selected shortcut",
        "sweepDetail.sweepSetting": "Sweep Settings",
        "ilmScenario.label.privatePublic": "Private/Public",
        "tooltip.alertType": "Alert type",
        "label.findoraddpopup.name": "Name",
        "tooltip.entity.balancelog": "Account balance log retention period",
        "tooltip.enterPostingDateFrom": "Enter posting date from",
        "ilmSummary.tooltip.preAdvice": "Pre-advice movements",
        "multipleMvtActions.label.bookLbl": "Book",
        "instancerecord.entityId_tooltip": "Entity",
        "defaultaccountmaintenance.BIC": "BIC/Network",
        "emailTemplateMaintenance.tooltip.description": "Description",
        "scenario.tooltip.otherIdColCombo": "Enter Scenario Other ID column",
        "ilaapccyparams.primaryAccId": "Primary Acc. ID",
        "link.backtoPreviousPage": "Back to previous page",
        "connection.confirmDBChange": "Are you sure you want to change the current DB?",
        "tooltip.currencyGroupIdentifier": "Enter currency group ID",
        "defaultaccountmaintenance.alert.xrefCode": "BIC/Network is empty",
        "errors.entity.entityName.minlength": "Entity name cannot be greater than 30 characters.<BR>",
        "confirm.acctBreakdown.excludeFromTotal": "Exclude this account from the totals?",
        "tooltip.bookCode": "Book Code",
        "rolemaintenance.addScreen": "Add Role - SMART-Predict",
        "datagrid.context_menu.text.reset_column": "Reset Column",
        "label.mail.emailAddress": "Email Address",
        "tooltip.selectMovType": "Select movement type",
        "scenarioAdvanced.FacilityParams": "Facility Parameters",
        "alert.scenarioQueryWasTestedSuccessfully": "The scenario query was tested successfully, identifying no records in ",
        "tooltip.entityLocalTime": "Select entity local time",
        "label.forecasttemplate.column.name": "Name",
        "accountmaintenance.legend.Sweeping": "Sweeping",
        "tooltip.sortCorrespondentAccId": "Sort by Correspondent Acc ID",
        "tooltip.sortInPredictStatus": "Sort by in Predict balance",
        "tooltip.extraID": "Extra ID",
        "account.schedulesweep.label.settleMethodDRLabel": "Settle Method DR",
        "auditLog.id.referenceId": "Item ID",
        "entitymaintenance.mainScreen": "Entity Maintenance - SMART-Predict",
        "alert.mail.mailSentWithSuccess": "All mails has been sent successfully ",
        "tooltip.chooseModule": "Choose Module ID",
        "multipleMvtActions.fieldset.process": "Process",
        "label.nonworkday.applyAccountCountry": "Apply Account Country",
        "tooltip.roleAccounts": "Maintain account based controls",
        "connectionPool.alertDBConnectionErr": "Database connection error, cannot get fresh informations from Database views,<br> if connection pool is exausted please kill some unused sessions.",
        "ilaapccyparams.currency": "Currency",
        "ilmthroughput.title.window": "ILM ThroughPut Monitor",
        "attributeusagesummary.display": "Display",
        "label.acctBreakdown.hideZeroBalances": "Hide Zero Balances",
        "tooltip.weekly": "Weekly",
        "tooltip.systemDstEndDate": "Date on which summertime daylight saving period ends",
        "matchQuality.ccyTolerance": "Currency tolerance:",
        "inputexceptions.messages.header.statusnotes": "Status Notes",
        "movement.extraText": "Extra Text",
        "label.forecastMonitorOptions.hidezerosum": "Hide Zero Sum Columns",
        "label.recalculateILMData": "Recalculate ILM data for the selected currency and date",
        "country.title.mainWindow": "Country Maintenance",
        "account.schedulesweep.tooltip.bookDrCombo": "Select the book for Debit Sweep",
        "inputconfig.header.property": "Property",
        "alertInstance.matchId": "MATCH_ID",
        "tooltip.sortOutPredictStatus": "Sort by out Predict balance",
        "tooltip.changeSelCurr": "Change selected currency",
        "balmaintenance.externalSODType": "External SOD Type",
        "tooltip.enterPageNo": "Enter page no",
        "ilmthroughput.group": "Account Group",
        "movementDisplay.counterPartyCustDetails": "Party Details",
        "menulabel.itemid.202": "PCM",
        "tooltip.sortAction": "Sort by action",
        "menulabel.itemid.201": "Dashboard",
        "sweep.creditedmessage": "Msg Type CR",
        "menulabel.itemid.204": "Stop Rule",
        "menulabel.itemid.203": "Account Groups",
        "menulabel.itemid.209": "Input Exception (PCM)",
        "scenario.tooltip.radioNo": "No",
        "menulabel.itemid.206": "Payment Categories",
        "menulabel.itemid.205": "Spread Profiles",
        "menulabel.itemid.208": "Interface Setting (PCM)",
        "menulabel.itemid.207": "Currency",
        "maintenanceevent.summary.userselection": "Show Events related to User",
        "holiday.holidayDate": "Holiday Date",
        "label.seconds": "seconds",
        "movementDisplay.excluded": "Excluded",
        "tooltip.optionYes": "Select Yes",
        "tooltip.timeZoneRegion": "Time Zone Region",
        "messageFields.value": "Value",
        "menulabel.itemid.213": "Payment Request Display",
        "scenario.displayOrder.alert": "Display order must be between 1 - 999",
        "menulabel.itemid.212": "PCM Breakdown Monitor",
        "menulabel.itemid.215": "Archive Search (PCM)",
        "balance.currency": "Currency",
        "menulabel.itemid.214": "Payment Request Search",
        "menulabel.itemid.211": "PCM Monitor",
        "usermaintenance.pwdDate": "Last Password Change",
        "sweep.sweepAmt": "Sweep Amount",
        "label.interfaceMonitor.bottomGrid.header.msgType": "Message Type",
        "menulabel.itemid.210": "Interface Monitor (PCM)",
        "exchange.add": "Add new exchange rate",
        "ilmReport.totalGrossValuePayements": "1. Total gross value of payments made  on behalf of correspondent banking  customers",
        "label.findoraddpopup.id": "ID",
        "account.schedulesweep.tooltip.accountIdLabelCombo": "Please select the other account Id",
        "bookMonitor.date": "Date",
        "label.forecasttemplate.column.multiplier": "Multiplier",
        "multipleMvtActions.updateStsRadio": "Update status(es)",
        "tooltip.movement.notes": "Movement Notes",
        "entity.EnterEntityID": "Enter entity ID",
        "ilaapccyparams.centralBankGroupId": "Central Bank Group ID",
        "tooltip.selectUsers": "Select user(s)",
        "movementsearch.valueto": "To",
        "inputException.showValue": "Show value must be 1 or greater",
        "currency.currencyCode": "Currency",
        "tooltip.enterLocationName": "Enter Location Name",
        "tooltip.movement.reference3": "Reference 3",
        "tooltip.movement.reference1": "Reference 1",
        "alert.warning.unavailableSI": "Input Engine not available.",
        "tooltip.bookcodeField": "Sort by bookcode",
        "tooltip.movement.reference2": "Reference 2",
        "ilmExcelReport.outgoingCriticalPaymentsAsOfTotalAvailableLiquidity": "Outgoing Critical Payments as % of Total Available Liquidity",
        "errorLog.source": "File",
        "reports.thresholdAmountType": "Amount Threshold Type",
        "tooltip.sortProcess": "Sort by process",
        "creditintrates": "Credit Interest Rate",
        "entity.GroupLevel3Name": "Group level 3 name",
        "errors.logon.exceednoofusers": "Licensing Error: Maximum number of users exceeded",
        "tooltip.cancelExit": "Cancel changes and Exit",
        "label.alertSummaryTooltip.facility": "Facility",
        "ilmAccountGroupDetails.name": "Name",
        "criticalMvtUpdate.radioEntity": "Entity",
        "account.schedulesweep.label.sweepIntervalLabel": "Sweep Interval",
        "tooltip.refreshScreen": "Refresh screen",
        "tooltip.scenarioActive": "Sort by Active Flag",
        "ilmExcelReport.asOfTotalAvailableLiquidityTitle": "as % of Total Available Liquidity",
        "tooltip.sortLogTime": "Sort by log time",
        "tooltip.enterInterfaceRuleKey": "Enter Interface Rule Key",
        "ilmReport.sumInOutflows.tooltip": "Indicate which group should be the source of data for 'Intraday liquidity usage', 'Total Payments', 'Critical Payment' and 'Throughput'",
        "messageFields.endPosFormatTypeFixed": "End Position",
        "inputexceptions.label.message_type": "Interface:",
        "tooltip.sortPredictedBalance": "Sort by Predicted balance",
        "tooltip.selectType": "Select type",
        "ilmScenario.public": "Public",
        "tooltip.clickLogin": "Click here to log in",
        "movementDisplay.amount": "Amount",
        "label.forecastMonitor.currency": "Currency",
        "msd.alertDeleteProfile": "Are you sure you want to delete the filter",
        "matchQueue.A": "A",
        "preAdviceInput.fieldSetImport1.legendText": "Data Definition ",
        "ilmReport.largestNegativeNetCumulativePosition": "Largest negative net cumulative position",
        "ilmExcelReport.summaryDailyTotalOutflow": "Daily Total Outflow",
        "preAdviceInput.column.status": "Status;",
        "label.mail.to": "To*",
        "label.forecastMonitor.breakdown": "Breakdown",
        "matchQueue.C": "C",
        "messageFormats.interfaceId": "Interface ID",
        "button.genericdisplaymonitor.reActivate": "Re-Activate",
        "matchQueue.B": "B",
        "maintenanceLog.tooltip.changedFrom": "Changed From",
        "userLog.tooltip.item": "Item",
        "matchQueue.E": "E",
        "matchQueue.D": "D",
        "interestCharges.account": "Account",
        "criticalMvtUpdate.scheduledTimeFrame": "Scheduling Timeframe",
        "matchQueue.CcyName": "Name",
        "exchange.addTitle": "Add Currency Exchange Rates - SMART-Predict",
        "matchQueue.Z": "Z",
        "tooltip.entity.smallMovementRetentionParameter": "Small Movements retention period",
        "ent.posLvlRecordExist": "Record Already Exists",
        "accountmaintenanceadd.defaultSettMethod": "Default Settlement Method",
        "sweepsearch.authorizedby": "Authorised By",
        "sweeppriorcutoff.cutOff": "Cut-off",
        "scenario.events.resolutionQuery": "Resolution Query Text:",
        "criticalMvtUpdate.workingDayOnly": "Working Days Only",
        "screen.requestForFlashPlayer": "This page requires a recent version of Flash Player.",
        "tooltip.addILMCcy": "Add new ILM currency",
        "instancerecord.status_header": "Status",
        "connectionPool.connectionSQLActive": "Active",
        "alert.deletion.confirm": "Confirm Deletion",
        "movementsearch.confirmed": "Confirmed",
        "tooltip.msgname": "Enter message name",
        "movement.amount1": "Amount",
        "messageFields.fieldType.keyword": "Keyword",
        "errors.integer": "{0} must be an integer",
        "tooltip.enterDaysRetainSysLog": "Enter number of days to retain system log",
        "sweepNAKQueue.title.window": "Sweep Exceptions Queue - SMART-Predict",
        "alert.interfaceSettings.directory": "Please enter a valid directory",
        "workqueueaccess.viewScreen": "View Work Queue Access - SMART-Predict",
        "tooltip.sortPath": "Sort by path",
        "multiMvtActions.vdate": "Vdate",
        "error.contactAdmin": "Please contact your System Administrator...",
        "systemLog.logDate_Time": "Time",
        "label.acctBreakdown.entity": "Entity",
        "maintenanceLog.action": "Action",
        "scenario.events.tooltip.emailFormatCombo": "Choose an email format",
        "messageFormats.path": "Path",
        "multiMvtActions.processFailed": "Process Failed",
        "account.linkacc": "Link Account To",
        "ilmExcelReport.veryCriticalPaymentsInflows": "Very Critical Payments - Inflows",
        "msd.tooltip.addColumns.sequence": "Sequence",
        "tooltip.selectMQInterface": "Select MQ interface",
        "tooltip.enterCategoryTitle": "Enter Title",
        "mvmMatchSummDisplay.title.window": "Movement Match Summary Display - SMART-Predict",
        "account.accountLevel": "Level",
        "acc.importinternal": "Import Internal",
        "tooltip.interfaceMonitor.lastMessage": "Last Message",
        "button.forecastMonitor.close": "Close",
        "accIntRate.Date": "Date",
        "scenarioCategory.displayorder": "Display Order",
        "batchScheduler.confirm.enable": "This will enable the selected process",
        "auditLog.id.reference": "Item",
        "ilmScenario.label.joinToMvt": "Join to P_MOVEMENT_EXT",
        "ilmReport.centralBankReserves": "Central bank balance",
        "messageFormats.id.formatId": "Format",
        "sweep.sweepId": "Sweep",
        "tooltip.metaGroupId": "Metagroup ID",
        "ilmReport.intraDayCreditLines": "B(ii) Intraday credit lines extended to  customers",
        "scenario.guiHighlight.note.text": "Grey rows indicates unavailable  facilities due to requiring scenario instances, or where this <br/>scenario's instance parameters are not sufficient for the facility to operate.",
        "centralBankMonitor.currencyLimit": "Currency Limit",
        "tooltip.usedInOtherSchedulers": "Number of other sweep schedules that refer to this account",
        "ilmccyparamsAdd.toolip.clearingEndTime": "Indicates the end of the business day for the currency. Specify as HH:MM in currency timeframe.",
        "exchange.changeTitle": "Change Currency Exchange Rates - SMART-Predict",
        "interfacemonitor.sub.threadhb": "Heartbeat",
        "label.forecastMonitor.title.window": "Forecast Monitor - SMART-Predict",
        "tooltip.scenarioCreateInst": "Select create instance",
        "scenario.tooltip.matchColCombo": "Enter Scenario Match ID column",
        "button.ratesDelete": "Delete Rate",
        "tooltip.thursday": "Thursday",
        "preAdviceInput.validateAgain": "All rows will be validated again, changes will be lost.    Do you wish to continue?",
        "tooltip.sortbypartyType": "Sort by type",
        "workflowmonitor.movements": "Movements",
        "mvmmatqsel.title.confirmed": "Confirmed Match Queue - SMART-Predict",
        "tooltip.minSweepAmount": "Minimum Sweep Amount",
        "entity.predict.thresholdParam.securitiesFilter": "Securities Filter",
        "ilmReport.timeSpecificCriticalPayments": "Time Specific / Critical Payments",
        "tooltip.personalEntityList.button.addsum": "Add Sum Entity",
        "intradayBalances.accountId": "Account ID",
        "ilmanalysismonitor.globalview.title": "Global View",
        "tooltip.selectAllInput": "Select All Input",
        "currencyalias.alias": "Alias",
        "usermaintenance.profile": "Profile",
        "ilmAccountGroupDetails.description": "Description",
        "tooltip.ccyGrpAccess": "Currency group access",
        "movement.update_date": "Update Date",
        "tooltip.enterMessageType": "Enter Message Type",
        "manualMatch.warning.messageForPredictStatusOnLoad": "There are included items at multiple position levels",
        "ilmanalysismonitor.grid.lastupdate": "Last Update",
        "tooltip.enterRuleId": "Enter Rule ID",
        "tooltip.resolveButton": "Resolve alert instance",
        "tooltip.clearingEndGrid": "Sort by Clearing End Time",
        "ilmanalysismonitor.grid.creator.tooltip": "Creator",
        "tooltip.changeSelInterfaceRule": "Change selected Interface Rule",
        "scenario.tooltip.payColCombo": "Enter Scenario Payment ID column",
        "sweepDetails.alert.fieldChanged": "WARNING: Changing this field could severely impact the business process.<BR>Are you sure?",
        "tooltip.forecastMonitorOptions.hidescenario": "Hide Scenario",
        "ilmExcelReport.unsettledCredits": "Credits",
        "notes.time": "Time",
        "tooltip.ccyMonitorOptions.enterRefreshRate": "Enter refresh rate",
        "batchScheduler.jobStatus.Pending": "Pending",
        "accountMonitor.title.accountMonitor": "Account Monitor",
        "alert.passwordExpiresInFewDaysPart1": "Your password will expire in ",
        "tooltip.sweepAccount": "Account Details",
        "entity.id": "Entity",
        "alert.matchAuditLog.toDate": "To date should be greater than From date",
        "alert.passwordExpiresInFewDaysPart2": "&nbsp;days. Do you want to change it now?",
        "label.dstenddate": "End Date",
        "tooltip.authorize": "Authorise",
        "button.search": "Search",
        "interfacerulesmaintenance.manualMessageType": "Manual",
        "pwdchange.enterAcceptableCharacter": "Please enter password (Acceptable characters:[a-z,A-Z],[0-9],[~!@#$%^&*()-_=+[]:;'&ldquo;,<.>/?])",
        "tooltip.movCurrency": "Select currency ID",
        "errors.CouldNotSaveJobReportWithSameLocationPrefixExceptioninAdd": "Report record with same combination of filename prefix and output location already exists",
        "changeBalance.preditExternal": "Predicted(external)",
        "main.alert.disable": "Do you want to disable alerts?",
        "tooltip.5positionName": "Fifth position name",
        "tooltip.acctName": "Account Name",
        "tooltip.enterCPartytext": "Enter Counterparty text",
        "scenario.events.after": "After",
        "tooltip.enterTargetBic": "Enter target BIC",
        "tooltip.forecastMonitorTemplateAddDetail.columnId": "Enter column number",
        "tooltip.scenarioName": "Enter Scenario Name",
        "ilmanalysismonitor.grid.extsod.tooltip": "External Start Of Day",
        "ilmAccountGroupDetails.minimum": "Minimum",
        "messagefieldformat.changeScreen": "Change Message Field - SMART-Predict",
        "limit.from": "Limit From",
        "acctMaintenance.tooltip.applyBetween": "Apply between",
        "currencymaitenance.changeScreen": "Change Currency - SMART-Predict",
        "roleBasedControl.changeScreen": "Change Role Based Control - SMART-Predict",
        "tooltip.systemDstStartDate": "Date on which summertime daylight saving period begins",
        "currency.currencyGroup": "Currency Group",
        "tooltip.maintainAnyIlmGroup": "Allow user to change any defined ILM account group and make account groups public",
        "tooltip.enterBookId": "Enter a book ID",
        "alert.interfaceSettings.requiresEmailLogsTo": "Please enter an email",
        "tooltip.acctIBAN": "The IBAN can be entered with or without spaces. For legibility it will be reformatted if necessary to display spaces, but on the database it is stored without spaces",
        "ilaapccyparams.globalCcyAcctGrp": "Global Currency Account Group",
        "connectionPool.sqlStatus": "DB Status",
        "tooltip.viewCrossReference": "View Cross Reference",
        "account.schedulesweep.label.fromLabel": "From",
        "SweepMsgDisplay.Message": "Format",
        "label.format.Int": "Internal",
        "tooltip.enterDaysRejectedSuppressedInput": "Enter number of days to retain rejected suppressed input",
        "ilmReport.UsePaymentTypeData": "Use payment type data",
        "multipleMvtActions.cancelledRadio": "Cancelled",
        "tooltip.enterRefExcl": "Exclude movements having references found with this value",
        "workflowmonitor.reff.title": "Referred (All days)",
        "custodian.entityId": "Entity",
        "movementsearch.openMovements": "Include open",
        "account.status.blocked": "Blocked",
        "tooltip.selectToDate": "Select to date",
        "interfacemonitor.details.text.storedproclastresult": "Last Result",
        "screen.version": "Version:",
        "addJob.tab.reportSettings": "Report Settings",
        "addjob.label.noAccessinEntity": "The selected role does not have access in {0} entity",
        "MaintenanceLogView.title.window": "View Maintenance Log - SMART-Predict",
        "connectionPool.tooltip.sqlId": "SQL ID of last statement",
        "group.groupIdadd": "Group",
        "ilmAccountGroupDetails.entity": "Entity",
        "tooltip.selectPosLevel": "Select position level",
        "entity.predict.metagroupLevelNames.level1": "Level 1",
        "scenario.events.tooltip.after": "Resolution overdue after (mins)",
        "ilmanalysismonitor.tree.forecIncludeAct": "Forecast (incl. actuals)",
        "entity.predict.metagroupLevelNames.level2": "Level 2",
        "entity.predict.metagroupLevelNames.level3": "Level 3",
        "scenario.selectAll": "Select All",
        "sweepSearchList.crdIntMsg": "CR INT MSG",
        "tooltip.loroToPredicted": "Include Loro in Prediction",
        "batchScheduler.alert.removeDenied": "Remove request denied: this process is currently running",
        "button.execute": "Execute",
        "ilmExcelReport.criticalIncomingPayments": "Critical Incoming Payments",
        "accountmaintenance.title.addWindow": "Add Account - SMART-Predict",
        "ilmReport.inflow": "Inflow",
        "ilmExcelReport.dailyMaximumLiquidityUsage": "Daily Maximum Liquidity Usage",
        "ilmExcelReport.veryCritical": "Very Critical",
        "inputconfig.header.outputdir": "Recovery Directory",
        "ilmSummary.tooltip.minBalT": "The lowest balance and latest time of its occurrence on the current value date, determined intraday from actual/external movements",
        "label.dststartdate": "Start Date",
        "sweep.authorby": "Authorised By",
        "throuputmonitor.tooltip.threshold2": "Threshold 2",
        "throuputmonitor.tooltip.threshold1": "Threshold 1",
        "tooltip.rate": "Rate",
        "scenario.events.tooltip.scenarioId": "Scenario ID",
        "scenarioCategory.systemflag": "System Flag",
        "tooltip.ilmdatascreenreports": "Build full cash flow, ILM time series and reporting data",
        "sweep.generatedby": "Generated By",
        "instancerecord.scenarioId_tooltip": "Scenario ID",
        "movementDisplay.predict": "Predict",
        "tooltip.sortOutputType": "Sort by output type",
        "multiMvtActions.book": "Book",
        "scenario.role.selectAll.tooltip": "Select All",
        "entity.crrlimit.zerovalue": "CRR Limit should be greater than zero",
        "label.serverHasRunOutOfMemory": "Server has run out of memory. Please contact your system administrator. The following parameters should be reviewed, ",
        "alert.mail.errorWhenSendingMail": "Error occurred when sending the mail, please contact your administrator",
        "sweep.NAKs": "NAKs",
        "sweeppriorcutoff.ccy": "Ccy",
        "status.userId": "UserId",
        "tooltip.submit": "Submit",
        "corporateAccount.tooltip.add": "Add",
        "dateFormatMMDDYY": "MM/DD/YYYY",
        "sweepPrior.title.window": "Sweeps Prior To Cut-Off - SMART-Predict",
        "ilmAccountGroupDetails.createdOn": "On",
        "tooltip.functGrp": "Select a functional group",
        "tooltip.selectFromDateMMDDYY": "Enter From date (MM/DD/YYYY)",
        "balmaintenance.startBalance": "Balance",
        "errors.stringistoosmall": "The text is shorter than the allowed minimum !!! characters",
        "button.entityMonitor.option": "Options",
        "usermaintenance.userName*": "User Name",
        "tooltip.sortMsgFormat": "Sort by Message Format",
        "errors.password.incorrect": "Old password incorrect",
        "tooltip.showSweepDetails": "Show sweep details",
        "movementsearch.accountid": "Account",
        "ilm.entityFilter": "Entity Filter",
        "account.schedSweep.sweepDirection.fund": "Only fund this account",
        "scenarioSummary.amountThreshold": "Apply amount threshold",
        "movementsearch.trade": "Trade",
        "button.exportErrors": "Export",
        "role.applytoalldisacct": "Apply to all displayed accounts:",
        "account.accountParty": "Account Party",
        "ilmReport.largestDailyTotalOutflow": "Largest daily total outflow",
        "tooltip.includeMvmInMonitors": "Include movement in dealer monitors",
        "tooltip.orderIns": "Enter Ordering Institution ID",
        "errors.logon.hostName": "Licensing Error: Not valid for this hostname ({0})",
        "book.bookCodeadd": "Book",
        "tooltip.accIntRateOverDraft": "Account Interest Rate Debit Margin",
        "inputException.endDate": "End Date",
        "tooltip.viewAccess": "View access",
        "tooltip.interfaceMonitor.awaiting": "Number of messages in awaiting state",
        "account.schedSweep.tooltip.sweepDirection": "Direction",
        "maintenanceevent.details.button.reject.label": "Reject",
        "matchQuality.paramCode": "Param Code",
        "maintenanceevent.summary.dateselection.forDateSelection": "Show All maintenance for a period",
        "label.interfaceExceptions.header.inputDate": "Input Date",
        "ilmExcelReport.totalAvailableIntradayLiquidityInclIncomingPayments": "Total Available Intraday Liquidity (incl. incoming payments)",
        "role.maintainAnyPCFeature": "Override four eyes principle",
        "CriticalPay.ccy": "Currency",
        "tooltip.contactName": "Enter contact name",
        "party.partyId": "Party",
        "ccyAccMaintPeriod.tooltip.startDate": "Start date of the maintenance period",
        "contactDetails.alert.emailaddcontainsnonasciichar": "Email address contains non ASCII characters",
        "workflowmonitor.offered.title": "Offered",
        "label.adjlimitexcess": "Limit Excess",
        "auditLog.sysLog": "System Log",
        "alert.categoryAssociatedScenarios": "This category has some associated scenarios",
        "label.accountspecificsweepformat.tooltip.externalCredit": "Select External Credit",
        "tooltip.lastlogout": "Last Logout",
        "ilmReport.creditLineSecured": "Secured",
        "logBalance.external": "External",
        "table.movement.ext": "Movement_Ext",
        "tooltip.personalEntityList.button.ok": "Save changes and Exit",
        "tooltip.scenarioCyclic": "Select cyclic",
        "turnoverReport.main": "Main",
        "sweep.valuedate": "Value Date",
        "ilmExcelReport.asOfIncomingPayments": "as % of incoming payments",
        "groupMonitor.title.bookGroupMonitor": "Book Group Monitor",
        "ilmReport.collateralPledgedCentralBank": "Collateral pledged at the central bank",
        "criticalMvtUpdate.change": "Change",
        "tooltip.optionNo": "Select No",
        "alert.mvmQSelectionStChange": "Status has changed. Do you want to refresh?",
        "connectionPool.module": "Module",
        "multipleMvtActions.colNameRadio": "Column Name",
        "scenario.eventTab.alert.missingFacility": "Please select event facility",
        "mvmDisplay.tooltip.enterValueDate": "Enter Value date",
        "ilmSummary.label.currentDateSummary": "Current Date Summary",
        "tooltip.minNumChars": "Minimum number of numeric characters",
        "label.accountspecificsweepformat.column.newExternalCrFormat": "External C",
        "errorLog.error": "Error",
        "scenario.endTime": "End Time",
        "acc.credit": "Credit",
        "acc.lorotype": "Loro",
        "errors.user.log": "User",
        "ilmExcelReport.currencyGlobalGroup": "Currency Global Groups",
        "tooltip.addNewCurrencyInterest": "Add new currency interest",
        "tooltip.sweep.uetr1": "Unique reference (SWEEP_UETR1), will be written to sweep and movement records",
        "tooltip.sweep.uetr2": "Additional unique reference (SWEEP_UETR2) will be written to sweep  record only",
        "tooltip.scenarioFacilityParameter": "Enter Facility Parameters",
        "label.entityMonitor.currency.group": "Currency Group",
        "ilmReport.globalCcygrp": "Currency Global Group",
        "movementsearch.allstatuses": "All",
        "tip.schedReportHist.reportTypeId": "Selected Type Id",
        "sweep.sort": "Sort",
        "tooltip.interfaceSettings.beginTime": "Alert Start",
        "entity.predict.thresholdParameter": "Thresholds",
        "scenario.tooltip.treeBreakDown2Combo": "Enter Scenario TreeBreakDown2 column",
        "format.default": "Default",
        "tooltip.findoraddpopup.id": "Enter ID",
        "tooltip.assocForSweepBalnce": "Number of additional accounts to be summed when  calculating a sweep based on an aggregated balance",
        "multiMvtActions.bookCode": "Book code",
        "ilmReport.otherbankcreditlines": "Other banks credit lines",
        "alert.assumption.deleteConfirm": "Are you sure you want to delete?",
        "tooltip.enterMatchingParty": "Enter matching party",
        "tooltip.7positionLevel": "Seventh position level",
        "addjob.JobType": "Job Type",
        "format.ddExt": "Debit External",
        "multipleMvtActions.label.noteLbl": "Note Text",
        "tooltip.fromDateDDMMYY": "Select From date (DD/MM/YYYY)",
        "criticalMvtUpdate.entityId": "Entity",
        "tooltip.sortCategoryDescription": "Sort by Category Description",
        "label.ilmccyprocess.lastexecutestatus": "Last Execute Status",
        "ilmreport.keyword.label.startOfCurrentWeek": "Start of the current week",
        "holidays.title.mainWindow": "Holiday Maintenance - SMART-Predict",
        "tooltip.sortMatchId": "Sort by match ID",
        "balmaintenance.reasonDesc": "Reason Description",
        "centralMonitor.alert.defDays": "Default days should be between 2 and 14",
        "acctmaintenance.useexternal": "Use External",
        "tooltip.noChange": "No Change",
        "alert.accountattribute.acctdelete": "Do you wish to also delete account attribute data?",
        "movementDisplay.extract": "Extract",
        "movementsearch.account.nostro": "Nostro",
        "account.schedulesweep.tooltip.othersettleMethodCRCombo": "Select the Settlement Method for other account  Credit Sweep",
        "currencyMaintenance.title.mainWindow": "Currency Maintenance - SMART-Predict",
        "custodian.custodianId": "Party ID*",
        "tooltip.notes": "Notes",
        "account.schedulesweep.label.entityIdLabel": "Entity ID",
        "tooltip.recCorrs": "Enter Receiver's Correspondent ID",
        "sweepdetail.rule.label": "Rule",
        "status.currentEntity": "CurrentEntity",
        "tooltip.rates": "Add rates",
        "label.areyouSure": "Are you sure?",
        "tooltip.sortCutOffOffset": "Sort by cut off offset",
        "tooltip.showSelMovDetail": "Show selected movement in detail",
        "accountMonitor.alert.label.notANumber": "Not a number",
        "movementsearch.like": "Like",
        "tooltip.sortByBalanceBefore": "Sort by balance before",
        "criticalMvtUpdate.toolTip.criticalType": "Critical Payment Type ID ",
        "tooltip.mgrpLvlID1": "Metagroup Level",
        "movementsearch.cancelled": "Cancelled",
        "screen.warning": "Warning",
        "preadvice.movetoauthorise": "There will be change in balance as this movement is moved to the authorize queue",
        "ilmReport.BalancesOtherBanks": "Balances other banks",
        "ilmreport.keyword.label.startOfCurrentMonth": "Start of the current month",
        "throuputmonitor.tooltip.entity": "Entity Id",
        "swpparameters": "Sweeping Parameters",
        "inputexceptions.label.message_status": "Message Status:",
        "tooltip.interfaceSettings.value": "Value",
        "ilmanalysismonitor.legend.accForeD.Title": "FCOut.",
        "mvmDisplay.title.window": "Movement Display - SMART-Predict",
        "tooltip.msgs": "Sweep messages",
        "corporateAccount.alert.mandatory": "Mandatory",
        "ilmExcelReport.tabNameCriticalOutflows": "Critical Outflows",
        "tooltip.sweepNotes": "Sweep Notes",
        "tooltip.enterProductType": "Enter product type",
        "crossReference.crossReference": "Cross Reference",
        "tooltip.exeJob": "Execute a manual job",
        "scenario.events.tooltip.eventFacilityDesc": "Please give a meaningful text to describe what/why of the event",
        "interest.tooltip.sortByInterestRate": "Interest Rate",
        "sweepSearch.title.window": "Sweep Search - SMART-Predict",
        "tooltip.addAccountCredit": "Credit",
        "groupMonitor.currency": "Currency",
        "movement.pred": "Pred",
        "account.tooltip.accountType": "Sort by Account Type",
        "scenario.displayOrder": "Display Order",
        "interfacemonitor.header.interface": "Interface",
        "role.workQueueAccessAdd.offered": "Offered",
        "tooltip.changeAccountGroup": "Change selected account group",
        "label.accountattributehdr.allowentrytime": "Allow Entry of Time",
        "tooltip.msgfield": "Message field",
        "tooltip.shwmvmntdetails": "Show movement details",
        "ilmreport.keyword.label.startOfPreviousMonth": "Start of the previous month",
        "button.crossReference": "XRefs",
        "screen.copyright": "Copyright (c) %s SwallowTech",
        "preAdviceInput.validRows": "Valid rows",
        "msd.heading.addColumns.column": "Column",
        "tooltip.enterSwpLimit": "Enter sweep limit",
        "label.ConnectionError": "Connection Error",
        "messagefieldadd.alert.duplicateSeqNo": "Duplicate Seq No",
        "scenario.startEndTime.alert": "You need to fill start time and end time",
        "errors.password.checkUserId": "Invalid password: should not contain user ID",
        "accountmonitor.prbalout": "Predicted Bal(Out)",
        "throuputmonitor.tooltip.forcinf": "Forecasted Inflows",
        "button.prev": "Prev",
        "batchScheduler.jobStatus.Closing": "Closing",
        "exchange.date": "Enter exchange date",
        "label.country.overrideWeekends2": "Override Weekend 2",
        "tooltip.entity.general.BIC": "BIC",
        "alert.pleaseFillAllMandatoryFields": "Please fill all mandatory fields (marked with *)",
        "label.country.overrideWeekends1": "Override Weekend 1",
        "mfa.internlUserPass": "User/Password",
        "entity.largeSmallMovementThresholdAsString": "Large/Small Movements",
        "tooltip.selectToDateDDMMYY": "Enter To date (DD/MM/YYYY)",
        "balmaintenance.accountId": "Account ID",
        "reports.csv": "CSV",
        "maintenanceevent.details.button.amendwinfacility.label": "Amend In Facility",
        "tooltip.selectInterestRateDate": "Select interest rate date",
        "label.forecastMonitorTemplateAdd.templateName": "Template Name *",
        "org.apache.struts.taglib.bean.format.sql.timestamp": "hh 'o''clock' a, zzzz",
        "tooltip.tolerance": "Tolerance",
        "button.tooltip.schedreporthist.download": "Download report",
        "messageFormats.id.formatIdcopy": "Format",
        "acctmaintenance.secondary": "Secondary Source",
        "msd.tooltip.addColumns.operator": "Operator",
        "ilmReport.correspondentBalance": "Correspondent balance",
        "label.accountspecificsweepformat.text.entity": "Entity",
        "tooltip.enterNewUserId": "Enter new user ID",
        "scenario.guiHighlight.note.title": "Note:",
        "role.noaccess.higherlevel": "WARNING:A menu option is granted but access to higher levels in the tree has not been granted, thus preventing access to the desired option",
        "criticalMvtUpdate.toolTip.enableUpdateProcessingCheck": "Check box to enable cyclic running of an SQL update",
        "balMaintenance.externalSODTypeAsString": "External SOD Type",
        "title.login.welcomeMessagePage": "Welcome to SMART-Predict",
        "sweep.errorinPredictBal": "Error calculating predict balance.<br>Refer to the error log for details!",
        "sweep.type1": "Type",
        "alert.interfaceSettings.url": "Please enter a valid URL",
        "multiMvtActions.expSettlement": "Exp Settlement",
        "tooltip.ChangeSelectedCurrency": "Change selected currency",
        "alert.currencyExchangeRate.invalidDraftRate": "Invalid debit margin: Negative value is not allowed.",
        "tooltip.selectJobName": "Specify a name for display in the scheduler",
        "ilmanalysismonitor.date.title": "Value Date",
        "ilmthroughputbreakdown.ccyThresholdCheckbox": "Currency Threshold",
        "instancemessage.formatId_tooltip": "Format ID",
        "accint.timedate": "Update Date/Time",
        "label.existingILMData": "Existing ILM data is not entirely up to date",
        "scenario.events.tooltip.value": "Choose a map from value ",
        "alertRemovingDeletematch": "Removing these movements will delete the match. Continue?",
        "button.recoverProcess": "Recover",
        "button.unlock": "Unlock",
        "tooltip.3positionLevel": "Third position level",
        "preAdviceInput.column.matchParty": "Match Party;",
        "tooltip.noInvalidAttempts": "Number of invalid attempts before user is blocked",
        "ilmReport.ofWhichSecured": "1a. Of which secured",
        "tooltip.sortMGName": "Sort by metagroup Name",
        "label.dailysavingperiodcurrencydetail.title.window": "Daylight Saving Period Detail - Currency",
        "tooltip.selectIncMvmInDataEx": "Select include movement in data extract",
        "tooltip.viewScenario": "View selected scenario",
        "accountmaintenance.NoSubAc": "No sub a/c are defined for this main account",
        "maintenanceLog.dateRange": "Maintenance Log",
        "sweep.crMsg": "CR A/C Msg",
        "tooltip.logSelMvm": "Log of selected movement",
        "tooltip.sortMsgCrAc": "Sort by message CR A/C",
        "tooltip.benID": "Enter Beneficiary Institution ID",
        "button.import": "Import",
        "tooltip.movement.custodian": "Custodian ID",
        "errors.csrf.attack.wrongToken": "As a security measure, your request is blocked, a CSRF Attack detected! -  wrong CSRF token",
        "tooltip.AuthorizeSelSweep": "Authorise selected sweep(s)",
        "tooltip.selectTarBalSign": "Select target balance sign",
        "tooltip.sortBySOD": "Sort by Forecast SOD",
        "CurrencyInterestmaintenance.title.window": "Currency Interest Rate Maintenance - SMART-Predict",
        "button.msd.configure.label": "Configure",
        "preAdviceInput.ClipboardAlert": "Clipboard is missing in the server... Try with paste",
        "instancerecord.expand_tooltip": "Expand",
        "errors.logon.userCreationDisabled": "No linked user was found in the application, please contact your Administrator",
        "ilmreport.keyword.label.endOfPreviousQuarter": "End of the previous quarter",
        "tooltip.interfaceSettings.endTime": "Alert End",
        "role.menuaccess.level3": "Level3",
        "role.menuaccess.level1": "Level1",
        "role.menuaccess.level2": "Level2",
        "tooltip.forecastMonitorTemplateAddDetail.columnType": "Select a column type",
        "currencyFunding.showCR": "Show CR",
        "tooltip.okbutton": "Generate the report",
        "inputdate": "Input Date",
        "movement.orderingInstitution": "Ordering Institution",
        "account.schedSweep.tooltip.accountId": "Account ID",
        "messageFormatScenario.title.MainWindow": "Scenario Message Format Maintenance - SMART-Predict",
        "report.amountThresholdTypeAbs": "Absolute",
        "alertInstance.otherIdType": "OTHER_ID Type",
        "currencyFunding.showDR": "Show DR",
        "role.alerttype.email": "Email",
        "button.genericdisplaymonitor.close": "Close",
        "defaultaccountmaintenance.title.changeWindow": "Change Default Account - SMART-Predict",
        "tooltip.sortbymvmnt": "Sort by movement type",
        "scenarioAdvanced.refColumns": "Ref Columns",
        "tooltip.sortDatabaseLink": "Sort by Database Link",
        "maintenanceLog": "Maintenance Log",
        "workflowmonitor.totalMov.title": "Total included movements today",
        "tooltip.crdIntMsg": "Credit internal message",
        "ccyAccMaintPeriod.tooltip.field": "Field",
        "ilmExcelReport.unsettledCredit": "Unsettled: Credits",
        "tooltip.enterStartPosition": "Enter start position",
        "sweep.postsubmitCut": "Post Submit Cut Off",
        "entity.SelectDomesticCCY/CCY": "Select domestic ccy/ccy",
        "turnoverReport.to": "to",
        "genericDisplayMonitor.error": "Error",
        "references": "References",
        "interfacerulesmaintenance.ruleValue": "Rule Value",
        "ilmthroughputCalculateAs": "Calculate % throughput as",
        "ccyAccMaintPeriod.tooltip.ccyCode": "Currency Code",
        "tooltip.scenarioEmailWhenDiff": "Enter Email When Difference",
        "locationaccess.locationName": "Name",
        "ilmExcelReport.total": "Total",
        "connectionPool.tooltip.sqlStatus": "Database session status",
        "tooltip.update": "Perform multiple movement action with 2 or more selected movements",
        "tooltip.changeSelAc": "Change selected account",
        "acctMaintenance.inServEntity": "In servicing entity...",
        "tooltip.account.autoopenunexpected": "Automatically hold open unexpected movements at end of day?",
        "msd.dateradio.option2.tooltip": "Date parameters will be saved as relative to current date",
        "ilmExcelReport.otherSystemsCollateral": "Other Systems Collateral",
        "general.closeConfirm": "You have chosen to close the window. All unsaved changes will be lost. Are you sure?",
        "scenario.guiHighlight.paramId": "Parameter ID",
        "errors.byte": "{0} must be a byte",
        "corporateAccount.wantToDelete": "Are you sure you want to delete?",
        "button.contact": "Contact",
        "maintenanceevent.summary.checkbox.accepted": "Show accepted maintenance events",
        "ilmanalysismonitor.grid.name": "Name",
        "tooltip.sortCurrencygroup": "Sort by currency group",
        "auditLog.id.action": "Action",
        "acct.name": "Account Name",
        "sweepSearchList.sweepType": "Type",
        "sweepDetail.trbalancetype": "Target Balance Type",
        "tooltip.entity.startDay": "Start Day",
        "messageFields.endPos": "End Position",
        "tooltip.acctBreakdown.refresh": "Refresh Account Breakdown Grid",
        "tooltip.addNewAccount": "Add new account",
        "label.entityMonitorOptions.fontnormal": "Normal",
        "sweep.notSuccess": "Sweep could not be saved successfully.<br>Please contact your System Administrator!",
        "currency.toleranceAdd": "Tolerance",
        "movementDisplay.source": "Source",
        "tooltip.ext": "Ext",
        "emailTemplateMaintenance.tooltip.subjectContent": "Subject Content",
        "tooltip.interfaceSettings.threshold": "Alert Threshold (s)",
        "ilmExcelReport.secondLow": "Second Low",
        "connectionPool.alertKillingConsequences": "Killing a connection can have severe consequences. DBA and Admin staff should be consulted.<br>Do you wish to continue",
        "inputconfig.header.endTime": "Alerts End",
        "tooltip.selectPostionLevel": "Select a position level",
        "account.schedSweep.heading.thisAccSettleMethodCr": "Settle Method<br>CR",
        "tooltip.sortByType": "Sort by Type",
        "confirm.changeCcyGroupAccessEntityId": "Changes made for the selected entity will be lost",
        "acctMaintenance.aggAccount": "Aggregate Account",
        "label.forecastMonitorOptions.hideweekend": "Hide Weekend",
        "workqueueaccess.confirm.selectedCurrency2": "Are you sure?",
        "workqueueaccess.confirm.selectedCurrency1": "All the entries for the selected currency will be deleted",
        "button.forecastMonitor.rate": "Rate",
        "tooltip.viewSelMatchQuality": "View selected match quality",
        "label.movUnder": "Under",
        "logBalance.workingSODChange": "Working SOD Change",
        "party.alias": "#Aliases",
        "throuputmonitor.actuals": "Actuals",
        "ilmReport.2ndMax": "2nd max",
        "label.schedReportHist.reportTypeId": "Report Type Id",
        "sweepsearch.submittedamt": "Submitted Amount",
        "account.schedSweep.heading.thisAccSettleMethodDr": "Settle Method<br>DR",
        "sweepIntermediaries.accountId": "Account",
        "criticalMvtUpdate.sumToCateg": "Sum to Category",
        "tooltip.entityMonitorOptions.cancel": "Cancel Changes and Exit",
        "role.sweepLimits.currency": "Currency*",
        "tooltip.shortCutName": "Shortcut name",
        "messageFormats.msgFormatDetails": "Details",
        "preAdviceInput.chkHeader": "If no headers, drag columns to represent order of appearance in dataset.",
        "manualInput.alert.mvmSaved": "Movement created with ID ",
        "tooltip.roleScenario": "Open role maintenance screen",
        "ilaapgeneral.generalSettings": "General",
        "tooltip.viewselectedmsgfield": "View selected message field",
        "account.schedSweep.tooltip.sweepEntityId": "Entity",
        "exchange.cal": "Select exchange date",
        "usermaintenance.lastLogout": "Last Logout",
        "multipleMvtActions.MvtsFieldSet": "Movements",
        "errors.minlength": "{0} cannot be less than {1} characters",
        "ilmExcelReport.unsettledDebit": "Unsettled: Debits",
        "label.lossConnection": "Unable to save server. Possible loss of connection",
        "tooltip.EnterNewOverdraftRates": "Enter new overdraft rates",
        "tooltip.sortFinalPositionLevel": "Sort by final position level",
        "tooltip.hideZeroBalances": "Hide Zero Balances",
        "ilmExcelReport.summaryMinimumBalance": "Minimum balance",
        "tooltip.sortSweepID": "Sort by sweep ID",
        "colors.context.xml": "Change colours",
        "balmaintenance.updateTime": "Input Time",
        "label.schedReportHist.fileSize": "File Size",
        "tooltip.deleteSeletedFile": "Delete selected file from history",
        "ilmReport.creditLineCommitted": "Committed",
        "label.noMessageSelected": "No Message Selected",
        "button.schedreporthist.details": "Details",
        "tooltip.hideControlBar": "Hide control bar",
        "reasonMaintenance.entity": "Entity",
        "addjob.Sunday": "Sunday",
        "label.entityprocess.runTimeEntity": "Run Time<br>(Entity Time)",
        "additionalColumns.alert.emptyValue": "Please enter a valid value",
        "entity.dateTo": "Date To",
        "ilmReport.incomplete": "Incomplete",
        "cashRsvrBal.minTargetBal": "Min Target Balance",
        "movementsearch.review": "Review",
        "user.askForPassword": "Please enter your Password",
        "tooltip.saveProfile": "Save profile",
        "button.match": "Match",
        "ccyMonitorOptions.useCcyMultiplier": "Use Currency Multiplier",
        "status.lastLogout": "LastLogout",
        "tooltip.sortBookName": "Sort by book name",
        "interfacemonitor.header.proc_result": "Proc result",
        "sweepDetails.alert.amountLimits": "Proposed Sweep Amount exceeds the maximum single payment limit for account",
        "confirm.acctBreakdown.moveToLoro": "Move balance to Loro/Curr column?",
        "errors.logon.verify.smartUserId": "Invalid login: Mandatory data is missing. Please contact your System Administrator.",
        "corporateAccount.changeCorporateEntries": "Change Corporate Entries",
        "button.tooltip.schedReportHist.reportjob": "Select a Report Job",
        "tooltip.accountGroupId": "Sort by Group ID",
        "batchScheduler.alert.executeDenied": "Execute request denied: this process is already running",
        "tooltip.sortMetagroupName": "Sort by Metagroup Name",
        "acc.currenttype": "Current",
        "connectionPool.connectionSQLKilled": "Killed",
        "scenario.apiTypeLbl": "API Type",
        "tooltip.jobEvaluateRunDate": "Indicate whether RUN_DATE is evaluated in system time-frame or entity time-frame",
        "role.sweepLimits.currencyCode": "Currency",
        "tooltip.sortSum": "Sort by Sum",
        "tooltip.activeFlag": "Active Flag",
        "ilmanalysismonitor.saveAsButtonprofilePopupLabel": "Save",
        "entity.general.weekEnd2": "Weekend Day 2",
        "tooltip.findoraddpopup.entity": "Select Entity",
        "preAdviceInput.fieldSet1.legendText": "Input Pre-Advice",
        "entity.general.weekEnd1": "Weekend Day 1",
        "preAdviceInput.rowStillInvalid": "Warning, this row is still invalid.",
        "ilmanalysismonitor.groupComboLabel": "Group",
        "alertDisplay.accountNotFound": "<Account not found>",
        "label.entityprocess.status": "Status",
        "personalCurrencyMaintenance.title.mainWindow": "Personal Currency List - SMART-Predict",
        "account.schedSweep.sweepDirection.defund": "Only defund this account",
        "tooltip.entityMonitorOptions.entity": "Click to open Personal Entity List screen",
        "messageFields.startPos": "Start Position",
        "button.alertInstance.msg": "Messages",
        "label.predictedbal": "Predicted Balance",
        "connectionPool.tooltip.connectionId": "Java pool connection ID",
        "changeBalance.userId": "User ID",
        "tooltip.sortCountryName": "Sort by country name",
        "confirm.addMovementsToExistingmatch": "The amount(s) of the selected movement(s) differ from movements already present in the match. Do you want to continue?",
        "tooltip.moviaccountWithInstitution": "Click to select an Account with Institution",
        "movementDisplay.openStatus": "OPEN",
        "interfacemonitor.header.active": "Active",
        "tooltip.changeSelBal": "Change selected balance",
        "scenario.events.button.configuration": "Configure Recipients ",
        "ilmExcelReport.totMonitoredPayTypesOutf": "Total Monitored Critical Payment Types - Outflows",
        "alert.interfaceSettings.requiresAlertThreshold": "Please enter threshold for ",
        "scenarioAdvanced.refTable": "Reference table",
        "title.changeContacts": "Change Contact Details - SMART-Predict",
        "identifiers": "Identifier",
        "label.entityprocess.processType": "Process Type",
        "multipleMvtActions.label.actualSettlLbl": "Actual Settlement",
        "ilmScenario.tooltip.unencumberedLiqAssetAvlbl": "Enter percentage of Unencumbered Liquid Assets Availability (0-100%)",
        "account.accountName": "Name",
        "accountMonitor.label.accountType": "Account Type",
        "tooltip.openSelectMvm": "Open selected movement",
        "userOptions.alert.me": "methodName",
        "reasonMaintenance.addScreen": "Add Reason Maintenance",
        "button.parms": "Parms",
        "instancerecord.dateTimeLog_tooltip": "Date Time",
        "logBalance.suppliedSODChange": "Supplied SOD Change",
        "logBalance.bvAdj": "BV Adj",
        "label.actual": "Actual",
        "reasonMaintenance.reasonCode": "Reason Code",
        "tooltip.acc.importMT950": "Select Import MT950",
        "ilmExcelReport.sign": "Sign",
        "addjob.Daily2": "Daily",
        "tooltip.ChangeSelectedCurrencyGroup": "Change selected currency group",
        "messageField.Title.mainWindow": "Message Fields Maintenance - SMART-Predict",
        "tooltip.selectAccount": "Select account",
        "currency.entityId": "Entity",
        "scenarioCategory.displayTab": "Display Tabs",
        "alert.sweepDetail.authorised": "&nbsp;successfully authorised",
        "label.acctBreakdown.total": "Total",
        "tooltip.newcdext": "Enter new credit external",
        "tooltip.ilmGrpType": "Specify if the group will contain a fixed list of accounts, or has dynamic content determined by selection filters applied to the account table ",
        "ilmReport.ILUsageAll": "Intraday Liquidity Usage (Not available for entity 'All')",
        "ilmccyparamsAdd.title.window.addScreen": "Add ILM Currency Parameter Maintenance",
        "accountMonitor.label.openUnexpected": "Open Unexpected",
        "correspondentaccountmaintenance.title.mainWindow": "Correspondent Account Alias Maintenance - SMART-Predict",
        "label.interfaceMonitor.header.totalMessagesAwaiting": "Awaiting",
        "tooltip.sortExchRate": "Sort by exchange rate",
        "tooltip.searchSweep": "Search sweep",
        "ilmReport.complete": "Complete",
        "sweep.sweepUser": "User",
        "tooltip.sortBookcode": "Sort by bookcode",
        "alert.weekend": "Selected Date is Weekend",
        "changePassword.alert.changed": "Your password has been changed",
        "tooltip.viewPrimaryExternal": "View Primary External",
        "instancerecord.raisedUser_tooltip": "Raised User",
        "label.accountattributehdr.minvalue": "Minimum Value",
        "ilmExcelReport.tabNameThroughputOUT": "Throughput OUT",
        "tooltip.userrole": "User Role",
        "instancerecord.id_header": "ID",
        "cashRsvrBal.tooltip.endDate": "Click to choose end date",
        "user.screen": "User Log - SMART-Predict",
        "tooltip.sortTypeArchive": "Sort by Archive Type",
        "scenario.tooltip.ccy": "Check CURRENCY CODE",
        "tooltip.outgoing": "Outgoing",
        "sweepDetail.authSwepAmt": "Authorise Sweep Amount",
        "tooltip.enterNewCurrId": "Enter new currency code",
        "ilmReport.scenario": "Scenario",
        "ilmanalysismonitor.grid.thresholds.tooltip": "Thresholds",
        "acctSweepBalGrp.tooltip.account": "Account ID",
        "label.accountSpecificSweepFormatAdd.title.window": "Define Account-Specific Sweep Formats",
        "matchQuality.posTotalInternal": "Internal",
        "ilmExcelReport.lch": "LCH",
        "currencyInterest.fromDate": "Date From",
        "scenario.paramDesc": "Define Parameters to be used in scheduled scenario checking",
        "inputexceptions.messages.header.inputdate": "Input Date",
        "preAdviceInput.importInitStatus": "No import performed yet",
        "accountMonitorNew.startOfDayBalance": "Start of day balance",
        "ilmExcelReport.high": "High",
        "role.entAccessList.noAccess": "No Access",
        "tooltip.personalEntityList.button.modifysum": "Change Sum Entity",
        "tooltip.currencyGroupId": "Currency group ID",
        "acc.othertype": "Other",
        "entity.general.country": "Country",
        "ilmreport.keyword.label.endOfPreviousWeek": "End of the previous week",
        "button.forecastMonitor.refresh": "Refresh",
        "alert.viewScreen": "View Alert Message - SMART-Predict",
        "tooltip.selectCrMsg": "Select credit message format",
        "tooltip.userPassword": "Enter password (Acceptable characters:[a-z,A-Z],[0-9],[~!@#$%^&*()-_=+;:'&quot;,<.>/?])",
        "tooltip.goTo": "Go to",
        "tooltip.Currency": "Add currency",
        "movement.Name": "Name",
        "label.acctBreakdown.acctName": "Name",
        "tooltip.likeFlag": "When Checked, search for references containing the specified value. When unchecked, search for exact value.",
        "scenarioSummary.context.xml": "Show summary details XML ",
        "ilm.options.global": "Global ?",
        "tooltip.ChngSwpLmtCurr": "Change selected sweep limits by currency",
        "sweep.valueDate": "Value",
        "tooltip.sortbynotes": "Sort by notes",
        "messageFields.fieldType.hexaDecimal": "Hexadecimal",
        "reports.excel": "Excel",
        "balMaintenance.title.viewWindow": "View Balance - SMART-Predict",
        "ilmAccountGroupDetails.createThroughput": "Create Throughput Ratio Data",
        "workflowmonitor.bkVal.title": "Back valued",
        "label.interfaceExceptions.messageStatus": "Message Status:",
        "queue.refferedPanel": "Referred Panel",
        "sweep.alreadyAuthorised": "Sweep already authorised",
        "tooltip.changeMaxSweepAmount": "Change maximum sweep amount",
        "internalMesgs.title.window": "Internal Message - SMART-Predict",
        "scenarioCategory.categoryDisplayTabName": "Display Tab",
        "criticalMvtUpdate.toolTip.where": "Enter the WHERE clause of an SQL UPDATE'",
        "label.accountattributehdr.requireddate": "Required ",
        "entity.OutputRetentionParameter": "Output retention parameter",
        "tooltip.viewSweepDisp": "View sweep display",
        "tooltip.account.allpreadviceentity": "Allow Pre-Advice Entry?",
        "tooltip.GeneratedOnTime": "Time",
        "ilmReport.ILUsage": "Intraday Liquidity Usage",
        "ilmScenario.createdBy": "Created by",
        "role.roleId1": "Role ID*",
        "movementSummDisplay.title.Window": "Movement Summary Display {0} - SMART-Predict",
        "logBalance.suppliedExterna": "External",
        "alert.currencyExchangeRate.invalidRate": "Invalid amount: Allowed format is 20 digits with up to 7 digits after the decimal place",
        "alert.changeParty.parentParty": "Invalid: specified Parent ID causes an endless loop, please choose a different Parent ID",
        "scenario.guiHighlight.reqScenario": "Requires Scenario Instances",
        "shortcuts.shortcutNameadd": "Shortcut Name",
        "msd.title.filterName": "Filter Name",
        "messageFormats.outputType.file": "File",
        "movement.beneficiary": "Beneficiary",
        "additionalColumns.alert.deleteColumn": "Existing additional column will be removed.    Do you want to continue?",
        "cashRsvrBal.tooltip.runningAvg": "Average Balance per Account",
        "ilmExcelReport.unconfirmedBalance": "Unconfirmed Balance",
        "label.interfaceMonitor.header.totalMessages": "Total",
        "tooltip.viewILMCcy": "View selected ILM currency",
        "scenario.valDateLbl": "VALUE_DATE",
        "ilmthroughput.entity": "Entity",
        "account.schedSweep.tooltip.minAmount": "Min Amount",
        "reportScheduler.title.window": "Report Scheduler - SMART-Predict",
        "format.Rule": "Rule",
        "mfa.externalSSO": "External (Single-sign-on)",
        "entity.smallMovementRetain": "Small Movements",
        "tooltip.accessrequired": "Sort by Access Required",
        "errors.required": "{0} is required",
        "button.printall": "PrintAll",
        "entityaccesslist.viewScreen": "View Entity Access - SMART-Predict",
        "scenario.system": "System",
        "tooltip.clickValueDate": "Click to select value date",
        "workflowmonitor.unxYestday.title": "Unexpected Yesterday",
        "label.fromTotalOf": "from a total of",
        "tooltip.enterMetaGrpId": "Enter a metagroup ID",
        "name": "Name",
        "preAdviceInput.extraHeader": "Extra movement attributes columns  detected.    Please check the above checkBox and retry.",
        "confirm.acctBreakdown.includeExcludeFromTotal": "Include/Exclude this account from the totals?",
        "alertMessage.alertmessage": "Alert Message",
        "button.reset": "Reset Header",
        "tooltip.setRunProcess": "Set Run",
        "tooltip.interfaceMonitor.startInterface": "Click to start interface",
        "attribute.id": "Attribute",
        "locationAccess.title.addWindow": "Add Location Access - SMART-Predict",
        "connectionPool.sqlExecStartTime": "Last SQL Start",
        "tooltip.showMain": "Select Show Main option",
        "instancerecord.movementId_tooltip": "Movement Id",
        "entity.currencyExchangeRate": "Ccy exchange rate",
        "criticalMvtUpdate.toolTip.delete": "Delete",
        "label.preadviceCreatedWithMovementID": "Pre-advice created with Movement ID",
        "screen.notValidParent": "This grid does not have a valid parent",
        "attributeusagesummaryadd.grandTotal.add": "Add",
        "screen.showXML": "Show XML",
        "report.repGen": "Details",
        "tooltip.AddNotes": "Add notes",
        "label.personalEntityList.button.deletesum": "Delete",
        "acctMaintenance.targetBalance": "Target Balance",
        "manualInput.id.movementId": "Movement",
        "tooltip.selectCurr": "Select currency",
        "multipleMvtActions.excludedRadio": "Excluded",
        "intradayBalances.rawData": "Raw Data",
        "tooltip.selectAmountSign": "Select amount sign",
        "preAdviceInput.column.cPartyTxt": "Cparty Text;",
        "matchQuality.currency.idadd": "Currency",
        "centralBankMonitor.whatIfAnalysis": "Central Bank Monitor - What If Analysis",
        "ilmReport.2ndMin": "2nd min",
        "ilmtransSetDetailAdd.title.window.changeScreen": "Change ILM Transaction Detail Display - SMART-Predict",
        "account.schedulesweep.label.targetBalanceTypeLabel": "Target Balance Type",
        "interestCharges.entity": "Entity",
        "messageFormats.hexaFldDelimeter": "(in Hexadecimal)",
        "throuputmonitor.actout": "Outflows",
        "connectionPool.status": "Java Status",
        "centralBankMonitor.currencyMultiplier": "Currency Multiplier",
        "criticalMvtUpdate.toolTip.sumToTotalCheck": "Check to contribute to grand total (used in ILM reporting)",
        "alertMessage.enableflg": "Activation Flag",
        "ilmScenario.succesRate": "Success Rate",
        "cash": "Cash",
        "ilmAccountGroupDetails.createdBy": "Created by",
        "accountMonitorNew.alert.date": "Please select date",
        "tooltip.selectInput": "Select Input",
        "movementsearch.reference": "Reference",
        "account.schedulesweep.tooltip.settleMethodCRCombo": "Select the Settlement Method for Credit Sweep",
        "label.taking": "taking",
        "tooltip.forecastMonitorTemplateAddDetail.shortName": "Enter short name",
        "ilmScenarioAdd.title.window.addScreen": "Add ILM Scenario Detail - SMART-Predict",
        "tooltip.enterStartDate": "Enter start date",
        "party.alert.partyid": "Please enter a valid party ID",
        "currencygroupmaintenance.title.currencies": "Currencies - SMART-Predict",
        "maintenanceLogView.recordRef": "Record reference",
        "movement.date": "Value",
        "tooltip.targetBIC": "Sort by Target BIC",
        "tooltip.sortLocationName": "Sort by Location Name",
        "scenario.title": "Title",
        "tooltip.sortSweepLimit": "Sort by sweep limit",
        "tooltip.accIntRateUpdateDate": "Account Interest Rate Update Date/Time",
        "tooltip.interfaceExceptions.Reprocess": "Reprocess the selected Message",
        "screen.aboutProject": "About&nbsp;-&nbsp;SMART-Predict",
        "preAdviceInput.importFailed": "Import failed",
        "tooltip.accountInterestRate": "Account Interest Rate",
        "tooltip.interInstit": "Enter Intermediary Institution ID",
        "queue.noIncludedMovementMatches": "No-Included Movement Matches",
        "attributeusagesummary.attributeNotFound": "There is no Account Attribute Definition",
        "breakdownmonitor.title.window": "PCM Breakdown Monitor - SMART-Predict",
        "tooltip.changeWrkQAcclist": "Change selected work queue access list",
        "tooltip.movBeneficiaryCustomer": "Click to select an Beneficiary Customer",
        "tooltip.entityMonitorOptions.defaultDays": "Default Days",
        "bookMonitor.total": "Total",
        "criticalMvtUpdate.runSqlUpdate": "Run SQL Update every",
        "movementDisplay.parties": "Parties",
        "criticalMvtUpdate.toolTip.save": "Save",
        "criticalMvtUpdate.toolTip.workingDayOnlyCheck": "Only run the SQL update on working days",
        "tooltip.deleteILMCcy": "Delete selected ILM currency",
        "ilmanalysismonitor.lastProfile": "<Last>",
        "tooltip.process": "Select process",
        "tooltip.selectNewDrMsgCancel": "Select new debit message cancel",
        "ilmExcelReport.incomingCriticalPaymentsAsOfAvailableLiquidityExclIncomingPayments": "Incoming Critical Payments as % of Available Liquidity (excl. incoming payments)",
        "tooltip.defineParams.add": "Add parameter",
        "tooltip.curName": "Sort by Currency Name",
        "label.forecastMonitorTemplate.title.window": "Forecast Monitor Template Maintenance - SMART-Predict",
        "ilmanalysismonitor.alertNodataForSelection": "The selected date is outside the retention period, No updated data will be displayed",
        "label.accountattributehdr.maxvalue": "Maximum Value",
        "ilmReport.Correspondent": "Correspondent",
        "alert.forecasttemplate.columnnumbers": "Column number should be in the range 3 to 959",
        "label.entityMonitor.accountId": "Account",
        "label.interfaceMonitor.buttonStop": "Stop button",
        "manualMatch.title.window": "Manual Match - SMART-Predict",
        "preAdviceInput.headerValue": "Source header value:",
        "alert.Viewonlyaccess": "&nbsp;So restricted to view only access",
        "tooltip.schedreporthist.fileName": "File Name",
        "ilmanalysismonitor.alertRecalculateRunning": "A calculation process is already running please wait...",
        "tooltip.addAccountGroup": "Add new account group",
        "ilmReport.showall": "Show All (Debits and Credits)",
        "changeBalance.legend.acctBalance": "Account Balance Details",
        "movement.postingDate": "Posting Date",
        "sendmail.title.window": "Send Mail",
        "account.ExternalBalance": "External Balance",
        "tooltip.menuLevel3Desc": "Menu Item Level3 description",
        "book.bookLocation": "Book Location",
        "scheduledReportParams.tooltip.runDateEntity": "Specify the entity whose time frame will be used to evaluate RUN_DATE",
        "tooltip.changeSelSweepDay": "Change selected sweep day",
        "tooltip.forecastMonitorOptions.cumulativetotal": "Cumulative Bucket totals",
        "ilmanalysismonitor.grid.openunexp": "Open Unexp.",
        "bookcode": "Book",
        "multiMvtActions.ilmFcast": "ILM Fcast",
        "MultiMvtActions.title.window": "Multiple Movement Action",
        "scenario.events.tooltip.eventSeq": "Event sequence",
        "ilmReport.balancesWithOtherBanks": "Balances with other banks",
        "ilmccyparams.title.window": "ILM Currency Parameter Maintenance",
        "groupMonitor.group": "Group",
        "tooltip.6positionName": "Sixth position name",
        "tooltip.includeMvmInInitMonitors": "Include movement in dealer monitors",
        "currencyFunding.entity": "Entity",
        "tooltip.enterRate": "Enter refresh rate",
        "label.forecastMonitorGrid.currency": "Ccy",
        "tooltip.EnterNewCrInterestRates": "Enter new credit interest rates",
        "manualMatch.warning.messageForAmtTotalsOnLoad": "This match will have different amount totals across position levels",
        "scenarioCategoryAdd.title.window.addScreen": "Add Scenario Category",
        "menulabel.itemid.39": "Holiday",
        "menulabel.itemid.38": "Currency",
        "tooltip.noIncludedMovementMatches": "No-Included Movement Matches",
        "menulabel.itemid.141": "Transaction Set",
        "menulabel.itemid.140": "ILM Scenarios",
        "tooltip.enterBeneficiaryCustomerText4": "Enter Beneficiary Customer Text 4",
        "menulabel.itemid.35": "Group",
        "tooltip.enterBeneficiaryCustomerText3": "Enter Beneficiary Customer Text 3",
        "menulabel.itemid.34": "Metagroup",
        "menulabel.itemid.37": "Entity",
        "tooltip.enterBeneficiaryCustomerText5": "Enter Beneficiary Customer Text 5",
        "tooltip.sortOverrideWeekend2": "Sort by override weekend 2",
        "menulabel.itemid.36": "Book",
        "errors.Dst.overlappingDateRangesException": "INVALID: This date range overlaps with an existing DST range",
        "workqueueaccess.alert.access": "Access to at least one queue should be given",
        "menulabel.itemid.31": "Sweep Message Format",
        "menulabel.itemid.147": "Attribute Usage",
        "menulabel.itemid.146": "Attribute Values",
        "role.maintainSchedulerReportHist": "Allow admin access to scheduled report history",
        "menulabel.itemid.149": "Report Scheduler",
        "menulabel.itemid.32": "Match Quality",
        "menulabel.itemid.148": "ILM Calculation Launcher",
        "menulabel.itemid.143": "ILM Report",
        "menulabel.itemid.142": "Liquidity Monitor",
        "throuputmonitor.current": "Latest",
        "acctMaintenance.useSubAcctTim": "Use Sub-Account Timing",
        "tooltip.counterId": "Counter ID",
        "menulabel.itemid.145": "Attribute Definition",
        "menulabel.itemid.144": "Account Attributes",
        "ilmReport.noDataFound": "NO DATA FOUND",
        "tooltip.enterValue": "Enter value",
        "movementsearch.account.other": "Other",
        "Summary.context.xml": "Show screen details XML ",
        "account.schedSweep.heading.sweepEntityId": "Entity<br>",
        "tooltip.enterBeneficiaryCustomerText2": "Enter Beneficiary Customer Text 2",
        "tooltip.sortByReasonNotes": "Sort by Reason Notes",
        "tooltip.enterBeneficiaryCustomerText1": "Enter Beneficiary Customer Text 1",
        "scenario.uniqueExpression": "Unique Expression",
        "message.alert.noRolesAssign": "You must have account access controls enabled for at least one role to use this facility",
        "menulabel.itemid.28": "Party",
        "menulabel.itemid.150": "Scheduled Report History",
        "menulabel.itemid.27": "Start of Day Balance",
        "menulabel.itemid.152": "ILM ThroughPut Monitor",
        "menulabel.itemid.29": "Account",
        "menulabel.itemid.151": "PCM Report ",
        "menulabel.itemid.24": "Cancel Queue",
        "movementsearch.excluded": "Excluded",
        "menulabel.itemid.23": "Sweep Display",
        "scheduledReportHist.status.failed": "Fail",
        "menulabel.itemid.26": "Authorise Queue",
        "menulabel.itemid.25": "Submit Queue",
        "menulabel.itemid.20": "Manual Sweep",
        "inputException.suppress": "Suppress",
        "menulabel.itemid.157": "Alert Instance Summary",
        "menulabel.itemid.22": "Sweep Search",
        "menulabel.itemid.159": "4-Eyes Review",
        "menulabel.itemid.154": "Cash Reserve Balance Management",
        "menulabel.itemid.153": "Account Currency Maintenance Period",
        "menulabel.itemid.156": "Scenario Message Format",
        "menulabel.itemid.155": "Alert Instance Display",
        "ilmreport.keyword.label.endOfCurrentYear": "End of the current year",
        "alert.ccyAccMaintPeriod.fillDaysvalidInput": "Fill days should not exceed End Date - Start Date",
        "label.nonworkday.facility": "Facility",
        "tooltip.sortOverrideWeekend1": "Sort by override weekend 1",
        "tooltip.deleteEntity": "Delete entity",
        "label.workflowMonitor": "Workflow Monitor",
        "menulabel.itemid.160": "Sweep Archive Search",
        "sweepsearch.bookcode": "Book",
        "cashRsvrBal.tooltip.accountType": "Account Status",
        "menulabel.itemid.57": "Maintenance",
        "scenario.guiHighlight.decription": "Description",
        "menulabel.itemid.56": "Error",
        "menulabel.itemid.59": "User",
        "menulabel.itemid.58": "System",
        "menulabel.itemid.53": "Internal Message",
        "partymaintenance.changeScreen": "Change Party - SMART-Predict",
        "menulabel.itemid.55": "Password Rule",
        "alert.personalEntityList.deletesum": "Are you sure to delete sum Entity?",
        "menulabel.itemid.54": "User Status",
        "menulabel.itemid.51": "Reports",
        "turnoverReport.reportTitle": "Turnover by Currency (Nostro)",
        "menulabel.itemid.50": "Audit Log",
        "label.manualInput": "Manual input requires authorisation",
        "tooltip.viewSelMessage": "View selected message",
        "entity.predict.retentionParameter": "Retention",
        "button.addCols": "Additional Columns",
        "tooltip.sortByjobType": "Sort by job type",
        "format.cancdInt": "Cancel Credit Internal",
        "account.schedSweep.tooltip.sweepOnGrpBalance": "Sum Accs",
        "menulabel.itemid.49": "Scheduler",
        "menulabel.itemid.46": "Role",
        "matchQuality.viewScreen": "View Match Quality - SMART-Predict",
        "currency.tolerance": "Tol",
        "menulabel.itemid.45": "My User Detail",
        "menulabel.itemid.48": "Section",
        "menulabel.itemid.47": "User",
        "contactDetails.alert.emailaddinvalidchar": "Email address contains invalid characters",
        "menulabel.itemid.42": "Shortcut",
        "menulabel.itemid.44": "My User Audit Log",
        "multipleMvtActions.label.mvtIdLocationLbl": "Movement ID location",
        "menulabel.itemid.43": "Change Password",
        "tooltip.interfaceMonitor.lastExecution": "Last Execution Date",
        "menulabel.itemid.40": "System Parameter",
        "tooltip.sortMsgName": "Sort by message name",
        "scenario.entityLbl": "ENTITY_ID",
        "messageFields.fieldType": "Field Type",
        "movementsearch.account.selectaccountclass": "Select Account Class",
        "inputexceptions.tooltip.autoFormatXML": "When appropriate, display formatted XML messages. When unchecked, show raw message",
        "tooltip.movement.matchId": "Match ID",
        "ilmReport.outflow": "Outflow",
        "button.susprnd": "Suspend",
        "currency.DisableCurrencyForPredict": "Disable this currency for Predict",
        "tooltip.extraTransactionSet": "Select Extra Transaction Set",
        "acctMaintenance.alert.screenOpened": "Sub screen with the same account ID is already opened. Please close it before continue!",
        "label.numberAccounts": "Number of accounts",
        "workflowmonitor.submit.Exception": "Exceptions",
        "metaGroup.id.mgroupId": "Metagroup",
        "scenario.fieldSet.legendText": "Type",
        "menulabel.itemid.78": "Account Breakdown Monitor",
        "matchQuality.matchQuaE": "E",
        "matchQuality.matchQuaD": "D",
        "sweepsearch.sweepId": "Sweep",
        "matchQuality.matchQuaC": "C",
        "menulabel.itemid.75": "Referred Queue",
        "matchQuality.matchQuaB": "B",
        "matchQuality.matchQuaA": "A",
        "tooltip.partyId": "Party ID",
        "menulabel.itemid.77": "Sweep",
        "menulabel.itemid.105": "Input Exception",
        "menulabel.itemid.76": "Pre-advice Display",
        "menulabel.itemid.104": "Workflow Monitor",
        "menulabel.itemid.71": "Pre-advice Input",
        "tooltip.sortbyPname": "Sort by party name",
        "menulabel.itemid.70": "Currency Interest Rate",
        "accountGroup.id": "Group ID",
        "sweepsearch.entityDr": "Entity DR",
        "menulabel.itemid.72": "Authorise Queue",
        "menulabel.itemid.100": "Interest Charge per Account",
        "ilmReport.netCumulativePositionDataCannotBeShown": "Data not available - All groups must have 'Create Net Cumulative Position Data' checked.",
        "tooltip.sortBycurrentStatus": "Sort by current status",
        "movementsearch.amountover": "Amount From",
        "menulabel.itemid.107": "Exceptions Queue",
        "tooltip.enterCutOffHM": "Enter cut off offset (hh:mm)",
        "menulabel.itemid.106": "Interface Setting",
        "messageFormats.fieldDelimeter1": "Field Delimiter",
        "menulabel.itemid.109": "Default Account",
        "preAdviceInput.column.movement": "ID;",
        "menulabel.itemid.108": "Location",
        "tooltip.sortNewValue": "Sort by new value",
        "tooltip.sortGroupName": "Sort by group name",
        "menulabel.itemid.68": "Currency Group",
        "menulabel.itemid.67": "Recovery",
        "menulabel.itemid.69": "Currency Exchange Rate",
        "sweepsearch.entityCr": "Entity CR",
        "menulabel.itemid.63": "Connection Pool Monitor",
        "menulabel.itemid.113": "Book Group Monitor",
        "password.retypeNew": "Confirm new password",
        "menulabel.itemid.66": "Movement",
        "menulabel.itemid.65": "Matching",
        "menulabel.itemid.115": "Interface Monitor",
        "ilmanalysismonitor.grid.openunexp.tooltip": "Open Unexpected Movement Adjustment ",
        "menulabel.itemid.110": "Sweep Intermediary",
        "menulabel.itemid.60": "PC",
        "menulabel.itemid.62": "About",
        "menulabel.itemid.112": "Currency Alias",
        "menulabel.itemid.61": "Help",
        "menulabel.itemid.111": "Sweeps Prior To Cut-Off",
        "workflowmonitor.context.xml": "Show workflow details XML",
        "sweep.NewAmt": "New Amount",
        "tooltip.sortHolidayDay": "Sort by holiday day",
        "tooltip.secondaryExternal": "Secondary External",
        "entity.MovementRetentionParameter": "Movement retention parameter",
        "menulabel.itemid.117": "Reason",
        "menulabel.itemid.119": "Currency Funding",
        "button.close": "Close",
        "movement.counterPartyId": "CParty",
        "tooltip.EnterBalSelBalParam": "Enter balance for selected balance parameter",
        "ilmSummary.tooltip.startOfDay": "Start of Day Balance (actual)",
        "passwordRules.label.aToz": "[a-z,A-Z]",
        "tip.accountattribute.currency": "Select currency code",
        "tooltip.refresh": "Refresh",
        "menulabel.itemid.97": "Opportunity Cost",
        "menulabel.itemid.125": "Country",
        "menulabel.itemid.96": "Archive Search",
        "menulabel.itemid.124": "Entity Monitor",
        "tooltip.chooseFile": "Upload file...",
        "menulabel.itemid.99": "Intraday Balance - Main Currency",
        "menulabel.itemid.127": "Forecast Monitor",
        "label.acctBreakdown.applyCurrencyThreshold": "Apply Currency Threshold",
        "menulabel.itemid.98": "Turnover Report - Payment Pattern",
        "menulabel.itemid.126": "Non Workday",
        "tooltip.intradayBalancesDate": "Enter report date",
        "menulabel.itemid.121": "Central Bank Monitor",
        "menulabel.itemid.120": "Account Access Control",
        "menulabel.itemid.95": "Create",
        "menulabel.itemid.123": "Interface Rule",
        "menulabel.itemid.122": "Correspondent Account Alias",
        "instancemessage.formatId_header": "Format ID",
        "SweepMsgDisplay.GeneratedOnTime": "Time",
        "scenario.events.tooltip.eventFacility": "Choose a facility",
        "tooltip.viewSwpLmtCurr": "View selected sweep limits by currency",
        "login.newPage.generated": "The server may have been rebooted, for security reasons, please login again",
        "menulabel.itemid.128": "Forecast Monitor Template",
        "matchQuality.addScreen": "Add Match Quality - SMART-Predict",
        "alertinstancesummary.title.window": "Alert Instance Summary",
        "movement.id.entityId": "Entity",
        "maintenanceevent.summary.checkbox.pending": "Show pending maintenance events",
        "tooltip.selectNewDrMsg": "Select new debit message format",
        "menulabel.itemid.130": "Unsettled Movements",
        "sweepDetails.title.window": "Sweep Details - SMART-Predict",
        "sweep.close": "Close",
        "addjob.alert.noConfigBeforeSave": "No configuration is supplied for the report job parameters, please define before saving",
        "menulabel.itemid.136": "ILM",
        "menulabel.itemid.135": "Category",
        "throuputmonitor.threshold2": "Threshold 2",
        "menulabel.itemid.138": "General",
        "throuputmonitor.threshold1": "Threshold 1",
        "menulabel.itemid.137": "Account Groups",
        "menulabel.itemid.132": "Scenario",
        "tooltip.clickSelMatchingParty": "Click to select matching party",
        "menulabel.itemid.131": "Excluded Movements ",
        "secureid.invalid": "Invalid login",
        "menulabel.itemid.134": "Role Assignment",
        "auditLog.logDate_Date": "Date",
        "menulabel.itemid.133": "Scenarios",
        "ilmthroughputbreakdown.title.window": "ILM ThroughPut Breakdown Monitor - SMART-Predict",
        "ilmScenario.tooltip.dynamicQuery": "SQL query to generate a dynamic extra transaction set which can be used in ILM scenario calculations. \\nThis is an advanced feature - Please consult STL support for guidelines",
        "tooltip.DebitAct": "Sort by DR account",
        "menulabel.itemid.139": "Currency Parameters",
        "ilmExcelReport.unsettled": "Unsettled",
        "tooltip.entityMonitorOptions.save": "Save changes and Exit",
        "acct.id": "Account",
        "account.schedulesweep.label.againstAccountLabel": "Against Account",
        "tooltip.minAlphaChars": "Minimum number of alpha characters",
        "label.ilmccyprocess.lastexecutestatus.successful": "Successful",
        "Jobmaintenance.JobId": "Job ID",
        "movement.account": "Account",
        "ilmScenario.tooltip.activeScen": "Active Scenario",
        "tooltip.sortLastExeTime": "Sort by last execution time",
        "cpyFromManualInput.alert.rightsCcyGrp": "Rights for the currency of selected movement do not exist. Please select another movement",
        "acctMaintenance.label.acctType": "Account Type",
        "tooltip.privPub": "Specify if group is public and available to all users or private and only available to the creator of the group ",
        "alert.entityMonitor.noMovements": "No movements available",
        "label.includedItemsForExternalBalance": "There are included items for external balance at multiple position levels.",
        "tooltip.addNewUser": "Add new user",
        "alert.enterValidToDate": "Please enter to date.",
        "tooltip.test": "Test scenario",
        "alert.accountattributehdr.acctdelete": "Are you sure you want to delete this attribute definition?",
        "label.ilmccyprocess.valuedate": "Value Date",
        "sweepDetail.valueDate": "Value Date",
        "label.movementHasBeenPlaced": ". This movement has been placed in queue for authorisation by another user.",
        "tooltip.RemovePersonalCurrency": "Remove selected personal currency",
        "accountMonitorNew.date": "Date",
        "tooltip.entityMonitorOptions.fontnormal": "Select Normal Font Size",
        "tip.schedReportHist.fileSize": "File Size on KB",
        "currencygroup.currencies": "Currency",
        "button.confirm": "Confirm",
        "contactDetails.alert.cntainonceemailaddcontain@": "Email address must contain only one @",
        "workqueueaccess.changeScreen": "Change Work Queue Access - SMART-Predict",
        "scenarioNotification.flashIcon": "Flash Icon",
        "tooltip.selectMonitor": "Select Monitor Type",
        "ilmReport.ofWhichPeakUsage": "1c. Of which used at peak usage",
        "tooltip.sortCutOffTime": "Sort by cut off time",
        "label.interfaceExceptions.header.exception": "Exception",
        "interestCharges.report": "Report",
        "confirm.remove": "Are you sure you want to remove?",
        "tooltip.interfaceSettings.emaillogsto": "Email Logs To",
        "movementsearch.group": "Group",
        "account.schedulesweep.label.minAountLabel": "Min Amount",
        "ilmanalysismonitor.reloadProfileImageTooltip": "Reload profile",
        "tooltip.load": "Process",
        "interestRate": "Ccy Interest Rate",
        "messageFields.startPosFormatTypeFixed": "Start Position",
        "custodian.custodianName": "Party Name",
        "movementsearch.amount": "Amount",
        "errors.logon.host": "Licensing Error: Not valid for this host ({0}).",
        "ilmthroughputbreakdown.foreIntlflowsCheckbox": "Forecasted Inflows",
        "sweepsearch.postlevel": "Position Level",
        "account.schedulesweep.fieldSet2.legendText": "Against Account",
        "exchange.mainTitle": "Currency Exchange Rate Maintenance - SMART-Predict",
        "tooltip.SortBySweepValueDate": "Sort by sweep value date",
        "label.schedReportHist.scheduleId": "Schedule Id",
        "tooltip.reportDate": "Enter Report date",
        "ilmExcelReport.incomingCriticalPaymentsAsOfTotalAvailableLiquidity": "Incoming Critical Payments as % of Total Available Liquidity",
        "label.nonworkday.applyCurrencyCountry": "Apply Currency Country",
        "currency.enableDst": "Enable Daylight Saving",
        "CriticalPay.defaultExpectedTime": "Default Epected time",
        "currency.CutoffTime": "Cut off time (hh:mm)",
        "tooltip.changeSelAcGL": "Change selected account GL code",
        "ilmExcelReport.actuals": "Actuals",
        "accountGroup.entityId": "Entity ID",
        "ilmExcelReport.averageThroughput": "Average Throughput (CCY timeframe)",
        "correspondentaccountmaintenance.accountId": "Account ID",
        "tooltip.balance.interBalEodDate": "Business day on which the supplied internal balance was evaluated",
        "contactDetails.alert.emailaddcontainperiod": "Email address must contain a period in the domain name",
        "preAdviceInput.unsavedData": "Grid contains unsaved data which will be lost if you continue.    Do you wish to continue?",
        "tooltip.cancdint": "Enter cancel credit internal",
        "label.corporateentries": "Corporate Entries",
        "tooltip.selectFromDateDDMMYY": "Enter From date (DD/MM/YYYY)",
        "label.accountMonitor.title.window": "Account Monitor - SMART-Predict",
        "scenario.events.eventSeq": "Event Sequence",
        "warn.outsideRange": "Limit date has been exceeded, please change the chosen date",
        "dateFormatDDMMYY": "DD/MM/YYYY",
        "metaGroup.mgrpLvlCode": "Level",
        "currencyGroupMaintenance.title.currencies": "Currencies - SMART-Predict",
        "workflowmonitor.excep.title": "Exceptions",
        "preAdviceInput.importComplete": "Import complete",
        "account.tooltip.mainAccountId": "Sort by Main Account ID",
        "maintenanceLog.tableName": "Facility",
        "movement.mostUnmatched": "This movement must be unmatched before it can be amended",
        "menulabel.itemid.17": "Suspended Queue",
        "tooltip.changePrimaryExternal": "Change Primary External",
        "menulabel.itemid.16": "Offered Queue",
        "menulabel.itemid.19": "Manual Match",
        "menulabel.itemid.18": "Confirmed Queue",
        "menulabel.itemid.13": "Manual Input",
        "menulabel.itemid.12": "System",
        "menulabel.itemid.15": "Movement Search",
        "msd.adhocFilter": "Ad hoc",
        "label.interfaceMonitor.engineActionStart": "START",
        "menulabel.itemid.14": "Movement Display",
        "menulabel.itemid.11": "Controls",
        "sweepsearch.submittedby": "Submitted By",
        "menulabel.itemid.10": "Maintenance",
        "role.Locations": "Location",
        "button.entityMonitor.close": "Close",
        "maintenanceevent.details.alert.areyousuretoreject": "Are you sure you want to reject the changes?",
        "preAdviceInput.dataSourceDesc": "Clipboard - (Values can be edited in grid after import)",
        "errors.entity.entityId.required": "Entity ID is required.<BR>",
        "ccyAccMaintPeriod.tooltip.tier": "The volume of reserve holdings in excess of minimum reserve requirements which will be exempt from the deposit facility rate. (determined as a multiple the minimum reserve)",
        "label.accountspecificsweepformat.tooltip.extViaIntermediaryCredit": "Select Ext Via Intermediary Credit",
        "accountmaintenance.legend.GenParam": "General Parameters",
        "tooltip.addJob": "Add job",
        "scenario.events.tooltip.change": "Change event",
        "button.fields": "Fields",
        "inputexceptions.messages.header.messageType": "Interface ID",
        "label.personalEntityList.entityId": "Entity",
        "toolTip.scenarioChange": "Change",
        "cashRsvrBal.accountType": "Account Status",
        "workqueueaccess.mainScreen": "Work Queue Access - SMART-Predict",
        "tooltip.interfaceExceptions.messageId": "Message ID",
        "tooltip.selectAccountId": "Select Account ID",
        "tooltip.actualSettlment": "Enter Actual Settlement",
        "movement.locked": "Movement is in use by another process",
        "label.entityprocess.entity": "Entity",
        "tooltip.deleteSelectedSweepIntermediary": "Delete selected Sweep Intermediary",
        "tooltip.deleteSeletedParty": "Delete selected party",
        "movementsearch.messageId": "Message Format",
        "Jobmaintenance.Job": "Job",
        "message.entityId": "Entity",
        "ilmReport.reportType.baselA": "Basel A - Direct Participants",
        "tooltip.sortbyalias": "Sort by Alias",
        "ilmReport.reportType.baselB": "Basel B - Banks That Use Correspondent Banks",
        "tooltip.acctBreakdown.sum": "Sum",
        "tooltip.jobExecuteAsRole": "Indicate the role-access privileges to use when generating the report",
        "button.forecastMonitor.add": "Add",
        "ilmReport.reportType.baselC": "Basel C - Banks That Provide Correspondent Banking Services",
        "tooltip.viewMsg": "View selected alert message",
        "ilmAccountGroupDetails.netCum": "Create Net Cumulative Position Data",
        "errors.content.notAllowed.description": "As a security measure, your request is blocked as it contains the word(s):",
        "tooltip.fromDateMMDDYY": "Select From date (MM/DD/YYYY)",
        "stp": "STP",
        "tooltip.viewAcStatus": "View an account status",
        "tooltip.selectAllRoles": "Select All Roles",
        "label.ilmdatascreens": "ILM data for screens",
        "tooltip.save": "Save changes and exit",
        "role.advancedUser": "Show advanced user configuration",
        "tooltip.SelectreasonCode": "Select Reason Code for Start of Day Balance",
        "sub": "Sub",
        "tooltip.AccountID": "Account ID",
        "movementsearch.predictstatus": "Predict Status",
        "label.ilmccyprocess.laststarted": "Last Started",
        "tooltip.viewBalanceLog": "View balance log details",
        "ccyAccMaintPeriod.tooltip.action": "Action",
        "preAdviceInput.closeAlert": "The grid contains unsaved data.     Continue without saving?",
        "button.schedReportHist.singleDate": "Single Date",
        "tooltip.SubmitSelSweep": "Submit selected sweep(s)",
        "msd.deleteFilterImageTooltip": "Delete Filter",
        "scenario.otherIdType": "OTHER_ID Type",
        "ilmReport.showonlypayments.tooltip": "Show Only Payments (Debits)",
        "label.workflowXML": "Workflow XML",
        "tooltip.changeposLvl": "Change position level",
        "alert.interfaceSettings.sec": "Please enter a number for seconds [1 - 99999]",
        "criticalMvtUpdate.toolTip.desc": "Description for Critical payment type",
        "label.matchIDFiledAmended": "Match ID field has been amended - Please choose action again after screen refreshes ",
        "ilmExcelReport.low": "Low",
        "currencyGroup.changeScreen": "Change Currency Group - SMART-Predict",
        "tooltip.selectMessageType": "Select Message Type",
        "tooltip.interfaceMonitor.status": "Engine Status",
        "acctmaintenance.usenone": "None",
        "sweepsearch.time": "Time",
        "label.forecastTemplateOption.endsAt": "End at",
        "currency.order": "Order",
        "ilmReport.dateRange": "Date Range",
        "tooltip.movStatus": "Select match status",
        "tip.schedReportHist.errorDescription": "Description of the error got when sending mail",
        "qualityTab.dayAfter": "Today+2",
        "tooltip.selectAllMatching": "Select All Matching",
        "logBalance.bvAdjustChange": "BV Adjust Change",
        "preAdviceInput.column.bookCode": "Book Code;",
        "notes.noteText": "Note Text*",
        "scheduledReportParams.tooltip.runDateWorkdays": "Check box to indicate that only working days should be used when evaluating RUN_DATE",
        "button.delete": "Delete",
        "alert.criticalPaymentType.valueLessThanMin": "At least 5 mins should be set",
        "userLog.tooltip.date": "Date",
        "alert.forecastMonitorOption.maxValue": "Maximum value 30",
        "changeBalance.legend.Balance": "Balance",
        "label.warningSoredProcedureDetails": "Warning - Stored Procedure Details",
        "accountmonitorbutton.move": "Move",
        "tooltip.clickSelCurrId": "Click to select currency ID",
        "addJob.title.showXML": "Report Configuration parameters XML",
        "label.fwdmovements": "Forward Movements",
        "cashRsvrBal.heading.valueDate": "Date         ",
        "fielset.genDeatils": "General",
        "label.forecasttemplate.column.description": "Description",
        "acctglcode": "GL Code",
        "tooltip.changeSelMG": "Change selected metagroup",
        "label.acctBreakdown.startBalance": "Start balance",
        "scenarioNotification.accessRequired": "Access Required?",
        "tooltip.EnterSweepID": "Enter sweep ID",
        "connectionPool.connectionSQLSniped": "Sniped",
        "scenario.events.tooltip.mins": "Enter resolution overdue after value",
        "ccyMonitorOptions.usePersonalCurrencyList": "Use Personal Currency List",
        "tooltip.entity.dateTo": "Date To",
        "ccyAccMaintPeriod.accountId": "Account",
        "tooltip.autoOpenUnexpected": "Auto Open Unexpected",
        "accountMonitor.label.startDayBalance": "Start of Day Balance",
        "tooltip.changemsgfield": "Change message field",
        "tooltip.interfaceMonitor.interfaceId": "Interface ID",
        "movement.source": "Source",
        "pwd.ExpireDays": "Expire in",
        "entity.FirstWeekendDay": "First weekend day",
        "generalsystem.generalSettings": "General",
        "sweepDetail.minSweepAmt": "Min Sweep Amount",
        "ilmSummary.alert.groupStagedNotFound": "Warning: Group-staged data not found. The ILM calculation Launcher must be run for the relevant Entity, Currency and date.",
        "groupMonitor.balance": "Balance",
        "accountmonitor.fnbalin": "Final Bal(In)",
        "Account.sweepTab": "Sweeping",
        "tooltip.displayMvmDetails": "Display movement details",
        "errors.SwtRecordNotExist": "Record does not exist",
        "scenario.events.tooltip.info": "Useful information about the type of data",
        "multipleMvtActions.predictFieldSet": "Predict Status",
        "tooltip.sort.time": "Sort by Time",
        "tooltip.forecastMonitorOptions.applyCurrencyMultiplier": "Apply Currency Multiplier",
        "ilmReport.UsePartyCashflowData.tooltip": "Display payments made on behalf of correspondent banking customers using party cashflow data (payments in currency global group have counterparty related to party of an ILM customer account)",
        "tooltip.movement.beneficiary": "Beneficiary ID",
        "party.parentId": "Parent Party",
        "tip.schedReportHist.scheduleId": "Selected Schedule Id",
        "login.user.secureId": "Please enter your SecurID",
        "tooltip.dataField": "Field",
        "errors.password.inHistory": "Invalid password: you have used this password before",
        "movementsearch.status.authorise": "Authorise",
        "tooltip.advanced": "Configure Search Filter",
        "tooltip.resolvedOnDate": "Instance resolved on date",
        "type.text": "Text",
        "acctmain.isCustomerAccount": "Is Customer Account",
        "ilmScenario.filterCondition": "Filter Condition",
        "user.password": "Password*",
        "metaGroup.mgrpLvlID": "MetaGrp Level ID",
        "alert.personalEntityList.entityExists": "Sum entity Already exists",
        "label.entityprocess.runOrder": "Run Order",
        "sweep.currency": "Currency",
        "corporateAccount.alertDelete": "Delete",
        "autologgedoff.msg.loggedoffIn": "Your session will be automatically logged off in ",
        "account.accountId": "Account",
        "group.changeScreen": "Change Group - SMART-Predict",
        "alert.mail.fileNoutFound": "Report file was not found on the server, please contact your administrator",
        "cashRsvrBal.targtAvgBal": "Target Avg Bal",
        "auditLog.id.ipAddress": "IP Address",
        "tooltip.sort.transactionCcy": "Sort by Ccy",
        "scenario.recordInstance": "Record Scenario Instances",
        "tooltip.moveButton": "Move the balance from Loro/Curr account between the Predicted and Loro/Curr columns",
        "tooltip.delayRateDebitPct": "Enter percentage of debits delayed (0-100%)",
        "movementsearch.currency": "Currency",
        "account.tooltip.corresAccId": "Sort by Correspondent Code",
        "tooltip.flashIcon": "Sort by Flash Icon",
        "tooltip.incoming": "Incoming",
        "scenario.tooltip.valDate": "Check VALUE DATE",
        "scenario.instanceExpiry": "Instance Expiry (mins)",
        "label.forecastMonitorOptions.usertemplate": "User Templates",
        "scenario.events.tooltip.resolved": "Set instance as Resolved",
        "startofday.editreason": "Edit Reason",
        "ilmanalysismonitor.alertOverwriteProfile": "Are you sure you want to overwrite this profile? ",
        "bookMonitor.today": "Today",
        "genericDisplayMonitor.contactAdmin": "Error occurred, Please contact your System Administrator:",
        "label.forecastMonitorOptions.hideassumption": "Hide Assumption",
        "acctMaintenance.currcode": "Ccy",
        "alert.movementIdNotFilled": "Movement ID location not filled",
        "swptime": "Sweep Time",
        "instancerecord.accountId_header": "Account ID",
        "addJob.button.DistList": "Dist List",
        "tooltip.firstMin": "Enter First Minimum",
        "label.acctBreakdown.acct": "Account",
        "match.doesnotexist": "Match not on file",
        "alert.sumCutOffWarn": "This change will not update accounts that are flagged to sum according to cut-off",
        "generalParams.timeZoneRegion.alert": "Changing the time zone will not update system time-frame values already stored in the database.  This could introduce inconsistencies.",
        "role.sweepLimits.currencyCode1": "Currency Code*",
        "movementsearch.account.fielset": "Account Class",
        "button.Filter": "Filter",
        "alert.interfaceSettings.posnumber": "Please enter a positive number",
        "linked.class": "Class",
        "messageFormats.formatType.tagged.variable": "Multi-Line - Variable Fields",
        "tooltip.scenarioCheckInterval": "Sort by Check Interval",
        "genericDisplayMonitor.labelPage": "Page",
        "label.whatifanalysis": "What If Analysis",
        "logBalance.suppliedInternal": "Internal",
        "account.schedSweep.heading.sweepOnGrpBalance": "Sum<br>&nbsp;&nbsp;&nbsp;Accs",
        "messageFields.id.serialNo1": "Seq No",
        "alert.enterValidPageNumber": "Please enter a valid page number",
        "tooltip.linkAcctId": "Link Account ID",
        "ilmExcelReport.tabNameDailyLiquidity": "Daily Liquidity",
        "viewNotes.title.window": "View Notes - SMART-Predict",
        "tooltip.sorBySweeping": "Sweeping",
        "tooltip.accIntRateUpdateUser": "Account Interest Rate Update User",
        "addjob.Minutes": "Min",
        "accIntRate.OverDraft": "Debit Margin",
        "alert.movementIdNotInHeader": "Entered movement ID not found in the excel",
        "groupMonitor.today1": "Today+1",
        "centralBankMonitor.validAmount": "Please enter a valid amount",
        "groupMonitor.today2": "Today+2",
        "label.accountspecificsweepformat.text.internal": "Internal",
        "roleBasedControl.viewScreen": "View Role Based Control - SMART-Predict",
        "scenario.events.tooltip.never": "Never",
        "moveDisplay.reference": "References",
        "ilmanalysismonitor.showscale": "Show Scale in Entity Timeframe",
        "label.forecastMonitorTemplateAddDetail.shortName": "Short Name *",
        "acctclass": "Account Class",
        "tooltip.sortExternalBalance": "Sort by External balance",
        "ilmScenario.mins": "mins",
        "ccyAccMaintPeriod.tooltip.addButton": "Add Account Currency Maintenance Period",
        "cashRsvrBal.endDate": "End",
        "ilmanalysismonitor.alertDeleteProfile": "Are you sure you want to delete the profile?",
        "tooltip.generateUserReport": "Generate report",
        "tooltip.selectILMExcelAccountGroup": "Select an ILM account group. The report output will be based solely on this group (No central bank data)",
        "accountmaintenance.alert.amountCompare": "Maximum sweep amount cannot be less than minimum sweep amount",
        "accountmonitor.confirm.title": "Microsoft Internet Explorer",
      };
      return translations[label] || label;
    }
  };
};




</script>
<script type="text/javascript" src="angularSources/runtime.b4d7a44e3b2ce5dc1c54.js"></script><script type="text/javascript" src="angularSources/polyfills.f15611604fb5c94155e0.js"></script><script type="text/javascript" src="angularSources/scripts.94f8991fd0fe74f2928f.js"></script><script type="text/javascript" src="angularSources/main.55e6b8ec72585d83ce60.js"></script></body>
</html>
