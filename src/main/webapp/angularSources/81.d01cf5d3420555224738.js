(window.webpackJsonp=window.webpackJsonp||[]).push([[81],{IISk:function(t,e,n){"use strict";n.r(e);var l=n("CcnG"),i=n("mrSG"),o=n("447K"),a=n("ZYCi"),c=function(t){function e(e,n){var l=t.call(this,n,e)||this;return l.commonService=e,l.element=n,l.jsonReader=new o.L,l.inputData=new o.G(l.commonService),l.saveData=new o.G(l.commonService),l.requestParams=[],l.baseURL=o.Wb.getBaseURL(),l.swtAlert=new o.bb(e),l}return i.d(e,t),e.prototype.onLoad=function(){var t=this;this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.templateId=o.x.call("eval","templateId"),this.userId=o.x.call("eval","userId"),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="forecastMonitorTemplate.do?method=",this.actionMethod="loadCopyFrom&loadFlex=true",""!=this.templateId&&""!=this.userId&&(this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&userId="+this.userId),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this.swtAlert.error("generic_exception")},e.prototype.inputDataResult=function(t){this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()?(this.lastRecievedJSON!=this.prevRecievedJSON&&this.cbTemplate.setComboData(this.jsonReader.getSelects(),!0),this.prevRecievedJSON=this.lastRecievedJSON):this.swtAlert.warning(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",o.c.OK,this)},e.prototype.OkHandler=function(){var t=this.cbTemplate.selectedValue;window.opener&&window.opener.instanceElement&&(window.opener.instanceElement.reloadCopy(t,this.cbTemplate.selectedLabel),this.closeHandler())},e.prototype.closeHandler=function(){o.x.call("close")},e.prototype.doHelp=function(){o.x.call("help")},e}(o.yb),b=[{path:"",component:c}],u=(a.l.forChild(b),function(){return function(){}}()),d=n("pMnS"),r=n("RChO"),h=n("t6HQ"),s=n("WFGK"),p=n("5FqG"),m=n("Ip0R"),g=n("gIcY"),R=n("t/Na"),w=n("sE5F"),f=n("OzfB"),C=n("T7CS"),y=n("S7LP"),I=n("6aHO"),v=n("WzUx"),k=n("A7o+"),S=n("zCE2"),T=n("Jg5P"),L=n("3R0m"),x=n("hhbb"),B=n("5rxC"),D=n("Fzqc"),O=n("21Lb"),_=n("hUWP"),J=n("3pJQ"),F=n("V9q+"),H=n("VDKW"),N=n("kXfT"),M=n("BGbe");n.d(e,"CopyForecastTemplateModuleNgFactory",function(){return U}),n.d(e,"RenderType_CopyForecastTemplate",function(){return A}),n.d(e,"View_CopyForecastTemplate_0",function(){return G}),n.d(e,"View_CopyForecastTemplate_Host_0",function(){return K}),n.d(e,"CopyForecastTemplateNgFactory",function(){return j});var U=l.Gb(u,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[d.a,r.a,h.a,s.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,j]],[3,l.n],l.J]),l.Rb(4608,m.m,m.l,[l.F,[2,m.u]]),l.Rb(4608,g.c,g.c,[]),l.Rb(4608,g.p,g.p,[]),l.Rb(4608,R.j,R.p,[m.c,l.O,R.n]),l.Rb(4608,R.q,R.q,[R.j,R.o]),l.Rb(5120,R.a,function(t){return[t,new o.tb]},[R.q]),l.Rb(4608,R.m,R.m,[]),l.Rb(6144,R.k,null,[R.m]),l.Rb(4608,R.i,R.i,[R.k]),l.Rb(6144,R.b,null,[R.i]),l.Rb(4608,R.f,R.l,[R.b,l.B]),l.Rb(4608,R.c,R.c,[R.f]),l.Rb(4608,w.c,w.c,[]),l.Rb(4608,w.g,w.b,[]),l.Rb(5120,w.i,w.j,[]),l.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),l.Rb(4608,w.f,w.a,[]),l.Rb(5120,w.d,w.k,[w.h,w.f]),l.Rb(5120,l.b,function(t,e){return[f.j(t,e)]},[m.c,l.O]),l.Rb(4608,C.a,C.a,[]),l.Rb(4608,y.a,y.a,[]),l.Rb(4608,I.a,I.a,[l.n,l.L,l.B,y.a,l.g]),l.Rb(4608,v.c,v.c,[l.n,l.g,l.B]),l.Rb(4608,v.e,v.e,[v.c]),l.Rb(4608,k.l,k.l,[]),l.Rb(4608,k.h,k.g,[]),l.Rb(4608,k.c,k.f,[]),l.Rb(4608,k.j,k.d,[]),l.Rb(4608,k.b,k.a,[]),l.Rb(4608,k.k,k.k,[k.l,k.h,k.c,k.j,k.b,k.m,k.n]),l.Rb(4608,v.i,v.i,[[2,k.k]]),l.Rb(4608,v.r,v.r,[v.L,[2,k.k],v.i]),l.Rb(4608,v.t,v.t,[]),l.Rb(4608,v.w,v.w,[]),l.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),l.Rb(1073742336,m.b,m.b,[]),l.Rb(1073742336,g.n,g.n,[]),l.Rb(1073742336,g.l,g.l,[]),l.Rb(1073742336,S.a,S.a,[]),l.Rb(1073742336,T.a,T.a,[]),l.Rb(1073742336,g.e,g.e,[]),l.Rb(1073742336,L.a,L.a,[]),l.Rb(1073742336,k.i,k.i,[]),l.Rb(1073742336,v.b,v.b,[]),l.Rb(1073742336,R.e,R.e,[]),l.Rb(1073742336,R.d,R.d,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,x.b,x.b,[]),l.Rb(1073742336,B.b,B.b,[]),l.Rb(1073742336,f.c,f.c,[]),l.Rb(1073742336,D.a,D.a,[]),l.Rb(1073742336,O.d,O.d,[]),l.Rb(1073742336,_.c,_.c,[]),l.Rb(1073742336,J.a,J.a,[]),l.Rb(1073742336,F.a,F.a,[[2,f.g],l.O]),l.Rb(1073742336,H.b,H.b,[]),l.Rb(1073742336,N.a,N.a,[]),l.Rb(1073742336,M.b,M.b,[]),l.Rb(1073742336,o.Tb,o.Tb,[]),l.Rb(1073742336,u,u,[]),l.Rb(256,R.n,"XSRF-TOKEN",[]),l.Rb(256,R.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,k.m,void 0,[]),l.Rb(256,k.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,a.i,function(){return[[{path:"",component:c}]]},[])])}),z=[[""]],A=l.Hb({encapsulation:0,styles:z,data:{}});function G(t){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{lblCopy:0}),l.Zb(402653184,3,{cbTemplate:0}),l.Zb(402653184,4,{loadingImage:0}),l.Zb(402653184,5,{okButton:0}),l.Zb(402653184,6,{closeButton:0}),(t()(),l.Jb(6,0,null,null,27,"SwtModule",[["height","96"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var l=!0,i=t.component;"creationComplete"===e&&(l=!1!==i.onLoad()&&l);return l},p.ad,p.hb)),l.Ib(7,4440064,null,0,o.yb,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(8,0,null,0,25,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","10"],["paddingRight","10"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),l.Ib(9,4440064,null,0,o.ec,[l.r,o.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),l.Jb(10,0,null,0,7,"SwtCanvas",[["height","50%"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(11,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(12,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),l.Ib(13,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(14,0,null,0,1,"SwtLabel",[["text","Copy from"],["width","80"]],null,null,null,p.Yc,p.fb)),l.Ib(15,4440064,[[2,4],["lblCopy",4]],0,o.vb,[l.r,o.i],{width:[0,"width"],text:[1,"text"]},null),(t()(),l.Jb(16,0,null,0,1,"SwtComboBox",[["dataLabel","templates"],["shiftUp","40"],["toolTip","Select a Template ID"],["width","163"]],null,[["window","mousewheel"]],function(t,e,n){var i=!0;"window:mousewheel"===e&&(i=!1!==l.Tb(t,17).mouseWeelEventHandler(n.target)&&i);return i},p.Pc,p.W)),l.Ib(17,4440064,[[3,4],["cbTemplate",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],shiftUp:[3,"shiftUp"]},null),(t()(),l.Jb(18,0,null,0,15,"SwtCanvas",[["height","50%"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(19,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(20,0,null,0,13,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),l.Ib(21,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(22,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),l.Ib(23,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(24,0,null,0,1,"SwtButton",[["label","OK"],["width","70"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.OkHandler()&&l);return l},p.Mc,p.T)),l.Ib(25,4440064,[[5,4],["okButton",4]],0,o.cb,[l.r,o.i],{width:[0,"width"],label:[1,"label"]},{onClick_:"click"}),(t()(),l.Jb(26,0,null,0,1,"SwtButton",[["id","closeButton"],["label","Close"],["width","70"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.closeHandler()&&l);return l},p.Mc,p.T)),l.Ib(27,4440064,[[6,4],["closeButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"]},{onClick_:"click"}),(t()(),l.Jb(28,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","5"],["top","3"]],null,null,null,p.Dc,p.K)),l.Ib(29,4440064,null,0,o.C,[l.r,o.i],{top:[0,"top"],horizontalAlign:[1,"horizontalAlign"],paddingRight:[2,"paddingRight"]},null),(t()(),l.Jb(30,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.doHelp()&&l);return l},p.Wc,p.db)),l.Ib(31,4440064,[["helpIcon",4]],0,o.rb,[l.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(32,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),l.Ib(33,114688,[[4,4],["loadingImage",4]],0,o.xb,[l.r],null,null)],function(t,e){t(e,7,0,"100%","96");t(e,9,0,"100%","100%","5","5","10","10");t(e,11,0,"100%","50%");t(e,13,0,"100%","5");t(e,15,0,"80","Copy from");t(e,17,0,"templates","Select a Template ID","163","40");t(e,19,0,"100%","50%");t(e,21,0,"100%");t(e,23,0,"100%","5");t(e,25,0,"70","OK");t(e,27,0,"closeButton","70","Close");t(e,29,0,"3","right","5");t(e,31,0,"helpIcon"),t(e,33,0)},null)}function K(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-copy-forecast-template",[],null,null,null,G,A)),l.Ib(1,4440064,null,0,c,[o.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var j=l.Fb("app-copy-forecast-template",c,K,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);