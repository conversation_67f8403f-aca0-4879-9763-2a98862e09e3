<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
     <class name="org.swallow.model.Program" table="S_PROGRAM" mutable="false">
		<cache usage="read-only"/>
		<id name="programId" column="PROGRAM_ID" unsaved-value="null">
		<generator class="assigned"/>
		</id>
		
		<property name="programName" column="PROGRAM_NAME" not-null="false"/>
		<property name="programDesc" column="PROGRAM_DESC" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
    </class>
</hibernate-mapping>
