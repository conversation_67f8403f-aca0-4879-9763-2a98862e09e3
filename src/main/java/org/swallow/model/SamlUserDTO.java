package org.swallow.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
/**
 * Saml user DTO that should be returned back to Predict/NewSmart
 * 
 * <AUTHOR>
 *
 */
public class SamlUserDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String username;
	private List<String> authorities = new ArrayList<String>();
	private List<UserAttribute> attributes = new ArrayList<SamlUserDTO.UserAttribute>();
	private List<UserAttribute> unmapped = new ArrayList<SamlUserDTO.UserAttribute>();
	private Boolean validToken = false;
	
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}

	public List<String> getAuthorities() {
		return authorities;
	}
	public void setAuthorities(List<String> authorities) {
		this.authorities = authorities;
	}
	public List<UserAttribute> getAttributes() {
		return attributes;
	}
	public void setAttributes(List<UserAttribute> attributes) {
		this.attributes = attributes;
	}
	public Boolean getValidToken() {
		return validToken;
	}
	public void setValidToken(Boolean validToken) {
		this.validToken = validToken;
	}
	
	
	public List<UserAttribute> getUnmapped() {
		return unmapped;
	}
	public void setUnmapped(List<UserAttribute> unmapped) {
		this.unmapped = unmapped;
	}
	
	@Override
	public String toString() {
		return "SamlUserDTO [username=" + username + ", authorities=" + authorities + ", attributes=" + attributes
				+ ", unmapped=" + unmapped + ", validToken=" + validToken + "]";
	}
	
	public static class UserAttribute implements Serializable {
		private static final long serialVersionUID = 1L;
		private String name;
		private String friendlyName;
		private String value;
		public UserAttribute() {
		}
		public UserAttribute(String name, String friendlyName, String value) {
			super();
			this.name = name;
			this.friendlyName = friendlyName;
			this.value = value;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public String getFriendlyName() {
			return friendlyName;
		}
		public void setFriendlyName(String friendlyName) {
			this.friendlyName = friendlyName;
		}
		public String getValue() {
			return value;
		}
		public void setValue(String value) {
			this.value = value;
		}
		@Override
		public String toString() {
			return "UserAttribute [name=" + name + ", friendlyName=" + friendlyName + ", value=" + value + "]";
		}
	}
	
	public String getAttributeValueByName(String name) {
		for (int i = 0; i < attributes.size(); i++) {
			if(attributes.get(i).getName().equals(name)) {
				return attributes.get(i).getValue();
			}
		}
		return null;
	}
}
