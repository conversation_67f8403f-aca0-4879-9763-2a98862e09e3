/*
 * Created on Jan 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.model;


import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.metamodel.spi.ValueAccess;
import org.hibernate.usertype.CompositeUserType;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */

public class UserProfileDetailIdType implements CompositeUserType, Serializable{
	
	private final Log log = LogFactory.getLog(UserProfileDetailIdType.class);	// log object
	
	/*private String hostId;
	private String userId;
	private String profileId;
	private String menuItemId;
	private Long sequenceNumber;
	*/
	
/*//TODO:SBR	
	private static final String[] PROPERTY_NAMES = {"hostId", "userId","profileId","menuItemId","sequenceNumber"}; // property name array 
	
	private static final Type[] PROPERTY_TYPES = {StringType.INSTANCE, StringType.INSTANCE,StringType.INSTANCE,StringType.INSTANCE,LongType.INSTANCE}; // property type array 

	  public String[] getPropertyNames() 
	  { 
	      return PROPERTY_NAMES; 
	  } 

	  public Type[] getPropertyTypes() 
	  { 
	      return PROPERTY_TYPES; 
	  }
*/	  
	   public Object getPropertyValue(Object component, int property) throws HibernateException 
	   {
	   		   	  
	   	  UserProfileDetail.Id userProfileIdObject = (UserProfileDetail.Id)component;
	   	  
	      switch (property) { 
	      case 0: 
	         return userProfileIdObject.getHostId(); 
	      case 1: 
	         return userProfileIdObject.getUserId();
	      case 2: 
	         return userProfileIdObject.getProfileId();
	      case 3: 
	         return userProfileIdObject.getMenuItemId();
	      case 4: 
	         return userProfileIdObject.getSequenceNumber();

	      	
	      default: 
	         throw new HibernateException(property+" is invalid number"); 
	      } 
	   }
	   
	   public void setPropertyValue(Object component, int property, Object value) throws HibernateException 
	   { 
	   	  
	   	UserProfileDetail.Id userProfileIdObject = (UserProfileDetail.Id)component;
	   	  
	      switch (property) 
		  { 
		      case 0:  
		      	userProfileIdObject.setHostId((String)value);
		      	break;
		      case 1: 
		      	userProfileIdObject.setUserId((String)value);
		      	break;
		      case 2: 
		      	userProfileIdObject.setProfileId((String)value);    
		      	break;
		      case 3: 
		      	userProfileIdObject.setMenuItemId((String)value);
		      	break;
		      case 4: 
		      	userProfileIdObject.setSequenceNumber((Long)value);    
		      	break;
		      			      	
		      default: 
		         throw new HibernateException(property+" is invalid index"); 
	      }
	      
	   } 	
	   
	   public Class returnedClass() 
	   { 
	      return  UserProfileDetail.Id.class; 
	   } 	   

	   public boolean equals(Object x, Object y) throws HibernateException 
	   { 
	      if (x == y) return true; 
	      if (x == null || y == null) return false; 
	      return x.equals(y); 
	   } 	   

	   public Object nullSafeGet(ResultSet rs, String[] names, SessionImplementor session, Object owner) 
			throws HibernateException, SQLException 
       { 
		   	if (rs.getObject(names[0])!=null && rs.getObject(names[1])!=null && rs.getObject(names[2])!=null && rs.getObject(names[3])!=null) 
		   	{ 
		   		String hostId = rs.getString(names[0]); 
		   		String userId = rs.getString(names[1]);
		   		String profileId = rs.getString(names[2]); 
		   		String menuItemId = rs.getString(names[3]);
		   		
		   		long sequenceNumber = rs.getLong(names[4]);
		   		
		   		UserProfileDetail.Id userProfileDetailIdObj = new UserProfileDetail.Id();
		   		userProfileDetailIdObj.setHostId(hostId);
		   		userProfileDetailIdObj.setUserId(userId);
		   		userProfileDetailIdObj.setProfileId(profileId);
		   		userProfileDetailIdObj.setMenuItemId(menuItemId);
		   		
		   		userProfileDetailIdObj.setSequenceNumber(new Long(sequenceNumber));		   		
		   		return userProfileDetailIdObj;
		   	} 
		   	else 
		   	{ 
		   		return null; 
		   	} 
       } 
	   
	   public void nullSafeSet(PreparedStatement st, Object value, int index, SessionImplementor session) throws HibernateException, SQLException { 
	      if (value==null) { 
	         st.setNull(index, Types.VARCHAR); 
	         st.setNull(index+1, Types.VARCHAR);
	         st.setNull(index, Types.VARCHAR); 
	         st.setNull(index+1, Types.VARCHAR);	         
	         st.setNull(index+1, Types.NUMERIC);	         
	      } 
	      else 
	      { 	      	
	      	UserProfileDetail.Id userProfileDetailIdObj = (UserProfileDetail.Id)value;
	      	
	         // Setting the host id
	      	 if (userProfileDetailIdObj.getHostId() == null) { 
	            st.setNull(index, Types.VARCHAR); 
	         } 
	         else { 
	            st.setString(index, userProfileDetailIdObj.getHostId()); 
	         } 
	      	 
	         // Setting the user id
	         if (userProfileDetailIdObj.getUserId() == null) { 
	            st.setNull(index+1, Types.VARCHAR); 
	         } 
	         else { 
	         	st.setString(index+1, userProfileDetailIdObj.getUserId()); 
	         }
	         
	         // Setting the profile id
	         if (userProfileDetailIdObj.getProfileId() == null) { 
	            st.setNull(index+2, Types.VARCHAR); // TODO 
	         } 
	         else { 
	         	st.setString(index+2, userProfileDetailIdObj.getProfileId()); 
	         }	         	
	         
	         // Setting the menuItem id
	         if (userProfileDetailIdObj.getMenuItemId() == null) { 
	            st.setNull(index+3, Types.VARCHAR);  
	         } 
	         else { 
	         	st.setString(index+3, userProfileDetailIdObj.getMenuItemId()); 
	         }	         	

	         // Setting the sequence number
	         if (userProfileDetailIdObj.getSequenceNumber() == null) { 
	            st.setNull(index+4, Types.INTEGER);  
	         } 
	         else { 
	         	st.setLong(index+4, userProfileDetailIdObj.getSequenceNumber().longValue()); 
	         }	         	
	      } 
	   } 

	   public Object deepCopy(Object value) throws HibernateException { 
	      return value; 
	   } 	   

	   public boolean isMutable() { 
	      return false; 
	   } 

	   public Serializable disassemble(Object value, SessionImplementor session) throws HibernateException { 
	      return (Serializable)value; 
	   } 

	   public Object assemble(Serializable cached, SessionImplementor session, Object owner) throws HibernateException { 
	      return cached; 
	   } 	  
	   
	   
	   @Override
		public int hashCode(Object x) throws HibernateException {
			return super.hashCode();
		}
/*//TODO:SBR
		@Override
		public Object nullSafeGet(ResultSet rs, String[] names, SharedSessionContractImplementor session, Object owner)
				throws HibernateException, SQLException {
			return nullSafeGet(rs, names, (SessionImplementor) session, owner);
		}

		@Override
		public void nullSafeSet(PreparedStatement st, Object value, int index, SharedSessionContractImplementor session)
				throws HibernateException, SQLException {
			nullSafeSet(st, value, index, (SessionImplementor) session);
		}

		@Override
		public Serializable disassemble(Object value, SharedSessionContractImplementor session) throws HibernateException {
			return disassemble(value, (SessionImplementor) session);
		}

		@Override
		public Object assemble(Serializable cached, SharedSessionContractImplementor session, Object owner)
				throws HibernateException {
			return assemble(session, (SessionImplementor) session, owner);
		}

		@Override
		public Object replace(Object original, Object target, SharedSessionContractImplementor session, Object owner)
				throws HibernateException {
			throw new HibernateException("Method [replace] not implemented in ForecastTemplateIdType");
		} 
*/

    @Override
    public Object instantiate(ValueAccess values, SessionFactoryImplementor sessionFactory) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Class embeddable() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Serializable disassemble(Object value) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object assemble(Serializable cached, Object owner) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object replace(Object detached, Object managed, Object owner) {
        // TODO Auto-generated method stub
        return null;
    }	   
}
