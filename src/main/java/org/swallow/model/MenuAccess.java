/*
 * Created on Dec 6, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.model;

import org.swallow.model.BaseObject;

import java.util.Date;
import java.util.Hashtable;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MenuAccess extends BaseObject implements org.swallow.model.AuditComponent{

	private String accessId;
	private Date updateDate;
	private String updateUser;
	public  final static String CLASSNAME = "MenuAccess";
	public final static String PARAMETER_FLD_NAME = "itemId";

	private Id id = new Id();
	private MenuItem menuItem = new MenuItem();

	public static class Id extends BaseObject{
		private String roleId;
		private String itemId;
		private String description;


		public Id() {}

		public Id(String roleId, String itemId, String description) {
			this.roleId = roleId;
			this.itemId = itemId;
			this.description = description;
		}

		/**
		 * @return Returns the description.
		 */
		public String getDescription() {
			return description;
		}
		/**
		 * @param description The description to set.
		 */
		public void setDescription(String description) {
			this.description = description;
		}
		/**
		 * @return Returns the itemId.
		 */
		public String getItemId() {
			return itemId;
		}
		/**
		 * @param itemId The itemId to set.
		 */
		public void setItemId(String itemId) {
			this.itemId = itemId;
		}
		/**
		 * @return Returns the roleId.
		 */
		public String getRoleId() {
			return roleId;
		}
		/**
		 * @param roleId The roleId to set.
		 */
		public void setRoleId(String roleId) {
			this.roleId = roleId;
		}
	}

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("itemId","Menu Item Id");
		logTable.put("accessId","Access Type");
	}


	/**
	 * @return Returns the accessId.
	 */
	public String getAccessId() {
		return accessId;
	}
	/**
	 * @param accessId The accessId to set.
	 */
	public void setAccessId(String accessId) {
		this.accessId = accessId;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the menuItem.
	 */
	public MenuItem getMenuItem() {
		return menuItem;
	}
	/**
	 * @param menuItem The menuItem to set.
	 */
	public void setMenuItem(MenuItem menuItem) {
		this.menuItem = menuItem;
	}

}