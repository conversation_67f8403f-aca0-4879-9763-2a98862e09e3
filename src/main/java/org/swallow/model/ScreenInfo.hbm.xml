<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.model.ScreenInfo" table="S_SCREEN_INFO">
  <composite-id name="id" class="org.swallow.model.ScreenInfo$Id" unsaved-value="any">
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="entityId" access="field" column="ENTITY_ID"/>
   <key-property name="userId" access="field" column="USER_ID"/>
   <key-property name="screenId" access="field" column="SCREEN_ID"/>
   <key-property name="clsId" access="field" column="CLASS_ID"/>  
   <key-property name="propertyName" access="field" column="PROPERTY_NAME"/>
  </composite-id>
  <property name="propertyValue" column="PROPERTY_VALUE"/>    
 </class>
</hibernate-mapping>
