/*
 * @(#)User.java
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.UserAuthDetails;
import org.swallow.control.model.UserStatus;
import org.swallow.exception.SwtException;
import org.swallow.service.LogonManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;

public class User extends BaseObject implements org.swallow.model.AuditComponent{

	private String userName;
	private String sectionId;
	private String language;
	private String phoneNumber;
	private String emailId;
	private String roleId;
	private String password;
	//Defined for change password screen
	private String password1;
	private String currentEntity;
	private Date lastLogin;
	private Date passwordChangeDate;
	private String passwordChangeDateAsString;
	private String lastLogoutAsString;
	private String lastLoginAsString;
	private String status;
	private Date lastLogout;
	private Date updateDate;
	private String updateUser;
	private String profileId;
	private Integer invPassAttempt;
//	Added for Currency Group Id
	private String currentCcyGrpId;
	//  Added for maintaining status of inactivity
	private boolean inactiveDisable;
	private boolean isManualChange = false;
	
	private UserAuthDetails userAuthDetails  = null;
	
	// SecureId for DFA authentication, Saber Chebka 13-03-2012
	private String secureId ;
	
	private final Log log = LogFactory.getLog(User.class);
	
	//Added for Implement amount and date formats specific to user
	private String amountDelimiter;
	private String dateFormat;	
	private boolean mfaUser = false;
	private String extAuthId = null;
	private Date lastLoginFailed = null;
	private String lastLoginFailedIp = null;
	private String lastLoginIp = null;
/**
 * @return Returns the currentCcyGrpId.
 */
public String getCurrentCcyGrpId() {
	return currentCcyGrpId;
}
/**
 * @param currentCcyGrpId The currentCcyGrpId to set.
 */
public void setCurrentCcyGrpId(String currentCcyGrpId) {
	this.currentCcyGrpId = currentCcyGrpId;
}
	private String alertType;
	private String domesticCurrency;
	/**
	 * @return Returns the domesticCurrency.
	 */
	public String getDomesticCurrency() {
		return domesticCurrency;
	}
	/**
	 * @param domesticCurrency The domesticCurrency to set.
	 */
	public void setDomesticCurrency(String domesticCurrency) {
		this.domesticCurrency = domesticCurrency;
	}
	private Id id = new Id();
	
	public static Hashtable  logTable = new Hashtable();
	public static ArrayList<String>  excludedAttributes = new ArrayList<String>();
	public static String  className = "User";
	
	static {
		logTable.put("userName","User Name");
		logTable.put("sectionId","Section Id");
		logTable.put("language","Language");
		logTable.put("phoneNumber","Phone Number");
		logTable.put("emailId","Email Address");
		logTable.put("roleId","Role Id");
		logTable.put("currententity","Default Entity");
		logTable.put("status","User Status");
		logTable.put("currentCcyGrpId","Currency Group ID");
		logTable.put("amountDelimiter","Amount Delimiter");
		logTable.put("dateFormat","Date Format");
			
	}
	
	static {
		excludedAttributes.add("lastLogin");
		excludedAttributes.add("lastLogout");
		excludedAttributes.add("invPassAttempt");
		excludedAttributes.add("passwordChangeDate");
		excludedAttributes.add("csrfTokens");
		excludedAttributes.add("extAuthId");
		excludedAttributes.add("lastLoginFailed");
		excludedAttributes.add("lastLoginFailedIp");
		excludedAttributes.add("lastLoginIp");

	}

	public boolean isManualChange() {
		return isManualChange;
	}

	public void setManualChange(boolean manualChange) {
		isManualChange = manualChange;
	}


	public static class Id extends BaseObject{
		private String hostId;
		private String userId;

		public Id() {}

		public Id(String hostId, String userId) {
			this.hostId = hostId;
			this.userId = userId;
		}
		public String getUserId() {
			return userId;
		}
		public void setUserId(String userId) {
			this.userId = userId;
		}		
		public String getHostId() {
			return hostId;
		}
		public void setHostId(String hostId){
			this.hostId = hostId;
		}
	}

	public void setId(Id id){
		this.id = id; 
		}
	

	
	public Id getId(){
		return id; 
		}
	
	/**
	 * @return Returns the currentEntity.
	 */
	public String getCurrentEntity() {
		return currentEntity;
	}
	/**
	 * @param currentEntity The currentEntity to set.
	 */
	public void setCurrentEntity(String currentEntity) {
		this.currentEntity = currentEntity;
	}
	/**
	 * @return Returns the emailId.
	 */
	public String getEmailId() {
		return emailId;
	}
	/**
	 * @param emailId The emailId to set.
	 */
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}
	
	/**
	 * @return Returns the language.
	 */
	public String getLanguage() {
		return language;
	}
	/**
	 * @param language The language to set.
	 */
	public void setLanguage(String language) {
		this.language = language;
	}
	
	
	/**
	 * @return Returns the password.
	 */
	public String getPassword() {
		return password;
	}
	/**
	 * @param password The password to set.
	 */
	public void setPassword(String password) {
		this.password = password;
	}
	
	/**
	 * @return Returns the lastLogin.
	 */
	public Date getLastLogin() {
		return lastLogin;
	}
	/**
	 * @return Returns the lastLogout.
	 */
	public Date getLastLogout() {
		return lastLogout;
	}
	/**
	 * @return Returns the passwordChangeDate.
	 */
	public Date getPasswordChangeDate() {
		return passwordChangeDate;
	}
	/**
	 * @return Returns the phoneNumber.
	 */
	public String getPhoneNumber() {
		return phoneNumber;
	}
	/**
	 * @param phoneNumber The phoneNumber to set.
	 */
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	/**
	 * @return Returns the roleId.
	 */
	public String getRoleId() {
		return roleId;
	}
	/**
	 * @param roleId The roleId to set.
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	/**
	 * @return Returns the sectionId.
	 */
	public String getSectionId() {
		return sectionId;
	}
	/**
	 * @param sectionId The sectionId to set.
	 */
	public void setSectionId(String sectionId) {
		this.sectionId = sectionId;
	}
	/**
	 * @return Returns the status.
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * @param status The status to set.
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	/**
	 * @return Returns the userName.
	 */
	public String getUserName() {
		return userName;
	}
	/**
	 * @param userName The userName to set.
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}
	/**
	 * @return Returns the profileId.
	 */
	public String getProfileId() {
		return profileId;
	}
	/**
	 * @param profileId The profileId to set.
	 */
	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}
	/**
	 * @return Returns the lastLoginAsString.
	 */
	public String getLastLoginAsString() {
		return lastLoginAsString;
	}
	/**
	 * @param lastLoginAsString The lastLoginAsString to set.
	 */
	public void setLastLoginAsString(String lastLoginAsString) {
		this.lastLoginAsString = lastLoginAsString;
	}
	/**
	 * @return Returns the lastLogoutAsString.
	 */
	public String getLastLogoutAsString() {
		return lastLogoutAsString;
	}
	/**
	 * @param lastLogoutAsString The lastLogoutAsString to set.
	 */
	public void setLastLogoutAsString(String lastLogoutAsString) {
		this.lastLogoutAsString = lastLogoutAsString;
	}
	/**
	 * @return Returns the passwordChangeDateAsString.
	 */
	public String getPasswordChangeDateAsString() {
		return passwordChangeDateAsString;
	}
	/**
	 * @param passwordChangeDateAsString The passwordChangeDateAsString to set.
	 */
	public void setPasswordChangeDateAsString(String passwordChangeDateAsString) {
		this.passwordChangeDateAsString = passwordChangeDateAsString;
	}
	/**
	 * @param lastLogin The lastLogin to set.
	 */
	public void setLastLogin(Date lastLogin) {
		this.lastLogin = lastLogin;
	}
	/**
	 * @param lastLogout The lastLogout to set.
	 */
	public void setLastLogout(Date lastLogout) {
		this.lastLogout = lastLogout;
	}
	/**
	 * @param passwordChangeDate The passwordChangeDate to set.
	 */
	public void setPasswordChangeDate(Date passwordChangeDate) {
		this.passwordChangeDate = passwordChangeDate;
	}
	
	
	public Integer getInvPassAttempt() {
		return invPassAttempt;
	}
	public void setInvPassAttempt(Integer invPassAttempt) {
		this.invPassAttempt = invPassAttempt;
	}
	/**
	 * @return Returns the alertType.
	 */
	public String getAlertType() {
		return alertType;
	}
	/**
	 * @param alertType The alertType to set.
	 */
	public void setAlertType(String alertType) {
		this.alertType = alertType;
	}
	
	
	public String getPassword1() {
		return password1;
	}
	public void setPassword1(String password1) {
		this.password1 = password1;
	}
	
	/**
	 * @return secureId
	 */
	public String getSecureId() {
		return secureId;
	}
	
	/**
	 * @param secureId 
	 */
	public void setSecureId(String secureId) {
		this.secureId = secureId;
	}
	
/**
 * @return Returns the inactiveDisable.
 */
public boolean isInactiveDisable() {
    return inactiveDisable;
}
/**
 * @param inactiveDisable The inactiveDisable to set.
 */
public void setInactiveDisable(boolean inactiveDisable) {
    this.inactiveDisable = inactiveDisable;
}
/* (non-Javadoc)
 * @see org.swallow.model.DatabaseListener#onUpdate(java.lang.Object, java.lang.Object)
 */
public void onUpdate(Object oldObject, Object newObject) {

    log.debug("Entering Update method of User");
    User oldUser = (User) oldObject;
    User newUser = (User) newObject;

    log.debug("Old status--->" + oldUser.getStatus());
    log.debug("New status--->" + newUser.getStatus());

    if (oldUser.getStatus().equals("2") && newUser.getStatus().equals("1")) {
        log.debug("User made active");

        String hostId = oldUser.getId().getHostId();

        String userId = oldUser.getId().getUserId();

        log.debug("The hostID is==>" + hostId + "==");
        log.debug("The userId for the old record is==>" + userId + "==");
        UserStatus userStatus = new UserStatus();
        
        userStatus.getId().setHostId(hostId);
        
        userStatus.getId().setUserId(userId);
        
        userStatus.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
        
        
        userStatus.getId().setLogOnTime(new Timestamp(new Date().getTime()));
        
        userStatus.setLogOutTime(new Date());
        
        
        LogonManager mgr = (LogonManager)SwtUtil.getBean("logonManager");
        LogonManager logonManager = (LogonManager) (SwtUtil
                .getBean("logonManager"));
        try {
            logonManager.saveUserStatusRow(userStatus);
        } catch (SwtException e) {
            log.error(e);
            e.printStackTrace();
        }

    } else {
        log.debug("Not enabled");
    }

}
public String getAmountDelimiter() {
	return amountDelimiter;
}
public void setAmountDelimiter(String amountDelimiter) {
	this.amountDelimiter = amountDelimiter;
}
public String getDateFormat() {
	return dateFormat;
}
public void setDateFormat(String dateFormat) {
	this.dateFormat = dateFormat;
}
public boolean isMfaUser() {
	return mfaUser;
}
public void setMfaUser(boolean mfaUser) {
	this.mfaUser = mfaUser;
}
public String getExtAuthId() {
	return extAuthId;
}
public void setExtAuthId(String extAuthId) {
	this.extAuthId = extAuthId;
}
public UserAuthDetails getUserAuthDetails() {
	return userAuthDetails;
}
public void setUserAuthDetails(UserAuthDetails userAuthDetails) {
	this.userAuthDetails = userAuthDetails;
}
public Date getLastLoginFailed() {
	return lastLoginFailed;
}
public void setLastLoginFailed(Date lastLoginFailed) {
	this.lastLoginFailed = lastLoginFailed;
}
public String getLastLoginFailedIp() {
	return lastLoginFailedIp;
}
public void setLastLoginFailedIp(String lastLoginFailedIp) {
	this.lastLoginFailedIp = lastLoginFailedIp;
}
public String getLastLoginIp() {
	return lastLoginIp;
}
public void setLastLoginIp(String lastLoginIp) {
	this.lastLoginIp = lastLoginIp;
}
}


