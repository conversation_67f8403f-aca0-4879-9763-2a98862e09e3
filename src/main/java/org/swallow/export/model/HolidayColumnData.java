/*
 * @(#)HolidayColumnData.java 1.0 ,10/10/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.export.model;
import java.io.Serializable;
import java.util.ArrayList;
import org.swallow.export.model.ColumnDTO;
/* Used to form the Column Headers in PDF,Excel and CSV format for Holidays Screen */
public class HolidayColumnData implements Serializable{
public static ArrayList<ColumnDTO> getColumnData() {
		
		ArrayList<ColumnDTO> rtn = new ArrayList<ColumnDTO> ();
		
		ColumnDTO cDTO = new ColumnDTO ();		       
		    cDTO.setHeading("Country");
	        cDTO.setType("str");
	        cDTO.setDataElement("country");		       
	        rtn.add(cDTO);
	        
	        cDTO = new ColumnDTO ();		       
	        cDTO.setHeading("HolidayDate");
	        cDTO.setType("str");
	        cDTO.setDataElement("date");		       
	        rtn.add(cDTO);
	        
	        cDTO = new ColumnDTO ();		       
	        cDTO.setHeading("Day");
	        cDTO.setType("str");
	        cDTO.setDataElement("day");		       
	        rtn.add(cDTO);
	        
	        
	                return rtn;
		
  }

}
