/*
 * @(#)Xml2Csv.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service;

import org.swallow.exception.SwtException;
import org.w3c.dom.NodeList;

/**
 * This interface has methods that are used in converting the DATAGRID values in
 * to the  export format:CSV
 * 
 * Modified by: Marshal .I<br>
 * Date: 29-August-2011
 */
public interface Xml2Csv {

	/**
	 * This method is used to convert the given xml data to comma separated
	 * value format.<br>
	 * 
	 * @param inputXML
	 * @param screenName
	 * @return String - result in CSV format
	 * @throws SwtException
	 */
	public String convertXML(String inputXML, String screenName)
			throws SwtException;

	/**
	 * This method is used to get the data from the monitor screen based on the
	 * filter criteria.<br>
	 * 
	 * @param filterData
	 * @return String - Filter data
	 * @throws SwtException
	 */
	public String getFilterData(NodeList filterData) throws SwtException;

	/**
	 * Start:Modified for Mantis 1624 by RK on 14-Dec-2011
	 */
	/**
	 * This method is used to get the column meta data.<br>
	 * 
	 * @param columnData
	 * @return String - Column meta data
	 * @throws SwtException
	 */
	public String getColumnMetaData(NodeList columnData) throws SwtException;

	/**
	 * This method is used to get the row data.<br>
	 * 
	 * @param rowData
	 * @param columnData
	 * @return String - Row Data
	 * @throws SwtException
	 */
	public String getRowData(NodeList rowData, NodeList columnData) throws SwtException;

	/**
	 * This method is used to get the total data.<br>
	 * 
	 * @param totalData
	 * @param columnData
	 * @return String - Total Data
	 * @throws SwtException
	 */
	public String getTotalData(NodeList totalData, NodeList columnData) throws SwtException;
	/**
	 * End:Modified for Mantis 1624 by RK on 14-Dec-2011
	 */
}
