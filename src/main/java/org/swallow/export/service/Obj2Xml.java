package org.swallow.export.service;

/*
 * @(#)Obj2Xml.java 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

import java.util.ArrayList;
import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;

public interface Obj2Xml {

	/**
	 * This method used to form the whole data in XML format
	 * 
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @param totalData
	 * @return
	 * @throws SwtException
	 */
	public String convertObj(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, Collection rowData,
			Collection totalData) throws SwtException;
}
