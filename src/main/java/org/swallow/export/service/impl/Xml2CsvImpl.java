/*
 * @(#)Xml2CsvImpl.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.io.IOException;
import java.io.StringReader;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.xerces.parsers.DOMParser;
import org.swallow.exception.SwtException;
import org.swallow.export.service.Xml2Csv;
import org.swallow.util.SwtUtil;
import org.w3c.dom.DOMException;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
/**
 * This class has methods that are used for the conversion of the resultant xml
 * data for the grid values of all Flex screens to corresponding '.csv' format.<br>
 * <br>
 * 
 * Modified by Marshal on 29-August-2011<br>
 * Modified by RK on 14-Dec-2011<br>
 */
public class Xml2CsvImpl implements Xml2Csv {

	// Initializes Log
	private final Log log = LogFactory.getLog(Xml2CsvImpl.class);

	/**
	 * This method is used to convert the given xml data to comma separated
	 * value format.<br>
	 * 
	 * @param inputXML
	 * @param screenName
	 * @return String - result in CSV format
	 * @throws SwtException
	 */
	public String convertXML(String inputXML, String screenName)
			throws SwtException {
		// Variable that holds the resulting xml data in comma separated values
		// format
		StringBuffer sbResultantData = null;
		// Declaring the DOMParser object - an XML parser to read and manipulate
		// XML
		DOMParser parser = null;
		// The Document interface represents the entire HTML or XML document.
		Document document = null;
		int columnNodeLenght = 0;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [convertXML] Begins");
			// Creating an instance of DOMParser - an XML parser to read and
			// manipulate XML
			parser = new DOMParser();
			// DOMParser traverse through the given input xml data
			parser.parse(new InputSource(new StringReader(inputXML)));
			// Assigns the parsed data to the Document class
			document = parser.getDocument();
			// Initializes String buffer to build result
			sbResultantData = new StringBuffer();
			// Appending the screen name, document's title and date with the
			// resulting data
			sbResultantData.append(screenName);
			sbResultantData.append(",");
			sbResultantData.append(SwtUtil.getWindowsTitleSuffix());
			sbResultantData.append(SwtUtil.getSysParamDate());
			sbResultantData.append(",\n\n");
			
			NodeList columnNodeList = document.getElementsByTagName("column");
	
			for (int i = 0; i < columnNodeList.getLength(); i++) {
	            Node  device = columnNodeList.item(i);
	            if (device.getNodeType() == Node.ELEMENT_NODE) {
	            	org.w3c.dom.Element e = (org.w3c.dom.Element) device;
	                if (e.getAttribute("dataelement").contentEquals("alerting")) {
	                	device.getParentNode().removeChild(device);
	                }
	            }
			}

			// Appending the filter values with the resulting data
			sbResultantData.append(getFilterData(document
					.getElementsByTagName("filter")));
			// Appending the column values with the resulting data
			sbResultantData.append(getColumnMetaData(columnNodeList));
			// Appending the row and column values with the resulting data
			sbResultantData.append(getRowData(document
					.getElementsByTagName("row"), columnNodeList));
			// Appending the total details (if any) in the totals column
			sbResultantData.append(getTotalData(document
					.getElementsByTagName("total"), columnNodeList));
			return sbResultantData.toString();
		} catch (SAXException ex) {
			// log error message
			log.error("SAXException occured in " + this.getClass().getName()
					+ " - [convertXML]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (IOException ex) {
			ex.printStackTrace();
			// log error message
			log.error("IOException occured in " + this.getClass().getName()
					+ " - [convertXML]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [convertXML]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// Cleaning unreferenced objects goes here
			parser = null;
			document = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [convertXML] Ends");
		}
	}

	/**
	 * This method is used to get the string value that holds the data after
	 * enabling filter sort in Flex screens.<br>
	 * 
	 * @param filterData
	 * @return String - Filter data
	 * @throws SwtException
	 */
	public String getFilterData(NodeList filterData) throws SwtException {
		// Variable to hold the filter data
		StringBuffer sbFilterDataCsv = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getFilterData] Begins");
			// Initialize String buffer to build filter data
			sbFilterDataCsv = new StringBuffer();
			// loops through the filterData to form the given data in a CSV
			// format
			for (int filterCount = 0; filterCount < filterData.getLength(); filterCount++) {
				// Gets the filtered value from the NodeList
				sbFilterDataCsv.append("\"");
				sbFilterDataCsv.append(filterData.item(filterCount)
						.getAttributes().getNamedItem("id").getNodeValue()
						.replaceAll("\"", "\'"));
				sbFilterDataCsv.append("\",\"");
				sbFilterDataCsv.append((filterData.item(filterCount)
						.getFirstChild() == null ? "" : filterData.item(
						filterCount).getFirstChild().getNodeValue().replaceAll(
						"\"", "\'")));
				sbFilterDataCsv.append("\",\n");
			}
			// Adds a line break
			sbFilterDataCsv.append("\n");
			return sbFilterDataCsv.toString();
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getFilterData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getFilterData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getFilterData] Ends");
		}
	}

	/**
	 * This method is used to get the column meta data.<br>
	 * 
	 * @param columnData
	 * @return String - Column meta data
	 * @throws SwtException
	 */
	public String getColumnMetaData(NodeList columnData) throws SwtException {
		// Holds the resultant column meta data
		StringBuffer sbColumnMetaData = null;

		try {
			int columnLenght = columnData.getLength();
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getColumnMetaData] Begins");
			// Initializes columnMetaData to build column metadata
			sbColumnMetaData = new StringBuffer();
			// Iterate through columns node and get column metadata
			for (int colCount = 0; colCount < columnLenght; colCount++) {
				sbColumnMetaData.append("\"");
				sbColumnMetaData.append(columnData.item(colCount)
						.getAttributes().getNamedItem("heading").getNodeValue()
						.replaceAll("\"", "\'"));
				sbColumnMetaData.append("\",");
			}
			sbColumnMetaData.append("\n");
			return sbColumnMetaData.toString();
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log
					.debug(this.getClass().getName()
							+ " - [getColumnMetaData] Ends");
		}
	}

	/**
	 * This method is used to get the row data.<br>
	 * 
	 * @param rowData
	 * @param columnData
	 * @return String - Row Data
	 * @throws SwtException
	 */
	public String getRowData(NodeList rowData, NodeList columnData)
			throws SwtException {
		// Holds the row data
		StringBuffer sbResultRowData = null;
		// Declares the subNodes object that gets the child nodes from the
		// rowData
		NodeList subNodes = null;
		int columnLenght = 0;
		int rowLenght = 0;
		try {
			rowLenght= rowData.getLength();
			columnLenght = 	columnData.getLength();
			// log debug message
			log.debug(this.getClass().getName() + " - [getRowData] Begins");
			// Initializes resultRowData to build row data
			sbResultRowData = new StringBuffer();

			// Iterates row and column nodes and get values for matched nodes
			for (int rowCount = 0; rowCount < rowLenght; rowCount++) {
				// Row sub nodes
				subNodes = rowData.item(rowCount).getChildNodes();
				int subNodesrowLenght = subNodes
						.getLength();
				for (int colCount = 0; colCount < columnLenght; colCount++) {
					for (int rowChildCount = 0; rowChildCount < subNodesrowLenght; rowChildCount++) {
						if (subNodes.item(rowChildCount) != null) {
							/**
							 * Start:Modified for Mantis 1624 by RK on
							 * 14-Dec-2011
							 */
							// If field matches, then get value (row data)
							if (columnData.item(colCount).getAttributes()
									.getNamedItem("dataelement").getNodeValue()
									.equalsIgnoreCase(
											subNodes.item(rowChildCount)
													.getNodeName())) {
								sbResultRowData.append("\"");
								if ((subNodes.item(rowChildCount))
										.getFirstChild() != null) {
									sbResultRowData.append((subNodes
											.item(rowChildCount))
											.getFirstChild().getNodeValue()
											.replaceAll("\"", "\'").trim());
								}
								sbResultRowData.append("\",");
								break;
							}
							/**
							 * End:Modified for Mantis 1624 by RK on 14-Dec-2011
							 */
						}
					}
				}
				sbResultRowData.append("\n");
			}
			return sbResultRowData.toString();
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getRowData] Ends");
		}
	}

	/**
	 * This method is used to get the total data.<br>
	 * 
	 * @param totalData
	 * @param columnData
	 * @return String - Total Data
	 * @throws SwtException
	 */
	public String getTotalData(NodeList totalData, NodeList columnData)
			throws SwtException {
		// Holds the total data
		StringBuffer sbResultTotalData = null;
		// Declares the subNodes object that gets the child nodes from the
		// rowData
		NodeList subNodes = null;
		int columnLenght = 0;
		int rowLenght = 0;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getTotalData] Begins");
			// Initializes resultTotalData to build totals data
			sbResultTotalData = new StringBuffer();
			rowLenght= totalData.getLength();
			columnLenght = 	columnData.getLength();
			
			// Iterates total and column nodes and get values for matched nodes
			for (int totalCount = 0; totalCount < rowLenght; totalCount++) {
				subNodes = totalData.item(totalCount).getChildNodes();
				int subNodesLenght = subNodes.getLength();
				/**
				 * Start:Modified for Mantis 1624 by RK on 14-Dec-2011
				 */
				if (subNodes != null && subNodes.item(0) != null) {
					for (int colSpanCount = 0; colSpanCount < (columnLenght - subNodesLenght); colSpanCount++) {
						if(subNodes.item(0).getFirstChild() != null)
						sbResultTotalData.append("\" \",");
					}
					// Add totals value
					for (int colCount = 0; colCount < columnLenght; colCount++) {
						for (int totalChildCount = 0; totalChildCount < subNodesLenght; totalChildCount++) {
							if (subNodes.item(totalChildCount) != null) {

								// If field matches, then get value (total data)
								if (columnData.item(colCount).getAttributes()
										.getNamedItem("dataelement")
										.getNodeValue().equalsIgnoreCase(
												subNodes.item(totalChildCount)
														.getNodeName())) {
									sbResultTotalData.append("\"");
									if ((subNodes.item(totalChildCount))
											.getFirstChild() != null) {
									
											sbResultTotalData.append((subNodes
												.item(totalChildCount))
												.getFirstChild().getNodeValue()
												.replaceAll("\"", "\'").replaceAll("\n", ""));
										
										
									}
									sbResultTotalData.append("\",");
									break;
								}
							}
						}
					}
				}
				/**
				 * End:Modified for Mantis 1624 by RK on 14-Dec-2011
				 */
				
			}
			sbResultTotalData.append("\n");
			return sbResultTotalData.toString();
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getTotalData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getTotalData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getTotalData] Ends");
		}
	}
}