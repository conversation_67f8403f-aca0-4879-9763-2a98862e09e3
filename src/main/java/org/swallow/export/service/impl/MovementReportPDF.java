/**
 * @(#)MovementReportPDF.java / 1.0 / 4 Jan 2010 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.awt.Color;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.model.Movement;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.lowagie.text.Chunk;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;

/**
 * MovementReportPDF.java
 * 
 * This class is used to generate movement summary report in PDF format
 * 
 * <AUTHOR> R
 * @version Predict / 31 Dec 2009
 */
public class MovementReportPDF extends MovementReport {

	// Logger object
	private Log log = LogFactory.getLog(this.getClass());

	/**
	 * This method is used to generate movement summary report in PDF format
	 * 
	 * @param columnData -
	 *            Column data list
	 * @param filterData -
	 *            Filter data list
	 * @param rowData -
	 *            Movement data list
	 * @param screenName -
	 *            Name of the screen
	 * @throws SwtException
	 */
	public void generateReport(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<Movement> rowData,
			String screenName) throws SwtException {
		/* Local variable declaration. */
		// PDF document
		Document document = null;
		/* Variable Declaration for whiteRow. */
		Color whiteRow = null;
		/* Variable Declaration for blueRow. */
		Color blueRow = null;
		/* Variable Declaration for headerRow. */
		Color headerRow = null;
		/* Variable Declaration for table. */
		PdfPTable table = null;
		/* Variable Declaration for movement. */
		Movement movement = null;
		/* Variable Declaration for CommonDataManager */
		CommonDataManager CDM = null;
		try {
			// log debug message
			log.debug("Movement PDF generation - Entry");
			// Colours used in the making of the table
			whiteRow = new Color(255, 255, 255);
			blueRow = new Color(224, 240, 255);
			headerRow = new Color(24, 126, 231);

			// Based on no of column initialize the pdf document
			if (columnData.size() > 10) {
				document = new Document(PageSize.A2.rotate(), 10, 10, 10, 10);
			} else {
				document = new Document(PageSize.A4.rotate(), 10, 10, 10, 10);
			}
			// Initialize PDF writer
			PdfWriter.getInstance(document, _outputStream);
			document.open();

			// log debug message
			log.debug("Add title");
			// Report title
			Paragraph pTitle = new Paragraph(new Chunk(screenName + " - "
					+ SwtUtil.getSystemDatewithTime(), FontFactory.getFont(
					FontFactory.HELVETICA, 10)));
			// Add title
			document.add(pTitle);
			// log debug message
			log.debug("Add filter data");
			// Add filter data
			for (int i = 0; i < filterData.size(); i++) {
				Paragraph pFilter = new Paragraph(new Chunk(filterData.get(i)
						.getName()
						+ " : ", FontFactory.getFont(
						FontFactory.HELVETICA_BOLD, 10)));
				pFilter.add(new Chunk(filterData.get(i).getValue() + "\n",
						FontFactory.getFont(FontFactory.HELVETICA, 10)));
				document.add(pFilter);
			}
			// Add new line
			Paragraph p2 = new Paragraph(new Chunk("\n", FontFactory.getFont(
					FontFactory.HELVETICA, 10)));
			document.add(p2);

			// Create the table and initial params
			if (columnData != null) {
				// Initialise a table
				table = new PdfPTable(columnData.size());
				// Set width
				table.setWidthPercentage(100);
				table.getDefaultCell().setPaddingBottom(5);
				// Set no of header rows
				table.setHeaderRows(1);

				// log debug message
				log.debug("Populate grid header");

				// Populate Headers
				table.getDefaultCell().setBackgroundColor(headerRow);
				table.getDefaultCell().setHorizontalAlignment(
						Element.ALIGN_CENTER);
				for (int i = 0; i < columnData.size(); i++) {
					table.addCell(new Phrase(""
							+ columnData.get(i).getHeading(), FontFactory
							.getFont(FontFactory.HELVETICA, 12, Font.NORMAL,
									Color.WHITE)));
				}
				// log debug message
				log.debug("Populate grid data");
				// Font for grid data
				Font fontData = FontFactory.getFont(FontFactory.HELVETICA, 10,
						Font.NORMAL, Color.BLACK);

				CDM = (CommonDataManager)UserThreadLocalHolder.getUserSession().getAttribute("CDM");
				// Populate the rows of the grid
				for (int i = 0; i < rowData.size(); i++) {
					if(CDM != null) {
						if(!SwtUtil.isEmptyOrNull(CDM.getCancelMSDExport()) && "true".equals(CDM.getCancelMSDExport())) {
							throw new SwtException("generatedException");
						}
					}
					// Get movement record
					movement = rowData.get(i);
					// Get current colour (alternate row colours)
					Color currentColor = (i % 2 == 1) ? blueRow : whiteRow;
					// Set background cell colour
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Position level
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getPositionLevelName()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Entity
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getId().getEntityId()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Value date
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getValueDateAsString()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Amount
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_RIGHT);
					table.addCell(new Phrase(cleanString(movement
							.getAmountAsString()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Sign
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_CENTER);
					table.addCell(new Phrase(cleanString(movement.getSign()),
							fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Currency
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getCurrencyCode()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					
					// Predict status
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_CENTER);
					table.addCell(new Phrase(cleanString(movement
							.getPredictStatus()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Match Status
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getMatchStatusDesc()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Account id
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getAccountId()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Ref 1
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getReference1()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Ref 2
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getReference2()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Ref 3
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getReference3()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					/*
					 * Start : Code modified by Balaji on 17-Jul-2012 for Mantis
					 * 1998: Movement Summary Display: Add 'Extra Ref' column to
					 * the MSD grid
					 */
					// Extra ref
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getReference4()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);
					
					// UETR
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(
							cleanString(movement.getUetr()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);
					
					
					/*
					 * End: Code modified by Balaji on 17-Jul-2012 for Mantis
					 * 1998: Movement Summary Display: Add 'Extra Ref' column to
					 * the MSD grid
					 */

					// Input date
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_CENTER);
					table.addCell(new Phrase(cleanString(movement
							.getInputDateAsString()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// External Balance status
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_CENTER);
					table.addCell(new Phrase(cleanString(movement
							.getExtBalStatus()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Book code
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(
							cleanString(movement.getBookCode()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Counter Party id
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getCounterPartyId()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Beneficiary id
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getBeneficiaryId()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Custodian id
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getCustodianId()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Matching Party
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getMatchingParty()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Product Type
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getProductType()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Posting date
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_CENTER);
					table.addCell(new Phrase(cleanString(movement
							.getPostingDateAsString()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Match Id
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_RIGHT);
					table.addCell(new Phrase(
							cleanString(movement.getMatchId()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Movement id
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_RIGHT);
					table.addCell(new Phrase(cleanString(movement.getId()
							.getMovementId()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Input Source
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getInputSource()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Message Format
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(cleanString(movement
							.getMessageFormat()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Update date
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_CENTER);
					table.addCell(new Phrase(cleanString(movement
							.getUpdateDateAsString()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);

					// Notes
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(
							cleanString(movement.getHasNotes()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);
					
					// Extra Text1
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_LEFT);
					table.addCell(new Phrase(
							cleanString(movement.getExtraText1()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);
					
					// ILM FCast
					table.getDefaultCell().setHorizontalAlignment(
							Element.ALIGN_CENTER);
					table.addCell(new Phrase(
							cleanString(movement.getIlmFcastStatus()), fontData));
					table.getDefaultCell().setBackgroundColor(currentColor);
					if(!SwtUtil.isEmptyOrNull(movement.getAttributeXml())){			
			        String xmlString = !SwtUtil.isEmptyOrNull(movement.getAttributeXml())? "<columns>"+movement.getAttributeXml()+"</columns>":"";
			        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			        DocumentBuilder builder = factory.newDocumentBuilder();
			        org.w3c.dom.Document document1 = (org.w3c.dom.Document) builder.parse(new ByteArrayInputStream(xmlString.getBytes()));
			        
			        NodeList nodeList = ((org.w3c.dom.Document) document1).getElementsByTagName("*");
			        for (int j = 0; j < nodeList.getLength(); j++) {
			            Node node = nodeList.item(j);
			            if (node.getNodeType() == Node.ELEMENT_NODE) {
			            	org.w3c.dom.Element element = (org.w3c.dom.Element) node;
			                String name = ((Node) element).getNodeName();
			                String value = ((Node) element).getTextContent();			                
			                if(!"columns".equalsIgnoreCase(name)) {
			                table.getDefaultCell().setHorizontalAlignment(
									Element.ALIGN_CENTER);
							table.addCell(new Phrase(
									cleanString(value), fontData));
							table.getDefaultCell().setBackgroundColor(currentColor);
			                }
			            }
			        }
					}
			        

					log.debug("Movement PDF generation - Exit");
				}
				// log debug message
				log.debug("Populated grid successfully");
				// Add table to the document
				document.add(table);
				document.newPage();
			}
			// log debug message
			log.debug("Report generated successfully");
			// Close the document
			document.close();
		} catch (DocumentException ex) {
			// log error message
			log.error("Error while generate movement report [PDF]."
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Error while generate movement report [PDF]."
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug("Movement PDF generation - Exit");
			document = null;
			whiteRow = null;
			blueRow = null;
			headerRow = null;
			table = null;
			movement = null;
		}
	}
}