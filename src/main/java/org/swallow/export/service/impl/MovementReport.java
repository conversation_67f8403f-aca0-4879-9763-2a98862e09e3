/**
 * @(#)MovementReport.java / 1.0 / 5 Jan 2010 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.work.model.Movement;

/**
 * MovementReport.java
 * 
 * This abstract class is used to generate movement summary report.
 * 
 * <AUTHOR> R
 * @version Predict / 5 Jan 2010
 */
public abstract class MovementReport {
	// Logger object
	private Log log = LogFactory.getLog(this.getClass());

	/** Global instance of ByteArrayOutputStream to write report */
	protected ByteArrayOutputStream _outputStream = new ByteArrayOutputStream();

	/**
	 * This abstract method is used to generate movement summary report.
	 * 
	 * @param columnData -
	 *            Column data list
	 * @param filterData -
	 *            Filter data list
	 * @param rowData -
	 *            Movement data list
	 * @param screenName -
	 *            Name of the screen
	 * @throws SwtException
	 */
	public abstract void generateReport(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<Movement> rowData,
			String screenName) throws SwtException;

	/**
	 * Returns report in ByteArrayOutputStream
	 * 
	 * @return ByteArrayOutputStream
	 */
	public ByteArrayOutputStream getReport() {
		return this._outputStream;
	}

	/**
	 * Close and nullify stream
	 */
	public void clearReport() {
		try {
			if (this._outputStream != null) {
				this._outputStream.close();
			}
		} catch (IOException ex) {
			// log error message
			log.error("Error while closing the stream");
		} finally {
			// nullify stream
			this._outputStream = null;
		}
	}

	/**
	 * This method replaces special characters like (&, < and >) with
	 * corresponding html entities
	 * 
	 * @param input
	 * @return String
	 */
	protected String cleanString(String input) {
		if (input == null || input.trim().length() == 0) {
			return "";
		} else {
			String rtn = input;
			// [\x26\x3C\x3E]
			/*Start:Modified for Mantis 1260:Movement Search to replace the message format for Special charector(&,+) by Arumugam on 29-Sep-10 */
			rtn = rtn.replaceAll("\\x26", "&");
			/*End:Modified for Mantis 1260:Movement Search to replace the message format for Special charector(&,+) by Arumugam on 29-Sep-10 */
//			rtn = rtn.replaceAll("\\x3C", "&lt;");
//			rtn = rtn.replaceAll("\\x3E", "&gt;");
			return rtn;
		}
	}

	/**
	 * This method returns empty string if the given value is null, otherwise
	 * converts the given value to string and returns the same
	 * 
	 * @param input
	 * @return String
	 */
	protected String cleanString(Long input) {
		if (input == null) {
			return "";
		} else {
			return String.valueOf(input);
		}
	}
}