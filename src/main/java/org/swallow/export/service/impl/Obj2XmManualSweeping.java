/*
 * @(#)Obj2XmManualSweeping.java 1.0 ,01/08/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.model.ExportObject;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.ManualSweepObject;
import org.swallow.work.model.Movement;
import org.swallow.work.service.SweepDetailVO;

public class Obj2XmManualSweeping extends Obj2XmlImpl {
	
	SystemFormats sysFormat = null;	
	public Obj2XmManualSweeping() {
	
	}
	
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(Obj2XmManualSweeping.class);
	
	/* Used to form the whole data in XML format */
	public String convertObj(ArrayList<ColumnDTO> columnData, ArrayList<FilterDTO> filterData, Collection rowData, Movement acctBDTotal) {
		String rtn = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<data>\n";
		rtn += getColumnData(columnData);		
		rtn += getRowData(rowData);		
		rtn += getTotalDatas(acctBDTotal);
		rtn += getFilterData(filterData);
		rtn += "</data>\n";
		return rtn;
	}
	

	/* Used to get the row data and  form in XML format */
	private String getRowData ( Collection rowData ) {
		String rtn = "<rows>\n";
		
		Iterator iter = rowData.iterator();
		ManualSweepObject manualSweepObject = null;
		while (iter.hasNext()) {
			manualSweepObject = (ManualSweepObject) (iter.next());
			rtn += "<row>\n";
			rtn += "<value>"+manualSweepObject.getId().getValueDateAsString()+"</value>\n"; 
			rtn += "<level>"+manualSweepObject.getDisplayLevel()+"</level>\n";			
			rtn += "<account>"+manualSweepObject.getId().getAccId()+"</account>\n";			
			rtn += "<name>"+manualSweepObject.getAccName()+"</name>\n";			
			if (manualSweepObject.getPredictedBalanceAsString()!=null && Double.valueOf(manualSweepObject.getPredictedBalanceAsString().replace(",","").replace(".", "")) >= 0 )				
			{
			rtn += "<predictedbalance negative=\"false\">"+(manualSweepObject.getPredictedBalanceAsString()==null?"-":manualSweepObject.getPredictedBalanceAsString()+" ")+"</predictedbalance>\n";
			}
			else if (manualSweepObject.getPredictedBalanceAsString()!=null && Double.valueOf(manualSweepObject.getPredictedBalanceAsString().replace(",","").replace(".", "")) < 0 )
			{
				rtn += "<predictedbalance negative=\"true\">"+(manualSweepObject.getPredictedBalanceAsString()==null?"-":manualSweepObject.getPredictedBalanceAsString()+" ")+"</predictedbalance>\n";	
			}
			else
			{
				rtn += "<predictedbalance negative=\"false\">"+(manualSweepObject.getPredictedBalanceAsString()==null?"-":manualSweepObject.getPredictedBalanceAsString()+" ")+"</predictedbalance>\n";
			}			
			if (manualSweepObject.getSweepAmountAsString()!=null && Double.valueOf(manualSweepObject.getSweepAmountAsString().replace(",","").replace(".", "")) >= 0 )				
			{
			rtn += "<sweepamount negative=\"false\">"+(manualSweepObject.getSweepAmountAsString()==null?"-":manualSweepObject.getSweepAmountAsString()+" ")+"</sweepamount>\n";
			}
			else if (manualSweepObject.getSweepAmountAsString()!=null && Double.valueOf(manualSweepObject.getSweepAmountAsString().replace(",","").replace(".", "")) < 0 )
			{
				rtn += "<sweepamount negative=\"true\">"+(manualSweepObject.getSweepAmountAsString()==null?"-":manualSweepObject.getSweepAmountAsString()+" ")+"</sweepamount>\n";	
			}
			else
			{
				rtn += "<sweepamount negative=\"false\">"+(manualSweepObject.getSweepAmountAsString()==null?"-":manualSweepObject.getSweepAmountAsString()+" ")+"</sweepamount>\n";
			}				
			if (manualSweepObject.getTargetBalanceAsString()!=null && Double.valueOf(manualSweepObject.getTargetBalanceAsString().replace(",","").replace(".", "")) >= 0 )				
			{
			rtn += "<targetbalance negative=\"false\">"+(manualSweepObject.getTargetBalanceAsString() == null?"-":manualSweepObject.getTargetBalanceAsString()+" ")+"</targetbalance>\n";
			}
			else if (manualSweepObject.getTargetBalanceAsString() !=null && Double.valueOf(manualSweepObject.getTargetBalanceAsString().replace(",","").replace(".", "")) < 0 )
			{
				rtn += "<targetbalance negative=\"true\">"+(manualSweepObject.getTargetBalanceAsString()==null?"-":manualSweepObject.getTargetBalanceAsString()+" ")+"</targetbalance>\n";	
			}
			else
			{
				rtn += "<targetbalance negative=\"false\">"+(manualSweepObject.getTargetBalanceAsString()==null?"-":manualSweepObject.getTargetBalanceAsString()+" ")+"</targetbalance>\n";
			}				
			rtn += "<cutoff>"+manualSweepObject.getCuttOff()+"</cutoff>\n";			
			rtn += "</row>\n";
		}
		rtn += "</rows>\n";
		return rtn;		
	}
	
	/* Used to get the total data and  form in XML format */
	private String getTotalDatas ( Movement acctBDTotal ) {
		String rtn = "<total>";
		rtn += "</total>\n";
		return rtn;
	}
	
	
	/**
	 * Used to form the whole data in XML format and return as string
	 * 
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @return String
	 * @throws SwtException
	 */
	public ArrayList<ArrayList<ExportObject>> getExportData(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<ManualSweepObject> rowData) throws SwtException {
			/*
			 * To Generate the list of object to export
			 */
			ArrayList<ArrayList<ExportObject>> result = null;
			ArrayList<ExportObject> data  = null;
			ExportObject export  = null;
			
			// To get the default account from list
			ManualSweepObject manualSweepObject = null;
			// To iterate the Default Account list
			int rowIndex;

			try {
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Enter");
				result = new ArrayList();
				// Iterates the rowData and get the default account object
				for (rowIndex = 0; rowIndex < rowData.size(); rowIndex++) {
					
					manualSweepObject = (ManualSweepObject) rowData.get(rowIndex);		
					data = new ArrayList();
					
					// Sets the value tag from value date
					export = new ExportObject();
					export.setValue(manualSweepObject.getId().getValueDateAsString() == null ? "-"
									: manualSweepObject.getId().getValueDateAsString());
					export.setType(columnData.get(0).getType());
					export.setColumnName(columnData.get(0).getDataElement());					
					data.add(export);	
					
					
					// Sets the level tag
					export = new ExportObject();					
					export.setValue(manualSweepObject.getDisplayLevel() == null ? "-"
									: manualSweepObject.getDisplayLevel());
					export.setType(columnData.get(1).getType());
					export.setColumnName(columnData.get(1).getDataElement());
					data.add(export);
					
					
					// Set the account tag from account id
					export = new ExportObject();					
					export.setValue((manualSweepObject.getId().getAccId() == null ? "-"
							: manualSweepObject.getId().getAccId()));
					export.setType(columnData.get(2).getType());
					export.setColumnName(columnData.get(2).getDataElement());
					data.add(export);
					
					// Set the name tag from account name
					export = new ExportObject();					
					export.setValue((manualSweepObject.getAccName() == null ? "-"
							: manualSweepObject.getAccName()));
					export.setType(columnData.get(3).getType());
					export.setColumnName(columnData.get(3).getDataElement());
					data.add(export);
					
					
					// Set the predicted balance tag
					export = new ExportObject();
					export.setValue((manualSweepObject.getPredictedBalanceAsString() == null ? "-"
							: manualSweepObject.getPredictedBalanceAsString()));
					export.setType(columnData.get(4).getType());
					export.setColumnName(columnData.get(4).getDataElement());
					
					if (manualSweepObject.getPredictedBalanceAsString()!=null && Double.valueOf(manualSweepObject.getPredictedBalanceAsString().replace(",","").replace(".", "")) >= 0 )				
			        {
						export.setNegative(false);
						
			        }else if (manualSweepObject.getPredictedBalanceAsString()!=null && Double.valueOf(manualSweepObject.getPredictedBalanceAsString().replace(",","").replace(".", "")) < 0 ){
			        	export.setNegative(true);
			        	
			        }else
			        	export.setNegative(false);
					data.add(export);
					
					
					// Set the sweep amount tag
					export = new ExportObject();
					export.setValue((manualSweepObject.getSweepAmountAsString() == null ? "-"
							: manualSweepObject.getSweepAmountAsString()));
					export.setType(columnData.get(5).getType());
					export.setColumnName(columnData.get(5).getDataElement());
					
					if (manualSweepObject.getSweepAmountAsString()!=null && Double.valueOf(manualSweepObject.getSweepAmountAsString().replace(",","").replace(".", "")) >= 0 )				
					{
						export.setNegative(false);
						
					}else if (manualSweepObject.getSweepAmountAsString()!=null && Double.valueOf(manualSweepObject.getSweepAmountAsString().replace(",","").replace(".", "")) < 0 ){
						export.setNegative(true);
						
					}else
						export.setNegative(false);
					data.add(export);
					
					
					// Set the target balance tag
					export = new ExportObject();
					export.setValue((manualSweepObject.getTargetBalanceAsString() == null ? "-"
							: manualSweepObject.getTargetBalanceAsString()));
					export.setType(columnData.get(6).getType());
					export.setColumnName(columnData.get(6).getDataElement());
					
					if (manualSweepObject.getTargetBalanceAsString()!=null && Double.valueOf(manualSweepObject.getTargetBalanceAsString().replace(",","").replace(".", "")) >= 0 )				
					{
						export.setNegative(false);
						
					}else if (manualSweepObject.getTargetBalanceAsString()!=null && Double.valueOf(manualSweepObject.getTargetBalanceAsString().replace(",","").replace(".", "")) < 0 ){
						export.setNegative(true);
						
					}else
						export.setNegative(false);
					data.add(export);
					
					// Set the cut off tag
					export = new ExportObject();					
					export.setValue((manualSweepObject.getCuttOff() == null ? "-"
							: manualSweepObject.getCuttOff()));
					export.setType(columnData.get(7).getType());
					export.setColumnName(columnData.get(7).getDataElement());
					data.add(export);
					//add row constructed to list
					result.add(data);
					
				}
				// Close the rows xml tag
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Exit");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [getExportData] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
		}
		return result;
	}
}
