/*
 * @(#)InterfaceInterruptManagerImpl.java 1.0 31/05/2007
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.InterfaceInterruptDAO;
import org.swallow.control.model.Notifications;
import org.swallow.control.service.InterfaceInterruptManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

/**
 * This class has methods that are used for Notification purpose.<br>
 * Notification will be done if the difference between the time of the last
 * message was received and current system time exceeds the threshold period.<br>
 * 
 * Modified by Marshal .I<br>
 * Date: 28-July-2011
 * Modified by Kais and Saber <br>
 * Date: 13-March-2012
 */
@Component("InterfaceInterruptManager")
public class InterfaceInterruptManagerImpl implements InterfaceInterruptManager {

	/*
	 * Log instance
	 */
	private final Log log = LogFactory
			.getLog(InterfaceInterruptManagerImpl.class);

	/*
	 * Instance of DAO
	 */
	@Autowired
	private InterfaceInterruptDAO dao;

	/*
	 * Setter method for the DAO for Spring IoC
	 */
	public void setInterfaceInterruptDAO(InterfaceInterruptDAO dao) {
		this.dao = dao;
	}

	/*
	 * Start: Modified for Mantis 1446:"GUI changes in Predict for Smart Input
	 * engine v6" by Marshal on 28-July-2011
	 */
	/**
	 * Manager layer class used for processing the interface statistics.
	 * <li>Checks if the filemanager_lastcommit is greater than the minimum of
	 * start time AND if the filemanager_lastcommit is lesser than the maximum
	 * of end time.</li>
	 * <li> If the previous condition is satisfied, checks if the difference
	 * between today and filemanager_lastcommit time in seconds is greater than
	 * the threshold time.</li>
	 * <li> If both of the above conditions are satisfied entries are added into
	 * P_NOTIFICATIONS</li>
	 * 
	 * @param Map
	 *            statistics
	 * @return boolean
	 */
	public boolean processInterfaceStatistics(Map<Object, Object> statistics, boolean fromPCM) {
		// A set to hold the interface names
		Set<Object> interfaceNames = null;
		// Iterator object
		Iterator itr = null;
		// Date object to hold the filemanager_lastcommit date and time
		Date lastCommit = null;
		// date object to hold minimum start time
		Date startTime = null;
		// date object to hold maximum end time
		Date endTime = null;
		// integer variable to hold threshold time in seconds
		int thresholdTime = 0;
		// long variable to hold time elapsed in seconds
		Long timeElapsed = null;
		// String variable to hold interface name
		String ifaceOrMsg = null;
		// String variable to hold interface name
		String interfaceName = null;
		// Notifications object for insertion in database
		Notifications notification = null;
		// Boolean variable to hold the result
		boolean result = false;
		// Collection object to hold notifications details
		List chkNotiExist = null;
		// Contains the elapsed time, a boolean value (true> interface, false> message) and the interface id
		Object[] elapsedTimeProperties;
		// Boolean to hold true if we iterate with an interface
		Boolean isInterface = false;
		boolean isEmailEnable = false;
		try {
			log.debug(this.getClass().getName()
					+ "- [processInterfaceStatistics] - entry ");
			
			// Get the set of interface names
			interfaceNames = statistics.keySet();

			// Get the iterator for the set
			itr = interfaceNames.iterator();
			// set the object using get bean method then get it here
			dao = interfaceInterruptDAO(fromPCM);
			
			// Delete Message or interface notifications 
			// Form new notification object
			notification = new Notifications();
			// get current host id then set into object
			notification.setHostId(CacheManager.getInstance().getHostId());
			// set Interface name
			notification.setRelationId(null);
			// delete the current interface details from P_NOTIFICATIONS table
			dao.deleteNotifications(notification, fromPCM);
			
			while (itr.hasNext()) {
				// Get the interface name in a string
				ifaceOrMsg = (String) itr.next();
				// Check filemanager_lastcommit date is null of an interface
				if (statistics.get(ifaceOrMsg) != null) {

					// Get the array of the required ifaceOrMsg  
					elapsedTimeProperties = (Object[]) statistics.get(ifaceOrMsg);
					
					// Get the filemanager_lastcommit field for the interface
					// Modified for Mantis 1748 to get time from Calendar Object, by Saber Chebka on 13-03-2012
					timeElapsed = Long.parseLong(elapsedTimeProperties[0].toString()) / 1000;
					// Boolean to be set true if we are with an interface or false if we are with a message
					isInterface = (Boolean) elapsedTimeProperties[1];
					// the interface name 
					interfaceName = (String) elapsedTimeProperties[2];
					
					// get start time for the interface from database
					startTime = dao.getStartAlertTime(ifaceOrMsg, fromPCM);
					// get End time for the interface from database
					endTime = dao.getEndAlertTime(ifaceOrMsg, fromPCM);
					// get threshold time from db of an interface
					thresholdTime = dao.getThresholdTime(ifaceOrMsg, fromPCM)*60;

					/*
					 * Check if current date is greater than the start time And
					 * Current time is lesser than the end time
					 */
					if (!(startTime == null || endTime == null || thresholdTime < 0)) {
						if ((startTime.compareTo(new Date())) == -1
								&& (endTime.compareTo(new Date())) == 1) {
							
							// Form new notification object
							notification = new Notifications();
							// Get current host id then set into object
							notification.setHostId(CacheManager.getInstance().getHostId());
							// Set entity id
							notification.setEntityId("");
							notification.setRelationId(ifaceOrMsg);
							// Set the notification type to interface or message
							if (isInterface)
								notification.setNotificationType(SwtConstants.INTERFACE_NOTIFICATION_TYPE);
							else
								notification.setNotificationType(SwtConstants.MESSAGE_NOTIFICATION_TYPE);
							
							// Check if the time exceeds the threshold else delete the row of a particular interface
							if (thresholdTime < timeElapsed) {
								// get notifications details from db
								chkNotiExist = dao.fetchNotifications(notification, fromPCM);
								// Get string constant message from SwtConstants then replace with interface name
								// like Swift and elapsed time converted into minutes and seconds from seconds
								notification
									.setNotificationMessage((fromPCM ? SwtConstants.INTERFACE_INTERRUPT_ALERT_PCM: SwtConstants.INTERFACE_INTERRUPT_ALERT)
										.replaceAll("\\{1\\}", interfaceName)
										.replaceAll("\\{2\\}", isInterface ? "" : (" (" + ifaceOrMsg + ")"))
										.replaceAll("\\{3\\}", timeElapsed / 60 + " mins"));
									
								// Insert the notifications details if it is then it gets true
								result = dao.insertNotifications(notification, fromPCM);
								isEmailEnable = true;

							} else if (thresholdTime > timeElapsed) {
								// Delete notifications details
								result = dao.deleteNotifications(notification, fromPCM);
								isEmailEnable = true;
							}

							/*
							 * This else block for deleting the particular interface details when end time is less than the
							 * current time or start time is greater than the current time of an interface
							 */
						}
					}
				}
				
			}// End while
			
			/* if a notification has been added or removed , update P_NOTIFICATIONS.email_flag = 'N' 
			 * to add the ability for 'email' job to construct and send required emails
			 */
			if(isEmailEnable){
				dao.enableSendingMails(fromPCM);
			}
			
			log.debug(this.getClass().getName()
					+ " - [processInterfaceStatistics] - Exit ");

		} catch (Exception e) { // Generic Exception block
			result = false;
			log.error(this.getClass().getName()
					+ " - [processInterfaceStatistics] - Exception -"
					+ e.getMessage());
		} finally {// Nullify the objects
			interfaceNames = null;
			itr = null;
			chkNotiExist = null;
			lastCommit = null;
			ifaceOrMsg = null;
			notification = null;
			ifaceOrMsg = null;
			elapsedTimeProperties = null;
		}

		return result;
	}

	/*
	 * End: Modified for Mantis 1446:"GUI changes in Predict for Smart Input
	 * engine v6" by Marshal on 28-July-2011
	 */

	/**
	 * Get time from the string time passed
	 * 
	 * @param String
	 *            time
	 * @return Date
	 */
	private Date getTimeFromString(String time) {
		// Date object for date formation
		Date date = null;
		// Calendar instance
		Calendar calendar = null;

		try {
			log.debug(this.getClass().getName()
					+ " -[getTimeFromString] - Enter");
			// Get calendar instance
			calendar = Calendar.getInstance();
			// Get the today's date
			calendar.setTime(new Date());
			// Set the hour
			calendar.set(Calendar.HOUR, Integer.parseInt(time.substring(0, 2)));
			// Set the minute
			calendar.set(Calendar.MINUTE, Integer
					.parseInt(time.substring(3, 5)));
			// Set the second
			calendar.set(Calendar.SECOND, Integer
					.parseInt(time.substring(6, 8)));

			calendar.set(Calendar.AM_PM, (Integer
					.parseInt(time.substring(0, 2)) <= 24) ? Calendar.AM
					: Calendar.PM);
			// Get the object as date
			date = calendar.getTime();
			log.debug(this.getClass().getName()
					+ " -[getTimeFromString] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getTimeFromString] - Exception -" + e.getMessage());
		} finally {
			// Cleaning unreferenced objects
			calendar = null;
		}

		return date;
	}

	/**
	 * Get the time elapsed that is the difference between time of operation and
	 * filemanager_lastcommit time
	 * 
	 * @param String
	 *            time
	 * @return long
	 */
	private long getTimeElapsed(String time) {
		// Date object to hold today's date
		Date today = null;
		// Date to hold filemanager_lastcommit date
		Date lastReceived = null;
		// Calendar instance
		Calendar calendar = null;

		try {
			log.debug(this.getClass().getName() + " -[getTimeElapsed] - Enter");
			// Today's date
			today = new Date();
			// Get calendar instance
			calendar = Calendar.getInstance();
			// Set the hour
			calendar.set(Calendar.HOUR, Integer.parseInt(time.substring(0, 2)));
			// Set the minute
			calendar.set(Calendar.MINUTE, Integer
					.parseInt(time.substring(3, 5)));
			// Set the second
			calendar.set(Calendar.SECOND, Integer
					.parseInt(time.substring(6, 8)));
			// Set the AM or PM. Warning : If left default, the default is taken
			// as 24 hours conversion

			calendar.set(Calendar.AM_PM, (Integer
					.parseInt(time.substring(0, 2)) <= 24) ? Calendar.AM
					: Calendar.PM);
			// Get the time
			lastReceived = calendar.getTime();

			log.debug(this.getClass().getName() + " -[getTimeElapsed] - Exit");

		} catch (Exception e) {// Generic exception handler
			log.error(this.getClass().getName()
					+ " - [getTimeFromString] - Exception -" + e.getMessage());
		} finally {
			// Cleaning unreferenced objects
			calendar = null;
		}
		return ((today.getTime() - lastReceived.getTime()) / 1000);
		// Return the difference time as seconds in long

	}

	/**
	 * Get the messages from P_NOTIFICATION table to notify the status of the
	 * interfaces
	 * 
	 * @param Object[]
	 *            notificationType
	 * @return Collection
	 */
	public Collection checkInterfaceInterruptionAlertMessages(
			Object[] notificationsType) {
		// reference for collection object
		Collection collNotification = null;
		Collection fetchedNotifications = null;
		Iterator notificationIterator;
		Notifications notification;
		try {
			log.debug(this.getClass().getName()
					+ " -[checkInterfaceInterruptionAlertMessages] - Enter");
			dao = interfaceInterruptDAO(false);
			// call dao to get all notifications for all interfaces
			collNotification = dao.getAllNotifications(notificationsType);

			if (collNotification != null) {
				notificationIterator = collNotification.iterator();
				fetchedNotifications = new ArrayList();
				while (notificationIterator.hasNext()) {
					notification = (Notifications) notificationIterator.next();
					fetchedNotifications.add(notification);
				}
			}

			log.debug(this.getClass().getName()
					+ " -[checkInterfaceInterruptionAlertMessages] - Exit");
		} catch (Exception e) {// Generic exception handler
			log
					.error(this.getClass().getName()
							+ " - [checkInterfaceInterruptionAlertMessages] - Exception -"
							+ e.getMessage());
		} finally {
			// To null the objects
			collNotification = null;
		}

		return fetchedNotifications;
	}
	
	/**
	 * Get the correct DAO object from spring context
	 * @return
	 */
	private InterfaceInterruptDAO interfaceInterruptDAO(boolean fromPCM) {
		if(!fromPCM) {
			return  (InterfaceInterruptDAO) (SwtUtil.getBean("interfaceInterruptDAO"));
		}
		else {
			return (InterfaceInterruptDAO) SwtUtil.getBean("interfaceInterruptDAOPCM");
		}
	}
}