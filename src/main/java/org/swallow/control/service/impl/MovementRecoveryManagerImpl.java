/*
 * Created on May 3, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.MovementRecoveryDAO;
import org.swallow.control.model.MovementRecovery;
import org.swallow.control.service.MovementRecoveryManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.work.model.MovementLock;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Component ("movementRecoveryManager")
public class MovementRecoveryManagerImpl implements MovementRecoveryManager{
	private final Log log = LogFactory.getLog(MovementRecoveryManagerImpl.class);
	@Autowired
	private MovementRecoveryDAO dao;
	
	public void setMovementRecoveryDAO(MovementRecoveryDAO dao)
	{
		this.dao = dao;
	}
	
	public Collection getMovementLockDetails(String hostId,String entityId,SystemInfo systemInfo, SystemFormats systemFormats) throws SwtException
	{
		try{
			log.debug("Entering into the getMovementLockDetails method()");
			log.debug("The hostId for getMovementLockDetails is ==>"+hostId);
			Collection collDatabase = dao.getMovementLockDetails(hostId,null);
			Collection coll = new ArrayList();
			if(entityId!= null && entityId.equals("all"))
			{
				coll = collDatabase;
			}else
			{
				if(collDatabase != null)
				{
					Iterator itrDatabase = collDatabase.iterator();
					while(itrDatabase.hasNext())
					{
						MovementRecovery mr = (MovementRecovery)(itrDatabase.next());
						if(mr.getEntityId()!= null && mr.getEntityId().equals(entityId))
							coll.add(mr);
					}
				}
				
			}
			if(coll != null)
			{
				
				Iterator itr = coll.iterator();
				while(itr.hasNext())
				{
					MovementRecovery movRec = (MovementRecovery)(itr.next());
					if(movRec.getAmount() != null)
						/* START : Code change at Onsight for Defect No: 97 */
						//movRec.setAmountAsString(movRec.getAmount().toString());
						movRec.setAmountAsString(SwtUtil.formatCurrency(
								movRec.getCurrencyCode(),movRec.getAmount()));
					/* END: Code change at Onsight for Defect No: 97 */
					if(movRec.getPositionLevel() !=null)
						movRec.setPositionLevelAsString(movRec.getPositionLevel().toString());
					
					movRec.setValueDateAsString(SwtUtil.formatDate(movRec.getValueDate(),systemFormats.getDateFormatValue()));
					movRec.setInputDateAsString(SwtUtil.formatDate(movRec.getInputDate()
							, systemFormats.getDateFormatValue()));
					
					
					/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
					Collection collMiscParams = (Collection)CacheManager.getInstance().getMiscParams("MATCHSTATUS",entityId);
					
					Iterator itrMiscParams =  collMiscParams.iterator();
					while(itrMiscParams.hasNext()){
						MiscParams mp = (MiscParams)(itrMiscParams.next());
						if(movRec.getStatus() != null || !"".equals(movRec.getStatus()))
							if(movRec.getStatus().equals(mp.getId().getKey2())){						
								/*Start code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
								movRec.setMatchStatusDesc(mp.getParValue());
								/*End code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
								
							}
					}
					/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
					if(movRec.getMatchStatusDesc() != null)
					{
						String matchStatusDesc = movRec.getMatchStatusDesc()+" "+movRec.getInputDateAsString();
						movRec.setMatchStatusDesc(matchStatusDesc);
					}
					
					if(movRec.getUpdateDate() != null)
					{
						String updateDateAsString = SwtUtil.formatDate(movRec.getUpdateDate(),systemFormats.getDateFormatValue());
						SimpleDateFormat  sdf = new SimpleDateFormat("HH:mm");
						String lockingInstant = updateDateAsString + " " + sdf.format(movRec.getUpdateDate());
						log.debug("The lockingInstant is==>"+lockingInstant);					
						movRec.setLockTime(lockingInstant);
					}
					
				}
			}
			log.debug("The MovementLock collection is==>"+coll);
			
			
			log.debug("Exiting from the getMovementLockDetails method()");
			return coll;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getMovementLockDetails",MovementRecoveryManagerImpl.class);

    	}
	}
	
	public void unlockMovement(MovementLock movLock)throws SwtException{
		try{
		log.debug("Entering into the unlockMovement method()");
	    
		dao.unlockMovement(movLock);
		log.debug("Exiting from the unlockMovement method()");	
		
	}catch(Exception e){
		log.debug("Exception in MovementRecoveryManagerImpl.unlockMovement");
		SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
		SwtException swtexp = swtErrorHandler.handleException(e, "unlockMovement",MovementRecoveryManagerImpl.class);
		log.debug("Got The Exception - " + swtexp);
		throw swtexp;
	}
	
	}
	
	

}
