/*
 * @(#)UserProfilesManagerImpl.java 1.0 15/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service.impl;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.service.UserProfilesManager;
import org.swallow.control.dao.UserProfilesDAO;
import org.swallow.control.dao.UserProfilesDtlDAO;
import java.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuItem;
import org.swallow.model.UserProfile;
import org.swallow.model.UserProfileDetail;
@Component("userProfilesManager")
public class UserProfilesManagerImpl implements UserProfilesManager{
	
	private final Log log=LogFactory.getLog(UserProfilesManagerImpl.class);
	@Autowired
	private UserProfilesDAO userprofilesDAO;
	@Autowired
	private UserProfilesDtlDAO userprofilesdtlDAO;
	
	public void setUserprofilesDAO(UserProfilesDAO userprofilesDAO){
		this.userprofilesDAO=userprofilesDAO; 
	}
	
	/**
	 * @param userprofilesdtlDAO
	 */
	public void setUserprofilesdtlDAO(UserProfilesDtlDAO userprofilesdtlDAO){
		this.userprofilesdtlDAO=userprofilesdtlDAO; 
	}
	
	/**
	 * @param hostId
	 * @param userId
	 * @param profileId
	 * @return
	 */
	
	public List fetchDetails(String hostId,String userId,String profileId){
		List userProfilesList=userprofilesDAO.fetchDetails(hostId,userId,profileId);
		return userProfilesList;
	}
	
	/**
	 * @param userprofiles
	 * @param userprofilesdtl
	 * @param i
	 */
	public void saveuserProfileDetails(UserProfile userprofiles,Collection userProfileDetailsColl){
		userprofilesDAO.saveuserProfileDetails(userprofiles);
		if(userProfileDetailsColl != null){
			Iterator itr = userProfileDetailsColl.iterator();
			while(itr.hasNext()){
				UserProfileDetail userProfileDetailObj = (UserProfileDetail)itr.next();
				userprofilesdtlDAO.savewindowdetails(userProfileDetailObj);
			}
		}
	}
	/**
	 * @param userprofiles
	 */
	public void updateuserProfileDetails(UserProfile userprofiles){
		userprofilesDAO.updateuserProfileDetails(userprofiles);
		
	}
	
	/**
	 * @param userprofiles
	 */
	public void updateuserProfileDetails(UserProfile userprofiles,Collection userProfileDetailsColl){
		log.debug("entering 'updateuserProfileDetails' method ");
		userprofilesDAO.updateuserProfileDetails( userprofiles,userProfileDetailsColl);
		log.debug("exiting 'updateuserProfileDetails' method ");
	}
	
	/**
	 * @param userprofiles
	 * @param userprofilesdtl
	 */
	public void deleteuserProfileDetails(UserProfile userprofiles,UserProfileDetail userprofilesdtl){
		userprofilesDAO.deleteUserProfileDetails(userprofiles);
		userprofilesdtlDAO.deletewindowdetails(userprofilesdtl);
	}
	/**
	 * @param hostId
	 * @param userId
	 * @param profileId
	 * @return
	 */
	public List fetchMenuDetails(String hostId,String userId,String profileId){
		List result=userprofilesdtlDAO.fetchMenuDetails(hostId,userId,profileId);
		return result;
		
	}

	/* (non-Javadoc)
	 * @see org.swallow.control.service.UserProfilesManager#getProfileDetails(java.lang.String, java.lang.String, java.lang.String)
	 */
	public List getProfileDetails(String hostId, String userId, String profileId,String roleId) throws SwtException {
		log.debug("entering getProfileDetails method");
		List profileDetails=userprofilesdtlDAO.getProfileDetails(hostId,userId,profileId);
		
		
		/* Start: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 : Should Open number of screens with full/view access on the role and save the profile 28-JUN-2010 */
    	MenuItem menuAccess=null;
    	UserProfileDetail userProfile = new UserProfileDetail();
    	List<UserProfileDetail> userProfileList = new ArrayList<UserProfileDetail>();
		Collection<MenuItem> menuitemids = userprofilesdtlDAO.getMenuList(roleId);		
		Iterator itr1 = profileDetails.iterator();		
		while(itr1.hasNext()){
			userProfile = (UserProfileDetail)itr1.next();
			boolean idFlag = false;
			Iterator itr = menuitemids.iterator();
			while(itr.hasNext()){
				menuAccess = (MenuItem)itr.next();
				if(menuAccess.getItemId().equals(userProfile.getId().getMenuItemId()))
				{
					userProfile.getMenuItem().setAccessId(menuAccess.getAccessId());					
					idFlag=true;
					break;
					}
				}
			if(idFlag)
				userProfileList.add(userProfile);
		}		
		return userProfileList;
		/* End Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 : Should Open number of screens with full/view access on the role and save the profile 28-JUN-2010 */
		
	}
	
}
