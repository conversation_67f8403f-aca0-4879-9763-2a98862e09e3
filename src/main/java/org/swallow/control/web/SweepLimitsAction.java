/*
 * 
 * ( c) Swallow Technology 
 * All Rights Reserved.
 * This software and documentation is the confidential and proprietary
 * information of Perot Systems ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use
 * it only in accordance with the terms of the license agreement you
 * entered into with Perot Systems.
 * Unauthorized reproduction or distribution of this Confidential
 * Information, or any portion of it, may result in severe civil
 * and criminal penalties.
 *
 * Developed by Perot Systems .
 */ 
/*
 * Created on Jan 9, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.web;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.CurrencyGroupAccessGui;
import org.swallow.control.model.SweepLimits;
import org.swallow.control.service.SweepLimitsManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.maintenance.model.Currency;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * This class is used to decide the Sweep limits for the Role 
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/sweepLimits", "/sweepLimits.do"})
public class SweepLimitsAction extends BaseController {
    private static final Map<String, String> viewMap = new HashMap<>();
    static {
        viewMap.put("add", "jsp/control/sweepinglimitsadd");
        viewMap.put("fail", "error");
        viewMap.put("sweepLimit", "jsp/control/sweepinglimits");
    }

    private String getView(String resultName) {
        return viewMap.getOrDefault(resultName, "error");
    }

    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
    public String execute(@RequestParam(value = "method", required = false) String method,
                          HttpServletRequest request, HttpServletResponse response) throws SwtException {
        method = String.valueOf(method);
        switch (method) {
            case "sweepLimit":
                return sweepLimit();
            case "add":
                return add();
            case "change":
                return change();
            case "sweepLimitSave":
                return sweepLimitSave();
            case "update":
                return update();
            case "delete":
                return delete();
            case "view":
                return view();
        }


    return null ;
}


private SweepLimits sweepLimits;
public SweepLimits getSweepLimits() {
    HttpServletRequest request = SwtUtil.getCurrentRequest();
    sweepLimits = RequestObjectMapper.getObjectFromRequest(SweepLimits.class, request, "sweepLimits");
    return sweepLimits;
}

public void setSweepLimits(SweepLimits sweepLimits) {
	this.sweepLimits = sweepLimits;
	HttpServletRequest request = SwtUtil.getCurrentRequest();
	request.setAttribute("sweepLimits", sweepLimits);
}

	@Autowired
	private SweepLimitsManager sweepLimitsMgr = null;
	private final Log log = LogFactory.getLog(SweepLimitsAction.class);
    	
	public void setSweepLimitsManager(SweepLimitsManager slMgr) 
	{
	        this.sweepLimitsMgr = slMgr;
	}
	
	/**
	 * This function shows the all sweepLimits corresponding to a particular roleId
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String sweepLimit()
	throws SwtException
			{
		String roleMaintenanceOperationFlag = null;
		Collection sessionSweepLimitDetails =null;
		Collection coll= null;
		String isViewRole = null;
		String roleId = null;
		CacheManager cacheManagerInst=null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
				try
				{
					log.debug("Entering 'sweepLimit' Method");
					
					
					//Setting status of the buttons
					setButtonStatus(request,SwtConstants.STR_TRUE,SwtConstants.STR_FALSE,SwtConstants.STR_FALSE,SwtConstants.STR_TRUE,SwtConstants.STR_TRUE);
					//Getting name of the operation
					 roleMaintenanceOperationFlag = (String)request.getSession().getAttribute("roleMaintenanceOperation");
										
					/* If Role Maintenance Operation is add */
					/*Start:Code Modified for Mantis_1410 by Chinna on 5-APR-11-For Avoiding Null Pointer Exception */
					if( !SwtUtil.isEmptyOrNull(roleMaintenanceOperationFlag)&& (roleMaintenanceOperationFlag.equals("add") || roleMaintenanceOperationFlag.equals("copyFrom"))){
						/*End:Code Modified for Mantis_1410 by Chinna on 5-APR-11-For Avoiding Null Pointer Exception */
					  sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");				
					 if(sessionSweepLimitDetails ==  null){
					 	sessionSweepLimitDetails = new ArrayList();
					 }
					 request.setAttribute("sweepLimitDetails",sessionSweepLimitDetails);
					}
					/* If Role Maintenance Operation is change */
					/*Start:Code Modified for Mantis_1410 by Chinna on 5-APR-11-For Avoiding Null Pointer Exception */
					if( !SwtUtil.isEmptyOrNull(roleMaintenanceOperationFlag)&& roleMaintenanceOperationFlag.equals("change")){
						/*End:Code Modified for Mantis_1410 by Chinna on 5-APR-11-For Avoiding Null Pointer Exception */
					
					//First getting roleId for the Role for which Sweep Limit Details are to be fetched
					 roleId = (String)request.getSession().getAttribute("roleIdInSession");
					request.getSession().setAttribute("roleIdInSession",roleId);
					 sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					 if(sessionSweepLimitDetails ==  null){
					 	putSweepLimitDetailsInSession(request,roleId);
					 	sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					 	request.setAttribute("sweepLimitDetails",sessionSweepLimitDetails);
					 	
					 }else{
					 	sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					 	request.setAttribute("sweepLimitDetails",sessionSweepLimitDetails);
					 	
					 }					
					}
					
					//If name of the operation is copyFrom
					/*Start:Code Modified for Mantis_1410 by Chinna on 5-APR-11-For Avoiding Null Pointer Exception */
					 if( !SwtUtil.isEmptyOrNull(roleMaintenanceOperationFlag)&& roleMaintenanceOperationFlag.equals("copyFrom"))
						 /*End:Code Modified for Mantis_1410 by Chinna on 5-APR-11-For Avoiding Null Pointer Exception */
					 {					 	
					 	log.debug("The name of the operation is copyFrom");
					 	 sessionSweepLimitDetails= (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");					 	
					 	request.setAttribute("sweepLimitDetails",sessionSweepLimitDetails);
					 }					 
					 cacheManagerInst = CacheManager.getInstance(); 
					 coll = cacheManagerInst.getCurrencies();
					request.setAttribute("currencyMaster",coll);
					
					//Checking the status of "isViewRole" flag to get visibility of different buttons on the screens
					 isViewRole = request.getParameter("isViewRole");
					if(isViewRole != null && isViewRole.equals("yes")){
						request.setAttribute("isViewRole",isViewRole);
					}								
					log.debug("Exiting 'sweepLimit' Method");		
					return getView("sweepLimit");								
							
				}catch (SwtException swtexp) {
		        	log.error("SwtException Catch in SweepLimitsAction.'sweepLimit' method : " + swtexp.getMessage());
		                SwtUtil.logException (SwtErrorHandler.getInstance().handleException(swtexp,
		                    "sweepLimit", SweepLimitsAction.class), request,"");          
		            return getView("fail");           
		        } catch(Exception e)
				{
		        	log.error("Exception Catch in SweepLimitsAction.'sweepLimit' method : " + e.getMessage());
		            SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e, "sweepLimit", SweepLimitsAction.class), request,"");
		            return getView("fail");
				}
			}
	
	
	/**
	 * This function  directs user to sweepLimitByCurrency.jsp
	 * where user can select the currency and enter the Sweep Limit amount
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String add()
	throws SwtException
			{
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
				try
				{
					log.debug("Entering 'add' Method");
// To remove: 					DynaValidatorForm dyForm = (DynaValidatorForm)form;
					
					CacheManager cacheManagerInst = CacheManager.getInstance(); 
					//Collection coll = cacheManagerInst.getCurrencies();
					String hostId = cacheManagerInst.getHostId();
					Collection coll = getCurrencyDropDown(request, hostId);
					
					request.setAttribute("currencyMaster",coll);
					
					log.debug("Exiting 'add' Method");		
					request.setAttribute("methodName","sweepLimitSave");
					
					return getView("add");								
				}catch (SwtException swtexp) {
		        	log.debug("SwtException Catch in SweepLimitsAction.'add' method : " + swtexp.getMessage());
		            swtexp.printStackTrace();      
		            SwtUtil.logException(swtexp,request,"");          
		            return getView("fail");           
		        } catch(Exception e)
				{
		        	log.debug("Exception Catch in SweepLimitsAction.'add' method : " + e.getMessage());
		            e.printStackTrace();
		            SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e, "add", SweepLimitsAction.class), request,"");
		            return getView("fail");
				}
			}
	
	public String change()
	throws SwtException
			{
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
				try
				{
					log.debug("Entering 'change' Method");
					
// To remove: 					DynaValidatorForm dyForm = (DynaValidatorForm)form;
					SweepLimits sweepLimits = (SweepLimits)getSweepLimits();
					
					//Setting status of different buttons
					request.setAttribute("screenFieldStatus","true");
					
					//Getting the currencyCode of the selected record
					String currencyCode = request.getParameter("selectedCurrencyCode");
					
					if(currencyCode != null)
						currencyCode =currencyCode.trim();
					sweepLimits.getId().setCurrencyCode(currencyCode);
					
					
					//Iterating through the whole temporary collection to get the details of the record selected
					SweepLimits sl = new SweepLimits();
					Collection sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					Iterator itr=sessionSweepLimitDetails.iterator();
					while(itr.hasNext()){
						sl = (SweepLimits)(itr.next());
						//sl.setSweepLimitAsString(SwtUtil.formatCurrency(sl.getId().getCurrencyCode(), sl.getSweepLimit()));
						//sl.setSweepLimitAsStringAnother(SwtUtil.formatCurrency(sl.getId().getCurrencyCode(), sl.getSweepLimit()));
						if(sl.getId().getCurrencyCode().equals(currencyCode.trim()))
							break;
					}					
					setSweepLimits(sl);//Setting the selected record to dynaForm
					request.setAttribute("methodName","update");
					CacheManager cacheManagerInst = CacheManager.getInstance(); 
					String hostId = cacheManagerInst.getHostId();
					Collection coll = getCurrencyDropDown(request, hostId);					
					request.setAttribute("currencyMaster",coll);
					if(coll != null)
					{
						Iterator itrColl = coll.iterator();
						while(itrColl.hasNext())
						{
							LabelValueBean lvb = (LabelValueBean)(itrColl.next());
							if(lvb.getValue().equals(currencyCode))
							{
								request.setAttribute("sweepLimitCurrName",lvb.getLabel());							
								break;
							}
								
						}
					}
					
					log.debug("Exiting 'change' Method");				
					return getView("add");							
				}catch (SwtException swtexp) {
		        	log.debug("SwtException Catch in SweepLimitsAction.'change' method : " + swtexp.getMessage());
		            swtexp.printStackTrace();      
		            SwtUtil.logException(swtexp,request,"");          
		            return getView("fail");           
		        } catch(Exception e)
				{
		        	log.debug("Exception Catch in SweepLimitsAction.'change' method : " + e.getMessage());
		            e.printStackTrace();
		            SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e, "change", SweepLimitsAction.class), request,"");
		            return getView("fail");
				}
			}
	
	/**
	 * This method saves the record added into a temporary collection
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String sweepLimitSave()
	throws SwtException
			{
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
				try
				{
					log.debug("Entering 'sweepLimitSave' Method");
					
// To remove: 					DynaValidatorForm dyForm = (DynaValidatorForm)form;					
					SweepLimits sweepLimits =(SweepLimits)getSweepLimits();					
					
					String currencyCode = sweepLimits.getId().getCurrencyCode();
					BigDecimal tempSweepLimit = SwtUtil.parseCurrencyBig(sweepLimits.getSweepLimitAsStringAnother(), SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat()) ;
					sweepLimits.setSweepLimitAsString(SwtUtil.formatCurrency(sweepLimits.getId().getCurrencyCode(), tempSweepLimit)) ;
					sweepLimits.setSweepLimitAsStringAnother(SwtUtil.formatCurrency(sweepLimits.getId().getCurrencyCode(), tempSweepLimit)) ;
					boolean isPresent = false;
					
					Collection sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					if(sessionSweepLimitDetails == null){
						sessionSweepLimitDetails = new ArrayList();
						sessionSweepLimitDetails.add(sweepLimits);
						request.setAttribute("parentFormRefresh","yes");
												
					}else{
						//Checking if the entry already exists
						Iterator itr=sessionSweepLimitDetails.iterator();
						while(itr.hasNext())
							{
								SweepLimits sl = (SweepLimits)(itr.next());
								if(sl.getId().getCurrencyCode().equals(currencyCode))
								{
									//Adding entries to the sessionSweepLimitDetails
									isPresent=true;
									break;
								}
							}
						if(!isPresent)
						{
							request.setAttribute("parentFormRefresh","yes");
							sessionSweepLimitDetails.add(sweepLimits);
						}else
						{
							request.setAttribute("sweepLimitExiting","yes");
							request.setAttribute("methodName","sweepLimitSave");
						}
					}
					
					request.getSession().setAttribute("sessionSweepLimitDetails",sessionSweepLimitDetails);										
									
					CacheManager cacheManagerInst = CacheManager.getInstance(); 
					String hostId = cacheManagerInst.getHostId();
					Collection coll = getCurrencyDropDown(request, hostId);					
					request.setAttribute("currencyMaster",coll);					
					log.debug("Exiting 'sweepLimitSave' Method");	
					return getView("add");							
				}catch(SwtException swtexp){
					log.debug("SwtException Catch in SweepLimitsAction.'sweepLimitSave' method : " + swtexp.getMessage());
					swtexp.printStackTrace();      
					
					saveErrors(request,SwtUtil.logException(swtexp,request,""));
					return getView("add");
				}catch(Exception e)
				{
					log.debug("Exception Catch in SweepLimitsAction.'sweepLimitSave' method : " + e.getMessage());
					e.printStackTrace();
					saveErrors(request,SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e,
			                "sweepLimitSave",SweepLimitsAction.class), request,""));
					return getView("fail");
				}
			}

	/**
	 * This method updates the record into the temporary collection
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String update()
	throws SwtException
			{
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
				try
				{
					log.debug("Entering 'update' Method");
					
// To remove: 					DynaValidatorForm dyForm = (DynaValidatorForm)form;					
					SweepLimits sweepLimits =(SweepLimits)getSweepLimits();
										
					String currencyCode = sweepLimits.getId().getCurrencyCode();
									
					Collection sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					if(sessionSweepLimitDetails != null)
					{
						
						//Finding the entry to be updated
						Iterator itr=sessionSweepLimitDetails.iterator();
						while(itr.hasNext())
						{
							SweepLimits sl = (SweepLimits)(itr.next());
							if(sl.getId().getCurrencyCode().equals(currencyCode))
							{
								BigDecimal tempSweepLimit = SwtUtil.parseCurrencyBig(sweepLimits.getSweepLimitAsStringAnother(), SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat()) ;
								sl.setSweepLimitAsString(SwtUtil.formatCurrency(sl.getId().getCurrencyCode(), tempSweepLimit)) ;
								sl.setSweepLimitAsStringAnother(SwtUtil.formatCurrency(sl.getId().getCurrencyCode(), tempSweepLimit)) ;
							     break;
							}
						}
					}
					
					request.getSession().setAttribute("sessionSweepLimitDetails",sessionSweepLimitDetails);					
					
					request.setAttribute("parentFormRefresh","yes");					
					ArrayList currencyMaster= new ArrayList();
					request.setAttribute("currencyMaster",currencyMaster);
					log.debug("Exiting 'update' Method");	
					return getView("add");							
				}catch(SwtException swtexp){
					log.debug("SwtException Catch in SweepLimitsAction.'update' method : " + swtexp.getMessage());
					swtexp.printStackTrace(); 
					saveErrors(request,SwtUtil.logException(swtexp,request,""));
					return getView("add");
				}catch(Exception e)
				{
					log.debug("Exception Catch in SweepLimitsAction.'update' method : " + e.getMessage());
					e.printStackTrace();
					saveErrors(request,SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e,
			                "update",SweepLimitsAction.class), request,""));
					return getView("fail");
				}
			}
/**
 * Deletes the record from the temporary collection
 * @param mapping
 * @param form
 * @param request
 * @param response
 * @return
 * @throws Exception
 */	
	
	public String delete()
	throws SwtException
			{
			HttpServletRequest request = SwtUtil.getCurrentRequest();
			HttpServletResponse response = SwtUtil.getCurrentResponse();	
				try
				{
					log.debug("Entering 'delete' Method");
					
// To remove: 					DynaValidatorForm dyForm = (DynaValidatorForm)form;					
					SweepLimits sweepLimits =(SweepLimits)getSweepLimits();
					
					String currencyCode = request.getParameter("selectedCurrencyCode");
					log.debug("The currencyCode in delete method in variable selectedCurrencyCode is"+currencyCode);
					
					Collection tempDetails = new ArrayList();
					Collection sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					if(sessionSweepLimitDetails != null)
					{						
						log.debug("The sessionSweepLimitDetails is not null");
						//Finding the entry to be deleted
						Iterator itr = sessionSweepLimitDetails.iterator();
						while(itr.hasNext())
						{
							    log.debug("The iterator has an object");
								SweepLimits sl = (SweepLimits)(itr.next());
								log.debug("The object in the iterator is => " + sl);
								log.debug("sl.getId().getCurrencyCode()>" + sl.getId().getCurrencyCode() + "<currencyCode>" + currencyCode); 
								if(sl.getId().getCurrencyCode().equals(currencyCode.trim()))
								{
									log.debug("The entry exists");
								}else
									tempDetails.add(sl);
						}
					  }
					
					request.getSession().setAttribute("sessionSweepLimitDetails",tempDetails);
					log.debug("The sessionSweepLimitDetails after deletion of the selected entry is=>"+tempDetails);
					
					request.setAttribute("parentFormRefresh","yes");					
					ArrayList currencyMaster= new ArrayList();
					request.setAttribute("currencyMaster",currencyMaster);
					log.debug("Exiting 'delete' Method");	
					return sweepLimit();								
				}catch(SwtException swtexp){
					log.debug("SwtException Catch in SweepLimitsAction.'delete' method : " + swtexp.getMessage());
					swtexp.printStackTrace(); 
					saveErrors(request,SwtUtil.logException(swtexp,request,""));
					return sweepLimit();
				}catch(Exception e)
				{
					log.debug("Exception Catch in SweepLimitsAction.'delete' method : " + e.getMessage());
					e.printStackTrace();
					saveErrors(request,SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e,
			                "delete",SweepLimitsAction.class), request,""));
					return getView("fail");
				}
			}	
/**
 * Method to view the sweepLimit details
 * @param mapping
 * @param form
 * @param request
 * @param response
 * @return
 * @throws Exception
 */	
	
	public String view()
	throws SwtException
			{
			HttpServletRequest request = SwtUtil.getCurrentRequest();
			HttpServletResponse response = SwtUtil.getCurrentResponse();
				try
				{
					log.debug("Entering 'view' Method");
					
// To remove: 					DynaValidatorForm dyForm = (DynaValidatorForm)form;
					SweepLimits sweepLimits = (SweepLimits)getSweepLimits();					
					String currencyCode = request.getParameter("selectedCurrencyCode");				
					currencyCode = currencyCode.trim();
					sweepLimits.getId().setCurrencyCode(currencyCode);					
					SweepLimits sl = new SweepLimits();
					Collection sessionSweepLimitDetails = (Collection)request.getSession().getAttribute("sessionSweepLimitDetails");
					Iterator itr=sessionSweepLimitDetails.iterator();
					while(itr.hasNext()){
						sl = (SweepLimits)(itr.next());
						if(sl.getId().getCurrencyCode().equals(currencyCode))
							break;
					}
					setSweepLimits(sl);
					
					request.setAttribute("methodName","view");
					request.setAttribute("screenFieldStatus","true");
					
					CacheManager cacheManagerInst = CacheManager.getInstance(); 
					String hostId = cacheManagerInst.getHostId();
					Collection coll = getCurrencyDropDown(request, hostId);					
					request.setAttribute("currencyMaster",coll);
					if(coll != null)
					{
						Iterator itrColl = coll.iterator();
						while(itrColl.hasNext())
						{
							LabelValueBean lvb = (LabelValueBean)(itrColl.next());
							if(lvb.getValue().equals(currencyCode))
							{
								request.setAttribute("sweepLimitCurrName",lvb.getLabel());
								log.debug("The sweepLimitCurrCode is "+lvb.getLabel());
								break;
							}
								
						}
					}	
					
					request.setAttribute("viewButtonStatus","view");
					log.debug("Exiting 'view' Method");	
					return getView("add");								
				}catch (SwtException swtexp) {
		        	log.debug("SwtException Catch in SweepLimitsAction.'view' method : " + swtexp.getMessage());
		            swtexp.printStackTrace();      
		            SwtUtil.logException(swtexp,request,"");          
		            return getView("fail");           
		        } catch(Exception e)
				{
		        	log.debug("Exception Catch in SweepLimitsAction.'view' method : " + e.getMessage());
		            e.printStackTrace();
		            SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e, "view", SweepLimitsAction.class), request,"");
		            return getView("fail");
				}
			}
	
	
	/**
	 * This function puts the sweepLimit details corresponding to a particular roleId into the session
	 * @param request
	 * @param roleId
	 */
	void  putSweepLimitDetailsInSession(HttpServletRequest request,String roleId)
	{
	try{	
	
	 log.debug("Entering into 'putSweepLimitDetailsInSession' Method ");
	 
	 //Getting Sweep Limit Details from roleManager
	 SystemInfo systemInfo = new SystemInfo();
	 Collection sessionSweepLimitDetails = sweepLimitsMgr.getSweepLimitsDetails(roleId,systemInfo,SwtUtil.getCurrentSystemFormats(request.getSession())); 
		 
	 //Putting Sweep Limit Details  in Session
	 request.getSession().setAttribute("sessionSweepLimitDetails",sessionSweepLimitDetails);
	 Collection sessionSweepLimitDetailsInitial = (ArrayList) SwtUtil.copy(sessionSweepLimitDetails) ;
	 request.getSession().setAttribute("sessionSweepLimitDetailsInitial",sessionSweepLimitDetailsInitial);		 
	 log.debug("Exiting from 'putSweepLimitDetailsInSession' Method ");	

	}catch(Exception e)
	{
		log.debug("Exception Catch"); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);
		
	}
	}
	
	/**
	 * Function to set status of different buttons on the screen
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @param viewStatus
	 * @param saveStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,String changeStatus, String deleteStatus, String viewStatus ,String saveStatus)
	{
		req.setAttribute(SwtConstants.ADD_BUT_STS,addStatus );
		req.setAttribute(SwtConstants.CHG_BUT_STS ,changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS,deleteStatus );
		req.setAttribute(SwtConstants.SAV_BUT_STS,saveStatus);
		req.setAttribute(SwtConstants.VIEW_BUT_STS,viewStatus);
	}  

	private Collection getCurrencyDropDown(HttpServletRequest request, String hostId) throws SwtException {
		log.debug("Entering inside getCurrencyDropDown() method ");
		Collection currDropDown = new ArrayList();
		Collection ccyGrpAccessList = (Collection) request.getSession().getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST);
		Iterator itr = ccyGrpAccessList.iterator();
		
		Map uniqueColl = new TreeMap(); //Holds only unique element in sorted order
		Set keysColl = null; //contains all keys of the map declared above
		
		while(itr.hasNext()){
			CurrencyGroupAccessGui ccyGrpAccGui = (CurrencyGroupAccessGui) (itr.next());
			if(ccyGrpAccGui.getCcyGrpAccessHTML1().equals("checked")){
				String ccyGrpId = ccyGrpAccGui.getCurrencyGroupId();
				String entityId = ccyGrpAccGui.getEntityId();				
				 Collection coll = sweepLimitsMgr.getCurrencyDetails(hostId, entityId, ccyGrpId);				 
				 if (coll != null) {
				 	Iterator itrColl = coll.iterator();
				 	while (itrColl.hasNext()) {
				 		Currency curr = (Currency) (itrColl.next());				 		
				 		uniqueColl.put(curr.getId().getCurrencyCode(), curr.getCurrencyMaster().getCurrencyName());		 		
				 	}
				 }
			}						
		}		
		if (uniqueColl != null && uniqueColl.size() > 0) {		
			keysColl = uniqueColl.keySet();
		}
		
		if (keysColl != null) {
			Iterator itrKey = keysColl.iterator();
			while (itrKey.hasNext()) {
				String keyRec = new String(""); 
				keyRec = 	(String) (itrKey.next());
				String valRec = (String) uniqueColl.get(keyRec);
				LabelValueBean lvb = new LabelValueBean(valRec, keyRec);
				currDropDown.add(lvb);
				
			}
		}
		
		return currDropDown;
		
	}
}