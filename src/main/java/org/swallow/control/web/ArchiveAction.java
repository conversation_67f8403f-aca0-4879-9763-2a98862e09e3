/*
 * @(#)ArchiveAction.java 1.0 05/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.Archive;
import org.swallow.control.service.ArchiveManager;
import org.swallow.control.service.impl.ArchiveManagerImpl;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Location;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * This is action class for Archive Maintenance screen.This class is used to
 * add/change/delete archive id.To test archive schema
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/archive", "/archive.do"})
public class ArchiveAction  extends BaseController  {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/control/archiveadd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/control/archive");
	}

	private String getView(String resultName) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		if(request.getAttribute("archive") == null) {
			request.setAttribute("archive", new Archive());
		}
		return viewMap.getOrDefault(resultName, "error");
	}


	private Archive archive;
	public Archive getArchive() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		archive = RequestObjectMapper.getObjectFromRequest(Archive.class, request);
		return archive;
	}
	public void setArchive(Archive archive) {
		this.archive = archive;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("archive", archive);
	}
	@Autowired
	private ArchiveManager archiveManager;
	public String archivehstIp;
	public String archiveHst;
	public String archivehstPort;
	public String archPasswrd;
	public String archUser;

	private final static Log log = LogFactory.getLog(ArchiveAction.class);

	/**
	 * @param archiveManager
	 *            The archiveManager to set.
	 */
	public void setArchiveManager(ArchiveManager archiveManager) {
		this.archiveManager = archiveManager;
	}

	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putHostIdListInReq' method");
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		log.debug("exiting 'putHostIdListInReq' method");
		return hostId;

	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "testConnection":
				return testConnection();
			case "display":
				return display();
			case "add":
				return add();
			case "change":
				return change();
			case "update":
				return update();
			case "save":
				return save();
			case "delete":
				return delete();
		}


		return unspecified();
	}
	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug("entering 'unspecified' method");
		log.debug("exiting 'unspecified' method");
		String hostId = putHostIdListInReq(request);
		Collection collArchive = archiveManager.getArchiveList(hostId);


		if(!collArchive.isEmpty()) {
			Iterator iterator = collArchive.iterator();

			// while loop
			while (iterator.hasNext()) {
				Archive archive = (Archive) iterator.next();
				if("D".equalsIgnoreCase(archive.getArchiveType())) {
					archive.setArchiveType(SwtUtil.getMessage("archive.typeD", request));
				}else {
					archive.setArchiveType(SwtUtil.getMessage("archive.typeS", request));
				}
			}
		}

		request.setAttribute("archiveColl", collArchive);
		return display();

	}

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {

			log.debug("entering 'display' method");

			String hostId = CacheManager.getInstance().getHostId();
			Collection collArchive = archiveManager.getArchiveList(hostId);

			if(!collArchive.isEmpty()) {
				Iterator iterator = collArchive.iterator();

				// while loop
				while (iterator.hasNext()) {
					Archive archive = (Archive) iterator.next();
					if("D".equalsIgnoreCase(archive.getArchiveType())) {
						archive.setArchiveType(SwtUtil.getMessage("archive.typeD", request));
					}else {
						archive.setArchiveType(SwtUtil.getMessage("archive.typeS", request));
					}
				}
			}

			request.setAttribute("archiveColl", collArchive);
			log.debug("exiting 'display' method");
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			return getView("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [display] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [display] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", ArchiveAction.class), request, "");
			return getView("fail");
		}

	}

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug("Entering the add method");
		request.setAttribute("method", "add");
		Collection moduleColl = new ArrayList();
		moduleColl.add(new LabelValueBean("Predict", "Predict"));
		moduleColl.add(new LabelValueBean("PCM", "PCM"));
		request.setAttribute("module", moduleColl);
		return getView("add");
	}

	/**
	 * This method is to load the change archive id screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// declaration for dyna form
// To remove: 		DynaValidatorForm dyForm = null;
		// declaration for archive
		Archive archive = null;
		// declaration to hold the archive Id
		String archiveId = null;
		// declaration to hold the host id
		String hostId = null;
		// declaration to hold the archive list collection
		Collection<Archive> collArchive = null;
		// declaration for Iterator
		Iterator<Archive> itrArchive = null;
		// declaration for Archive pojo class
		Archive archieveobj = null;
		try {
			log.debug(this.getClass().getName() + "- [change] - Entering ");
			// setting field status in request
			request.setAttribute("screenFieldsStatus", "true");
// To remove: 			dyForm = (DynaValidatorForm) form;
			// get the movement details from form
			archive = (Archive) getArchive();
			// getting archive id from request
			archiveId = request.getParameter("archiveId");
			// getting host id from request
			hostId = putHostIdListInReq(request);
			// set host id and archive id in bean
			archive.getId().setHostId(hostId);
			archive.getId().setArchiveId(archiveId);
			// get the archive details from manager class
			collArchive = archiveManager.getArchiveList(hostId);
			itrArchive = collArchive.iterator();
			// iterating the collection
			while (itrArchive.hasNext()) {
				archieveobj = (Archive) itrArchive.next();
				if (archieveobj.getId().getArchiveId().trim().equalsIgnoreCase(
						archiveId.trim())) {
					archive.setArchiveName(archieveobj.getArchiveName());
					archive.setDb_link(archieveobj.getDb_link());
					archive.setDefaultDb(archieveobj.getDefaultDb());
					archive.setModuleId((archieveobj.getModuleId()));
					archive.setArchiveType((archieveobj.getArchiveType()));
					break;
				}
			}
			// setting dyna form
			setArchive(archive);
			// setting values in request
			request.setAttribute("methodName", "change");
			request.setAttribute("screenStatus", "change");
			request.setAttribute("method", "change");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "change", ArchiveAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify
			archive = null;
			archiveId = null;
			hostId = null;
			collArchive = null;
			itrArchive = null;
			archieveobj = null;
			log.debug(this.getClass().getName() + "- [change] - Exit ");
		}

	}

	/**
	 * This method is used to update the archive id details
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// declaration for dyform
// To remove: 		DynaValidatorForm dyForm = null;
		// declaration for archive
		Archive archive = null;
		// declaration for host id
		String hostId = null;
		// declaration for collection
		Collection<Archive> dbColl = null;

		// declaration for the Iterator
		Iterator<Archive> itrArchive = null;
		// declaration for the Archive class
		Archive archieveobj = null;
		// declaration for defaultDB
		String defaultDB = null;
		try {
			log.debug(this.getClass().getName() + "- [update] - Entering ");
// To remove: 			dyForm = (DynaValidatorForm) form;
			archive = (Archive) getArchive();
			// TODO : populate all the fields that you have to change from the
			// archive object of dyna form.
			// get the host id
			hostId = putHostIdListInReq(request);
			// get the archive id details
			dbColl = archiveManager.getcurrentDbList(hostId, archive.getModuleId());

			archive.getId().setHostId(hostId);
			defaultDB = archive.getDefaultDb();
			// check current archive status.if it is 'Y' update others as 'N'
			if (SwtUtil.isEmptyOrNull(defaultDB)) {
				archive.setDefaultDb("N");
			} else {
				itrArchive = dbColl.iterator();
				while (itrArchive.hasNext()) {
					archieveobj = (Archive) itrArchive.next();
					if (archieveobj.getDefaultDb().equalsIgnoreCase("Y")) {
						archieveobj.setDefaultDb("N");
						archiveManager.updateArchive(archieveobj);
					}
				}
			}
			// updating archive
			archiveManager.updateArchive(archive);
			request.setAttribute("parentFormRefresh", "yes");

			return getView("add");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch - swtexp.getErrorCode() - "
					+ swtexp.getErrorCode() + "swtexp.getErrorLogFlag() - "
					+ swtexp.getErrorLogFlag());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "display",
							ArchiveAction.class), request, ""));
			return getView("Success ");
		} finally {
			// nullify
			archive = null;
			hostId = null;
			dbColl = null;
			itrArchive = null;
			archieveobj = null;
			defaultDB = null;
			log.debug(this.getClass().getName() + "- [update] - Entering ");
		}
	}

	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */
	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to test the connection of the DB Link to which user
	 * connecting
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	public String testConnection()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// connection flag used to determine the connection
		boolean connFlag;
		// declared hold the db_link from the request
		String dbLink = null;
		String moduleId = null;
		String archiveType = null;
		try {
			connFlag = false;
			log.debug(this.getClass().getName()
					+ "- [testConnection] - Entering ");
			// get dblink from request
			dbLink = request.getParameter("dbLink");
			moduleId = request.getParameter("moduleId");
			archiveType = request.getParameter("archiveType");
			// get the connection status flag from manager
			if (dbLink != null) {
				connFlag = archiveManager.testConnection(dbLink, moduleId, archiveType);
			}
			response.getWriter().print(connFlag);
		} catch (SwtException e) {
			log.error("Exception caught in" + this.getClass().getName()
					+ "[testConnection] " + e.getMessage());
			// Re-throwing SwtException
			throw e;
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ "- [testConnection] - Exiting " + ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"testConnection", ArchiveManagerImpl.class);
		} finally {
			// nullify
			dbLink = null;
			log.debug(this.getClass().getName() + "- [testConnection] - Exit ");
		}
		return null;

	}

	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to save Archive details in database
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return Action forward object
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// declaration for Action messages
		ActionMessages errors = null;
		// declaration for dyna form
// To remove: 		DynaValidatorForm dyForm = null;
		// declaration for Archive class
		Archive archive = null;
		// declaration for host id
		String hostId = null;
		// declaration for collection
		Collection<Archive> dbColl = null;
		// declaration for defaultDB
		String defaultDB = null;
		// declaration for integer
		int collSize;
		// declaration for Iterator
		Iterator<Archive> itrArchive = null;
		// declaration for Archive class
		Archive archieveobj = null;
		String moduleId = null;
		try {

			log.debug(this.getClass().getName() + " - [save] - Entry");
			errors = new ActionMessages();
// To remove: 			dyForm = (DynaValidatorForm) form;
			archive = (Archive) getArchive();
			/* Read the host id from swtutil file */
			hostId = SwtUtil.getCurrentHostId();
			/* Fetches database list by calling manager class */
			moduleId = archive.getModuleId();
			dbColl = archiveManager.getcurrentDbList(hostId, moduleId);
			collSize = dbColl.size();
			/* Setting host id and default database using bean class */
			archive.getId().setHostId(hostId);
			defaultDB = archive.getDefaultDb();
			/* Condition to check the collection size */
			if (collSize == 0) {
				/* Condition to check default database null value */
				if (SwtUtil.isEmptyOrNull(defaultDB)) {
					throw new SwtException("errors.archive.add", "N");
				} else {
					/* Condition to check encrypted password has null value */
					archive.setDefaultDb(archive.getDefaultDb());
					archiveManager.saveArchive(archive);
				}
			} else {
				itrArchive = dbColl.iterator();

				if (SwtUtil.isEmptyOrNull(defaultDB)) {
					archive.setDefaultDb("N");
					archiveManager.saveArchive(archive);
				} else if (defaultDB != null && defaultDB.equals("Y")) {
					archiveManager.saveArchive(archive);
					while (itrArchive.hasNext()) {
						archieveobj = (Archive) itrArchive.next();
						if (archieveobj.getDefaultDb().equalsIgnoreCase("Y")) {
							archieveobj.setDefaultDb("N");
							archiveManager.updateArchive(archieveobj);
						}
					}
				}
			}
			request.setAttribute("parentFormRefresh", "yes");

			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			request.setAttribute("method", "add");
			Collection moduleColl = new ArrayList();
			moduleColl.add(new LabelValueBean("Predict", "Predict"));
			moduleColl.add(new LabelValueBean("PCM", "PCM"));
			request.setAttribute("module", moduleColl);
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			return getView("add");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "save",
							ArchiveAction.class), request, ""));
			return getView("Success ");
		} finally {
			// nullify
			errors = null;
			archive = null;
			hostId = null;
			dbColl = null;
			defaultDB = null;
			itrArchive = null;
			archieveobj = null;
			log.debug(this.getClass().getName() + " - [save] - Exit");
		}
	}

	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		Archive archive = new Archive();
		ActionErrors errors = new ActionErrors();

		try {
			log.debug("entering 'delete' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Archive arc = (Archive) getArchive();
			String archiveId = request.getParameter("archiveId");
			String moduleId = request.getParameter("module");
			log.debug("value of archiveId is" + archiveId);
			String defaultDB = request.getParameter("selectedDefaultDb");
			log.debug("Value of Db in delete defaultDB---." + defaultDB);
			String hostId = putHostIdListInReq(request);
			Collection dbColl = archiveManager.getcurrentDbList(hostId, arc.getModuleId());
			int i = dbColl.size();
			arc.getId().setHostId(hostId);
			arc.getId().setArchiveId(archiveId);
			arc.setModuleId(moduleId);

			if (i <= 1) {
				log.debug("Inside delete if 1");
				archiveManager.deleteArchive(arc);

			} else {

				log.debug("Inside delete else 1");

				if (defaultDB != null) {
					log.debug("SSS DEFAULT DB--->" + defaultDB);
					if (defaultDB.trim().equals("Y")) {

						log.debug("THE EXCEPTION IS:");
						throw new SwtException("errors.archive.delete", "N");
					}

					else {
						log.debug("The row is deleted:");
						archiveManager.deleteArchive(arc);

					}
				}

			}

			request.setAttribute("method", "delete");
			request.setAttribute("screenStatus", "delete");
			log.debug("exiting 'delete' method");
			return display();
		} catch (SwtException swtexp) {
			request.setAttribute("method", "display");
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			return getView("Success");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "display",
							ArchiveAction.class), request, ""));
			return getView("Success ");
		}

	}

}