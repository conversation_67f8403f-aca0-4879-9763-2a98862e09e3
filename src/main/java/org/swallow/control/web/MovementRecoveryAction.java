/*
 * @(#)MovementRecoveryAction 03/05/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.web;

import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;

import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.ErrorLog;
import org.swallow.control.model.MovementRecovery;
import org.swallow.control.service.ErrorLogManager;
import org.swallow.control.service.MovementRecoveryManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.swallow.work.model.MovementLock;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.TokenHelper;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * This class issue used to show the locked movements and also to unlock the
 * movements
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/movementrecovery", "/movementrecovery.do"})
public class MovementRecoveryAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/control/movementrecovery");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}



	private MovementRecovery movementrecovery;
	public MovementRecovery getMovementrecovery() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		movementrecovery = RequestObjectMapper.getObjectFromRequest(MovementRecovery.class, request);
		return movementrecovery;
	}

	public void setMovementrecovery(MovementRecovery movementrecovery) {
		this.movementrecovery = movementrecovery;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("movementrecovery", movementrecovery);
	}

	private final Log log = LogFactory.getLog(MovementRecoveryAction.class);

	/* Creating and Setting instance of MovmentRecovery Manager */
	@Autowired
	private MovementRecoveryManager movRecMgr = null;

	public void setMovementRecoveryManager(
			MovementRecoveryManager movementRecoveryManager) {
		this.movRecMgr = movementRecoveryManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "unlockMovement":
				return unlockMovement();
			case "detailsByEntity":
				return detailsByEntity();
		}


		return unspecified();
	}

	/**
	 * This method is called when no method available in the request
	 *
	 * @return findForward
	 * @throws Exception
	 */

	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		/*
		 * Start:Code added for Mantis 1829 by Chinniah on 24-Sep-2012:Unable to
		 * lock the movement which is unlocked through movement recovery screen
		 */
		// String used to hold the token
		String token = null;
		try {
			log.debug(this.getClass().getName() + " - [unspecified] - Entry");
			// get token
			token  = TokenHelper.setToken();
			request.getSession().setAttribute(TokenHelper.TOKEN_NAME_FIELD,	token);
			/*
			 * End:Code added for Mantis 1829 by Chinniah on 24-Sep-2012:Unable
			 * to lock the movement which is unlocked through movement recovery
			 * screen
			 */
			return display();

		} catch (SwtException e) {
			log
					.error("SwtException Catch in MovementAction.'unspecified' method : "
							+ e.getMessage());
			SystemExceptionHandler.logError(e);
			return getView("fail");
		} finally {
			token = null;
			log
					.debug(this.getClass().getName()
							+ " - [unspecified] - Existing");

		}
	}

	/**
	 *
	 * @return
	 * @throws Exception
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug("Entering into the display() method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			String hostId = putHostIdListInReq(request);
			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());

			// Code added to set entity access
			Collection collEntity = SwtUtil.getUserEntityAccessList(request
					.getSession());
			int menuEntityCurrGrpAcess = SwtUtil.getMenuEntityCurrGrpAccess(
					request, entityId, null);
			if (menuEntityCurrGrpAcess == 0) {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			MovementRecovery movRec = new MovementRecovery();
			movRec.setEntityId(entityId);
			setMovementrecovery(movRec);
			SystemInfo systemInfo = new SystemInfo();
			Collection coll = movRecMgr.getMovementLockDetails(hostId,
					entityId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			settingStatusWithUserAppended(request, coll);
			request.setAttribute("movementLockDetails", coll);
			putEntityListInReq(request);
			log.debug("Exiting from display() method");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'display' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("SwtException Catch in MovementAction.'display' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", MovementRecoveryAction.class), request, "");
			return getView("fail");
		}

	}

	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putHostIdListInReq' method");
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		log.debug("exiting 'putHostIdListInReq' method");
		return hostId;

	}

	/**
	 *
	 * @return
	 * @throws Exception
	 */
	public String unlockMovement()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();


		// decalred to get the errors
		ActionMessages errors = null;
		/*
		 * Code added for Mantis 1829 by Chinniah on 24-Sep-2012:Unable to lock
		 * the movement which is unlocked through movement recovery screen
		 */
		// Decalred to store tokens
		String token = null;
		// declated hold Movement Id as string
		String selectedMovementId = null;
		// declared to hold the Dyna Validator Form
// To remove: 		DynaValidatorForm dyForm = null;
		// Declared to hold the object MovementRecovery
		MovementRecovery movRec = null;
		// declared to hold the entityId
		String entityId = null;
		// Declared to hold the object MovementLock
		MovementLock movLock = null;
		// declared to hold the host id
		String hostId = null;
		// declared to hold the Movement Id
		Long movementId = null;
		// declared to hold the list of entity
		Collection<EntityUserAccess> collEntity = null;
		// declared to hold the menuEntityCurrGrpAcess value
		int menuEntityCurrGrpAcess = 0;
		// declared to hold the SystemInfo
		SystemInfo systemInfo = null;
		// Declared to hold the Collection
		Collection<MovementRecovery> collMovemenLock = null;
		// Declared to hold the ErrorLogManager object
		ErrorLogManager errorLogManager = null;
		// Declared to hold the ErrorLog object
		ErrorLog errorLog = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " - [unlockMovement] - Entry");

			errors = new ActionMessages();
			// get Selected Movement id from request
			selectedMovementId = request.getParameter("selectedMovementId");
			// get form
// To remove: 			dyForm = (DynaValidatorForm) form;
			movRec = (MovementRecovery) (getMovementrecovery());
			// get entity id
			entityId = movRec.getEntityId();
			// get Movement Lock object
			movLock = new MovementLock();
			// get host id
			hostId = putHostIdListInReq(request);
			// set values in MovementLock
			movLock.getId().setHostId(hostId);
			movementId = new Long(selectedMovementId);
			movLock.getId().setMovementId(movementId);
			movLock.getId().setEntityId(entityId);
			// validating consecutive submission of request
			/*
			 * Start:Code added for Mantis 1829 by Chinniah on
			 * 24-Sep-2012:Unable to lock the movement which is unlocked through
			 * movement recovery screen
			 */
			if(!TokenHelper.validToken()) {
				return display();
			}
			/*
			 * End:Code added for Mantis 1829 by Chinniah on 24-Sep-2012:Unable
			 * to lock the movement which is unlocked through movement recovery
			 * screen
			 */
			// unlocking movement
			movRecMgr.unlockMovement(movLock);
			// get entity list
			collEntity = SwtUtil.getUserEntityAccessList(request.getSession());
			// get menu and entity access
			menuEntityCurrGrpAcess = SwtUtil.getMenuEntityCurrGrpAccess(
					request, entityId, null);
			if (menuEntityCurrGrpAcess == 0) {

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			// set token in request
			token  = TokenHelper.setToken();
			request.getSession().setAttribute(TokenHelper.TOKEN_NAME_FIELD,	token);
			systemInfo = new SystemInfo();
			// get locked movement details
			collMovemenLock = movRecMgr.getMovementLockDetails(hostId,
					entityId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			request.setAttribute("movementLockDetails", collMovemenLock);
			putEntityListInReq(request);
			settingStatusWithUserAppended(request, collMovemenLock);
			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch - swtexp.getErrorCode() - "
					+ swtexp.getErrorCode() + "swtexp.getErrorLogFlag() - "
					+ swtexp.getErrorLogFlag());
			if (swtexp.getErrorLogFlag().equals("Y")) {
				errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("Success");
		} catch (Exception e) {
			log
					.error("SwtException Catch in MovementAction.'unlockMovement' method : "
							+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "unlockMovement",
							MovementRecoveryAction.class), request, ""));
			return getView("Success ");
		} finally {
			errors = null;
			token = null;
			selectedMovementId = null;
			movRec = null;
			entityId = null;
			movLock = null;
			hostId = null;
			movementId = null;
			collEntity = null;
			systemInfo = null;
			collMovemenLock = null;
			log.debug(this.getClass().getName() + " - [unlockMovement] - Exit");

		}
	}

	/**
	 *
	 * @param request
	 * @throws SwtException
	 */

	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putEntityListInReq' method");
		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
				.getSession());
		request.setAttribute("entities", coll);
		log.debug("exiting 'putEntityListInReq' method");
	}

	/**
	 *
	 * @return
	 * @throws Exception
	 */
	public String detailsByEntity() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug("Entering into the detailsByEntity() method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			MovementRecovery movRec = (MovementRecovery) (getMovementrecovery());
			String entityId = movRec.getEntityId();
			Collection collEntity = SwtUtil.getUserEntityAccessList(request
					.getSession());
			int menuEntityCurrGrpAcess = SwtUtil.getMenuEntityCurrGrpAccess(
					request, entityId, null);
			if (menuEntityCurrGrpAcess == 0) {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			String hostId = putHostIdListInReq(request);
			SystemInfo systemInfo = new SystemInfo();
			Collection coll = movRecMgr.getMovementLockDetails(hostId,
					entityId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			request.setAttribute("movementLockDetails", coll);
			putEntityListInReq(request);
			settingStatusWithUserAppended(request, coll);
			setMovementrecovery(movRec);
			log.debug("Exiting from detailsByEntity() method");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'detailsByEntity' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("SwtException Catch in MovementAction.'detailsByEntity' method : "
							+ e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "detailsByEntity", MovementRecoveryAction.class),
					request, "");
			return getView("fail");
		}
	}

	private void settingStatusWithUserAppended(HttpServletRequest request,
											   Collection coll) throws SwtException {
		log.debug("Entering into the settingStatusWithUserAppended() method");
		String currentUser = SwtUtil.getCurrentUserId(request.getSession());
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				MovementRecovery movRec = (MovementRecovery) (itr.next());
				String matchStatus = movRec.getMatchStatusDesc();
				matchStatus = matchStatus + " " + currentUser;
				movRec.setMatchStatusDesc(matchStatus);
			}
		}
		log.debug("Exiting from settingStatusWithUserAppended() method");

	}
}