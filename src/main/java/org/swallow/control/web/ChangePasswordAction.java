/*
 * @(#)ChangePasswordAction.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.web;

// Servlet Related classes
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;







import org.swallow.control.model.PasswordHistory;
import org.swallow.control.model.Role;
import org.swallow.control.service.ChangePasswordManager;
import org.swallow.control.service.RoleManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.model.User;
import org.swallow.service.LogonManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.StlHash;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.web.LogonBean;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/changepassword", "/changepassword.do"})
public class ChangePasswordAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("successfrmLogon", "pwdchange");
		viewMap.put("fail", "error");
		viewMap.put("mainscreen", "main");
		viewMap.put("success", "jsp/control/changepassword");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	private User user;
	public User getUser() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		user = RequestObjectMapper.getObjectFromRequest(User.class, request);
		return user;
	}

	public void setUser(User user) {
		this.user = user;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("user", user);
	}


	/**
	 * Manager class for this action class provided by Spring framework
	 */
	@Autowired
	private ChangePasswordManager changePasswordManager = null;

	/**
	 * Logger object for logging details into server.log
	 */
	private final Log log = LogFactory.getLog(ChangePasswordAction.class);

	/**
	 * Initiate the manager class for this action class.
	 *
	 * @param changePasswordManager
	 */
	public void setChangePasswordManager(
			ChangePasswordManager changePasswordManager) {
		this.changePasswordManager = changePasswordManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "save":
				return save();
		}


		return unspecified();
	}
	/**
	 * Default method used to called from change password screen.
	 *
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			request.setAttribute("methodName", "save");
			if ((request.getParameter("screen") != null)
					&& (request.getParameter("screen").equals("logon"))) {
				request.setAttribute("deletePwdHst", request
						.getParameter("deletePwdHst"));
				request.setAttribute("screen", "logon");
				return getView("successfrmLogon");
			}
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in ChangePasswordAction.'unspecified' method : "
							+ swtexp.getMessage());
			SwtUtil.logErrorInDatabase(swtexp);
			return getView("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in ChangePasswordAction.'unspecified' method : "
							+ e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "unspecified",
							ChangePasswordAction.class));
			return getView("fail");
		}
		return getView("success");
	}
	/*Start:Code Modified by Alibasha for Mantis 1608 */
	/**
	 * This method used to save the Password change of the user
	 *
	 * @return
	 * @throws Exception
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold errors object
		ActionErrors errors = null;
		// String variable to roleId
		String roleId = null;
		// Variable to hold role object
		Role role = null;
		// Variable to hold userRole object
		Role userRole = null;
		// Collection variable to collRole
		Collection collRole = null;
		// Variable to hold userinsession object
		User userInSession = null;
		// Variable to hold dyForm object
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable to hold user object
		User user = null;
		// String variable to newPwd
		String newPwd = null;
		// String variable to oldpwdEncoded
		String oldPwdEncoded = null;
		// Variable to hold tempUser object
		User tempUser = null;
		// boolean variable to pwdChangeOK
		boolean pwdChangeOK;
		// Variable to hold pwdhis object
		PasswordHistory pwdHis = null;
		// Variable to hold logBean object
		LogonBean logBean = null;
		// Variable to hold userFromCDM object
		User userFromCDM = null;
		// Variable to hold sysParamsManage object
		SysParamsManager sysParamsManager = null;
		// Variable to hold sysParams object
		SysParams sysParams = null;
		// String variable to sysDate
		String sysDate = "";
		// Variable to hold roleManager object
		RoleManager roleManager = null;
		// Variable to hold itrRole object
		Iterator itrRole = null;
		try {
			log.debug(this.getClass().getName() + "- [save] - Entering ");
			// creating the errors object
			errors = new ActionErrors();
			// Setting deletePwdHst in request
			request.setAttribute("deletePwdHst", request
					.getParameter("deletePwdHst"));
			// getting the current user from session
			userInSession = SwtUtil.getCurrentUser(request.getSession());
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
// To remove: 			dyForm = (DynaValidatorForm) form;
			// getting user from the dynaform
			user = (User) getUser();
			// BEGIN: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...
			String userId = SwtUtil.getCurrentUserId(request.getSession());
			String sessionId = request.getSession().getId();

			// Added by Saber Chebka: Keep the key creation out of radar.. (TODO: add obfuscation for this class to prevent reverse engineering)
			String pass = sessionId.substring(0, sessionId.length() > 12 ? 12 : sessionId.length());
			pass+=userId.substring(0,userId.length()>4?4:userId.length());
//			pass = SwtUtil.hash(pass);

			String encryptedPwd = request.getParameter("encpasswd");
			user.setPassword(SwtUtil.decryptCBC(pass, encryptedPwd));

			String encryptedPwd1 = request.getParameter("encpasswd1");
			user.setPassword1(SwtUtil.decryptCBC(pass, encryptedPwd1));

			String encryptedOldPwd = request.getParameter("encoldpasswd");
			String decryptOldPassword = SwtUtil.decryptCBC(pass, encryptedOldPwd);
			// END: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...
			// getting user password
			newPwd = user.getPassword();
			oldPwdEncoded = StlHash.getStlHash(userInSession.getId().getHostId(), userInSession.getId().getUserId(), decryptOldPassword);

			if (!oldPwdEncoded.equals(userInSession.getPassword())) {
				throw new SwtException("errors.password.incorrect", "N");
			}

			tempUser = userInSession;
			// setting updateuser to tempuser

			tempUser.setUpdateUser(tempUser.getId().getUserId());
			// getting password change value from changePasswordManager
			pwdChangeOK = changePasswordManager
					.setNewPassword(tempUser, newPwd);
			if (pwdChangeOK) {
				updateCurrentUser(tempUser, request); // put new password
				// settings closescreen in request
				request.setAttribute("closescreen", "close");
			}
			if (request.getParameter("deletePwdHst") != null
					&& request.getParameter("deletePwdHst").trim()
					.equalsIgnoreCase("y")) {
				// creating the pwdhis object
				pwdHis = new PasswordHistory();
				// setting HostId to pwdhis
				pwdHis.getId().setHostId(tempUser.getId().getHostId());
				// setting UserId to pwdhis
				pwdHis.getId().setUserId(tempUser.getId().getUserId());
				// setting SeqNo to pwdhis
				pwdHis.getId().setSeqNo("0");
				changePasswordManager.deletePasswordHistryObject(pwdHis);
			}

			// If the screen is coming from logon screen
			if ((request.getParameter("screen") != null)
					&& (request.getParameter("screen").equals("logon"))) {
				log.debug("Doing Logon Process After changing Password");
				// creating logBean object
				logBean = new LogonBean();
				// getting currentuser from seesion
				userFromCDM = SwtUtil.getCurrentUser(request.getSession());
				logBean.afterLogonProcess(request, userFromCDM,
						(LogonManager) SwtUtil.getBean("logonManager"));
				// getting sysParamsManager from sysParamsManager bean
				sysParamsManager = (SysParamsManager) SwtUtil
						.getBean("sysParamsManager");
				sysParams = sysParamsManager.getSysParamsDetail(SwtUtil
						.getCurrentHostId());
				if (sysParams.getTestDate() != null) {
					sysDate = sysParams.getTestDate().toString();
				}
				// Setting systemdtestdate in request
				request.setAttribute("sytemTestDate", sysDate);
				roleId = userFromCDM.getRoleId();
				// getting rolemanager from rolemanager bean
				roleManager = (RoleManager) SwtUtil.getBean("roleManager");
				// creating role object
				role = new Role();
				// getting collection role from the roleManager
				collRole = roleManager.getRoleDetails(role);
				itrRole = collRole.iterator();
				// Loop to iterate role
				while (itrRole.hasNext()) {
					userRole = (Role) (itrRole.next());
					if (role.getRoleId() == null
							|| ((role.getRoleId()).equals(userRole.getRoleId()))) {
						// Setting notifyUserFlag in request
						request.setAttribute("notifyUserFlag", userRole
								.getInputInterruption());
					} else {
						// Setting notifyUserFlag in request
						request.setAttribute("notifyUserFlag", SwtConstants.NO);
					}
				}
				return getView("mainscreen");
			}

			if ((request.getParameter("screen") != null)
					&& (request.getParameter("screen").equals("logon"))) {
				// Setting screen in request
				request.setAttribute("screen", "logon");
				return getView("successfrmLogon");
			} else {
				return getView("success");
			}
		} catch (SwtException swtexp) {
			if(!(swtexp.getMessage().equals("errors.password.alphaChars")|| swtexp.getMessage().equals("errors.password.mixedCase")
					|| swtexp.getMessage().equals("errors.password.numbers")|| swtexp.getMessage().equals("errors.password.specialChar") ||
					swtexp.getMessage().equals("errors.password.minLength") ||
					swtexp.getMessage().equals("errors.password.maxLength") || swtexp.getMessage().equals("errors.password.inHistory")))

				SwtUtil.logErrorInDatabase(swtexp);
			request.setAttribute("methodName", "save");
			if (errors != null) {
				errors.add("entityId",
						new ActionMessage("NEIL :: Update method, error code: "
								+ swtexp.getMessage()));
				if (swtexp.getErrorLogFlag() != null
						&& !swtexp.getErrorLogFlag().equalsIgnoreCase("Y")
						&& !swtexp.getErrorLogFlag().equalsIgnoreCase("N"))
					errors.add("entityId", new ActionMessage(swtexp.getErrorCode(),
							swtexp.getErrorLogFlag()));
				else
					errors
							.add("entityId", new ActionMessage(swtexp
									.getErrorCode()));
			}
			saveErrors(request, errors);
			if ((request.getParameter("screen") != null)
					&& (request.getParameter("screen").equals("logon"))) {
				request.setAttribute("screen", "logon");
				return getView("successfrmLogon");
			} else {
				return getView("success");
			}
		} catch (Exception e) {
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "save", ChangePasswordAction.class));
			return getView("fail");
		} finally {
			roleId = null;
			role = null;
			userRole = null;
			collRole = null;
			userInSession = null;
			newPwd = null;
			oldPwdEncoded = null;
			tempUser = null;
			pwdHis = null;
			logBean = null;
			userFromCDM = null;
			sysParamsManager = null;
			sysParams = null;
			roleManager = null;
			itrRole = null;
		}
	}
	/*End:Code Modified by Alibasha for Mantis 1608 */
	/**
	 * This method is used to update the current user in the session with the
	 * values that has been changes vai User Maintenance screeen.
	 *
	 * @param user
	 * @param request
	 * @throws SwtException
	 * <AUTHOR>
	 */
	private void updateCurrentUser(User user, HttpServletRequest request)
			throws SwtException {
		CommonDataManager cdm = (CommonDataManager) request.getSession()
				.getAttribute(SwtConstants.CDM_BEAN);
		User currentUser = cdm.getUser();

		if (user.getId().getUserId().equals(currentUser.getId().getUserId())) {
			// Populate user in memory with new password parameters that have
			// been written to the DB
			currentUser.setPassword(user.getPassword());
			currentUser.setPasswordChangeDate(user.getPasswordChangeDate());
			// Updating the CDM request object for further reference.
			cdm.setUser(currentUser);
			request.setAttribute(SwtConstants.CDM_BEAN, cdm);
		}
	}
}