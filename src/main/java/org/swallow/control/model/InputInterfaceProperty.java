package org.swallow.control.model;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;

public class InputInterfaceProperty extends BaseObject {
	private final Log log = LogFactory.getLog(InputInterfaceProperty.class);
	
	private Id id = new Id ();
	private String value;
	private InputInterface inputInterface;
	
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}
	public InputInterface getInputInterface() {
		return inputInterface;
	}
	public void setInputInterface(InputInterface inputInterface) {
		this.inputInterface = inputInterface;
	}
	
	public static class Id extends BaseObject {
		private String interfaceId;
		private String logicalClass;
		private String name;
		
		public String getInterfaceId() {
			return interfaceId;
		}
		public void setInterfaceId(String interfaceId) {
			this.interfaceId = interfaceId;
		}
		public String getLogicalClass() {
			return logicalClass;
		}
		public void setLogicalClass(String logicalClass) {
			this.logicalClass = logicalClass;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
	}
}
