<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.control.model.SystemLog" table="S_SYSTEM_LOG">
  <!--<composite-id class="org.swallow.control.model.SystemLog$Id" name="id" unsaved-value="any">
   <key-property name="systemSeqNo" access="field" column="SYSTEM_SEQ_NO"/>
  </composite-id>
  -->
  <id name="systemSeqNo" column="SYSTEM_SEQ_NO" type="long">
   <generator class="sequence">
    <param name="sequence_name">S_SYSTEM_LOG_SEQUENCE</param>
   <param name="increment_size">1</param>
   </generator>
  </id>
  <property name="hostId" column="HOST_ID"/>
  <property name="logDate" column="LOG_DATE"/>
  <property name="userId" column="USER_ID"/>
  <property name="ipAddress" column="IP_ADDRESS"/>
  <property name="process" column="PROCESS"/>
  <property name="action" column="ACTION"/>
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
 </class>
</hibernate-mapping>
