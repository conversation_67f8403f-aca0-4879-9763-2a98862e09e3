package org.swallow.control.model;

import org.swallow.work.model.Movement;

import java.util.ArrayList;
import java.util.List;

public class ProcessStatus {
    private int failedCount;
    private int successCount;
    private List<Movement> failedMovements;
    private List<Movement> successfulMovements;
    private int pendingCount;
    private int totalCount;

    public ProcessStatus() {
        this.failedMovements = new ArrayList<>();
        this.successfulMovements = new ArrayList<>();
    }

    public int getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(int failedCount) {
        this.failedCount = failedCount;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public List<Movement> getFailedMovements() {
        return failedMovements;
    }

    public void setFailedMovements(List<Movement> failedMovements) {
        this.failedMovements = failedMovements;
    }

    public List<Movement> getSuccessfulMovements() {
        return successfulMovements;
    }

    public void setSuccessfulMovements(List<Movement> successfulMovements) {
        this.successfulMovements = successfulMovements;
    }

    public void setPendingCount(int pendingCount) {
        this.pendingCount = pendingCount;
    }

    public int getPendingCount() {
        return pendingCount;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
}
