<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.InputInterfaceProperty" table="I_INTERFACE_PROPERTIES">
		<composite-id class="org.swallow.control.model.InputInterfaceProperty$Id" name="id" unsaved-value="any">
		 	 	<key-property name="interfaceId" access="field" column="INTERFACE_ID"/>
		  	 	<key-property name="logicalClass" access="field" column="CLASS"/>
		  	 	<key-property name="name" access="field" column="PROP_NAME"/>
		</composite-id>
		
		<property name="value" column="PROP_VALUE" not-null="false" />
		
		<many-to-one  lazy="false" name="inputInterface" class="org.swallow.control.model.InputInterface" insert="false" update="false" column="INTERFACE_ID" />
    </class>
</hibernate-mapping>
