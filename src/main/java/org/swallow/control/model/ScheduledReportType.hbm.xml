<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScheduledReportType" table="S_SCHEDULED_REPORT_TYPE">
		<id name="reportTypeId" column="REPORT_TYPE_ID">
			<generator class="assigned" />
		</id>
	
		<property name="reportName" column="REPORT_NAME" not-null="false"/>		
		<property name="hostId" column="HOST_ID" not-null="false"/>		
		<property name="jobId" column="JOB_ID" not-null="false"/>		
		<property name="mapDateMethod" column="MAP_DATE_METHOD" not-null="false"/>
		<property name="outputFormat" column="OUTPUT_FORMAT" not-null="false"/>
		<property name="menuItemId" column="MENU_ITEM_ID" not-null="false"/>
					
    </class>
</hibernate-mapping>
