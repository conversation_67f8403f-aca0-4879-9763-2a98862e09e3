<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.Archive" table="P_ARCHIVE">

		<composite-id name="id" class="org.swallow.control.model.Archive$Id" unsaved-value="any">
			 <key-property name="hostId" access="field" column="HOST_ID"/>
			 <key-property name="archiveId" access="field" column="ARCHIVE_ID"/>
		</composite-id>
		<property name="archiveName" column="ARCHIVE_NAME" not-null="true"/>	
		<property name="db_link" column="DBLINK_SCHEMA_NAME" not-null="true"/>
		<property name="defaultDb" column="DEFAULT_DB" not-null="true"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="true"/>
		<property name="updateUser" column="UPDATE_USER" not-null="true"/>		
		<property name="moduleId" column="MODULE_ID" not-null="true"/>		
		<property name="archiveType" column="ARCHIVE_TYPE" not-null="true"/>	
	    </class>
</hibernate-mapping>