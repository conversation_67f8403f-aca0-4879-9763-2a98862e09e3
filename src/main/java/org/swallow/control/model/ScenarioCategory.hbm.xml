<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScenarioCategory" table="P_CATEGORY" >
		<composite-id class="org.swallow.control.model.ScenarioCategory$Id" name="id" unsaved-value="any">
		   <key-property name="categoryid" access="field" column="CATEGORY_ID"/>
		</composite-id>
		<property name="description" column="DESCRIPTION" not-null="false"/>
		<property name="title" column="TITLE" not-null="false"/>
		<property name="systemflag" column="SYSTEM_FLAG" not-null="false"/>
		<property name="displayorder" column="DISPLAY_ORDER" not-null="false"/>
		<property name="displayTab" column="DISPLAY_TAB" not-null="false"/>
    </class>
</hibernate-mapping>