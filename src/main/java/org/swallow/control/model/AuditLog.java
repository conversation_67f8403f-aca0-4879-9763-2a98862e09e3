/*
 * @(#)AuditLog.java 23/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;
import java.text.SimpleDateFormat;
import java.util.Date;



public class AuditLog extends BaseObject implements Comparable {
    private final Log log = LogFactory.getLog(AuditLog.class);
    private Date fromDate;
    private Date toDate;
    private String fromDateAsString;
    private String toDateAsString;
    private String logDate_Date;
    private Date logDate_Time;
    private String hostId;
    private String dropDownUserId;


    private String columnName;
    private String oldValue;
    private String newValue;
    private String ipAddress;
    private Id id = new Id();

    /*    A negative integer if this is less than obj
        Zero if this and obj are equivalent
        A positive integer if this is greater than obj
       */
    public int compareTo(Object obj) {
        log.debug("Calling compareTo");

        int retValue = 0;

        if (obj instanceof AuditLog) {
            AuditLog logObj = (AuditLog) obj;

            if (this.getId().getLogDate().getTime() > logObj.getId().getLogDate()
                                                                .getTime()) {
                retValue = 1;
            } else if (this.getId().getLogDate().getTime() < logObj.getId()
                                                                       .getLogDate()
                                                                       .getTime()) {
                retValue = -1;
            }
        }

        return retValue;
    }

    /**
     * @return Returns the dropDownUserId.
     */
    public String getDropDownUserId() {
        return dropDownUserId;
    }

    /**
     * @param dropDownUserId The dropDownUserId to set.
     */
    public void setDropDownUserId(String dropDownUserId) {
        this.dropDownUserId = dropDownUserId;
    }

    /**
     * @return Returns the fromDate.
     */
    public Date getFromDate() {
        return fromDate;
    }

    /**
     * @param fromDate The fromDate to set.
     */
    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    /**
     * @return Returns the hostId.
     */
    public String getHostId() {
        return hostId;
    }

    /**
     * @param hostId The hostId to set.
     */
    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    /**
     * @return Returns the logDate_Date.
     */
    public String getLogDate_Date() {
        return logDate_Date;
    }

    /**
     * @param logDate_Date The logDate_Date to set.
     */
    public void setLogDate_Date(String logDate_Date) {
        this.logDate_Date = logDate_Date;
    }

    /**
     * @return Returns the logDate_Time.
     */
    public String getLogDate_Time() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");

        return sdf.format(id.getLogDate());
    }

    /**
     * @param errDate_Time The logDate_Time to set.
     */
    public void setLogDate_Time(Date logDate_Time) {
        this.logDate_Time = logDate_Time;
    }

    /**
     * @return Returns the toDate.
     */
    public Date getToDate() {
        return toDate;
    }

    /**
     * @param toDate The toDate to set.
     */
    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    /**
     * @return Returns the fromDateAsString.
     */
    public String getFromDateAsString() {
        return fromDateAsString;
    }

    /**
     * @param fromDateAsString The fromDateAsString to set.
     */
    public void setFromDateAsString(String fromDateAsString) {
        this.fromDateAsString = fromDateAsString;
    }

    /**
     * @return Returns the toDateAsString.
     */
    public String getToDateAsString() {
        return toDateAsString;
    }

    /**
     * @param toDateAsString The toDateAsString to set.
     */
    public void setToDateAsString(String toDateAsString) {
        this.toDateAsString = toDateAsString;
    }

    /**
     * @return Returns the id.
     */
    public Id getId() {
        return id;
    }

    /**
     * @param id The id to set.
     */
    public void setId(Id id) {
        this.id = id;
    }

    public boolean equals(Object o) {
        boolean retVal = true;

        if (o instanceof AuditLog) {
            log.debug("Calling equals");

            AuditLog oLog = (AuditLog) o;

            if ((this.getId().getHostId() != null) &&
                    (oLog.getId().getHostId() != null)) {
                if (!(oLog.getId().getHostId().equals(this.getId().getHostId()))) {
                    retVal = false;
                }
            }

            if ((id.ipAddress != null) &&
                    (oLog.getId().getIpAddress() != null)) {
                if (!(id.ipAddress.equals(oLog.getId().getIpAddress()))) {
                    retVal = false;
                }
            }

            if ((id.userId != null) && (oLog.getId().getUserId() != null)) {
                if (!(id.userId.equals(oLog.getId().getUserId()))) {
                    retVal = false;
                }
            }

            if ((id.reference != null) &&
                    (oLog.getId().getReference() != null)) {
                if (!(id.reference.equals(oLog.getId().getReference()))) {
                    retVal = false;
                }
            }

            if ((id.referenceId != null) &&
                    (oLog.getId().getReferenceId() != null)) {
                if (!(id.referenceId.equals(oLog.getId().getReferenceId()))) {
                    retVal = false;
                }
            }

            /*
            if(this.getId().getHostId() != null && oLog.getId().getHostId() != null && oLog.getId().getHostId().equals(this.getId().getHostId()) &&
               id.ipAddress != null && oLog.getId().getIpAddress() != null && oLog.getId().getIpAddress().equals(id.ipAddress) &&
               id.userId != null && oLog.getId().getUserId() != null && oLog.getId().getUserId().equals(id.userId) &&
               id.reference != null && oLog.getId().getReference() != null && oLog.getId().getReference().equals(id.reference) &&
               id.referenceId != null && oLog.getId().getReferenceId() != null && oLog.getId().getReferenceId().equals(id.referenceId) &&
               id.logDate != null && oLog.getId().getLogDate() != null && oLog.getId().getLogDate().equals(id.logDate)
               )
            {
                retVal = true;
            }*/
        }

        return retVal;
    }

    public int hashCode() {
        log.debug("Calling hashCode");

        int hashCode = 0;

        if (id.hostId != null) {
            hashCode += id.hostId.hashCode();
        }

        if (id.ipAddress != null) {
            hashCode += id.ipAddress.hashCode();
        }

        if (id.userId != null) {
            hashCode += id.userId.hashCode();
        }

        if (id.reference != null) {
            hashCode += id.reference.hashCode();
        }

        if (id.referenceId != null) {
            hashCode += id.referenceId.hashCode();
        }

        if (id.logDate != null) {
            hashCode += id.logDate.hashCode();
        }

        if (id.action != null) {
            hashCode += id.action.hashCode();
        }

        return hashCode;
    }

    public static class Id extends BaseObject {
        private final Log log = LogFactory.getLog(AuditLog.class);
        private String hostId;
        private Date logDate;
        private String userId;

        private String reference;
        private String referenceId;
        private String action;
        private String columnName;
        private String oldValue;
        private String newValue;
        private String ipAddress;
        public Id() {
        }

        public Id(String hostId, Date logDate, String userId, String ipAddress,
            String reference, String referenceId, String columnName,
            String action, String oldValue, String newValue) {
            this.hostId = hostId;
            this.logDate = logDate;
            this.userId = userId;
            this.ipAddress = ipAddress;
            this.reference = reference;
            this.referenceId = referenceId;
            this.columnName = columnName;
            this.action = action;
            this.oldValue = oldValue;
            this.newValue = newValue;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        /**
         * @return Returns the action.
         */
        public String getAction() {
            log.debug("action======>" + action);

            if (action != null) {
                if (action.equalsIgnoreCase("d")) {
                    action = "Deleted";
                } else if ((action.equalsIgnoreCase("c")) ||
                        (action.equalsIgnoreCase("u"))) {
                    action = "Changed";
                } else if (action.equalsIgnoreCase("I")) {
                    action = "Input";
                } else if (action.equalsIgnoreCase("a")) {
                    action = "Added";
                } else if (action.equalsIgnoreCase("m")) {
                    action = "Matched";
                } else if (action.equalsIgnoreCase("n")) {
                    action = "Unmatched";
                } else if (action.equalsIgnoreCase("e")) {
                    action = "Reconciled";
                }
                /* START: Code fixed for DEFECT: Wrong text in movement log, 26-JUL-2007 */
                else if (action.equalsIgnoreCase("T")) {
				    action = "Authorised";
				}
				/* END: Code fixed for DEFECT: Wrong text in movement log, 26-JUL-2007 */
            } else {
                action = "";
            }

            return action;
        }

        /**
         * @param action The action to set.
         */
        public void setAction(String action) {
            this.action = action;
        }

        /**
         * @return Returns the columnName.
         */
        public String getColumnName() {
            return columnName;
        }

        /**
         * @param columnName The columnName to set.
         */
        public void setColumnName(String columnName) {
            this.columnName = columnName;

        }

        /**
         * @return Returns the ipAddress.
         */
        public String getIpAddress() {
            return ipAddress;
        }

        /**
         * @param ipAddress The ipAddress to set.
         */
        public void setIpAddress(String ipAddress) {
            this.ipAddress = ipAddress;
        }

        /**
         * @return Returns the log.
         */
        public Log getLog() {
            return log;
        }

        /**
         * @return Returns the logDate.
         */
        public Date getLogDate() {
            return logDate;
        }

        /**
         * @param logDate The logDate to set.
         */
        public void setLogDate(Date logDate) {
            this.logDate = logDate;
        }

        /**
         * @return Returns the newValue.
         */
        public String getNewValue() {
            return newValue;
        }

        /**
         * @param newValue The newValue to set.
         */
        public void setNewValue(String newValue) {
            this.newValue = newValue;
        }

        /**
         * @return Returns the oldValue.
         */
        public String getOldValue() {
            return oldValue;
        }

        /**
         * @param oldValue The oldValue to set.
         */
        public void setOldValue(String oldValue) {
            this.oldValue = oldValue;
        }

        /**
         * @return Returns the reference.
         */
        public String getReference() {
            return reference;
        }

        /**
         * @param reference The reference to set.
         */
        public void setReference(String reference) {
            this.reference = reference;
        }

        /**
         * @return Returns the referenceId.
         */
        public String getReferenceId() {
            return referenceId;
        }

        /**
         * @param referenceId The referenceId to set.
         */
        public void setReferenceId(String referenceId) {
            this.referenceId = referenceId;
        }

        /**
         * @return Returns the userId.
         */
        public String getUserId() {
            return userId;
        }

        /**
         * @param userId The userId to set.
         */
        /**
         * @return Returns the hostId.
         */
        public String getHostId() {
            return hostId;
        }

        /**
         * @param hostId The hostId to set.
         */
        public void setHostId(String hostId) {
            this.hostId = hostId;
        }
    }


    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
        this.id.columnName  = columnName;
    }

    public String getOldValue() {
        return oldValue;
    }

    public void setOldValue(String oldValue) {
        this.oldValue = oldValue;
        this.id.oldValue  = oldValue;
    }

    public String getNewValue() {
        return newValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
        this.id.newValue  = newValue;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        this.id.ipAddress  = ipAddress;
    }
}
