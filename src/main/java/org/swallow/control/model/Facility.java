/*
 * @(#)Facility.java 1.0 15/11/2012
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *  
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values.
 */
public class Facility extends BaseObject {
	
	private static final long serialVersionUID = 1L;
	private String facilityid;
	private String facilityname;
	private String reftable;
	private String refcolumns;
	private String refparam;
	private String programid;
	private String secmenuitemid;
	private String userselectable;
	private String supportAllCurrency;
	private String supportAllEntity;
	
	
	public String getFacilityid() {
		return facilityid;
	}
	public void setFacilityid(String facilityid) {
		this.facilityid = facilityid;
	}
	public String getFacilityname() {
		return facilityname;
	}
	public void setFacilityname(String facilityname) {
		this.facilityname = facilityname;
	}
	public String getReftable() {
		return reftable;
	}
	public void setReftable(String reftable) {
		this.reftable = reftable;
	}
	public String getRefcolumns() {
		return refcolumns;
	}
	public void setRefcolumns(String refcolumns) {
		this.refcolumns = refcolumns;
	}
	public String getRefparam() {
		return refparam;
	}
	public void setRefparam(String refparam) {
		this.refparam = refparam;
	}
	public String getProgramid() {
		return programid;
	}
	public void setProgramid(String programid) {
		this.programid = programid;
	}
	public String getSecmenuitemid() {
		return secmenuitemid;
	}
	public void setSecmenuitemid(String secmenuitemid) {
		this.secmenuitemid = secmenuitemid;
	}
	public String getUserselectable() {
		return userselectable;
	}
	public void setUserselectable(String userselectable) {
		this.userselectable = userselectable;
	}
	/**
	 * @return the supportAllCurrency
	 */
	public String getSupportAllCurrency() {
		return supportAllCurrency;
	}
	/**
	 * @param supportAllCurrency the supportAllCurrency to set
	 */
	public void setSupportAllCurrency(String supportAllCurrency) {
		this.supportAllCurrency = supportAllCurrency;
	}
	/**
	 * @return the supportAllEntity
	 */
	public String getSupportAllEntity() {
		return supportAllEntity;
	}
	/**
	 * @param supportAllEntity the supportAllEntity to set
	 */
	public void setSupportAllEntity(String supportAllEntity) {
		this.supportAllEntity = supportAllEntity;
	}
	
	

}
