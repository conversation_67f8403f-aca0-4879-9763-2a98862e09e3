/*
 * Created on Jan 24, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;


import java.io.Serializable;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;




/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class Scheduler extends BaseObject implements Serializable, AuditComponent{
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("scheduleId","Schedule Id");
		logTable.put("jobId","job Id");
		logTable.put("startingDate","Starting Date");
		logTable.put("startingTime","Starting Time");
		logTable.put("endingDate","Ending Date");
		logTable.put("endingTime","Ending Time");
		logTable.put("jobType","Job Type");
		logTable.put("durationHours","Duration Hours");
		logTable.put("durationMins","Duration Mins");
		logTable.put("durationSecs","Duration Secs");
		logTable.put("scheduleDay","Schedule Day");
		logTable.put("scheduleDate","Schedule Date");
		logTable.put("scheduleTime","Schedule Time");
		logTable.put("monthFirst","Month First");
		logTable.put("monthLast","Month Last");
		logTable.put("monthDate","Month Date");
		logTable.put("jobStatus","Job Status");
	}
	
	private Integer scheduleId;
	private String hostId;
	private String jobId;
	private Date startingDate;    			// 3STARTING_DATE
	private String startingTime;			// 4STARTING_TIME
	private Date endingDate;				// 5ENDING_DATE
	private String endingTime;				// 6ENDING_TIME
	
	private String startingDateAsString;    			
	//private String startingTimeAsString;				
	private String endingDateAsString;				
	//private String endingTimeAsString;				
	
	private String jobType;					// 7JOB_TYPE
	private Integer durationHours;			// 8DURATION_HOURS
	private String  durationHoursasString;
	private String durationMinsasString;
	private String durationSecsasString ;	
	private Integer durationMins ;			// 9DURATION_MINS
	private Integer durationSecs ;			// 10DURATION_SECS		
	private String scheduleDay;				// 11SCHEDULE_DAY
	private String scheduleDayAll;
	private String scheduleDayMon;
	private String jobStatus;
	private ScheduledReportParams scheduledReportParams;
	public  final static String CLASSNAME = "Scheduler";
	private boolean isClusterUpdate = false; 
	private boolean runAfterChange = true; 
	/**
	 * @return Returns the jobStatus.
	 */
	public String getJobStatus() {
		return jobStatus;
	}
	/**
	 * @param jobStatus The jobStatus to set.
	 */
	public void setJobStatus(String jobStatus) {
		this.jobStatus = jobStatus;
	}
	/**
	 * @return Returns the job.
	 */
	public Job getJob() {
		return job;
	}
	/**
	 * @param job The job to set.
	 */
	public void setJob(Job job) {
		this.job = job;
	}
	private String scheduleDayTue;
	private String scheduleDayWed;
	private String scheduleDayThr;
	private String scheduleDayFri;
	private String scheduleDaySat;
	private Job job = new Job();						// Job Class
	///private String jobDescription;			// JOB_DESCRIPTION
	
	/**
	 * @return Returns the scheduleDaySun.
	 */
	public String getScheduleDaySun() {
		return scheduleDaySun;
	}
	/**
	 * @param scheduleDaySun The scheduleDaySun to set.
	 */
	public void setScheduleDaySun(String scheduleDaySun) {
		this.scheduleDaySun = scheduleDaySun;
	}
	private String scheduleDaySun;
	
	private Date scheduleDate;			    // 12SCHEDULE_DATE
	private String scheduleDateAsString;		
	
	private String scheduleTime;			// 13SCHEDULE_TIME	
	private String filePath;				// 14FILE_PATH
	private String reportNameConv;			// 15REPORT_NAME_CONV
	private String monthFirst;				// 18MONTH_FIRST
	private String monthLast;				// 19MONTH_LAST
	private Integer monthDate;				// 20MONTH_DATE
	private String monthDateAsString;			
	private Date updateDate = new Date();
	private String updateUser;
	
	/**
	 * @return Returns the durationHours.
	 */
	public Integer getDurationHours() {
		return durationHours;
	}
	/**
	 * @param durationHours The durationHours to set.
	 */
	public void setDurationHours(Integer durationHours) {
		this.durationHours = durationHours;
	}
	/**
	 * @return Returns the durationMins.
	 */
	public Integer getDurationMins() {
		return durationMins;
	}
	/**
	 * @param durationMins The durationMins to set.
	 */
	public void setDurationMins(Integer durationMins) {
		this.durationMins = durationMins;
	}
	/**
	 * @return Returns the durationSecs.
	 */
	public Integer getDurationSecs() {
		return durationSecs;
	}
	/**
	 * @param durationSecs The durationSecs to set.
	 */
	public void setDurationSecs(Integer durationSecs) {
		this.durationSecs = durationSecs;
	}
	/**
	 * @return Returns the endingDate.
	 */
	public Date getEndingDate() {
		return endingDate;
	}
	/**
	 * @param endingDate The endingDate to set.
	 */
	public void setEndingDate(Date endingDate) {
		this.endingDate = endingDate;
	}
	/**
	 * @return Returns the endingTime.
	 */
	public String getEndingTime() {
		return endingTime;
	}
	/**
	 * @param endingTime The endingTime to set.
	 */
	public void setEndingTime(String endingTime) {
		this.endingTime = endingTime;
	}
	/**
	 * @return Returns the filePath.
	 */
	public String getFilePath() {
		return filePath;
	}
	/**
	 * @param filePath The filePath to set.
	 */
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}
	/**
	 * @return Returns the jobType.
	 */
	public String getJobType() {
		return jobType;
	}
	/**
	 * @param jobType The jobType to set.
	 */
	public void setJobType(String jobType) {
		this.jobType = jobType;
	}
	/**
	 * @return Returns the monthDate.
	 */
	public Integer getMonthDate() {
		return monthDate;
	}
	/**
	 * @param monthDate The monthDate to set.
	 */
	public void setMonthDate(Integer monthDate) {
		this.monthDate = monthDate;
	}
	/**
	 * @return Returns the monthFirst.
	 */
	public String getMonthFirst() {
		return monthFirst;
	}
	/**
	 * @param monthFirst The monthFirst to set.
	 */
	public void setMonthFirst(String monthFirst) {
		this.monthFirst = monthFirst;
	}
	/**
	 * @return Returns the monthLast.
	 */
	public String getMonthLast() {
		return monthLast;
	}
	/**
	 * @param monthLast The monthLast to set.
	 */
	public void setMonthLast(String monthLast) {
		this.monthLast = monthLast;
	}
	/**
	 * @return Returns the reportNameConv.
	 */
	public String getReportNameConv() {
		return reportNameConv;
	}
	/**
	 * @param reportNameConv The reportNameConv to set.
	 */
	public void setReportNameConv(String reportNameConv) {
		this.reportNameConv = reportNameConv;
	}
	/**
	 * @return Returns the scheduleDate.
	 */
	public Date getScheduleDate() {
		return scheduleDate;
	}
	/**
	 * @param scheduleDate The scheduleDate to set.
	 */
	public void setScheduleDate(Date scheduleDate) {
		this.scheduleDate = scheduleDate;
	}
	/**
	 * @return Returns the scheduleDay.
	 */
	public String getScheduleDay() {
		return scheduleDay;
	}
	/**
	 * @param scheduleDay The scheduleDay to set.
	 */
	public void setScheduleDay(String scheduleDay) {
		this.scheduleDay = scheduleDay;
	}
	/**
	 * @return Returns the scheduleTime.
	 */
	public String getScheduleTime() {
		return scheduleTime;
	}
	/**
	 * @param scheduleTime The scheduleTime to set.
	 */
	public void setScheduleTime(String scheduleTime) {
		this.scheduleTime = scheduleTime;
	}
	/**
	 * @return Returns the startingDate.
	 */
	public Date getStartingDate() {
		return startingDate;
	}
	/**
	 * @param startingDate The startingDate to set.
	 */
	public void setStartingDate(Date startingDate) {
		this.startingDate = startingDate;
	}
	/**
	 * @return Returns the startingTime.
	 */
	public String getStartingTime() {
		return startingTime;
	}
	/**
	 * @param startingTime The startingTime to set.
	 */
	public void setStartingTime(String startingTime) {
		this.startingTime = startingTime;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return Returns the endingDateAsString.
	 */
	public String getEndingDateAsString() {
		return endingDateAsString;
	}
	/**
	 * @param endingDateAsString The endingDateAsString to set.
	 */
	public void setEndingDateAsString(String endingDateAsString) {
		this.endingDateAsString = endingDateAsString;
	}

	/**
	 * @return Returns the scheduleDateAsString.
	 */
	public String getScheduleDateAsString() {
		return scheduleDateAsString;
	}
	/**
	 * @param scheduleDateAsString The scheduleDateAsString to set.
	 */
	public void setScheduleDateAsString(String scheduleDateAsString) {
		this.scheduleDateAsString = scheduleDateAsString;
	}
	/**
	 * @return Returns the startingDateAsString.
	 */
	public String getStartingDateAsString() {
		return startingDateAsString;
	}
	/**
	 * @param startingDateAsString The startingDateAsString to set.
	 */
	public void setStartingDateAsString(String startingDateAsString) {
		this.startingDateAsString = startingDateAsString;
	}

	/**
	 * @return Returns the scheduleDayAll.
	 */
	public String getScheduleDayAll() {
		return scheduleDayAll;
	}
	/**
	 * @param scheduleDayAll The scheduleDayAll to set.
	 */
	public void setScheduleDayAll(String scheduleDayAll) {
		this.scheduleDayAll = scheduleDayAll;
	}
	/**
	 * @return Returns the scheduleDayFri.
	 */
	public String getScheduleDayFri() {
		return scheduleDayFri;
	}
	/**
	 * @param scheduleDayFri The scheduleDayFri to set.
	 */
	public void setScheduleDayFri(String scheduleDayFri) {
		this.scheduleDayFri = scheduleDayFri;
	}
	/**
	 * @return Returns the scheduleDayMon.
	 */
	public String getScheduleDayMon() {
		return scheduleDayMon;
	}
	/**
	 * @param scheduleDayMon The scheduleDayMon to set.
	 */
	public void setScheduleDayMon(String scheduleDayMon) {
		this.scheduleDayMon = scheduleDayMon;
	}
	/**
	 * @return Returns the scheduleDaySat.
	 */
	public String getScheduleDaySat() {
		return scheduleDaySat;
	}
	/**
	 * @param scheduleDaySat The scheduleDaySat to set.
	 */
	public void setScheduleDaySat(String scheduleDaySat) {
		this.scheduleDaySat = scheduleDaySat;
	}
	/**
	 * @return Returns the scheduleDayThr.
	 */
	public String getScheduleDayThr() {
		return scheduleDayThr;
	}
	/**
	 * @param scheduleDayThr The scheduleDayThr to set.
	 */
	public void setScheduleDayThr(String scheduleDayThr) {
		this.scheduleDayThr = scheduleDayThr;
	}
	/**
	 * @return Returns the scheduleDayTue.
	 */
	public String getScheduleDayTue() {
		return scheduleDayTue;
	}
	/**
	 * @param scheduleDayTue The scheduleDayTue to set.
	 */
	public void setScheduleDayTue(String scheduleDayTue) {
		this.scheduleDayTue = scheduleDayTue;
	}
	/**
	 * @return Returns the scheduleDayWed.
	 */
	public String getScheduleDayWed() {
		return scheduleDayWed;
	}
	/**
	 * @param scheduleDayWed The scheduleDayWed to set.
	 */
	public void setScheduleDayWed(String scheduleDayWed) {
		this.scheduleDayWed = scheduleDayWed;
	}
	
	/**
	 * @return Returns the durationHoursasString.
	 */
	public String getDurationHoursasString() {
		return durationHoursasString;
	}
	/**
	 * @param durationHoursasString The durationHoursasString to set.
	 */
	public void setDurationHoursasString(String durationHoursasString) {
		this.durationHoursasString = durationHoursasString;
	}
	/**
	 * @return Returns the durationMinsasString.
	 */
	public String getDurationMinsasString() {
		return durationMinsasString;
	}
	/**
	 * @param durationMinsasString The durationMinsasString to set.
	 */
	public void setDurationMinsasString(String durationMinsasString) {
		this.durationMinsasString = durationMinsasString;
	}
	/**
	 * @return Returns the durationSecsasString.
	 */
	public String getDurationSecsasString() {
		return durationSecsasString;
	}
	/**
	 * @param durationSecsasString The durationSecsasString to set.
	 */
	public void setDurationSecsasString(String durationSecsasString) {
		this.durationSecsasString = durationSecsasString;
	}
	/**
	 * @return Returns the monthDateAsString.
	 */
	public String getMonthDateAsString() {
		return monthDateAsString;
	}
	/**
	 * @param monthDateAsString The monthDateAsString to set.
	 */
	public void setMonthDateAsString(String monthDateAsString) {
		this.monthDateAsString = monthDateAsString;
	}
	/**
	 * @return the scheduleId
	 */
	public Integer getScheduleId() {
		return scheduleId;
	}
	/**
	 * @param scheduleId the scheduleId to set
	 */
	public void setScheduleId(Integer scheduleId) {
		this.scheduleId = scheduleId;
	}
	/**
	 * @return the hostId
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId the hostId to set
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * @return the jobId
	 */
	public String getJobId() {
		return jobId;
	}
	/**
	 * @param jobId the jobId to set
	 */
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	/**
	 * @return the scheduledReportParams
	 */
	public ScheduledReportParams getScheduledReportParams() {
		return scheduledReportParams;
	}
	/**
	 * @param scheduledReportParams the scheduledReportParams to set
	 */
	public void setScheduledReportParams(ScheduledReportParams scheduledReportParams) {
		this.scheduledReportParams = scheduledReportParams;
	}
	public boolean isClusterUpdate() {
		return isClusterUpdate;
	}
	public void setClusterUpdate(boolean isClusterUpdate) {
		this.isClusterUpdate = isClusterUpdate;
	}
	public boolean isRunAfterChange() {
		return runAfterChange;
	}
	public void setRunAfterChange(boolean runAfterChange) {
		this.runAfterChange = runAfterChange;
	}
}
