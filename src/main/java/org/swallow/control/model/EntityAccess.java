/*
 * Created on Dec 30, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class EntityAccess extends BaseObject implements org.swallow.model.AuditComponent{
	
	
	private String accessId;	
	private Date updateDate = new Date();
	private String updateUser;
	private Id id = new Id();
	
	
	
	public static class Id extends BaseObject{
		private String hostId;
		private String roleId;
		private String entityId;

		public Id() {}

		public Id(String hostId, String roleId,String entityId) {
			this.hostId = hostId;
			this.roleId = roleId;
			this.entityId = entityId;
		}
		
			
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the roleId.
		 */
		public String getRoleId() {
			return roleId;
		}
		/**
		 * @param roleId The roleId to set.
		 */
		public void setRoleId(String roleId) {
			this.roleId = roleId;
		}
	}

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("entityId","Entity Id");
		logTable.put("accessId","Access Type");
	}
	
	
	
	/**
	 * @return Returns the accessId.
	 */
	public String getAccessId() {
		return accessId;
	}
	/**
	 * @param accessId The accessId to set.
	 */
	public void setAccessId(String accessId) {
		this.accessId = accessId;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	public void onAdd(){
		String roleId = getId().getRoleId();
		RoleTO roleTO = new RoleTO(roleId);
		Object obj = SwtUtil.getSwtMaintenanceCache().remove(roleTO);
	}
	public void onUpdate(){
		String roleId = getId().getRoleId();
		RoleTO roleTO = new RoleTO(roleId);
		Object obj = SwtUtil.getSwtMaintenanceCache().remove(roleTO);				
	}
	public void onDelete(){
		String roleId = getId().getRoleId();
		RoleTO roleTO = new RoleTO(roleId);
		Object obj = SwtUtil.getSwtMaintenanceCache().remove(roleTO);		
	}
	
}
