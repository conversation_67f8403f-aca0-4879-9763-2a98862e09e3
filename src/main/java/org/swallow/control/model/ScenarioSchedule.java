package org.swallow.control.model;
import java.util.Date;

import org.swallow.model.BaseObject;

public class ScenarioSchedule extends BaseObject  {
	
	/**
	 * 
	 */
	private Long scenScheduleId;
	private String scenarioId;
	private String checkTime;
	private String parameterXml;
    private Date lastRunStarted;
    private Date lastRunEnded;
    private String lastRunStatus;
    
	public Long getScenScheduleId() {
		return scenScheduleId;
	}

	public void setScenScheduleId(Long scenScheduleId) {
		this.scenScheduleId = scenScheduleId;
	}
	
	public String getScenarioId() {
		return scenarioId;
	}
	public void setScenarioId(String scenarioId) {
		this.scenarioId = scenarioId;
	}
	public String getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(String checkTime) {
		this.checkTime = checkTime;
	}

	public String getParameterXml() {
		return parameterXml;
	}
	public void setParameterXml(String parameterXml) {
		this.parameterXml = parameterXml;
	}

	public Date getLastRunStarted() {
		return lastRunStarted;
	}

	public void setLastRunStarted(Date lastRunStarted) {
		this.lastRunStarted = lastRunStarted;
	}

	public Date getLastRunEnded() {
		return lastRunEnded;
	}

	public void setLastRunEnded(Date lastRunEnded) {
		this.lastRunEnded = lastRunEnded;
	}

	public String getLastRunStatus() {
		return lastRunStatus;
	}

	public void setLastRunStatus(String lastRunStatus) {
		this.lastRunStatus = lastRunStatus;
	}

	


}
