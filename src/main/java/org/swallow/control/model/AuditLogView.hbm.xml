<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
 <class name="org.swallow.control.model.AuditLogView" table="VW_MVM_SWEEP_LOG_VIEW">
        

 <composite-id class="org.swallow.control.model.AuditLogView$Id" name="id" unsaved-value="any">
	  <key-property name="logDate" access="field" column="LOG_DATE"/>
	  <key-property name="userId" access="field" column="USER_ID"/>
	  <key-property name="reference" access="field" column="REFERENCE"/>
	  <key-property name="referenceId" access="field" column="REFERENCE_ID"/>
	  <key-property name="action" access="field" column="ACTION"/>
  </composite-id>

 </class>
</hibernate-mapping>