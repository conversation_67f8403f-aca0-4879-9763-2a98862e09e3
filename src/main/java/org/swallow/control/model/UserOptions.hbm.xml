<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.UserOptions" table="S_USERS">
    
    
		<composite-id name="id" class="org.swallow.control.model.UserOptions$Id" unsaved-value="any">
        <key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="userId" access="field" column="USER_ID"/>
		</composite-id>
	
		<property name="username" column="USER_NAME" not-null="true"/>	
		<property name="sectionid" column="SECTION_ID" not-null="true"/>	
		<property name="language" column="LANG" not-null="true"/>	
		<property name="phonenumber" column="PHONE_NUMBER" not-null="false"/>	
		<property name="emailId" column="EMAIL_ADDRESS" not-null="false"/>	
		<property name="roleId" column="ROLE_ID" not-null="true"/>	
		<property name="password" column="USER_PASSWORD" not-null="false"/>	
		<property name="currententity" column="CURRENT_ENTITY" not-null="false"/>	
		<property name="lastlogin" column="LAST_LOGIN" not-null="false"/>	
		<property name="passwordchangedate" column="PASSWD_CHANGE_DATE" not-null="false"/>	
		<property name="lastlogout" column="LAST_LOGOUT" not-null="false"/>	
		<property name="status" column="STATUS" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		<property name="invPassAttempt" column="INV_PASS_ATTEMPT" not-null="false"/>
		<property name="currentCcyGrpId" column="CURRENT_CURRENCY_GROUP_ID" not-null="false"/>	
		<property name="dateFormat" column="DATE_FORMAT" not-null="false"/>	
		<property name="amountDelimiter" column="AMOUNT_DELIMITER" not-null="false"/>	
		<property name="extAuthId" column="EXT_AUTH_ID" not-null="false"/>

<property name="lastLoginFailed" column="LAST_LOGIN_FAILED" not-null="false"/>
		<property name="lastLoginFailedIp" column="LAST_LOGIN_FAILED_IP" not-null="false"/>
		<property name="lastLoginIp" column="LAST_LOGIN_IP" not-null="false"/>


    </class>
</hibernate-mapping>
