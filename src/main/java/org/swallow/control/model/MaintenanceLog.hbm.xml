<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
 <class name="org.swallow.control.model.MaintenanceLog" table="S_MAINTENANCE_LOG">
 
 <!--composite-id class="org.swallow.control.model.MaintenanceLog$Id" name="id" unsaved-value="any">
    <key-property name="mainSeqNo" access="field" column="MAIN_SEQ_NO"/>
 </composite-id -->
	
	<id name="mainSeqNo" type="long" column="MAIN_SEQ_NO">
		<generator class="sequence">
			<param name="sequence_name">S_MAINTENANCE_LOG_SEQUENCE</param>
		 	<param name="increment_size">1</param>
		 </generator>
	</id>

  <property name="hostId" column="HOST_ID"/>
  <property name="logDate" column="LOG_DATE"/>
  <property name="userId" column="USER_ID"/>
  <property name="ipAddress" column="IP_ADDRESS"/>
  <property name="tableName" column="TABLE_NAME" />
  <property name="reference" column="REFERENCE"/>
  <property name="columnName" column="COLUMN_NAME"/>
  <property name="action" column="ACTION"/>
  <property name="oldValue" column="OLD_VALUE"/>
  <property name="newValue" column="NEW_VALUE"/>
 </class>
</hibernate-mapping>