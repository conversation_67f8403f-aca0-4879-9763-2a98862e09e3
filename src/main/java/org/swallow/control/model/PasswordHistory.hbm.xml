<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.PasswordHistory" table="S_PASSWORD_HISTORY">
		<composite-id name="id" class="org.swallow.control.model.PasswordHistory$Id" unsaved-value="any">
	        <key-property name="hostId" access="field" column="HOST_ID"/>
	        <key-property name="seqNo" access="field" column="SEQ_NO"/>
	        <key-property name="userId" access="field" column="USER_ID"/>
		</composite-id>
	
		<!-- <property name="password" column="PASSWORD" not-null="false"/>	-->
		<property name="password" column="USER_PASSWORD" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		

    </class>
</hibernate-mapping>
