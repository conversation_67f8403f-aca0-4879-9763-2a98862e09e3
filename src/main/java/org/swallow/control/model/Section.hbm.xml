<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.Section" table="S_SECTION">
		<composite-id name="id" class="org.swallow.control.model.Section$Id" unsaved-value="any">
	        <key-property name="hostId" access="field" column="HOST_ID"/>
	        <key-property name="sectionId" access="field" column="SECTION_ID"/>
		</composite-id>
		<property name="sectionName" column="SECTION_NAME" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
    </class>
</hibernate-mapping>
