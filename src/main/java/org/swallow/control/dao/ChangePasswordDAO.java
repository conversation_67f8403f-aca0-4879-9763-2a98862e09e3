/*
 * Created on Dec 26, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao;



import java.util.Collection;
import java.util.List;

import org.springframework.dao.DataAccessException;
import org.swallow.control.model.PasswordHistory;
import org.swallow.dao.*;
import org.swallow.exception.SwtException;
import org.swallow.model.User;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface ChangePasswordDAO extends DAO{
	/**
	 * @param user
	 * @throws SwtException
	 */
	public void setNewPassword(User	user) throws SwtException;	
	/**
	 * @param pwdhis
	 * @throws SwtException
	 */
	public void updatePasswordHistory(PasswordHistory pwdhis)throws SwtException;	
	/**
	 * @param user
	 * @return
	 * @throws SwtException
	 */
	public Collection getPasswordRules(User user)throws SwtException;
	/**
	 * @param hostId
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	public List getLastSeqNo(String hostId,String userId)throws SwtException;
	/**
	 * @param hostId
	 * @param userId
	 * @param num
	 * @return
	 * @throws SwtException
	 */
	public List getPasswordList(String hostId, String userId, int num)throws SwtException;	
	
	/*Start: Refer to Mantis issue : 0000391: Various login issues */
	/**Saving a record in the table s_password_history
	 * @param pwdhis
	 * @throws SwtException
	 */
	public void savePasswordHistory(PasswordHistory pwdhis)throws DataAccessException;
	/**
	 * Delete the row from s_password_history table with seq no 0
	 * @param hostId
	 * @param userId
	 */
	public void deletePasswordHistryObject(PasswordHistory pwdhis) 
	throws SwtException;
	/*End: Refer to Mantis issue : 0000391: Various login issues */	
}