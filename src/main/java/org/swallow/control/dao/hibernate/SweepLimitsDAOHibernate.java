/*
 * Created on Jan 9, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.CountryDAOHibernate;
import org.swallow.maintenance.model.Currency;
import org.swallow.control.dao.*;
import org.swallow.control.model.*;
import org.springframework.orm.ObjectRetrievalFailureException;

import java.util.Collection;
import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Repository("sweepLimitsDAO")
@Transactional
public class SweepLimitsDAOHibernate extends CustomHibernateDaoSupport implements SweepLimitsDAO {
    
    private final Log log = LogFactory.getLog(SweepLimitsDAOHibernate.class);
    
    /**
     * Constructor with dependency injection
     * @param sessionfactory The session factory
     * @param entityManager The entity manager
     */
    public SweepLimitsDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }
    
    /**
     * Get sweep limits details for a specific role
     * @param roleId The role ID
     * @return Collection of sweep limits
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection getSweepLimitsDetails(String roleId) throws SwtException {
        log.debug("Entering getSweepLimitsDetails");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<SweepLimits> query = session.createQuery(
                "from SweepLimits s where s.id.roleId = :roleId", 
                SweepLimits.class);
            query.setParameter("roleId", roleId);
            List<SweepLimits> sweepLimits = query.getResultList();
            
            log.debug("noofRecords.size : " + sweepLimits.size());
            log.debug("The sweepLimits details got from the database are=>" + sweepLimits);
            log.debug("exiting getSweepLimitsDetails");
            
            return sweepLimits;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getSweepLimitsDetails: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getSweepLimitsDetails", this.getClass());
        }
    }
    
    /**
     * Get currency details for specific parameters
     * @param hostId The host ID
     * @param entityId The entity ID
     * @param ccyGrpId The currency group ID
     * @return Collection of currencies
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection getCurrencyDetails(String hostId, String entityId, String ccyGrpId) throws SwtException {
        log.debug("Entering getCurrencyDetails");
        log.debug("The parameters to getCurrencyDetails.. hostId, entityId, ccyGrpId ==>"
                + hostId + "==" + entityId + "==" + ccyGrpId + "==");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Currency> query = session.createQuery(
                "from Currency c where c.id.hostId = :hostId and c.id.entityId = :entityId " +
                "and c.currencyGroupId = :ccyGrpId and c.id.currencyCode != '*'", 
                Currency.class);
            query.setParameter("hostId", hostId);
            query.setParameter("entityId", entityId);
            query.setParameter("ccyGrpId", ccyGrpId);
            List<Currency> currencies = query.getResultList();
            
            log.debug("exiting getCurrencyDetails");
            return currencies;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getCurrencyDetails: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyDetails", this.getClass());
        }
    }
}
