/*
 * @(#)UserMaintenanceDAOHibernate.java  15/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.servlet.http.HttpServletRequest;
import javax.sql.DataSource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.UserMaintenanceDAO;
import org.swallow.control.model.PasswordHistory;
import org.swallow.control.model.Role;
import org.swallow.control.model.UserMaintenance;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;


import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;

@Repository ("usermaintenanceDAO")
public class UserMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements
		UserMaintenanceDAO {

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory
			.getLog(UserMaintenanceDAOHibernate.class);

	public UserMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
	    super(sessionfactory, entityManager);
	}
	
	/*
	 * Mantis 660 : User maintenance screen does not populate currency group
	 * when adding new user Start : Modified for fetching the user details of
	 * the entities for which the current user has access rights Modified by
	 * Balaji on 19-07-2008
	 */
	/**
	 * This method fetches the users details of the entities for which the
	 * current user has access rights
	 * 
	 * @param hostId
	 * @param roleId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<UserMaintenance> getUserList(String hostId, String roleId) throws SwtException {
		log.debug("Entering UserMaintenanceDAOHibernate.getUserList method");
		Collection<UserMaintenance> userList = new ArrayList<>();
		String userQry = "FROM UserMaintenance u WHERE u.id.hostId = :hostId";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<UserMaintenance> query = session.createQuery(userQry, UserMaintenance.class);
			query.setParameter("hostId", hostId);
			userList = query.getResultList();
		}
		return userList;
	}

	@SuppressWarnings("unchecked")
	public void saveUserDetail(User user) throws SwtException {
		log.debug(this.getClass().getName() + " - [saveUserDetail] - Entering ");
		SwtInterceptor interceptor = null;
		Transaction tx = null;
		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {
			tx = session.beginTransaction();
			String hql = "FROM User u WHERE u.id.hostId = :hostId AND u.id.userId = :userId";
			TypedQuery<User> query = session.createQuery(hql, User.class);
			query.setParameter("hostId", user.getId().getHostId());
			query.setParameter("userId", user.getId().getUserId());
			if (query.getResultList().isEmpty()) {
				session.save(user);
				tx.commit();
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName() + " - [saveUserDetail] - Exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [saveUserDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveUserDetail", UserMaintenanceDAOHibernate.class);
		}
	}

	public JasperPrint getUserReport(HttpServletRequest request, String userId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [ getUserReport ]- Entry");
		Connection con = null;
		DataSource ds;
		JasperReport jasperReport = null;
		JasperPrint jasperPrint = null;
		Map param = new HashMap();
		try {
			if (request.getParameter("fileType").equals("pdf"))
				jasperReport = JasperCompileManager.compileReport(request
				        .getServletContext().getRealPath("/")
						+ SwtConstants.USER_REPORT_FILE);
			else if (request.getParameter("fileType").equals("excel"))
				jasperReport = JasperCompileManager.compileReport(request
				        .getServletContext().getRealPath("/")
						+ SwtConstants.USER_REPORT_FILE_Excel);
			ds = (DataSource) SwtUtil.getBean("dataSource");
			con = ds.getConnection();
			CommonDataManager CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");		
            String dateFormat="";
            if (CDM.getDateFormat().equals("datePat1"))
        	    dateFormat="dd/MM/yyyy HH24:MI:SS";
            else
        	    dateFormat="MM/dd/yyyy HH24:MI:SS";
			param.put("P_USER", userId);
			param.put("p_dateFormat", dateFormat);
			jasperPrint = JasperFillManager
					.fillReport(jasperReport, param, con);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getUserReport] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getUserReport] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getUserReport", UserMaintenanceDAOHibernate.class);
		} finally {
			JDBCCloser.close(con);
		}
		log.debug(this.getClass().getName() + " - [ getUserReport ]- Exit");
		return jasperPrint;
	}

	public void deleteUserDetail(UserMaintenance usermaintenance)
			throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.deleteusermaintenance");
		SwtInterceptor interceptor = null;
		Collection coll = getUserDepenedntObject(usermaintenance);
		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {
			Transaction tx = session.beginTransaction();
			for (Object obj : coll) {
				session.delete(obj);
			}
			session.delete(usermaintenance);
			tx.commit();
		} catch (HibernateException e) {
			log.error(this.getClass().getName() + "- [deleteUserDetail] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "deleteUserDetail", UserMaintenanceDAOHibernate.class);
		}
	}

	private Collection getUserDepenedntObject(UserMaintenance usermaintenance)
			throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getUserDepenedntObject");
		String hostId = usermaintenance.getId().getHostId();
		String userId = usermaintenance.getId().getUserId();
		ArrayList coll = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<PasswordHistory> passwordHistQuery = session.createQuery(
				"from PasswordHistory pwadHist where pwadHist.id.hostId=:hostId and pwadHist.id.userId=:userId",
				PasswordHistory.class);
			passwordHistQuery.setParameter("hostId", hostId);
			passwordHistQuery.setParameter("userId", userId);
			coll.addAll(passwordHistQuery.getResultList());

			TypedQuery<?> shortcutListQuery = session.createQuery(
				"from Shortcut shortcut where shortcut.id.hostId=:hostId and shortcut.id.userId=:userId");
			shortcutListQuery.setParameter("hostId", hostId);
			shortcutListQuery.setParameter("userId", userId);
			coll.addAll(shortcutListQuery.getResultList());

			TypedQuery<?> userProfileDetailListQuery = session.createQuery(
				"from UserProfileDetail userProfileDetail where userProfileDetail.id.hostId=:hostId and userProfileDetail.id.userId=:userId");
			userProfileDetailListQuery.setParameter("hostId", hostId);
			userProfileDetailListQuery.setParameter("userId", userId);
			coll.addAll(userProfileDetailListQuery.getResultList());

			TypedQuery<?> userProfileListQuery = session.createQuery(
				"from UserProfile userProfile where userProfile.id.hostId=:hostId and userProfile.id.userId=:userId");
			userProfileListQuery.setParameter("hostId", hostId);
			userProfileListQuery.setParameter("userId", userId);
			coll.addAll(userProfileListQuery.getResultList());

			TypedQuery<?> userStatusListQuery = session.createQuery(
				"from UserStatus userStatus where userStatus.id.hostId=:hostId and userStatus.id.userId=:userId");
			userStatusListQuery.setParameter("hostId", hostId);
			userStatusListQuery.setParameter("userId", userId);
			coll.addAll(userStatusListQuery.getResultList());

			TypedQuery<?> userScreenInfoListQuery = session.createQuery(
				"from ScreenInfo screenInfo where screenInfo.id.hostId=:hostId and screenInfo.id.userId=:userId");
			userScreenInfoListQuery.setParameter("hostId", hostId);
			userScreenInfoListQuery.setParameter("userId", userId);
			List userScreenInfoList = userScreenInfoListQuery.getResultList();
			if (userScreenInfoList != null && userScreenInfoList.size() > 0)
				coll.addAll(userScreenInfoList);

			TypedQuery<?> userScreenOptionListQuery = session.createQuery(
				"from ScreenOption screenOption where screenOption.id.hostId=:hostId and screenOption.id.userId=:userId");
			userScreenOptionListQuery.setParameter("hostId", hostId);
			userScreenOptionListQuery.setParameter("userId", userId);
			List userScreenOptionList = userScreenOptionListQuery.getResultList();
			if (userScreenOptionList != null && userScreenOptionList.size() > 0)
				coll.addAll(userScreenOptionList);

			TypedQuery<?> userPersonalCcyListQuery = session.createQuery(
				"from PersonalCurrency personalCurrency where personalCurrency.id.hostId=:hostId and personalCurrency.id.userId=:userId");
			userPersonalCcyListQuery.setParameter("hostId", hostId);
			userPersonalCcyListQuery.setParameter("userId", userId);
			List userPersonalCcyList = userPersonalCcyListQuery.getResultList();
			if (userPersonalCcyList != null && userPersonalCcyList.size() > 0)
				coll.addAll(userPersonalCcyList);
		}
		return coll;
	}

	public UserMaintenance fetchUserDetail(String hostId, String userId) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.fetchUserDetail");
		UserMaintenance usermaintenance = null;
		String hql = "FROM UserMaintenance usermaint WHERE usermaint.id.hostId = :hostId AND usermaint.id.userId = :userId";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<UserMaintenance> query = session.createQuery(hql, UserMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			usermaintenance = query.getResultList().stream().findFirst().orElse(null);
		}
		return usermaintenance;
	}

	public void updateUserDetail(User user) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.updateUserDetail");
		SwtInterceptor interceptor = null;
		User oldDetails = null;
		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {
			Transaction tx = session.beginTransaction();
			String hql = "FROM User u WHERE u.id.hostId = :hostId AND u.id.userId = :userId";
			TypedQuery<User> query = session.createQuery(hql, User.class);
			query.setParameter("hostId", user.getId().getHostId());
			query.setParameter("userId", user.getId().getUserId());
			List<User> records = query.getResultList();
			if (!records.isEmpty()) {
				oldDetails = records.get(0);
				if (oldDetails != null) {
					user.setLastLoginFailedIp(oldDetails.getLastLoginFailedIp());
					user.setLastLoginIp(oldDetails.getLastLoginIp());
					user.setLastLoginFailed(oldDetails.getLastLoginFailed());
				}
			}
			session.merge(user);
			tx.commit();
		} catch (HibernateException e) {
			log.error(this.getClass().getName() + "- [updateUserDetail] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "updateUserDetail", UserMaintenanceDAOHibernate.class);
		}
	}

	public Collection<Role> getRoleList(String hostId) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getRoleList");
		Collection<Role> roles = new ArrayList<>();
		String hql = "FROM Role r WHERE r.hostId = :hostId ORDER BY r.roleId";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Role> query = session.createQuery(hql, Role.class);
			query.setParameter("hostId", hostId);
			roles = query.getResultList();
		}
		return roles;
	}

	public Collection<Role> getRoleList(String hostId, String entityId) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getRoleList");
		Collection<Role> roles = new ArrayList<>();
		String hql = "SELECT DISTINCT r FROM Role r, EntityAccess e WHERE r.hostId = :hostId " +
				"AND r.roleId = e.id.roleId AND e.id.entityId IN (" + entityId + ") " +
				"ORDER BY r.roleId";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Role> query = session.createQuery(hql, Role.class);
			query.setParameter("hostId", hostId);
			roles = query.getResultList();
		}
		return roles;
	}

	public Collection getRoleEntityAccess(String roleId) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getRoleEntityAccess");
		List noofRecords = new ArrayList();
		String hql = "select s from Entity s, EntityAccess e where e.id.roleId = :roleId and e.id.entityId = s.id.entityId order by s.id.entityId";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Query query = session.createQuery(hql);
			query.setParameter("roleId", roleId);
			noofRecords = query.getResultList();
		}
		return noofRecords;
	}

	public Collection getLastPasswordChangeDate(String hostId, String userId) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getLastPasswordChangeDate");
		List noofRecords = new ArrayList();
		String hql = "Select p from PasswordHistory p where p.id.hostId = :hostId and p.id.userId = :userId and p.id.seqNo = (Select max(p1.id.seqNo) from PasswordHistory p1 where p1.id.hostId = :hostIdSub and p1.id.userId = :userIdSub)";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			query.setParameter("hostIdSub", hostId);
			query.setParameter("userIdSub", userId);
			noofRecords = query.getResultList();
		}
		return noofRecords;
	}

	public Collection getLoginDateInfo(String hostId, String userId)
			throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getLoginDateInfo");
		List noofRecords = new ArrayList();
		String hql = "select u from UserStatus u where u.id.hostId=:hostId and u.id.userId=:userId and u.id.logOnTime = (Select max(u1.id.logOnTime) from UserStatus u1 where u1.id.hostId=:hostId2 and u1.id.userId=:userId2 )";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			query.setParameter("hostId2", hostId);
			query.setParameter("userId2", userId);
			noofRecords = query.getResultList();
		}
		return noofRecords;
	}

	public void savePwdHistory(PasswordHistory pwdhis) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.savePwdHistory");
		int No;
		String hostId = pwdhis.getId().getHostId();
		String userId = pwdhis.getId().getUserId();
		List seqNo = getLastSeqNo(hostId.trim(), userId.trim());
		if ((seqNo.get(0) != null)) {
			No = Integer.parseInt(seqNo.get(0).toString());
			No = No + 1;
		} else {
			No = 1;
		}
		pwdhis.getId().setSeqNo(Integer.toString(No));
		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {
			Transaction tx = session.beginTransaction();
			session.save(pwdhis);
			tx.commit();
		} catch (HibernateException e) {
			log.error(this.getClass().getName() + "- [saveUserStatus] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "saveUserStatus", UserMaintenanceDAOHibernate.class);
		}
	}

	public Collection getPwdHisDetails(PasswordHistory pwdhist)
			throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getPwdHisDetails");
		String hostId = pwdhist.getId().getHostId();
		String userId = pwdhist.getId().getUserId();
		List collHstry = new ArrayList();
		String hql = "select p from PasswordHistory p where p.id.hostId=:hostId and p.id.userId=:userId";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			collHstry = query.getResultList();
		}
		return collHstry;
	}

	public void deletePassHist(PasswordHistory hist) throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.deletePassHist");
		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {
			Transaction tx = session.beginTransaction();
			session.delete(hist);
			tx.commit();
		} catch (HibernateException e) {
			log.error(this.getClass().getName() + "- [deletePassHist] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "deletePassHist", UserMaintenanceDAOHibernate.class);
		}
	}

	/**
	 * @param hostId
	 * @param userId
	 */
	public Date getLastLogoutDate(String hostId, String userId)
			throws SwtException {
		log.debug("entering UserMaintenanceDAOHibernate.getLastLogoutDate");
		Date lastLogoutDate = null;
		// Migrated: Use Hibernate session and named parameters
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select max(u.logOutTime) from UserStatus u where u.id.hostId=:hostId and u.id.userId=:userId and u.logOutTime is not null";
			TypedQuery<Date> query = session.createQuery(hql, Date.class);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			List<Date> records = query.getResultList();
			if (records != null && records.size() == 1) {
				if (records.get(0) != null) {
					lastLogoutDate = records.get(0);
				}
			}
		}
		return lastLogoutDate;
	}

	/*-- START:- CODE CHANGED FOR DEFECT NO.39 IN MENTISS --*/
	public List getLastSeqNo(String hostId, String userId) throws SwtException {
		// Migrated: Use Hibernate session and named parameters
		List records = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "Select max(p1.id.seqNo) from PasswordHistory p1 where p1.id.hostId = :hostId and p1.id.userId = :userId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			records = query.getResultList();
		}
		return records;
	}
	/*-- END:- CODE CHANGED FOR DEFECT NO.39 IN MENTISS --*/

	public List getMinSeqNo(String hostId, String userId) throws SwtException {
		// Migrated: Use Hibernate session and named parameters
		List records = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "Select min(p1.id.seqNo) from PasswordHistory p1 where p1.id.hostId = :hostId and p1.id.userId = :userId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			records = query.getResultList();
		}
		return records;
	}

	/*
	 * Code modified as per mail from Steve and forwarded by JP for Amending the
	 * User Profile Changes on 17-APR-2008 --> Reference SRS :
	 * Smart-Predict_SRS_USER_PROFILE_0.2.doc by James Cook and David
	 * Description : It is required to have a user whose sole purpose to be able
	 * to reset user passwords. This requires a change to the "Change Role"
	 * window.
	 */
	// Start : Method added to retrieve whether the user has Advanced user
	// rights or not on 21-04-2008
	/**
	 * Method added to fetch the Advanced user rights for the passed userId and
	 * hostId
	 * 
	 * @param String
	 *            strHostId
	 * @param String
	 *            strUserId
	 * @return List
	 */
	public String getAdvancedUser(String strHostId, String strUserId) {
		log.debug(" UserMaintenanceDAOHibernate : Entering into getAdvancedUser method");
		String strAdvancedUser = SwtConstants.YES;
		ArrayList<User> userAndPasswordRules = new ArrayList<User>();
		// Migrated: Use Hibernate session and named parameters
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String userHql = "from User c where c.id.userId = :userId and c.id.hostId = :hostId";
			TypedQuery<User> userQuery = session.createQuery(userHql, User.class);
			userQuery.setParameter("userId", strUserId);
			userQuery.setParameter("hostId", strHostId);
			List<User> records = userQuery.getResultList();

			if (records != null && records.size() > 0) {
				// Extracting User information from Database
				User usrFromDB = records.get(0);
				userAndPasswordRules.add(usrFromDB);
				// Extracting Role information and setting up the alertType
				// information
				String roleHql = "from Role r where r.hostId = :hostId and r.roleId = :roleId";
				TypedQuery<Role> roleQuery = session.createQuery(roleHql, Role.class);
				roleQuery.setParameter("hostId", usrFromDB.getId().getHostId());
				roleQuery.setParameter("roleId", usrFromDB.getRoleId());
				List<Role> roleList = roleQuery.getResultList();
				// Getting the Advanced user Rights from role table
				if (roleList != null && roleList.size() > 0) {
					Role roleObj = roleList.get(0);
					strAdvancedUser = roleObj.getAdvancedUser();
				}
			}
		}
		log.debug(" UserMaintenanceDAOHibernate : Exiting From getAdvancedUser method");
		return strAdvancedUser;
	}
	// End : Method Ends to retrieve whether the user has Advanced user rights
	// or not on 21-04-2008
}