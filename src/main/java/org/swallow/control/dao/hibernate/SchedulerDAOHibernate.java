/*
 * Created on Jan 27, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.SchedulerDAO;
import org.swallow.control.model.Job;
import org.swallow.control.model.JobStatus;
import org.swallow.control.model.ScheduledReportParams;
import org.swallow.control.model.ScheduledReportType;
import org.swallow.control.model.Scheduler;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
/**
 *
 * This is DAO class for Scheduler screen.
 *
 */
@Repository ("schedulerDAO")
@Transactional
public class SchedulerDAOHibernate extends CustomHibernateDaoSupport implements
		SchedulerDAO {
	public SchedulerDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}

	private SessionFactory sessionFactory;

	private static
	String HQL_JOBLIST = "select j.hostId, j.jobId, j.lastExecStatus, j.lastExecTime, j.nextExecTime, "
			+ "j.updateDate, j.updateUser, j.currentStatus, s.jobType, jm.jobDescription, s.jobStatus, s.scheduleDate, s.scheduleTime, "
			+ "s.durationHours, s.durationMins, s.durationSecs, s.scheduleDay, s.monthFirst, s.monthLast, s.monthDate, s.scheduleId, jm.jobType, SRP.reportName "
			+ "from JobStatus j, Scheduler s, Job jm "
			+ "left join ScheduledReportParams SRP on s.scheduleId = SRP.scheduleId "
			+ "where j.hostId = s.hostId and j.jobId = s.jobId and jm.id.jobId = j.jobId and s.scheduleId = j.scheduleId";

	private final Log log = LogFactory.getLog(SchedulerDAOHibernate.class);

	public Collection<JobStatus> getJobStatusDetails(String hostId, String scheduledJobType) throws SwtException {
		log.debug("Entering getJobStatusDetails");
		String hql = "select j.hostId, j.jobId, j.lastExecStatus, j.lastExecTime, j.nextExecTime, "
				+ "j.updateDate, j.updateUser, j.currentStatus, s.jobType, jm.jobDescription, "
				+ "s.jobStatus, s.scheduleDate, s.scheduleTime, s.durationHours, s.durationMins, "
				+ "s.durationSecs, s.scheduleDay, s.monthFirst, s.monthLast, s.monthDate, s.scheduleId, "
				+ "jm.jobType, SRP.reportName "
				+ "from JobStatus j "
				+ "join Scheduler s on j.hostId = s.hostId and j.jobId = s.jobId "
				+ "join Job jm on jm.id.jobId = j.jobId "
				+ "left join ScheduledReportParams SRP on s.scheduleId = SRP.scheduleId "
				+ "where s.scheduleId = j.scheduleId";


		// If a specific job type is provided, add a filter for jobType
		if (!SwtConstants.JOB_TYPE_BOTH.equals(scheduledJobType)) {
			hql += " and jm.jobType = :scheduledJobType";
		}

		// Create a TypedQuery
		TypedQuery<Object[]> query = entityManager.createQuery(hql, Object[].class);

		// If a specific job type is provided, set it as a parameter
		if (!SwtConstants.JOB_TYPE_BOTH.equals(scheduledJobType)) {
			query.setParameter("scheduledJobType", scheduledJobType);
		}

		// Execute the query and retrieve the result list
		List<Object[]> jobListFromDB = query.getResultList();
		Collection<JobStatus> jobList = new ArrayList<>();

		// Process each row in the result
		if (jobListFromDB != null) {
			for (Object[] row : jobListFromDB) {
				JobStatus jobStatusItem = new JobStatus();
				Job job = new Job();

				jobStatusItem.setHostId((String) row[0]);
				jobStatusItem.setJobId((String) row[1]);
				jobStatusItem.setLastExecStatus((String) row[2]);
				jobStatusItem.setLastExecTime((Date) row[3]);
				jobStatusItem.setNextExecTime((Date) row[4]);
				jobStatusItem.setUpdateDate((Date) row[5]);
				jobStatusItem.setUpdateUser((String) row[6]);
				jobStatusItem.setCurrentStatus((String) row[7]);
				jobStatusItem.setJobType((String) row[8]);

				job.getId().setHostId((String) row[0]);
				job.getId().setJobId((String) row[1]);
				job.setJobDescription((String) row[9]);
				jobStatusItem.setJob(job);

				jobStatusItem.setJobStatus((String) row[10]);
				jobStatusItem.setScheduleDate((Date) row[11]);
				jobStatusItem.setScheduleTime((String) row[12]);
				jobStatusItem.setDurationHrs((Integer) row[13]);
				jobStatusItem.setDurationMins((Integer) row[14]);
				jobStatusItem.setDurationSecs((Integer) row[15]);
				jobStatusItem.setScheduleDay((String) row[16]);
				jobStatusItem.setMonthFirst((String) row[17]);
				jobStatusItem.setMonthLast((String) row[18]);
				jobStatusItem.setMonthDate((Integer) row[19]);
				jobStatusItem.setScheduleId((Integer) row[20]);
				jobStatusItem.setJobTypeProcessOrReport((String) row[21]);
				jobStatusItem.setReportName((String) row[22]);

				jobList.add(jobStatusItem);
			}
		}

		log.debug("Exiting getJobStatusDetails");
		return jobList;
	}

	public Collection<Job> getJobNameList(String hostId) throws SwtException {
		log.debug("Entering getJobNameList");

		List<Job> noofRecords;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Job> query = session.createQuery(
					"from Job s where s.id.hostId = :hostId order by s.jobDescription",
					Job.class
			);
			query.setParameter("hostId", hostId);
			noofRecords = query.getResultList();
		}

		log.debug("Exiting getJobNameList");

		return noofRecords;
	}

	public Collection<Scheduler> getJobNames(String hostId) throws SwtException {
		log.debug("Entering getJobNames");

		List<Scheduler> noofRecords;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Scheduler> query = session.createQuery(
					"from Scheduler s where s.hostId = :hostId",
					Scheduler.class
			);
			query.setParameter("hostId", hostId);
			noofRecords = query.getResultList();
		}

		log.debug("Exiting getJobNames");

		return noofRecords;
	}

	public Collection<Scheduler> getSchedulerDetails(Integer scheduleId) throws SwtException {
		List<Scheduler> noofRecords;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Scheduler> query = session.createQuery(
					"from Scheduler s where s.scheduleId = :scheduleId",
					Scheduler.class
			);
			query.setParameter("scheduleId", scheduleId);
			noofRecords = query.getResultList();
		}

		log.debug("Exiting getSchedulerDetails");

		return noofRecords;
	}


	/**
	 * This is used to save the scheduler in database
	 *
	 * @param scheduler
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveSchedulerDetail(Scheduler scheduler) throws SwtException {
		List<?> records = null;
		List<?> records2 = null;
		ScheduledReportParams scheduledReportParams = scheduler.getScheduledReportParams();
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [saveSchedulerDetail] - Entering");

			if (scheduledReportParams == null) {
				// Job type is Process
				try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
					TypedQuery<Scheduler> query = session.createQuery(
							"from Scheduler s where s.hostId = :hostId and s.jobId = :jobId",
							Scheduler.class);
					query.setParameter("hostId", scheduler.getHostId());
					query.setParameter("jobId", scheduler.getJobId());
					records = query.getResultList();
				}

				if (records.isEmpty()) {
					interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
					try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
						tx = session.beginTransaction();
						session.save(scheduler);
						tx.commit();
					}
				} else {
					throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
				}

			} else {
				// Job type is Report

				try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
					TypedQuery<ScheduledReportParams> query1 = session.createQuery(
							"from ScheduledReportParams s where s.hostId = :hostId and s.reportTypeId = :reportTypeId and s.reportName = :reportName",
							ScheduledReportParams.class);
					query1.setParameter("hostId", scheduler.getHostId());
					query1.setParameter("reportTypeId", scheduledReportParams.getReportTypeId());
					query1.setParameter("reportName", scheduledReportParams.getReportName());
					records = query1.getResultList();

					String uniquePathQuery = getIfUniquePathQuery(true);
					Query<ScheduledReportParams> query2 = session.createQuery(uniquePathQuery, ScheduledReportParams.class);
					query2.setParameter("hostId", scheduler.getHostId());
					query2.setParameter("outputFormat", scheduledReportParams.getOutputFormat());
					query2.setParameter("outputLocation", scheduledReportParams.getOutputLocation());
					query2.setParameter("fileNamePrefix", scheduledReportParams.getFileNamePrefix());
					records2 = query2.getResultList();
				}

				if (records.isEmpty() && records2.isEmpty()) {
					interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
					try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
						tx = session.beginTransaction();

						session.save(scheduler);

						scheduledReportParams.setHostId(scheduler.getHostId());
						scheduledReportParams.setJobId(scheduler.getJobId());
						scheduledReportParams.setScheduleId(scheduler.getScheduleId());

						session.save(scheduledReportParams);

						tx.commit();
					}
				} else if (!records.isEmpty()) {
					throw new SwtException("errors.CouldNotSaveJobReportWithSameNameExceptioninAdd");
				} else if (!records2.isEmpty()) {
					throw new SwtException("errors.CouldNotSaveJobReportWithSameLocationPrefixExceptioninAdd");
				}
			}

			log.debug(this.getClass().getName() + " - [saveSchedulerDetail] - Exiting");

		} catch (SwtException exp) {
			if (!SwtErrorHandler.JOB_SAME_LOCATION_ERROR.equals(exp.getErrorCode()) &&
				!SwtErrorHandler.JOB_SAME_NAME_ERROR.equals(exp.getErrorCode())) {
				log.error(this.getClass().getName() + "- [saveSchedulerDetail] - Exception " + exp.getMessage());
			}
			throw SwtErrorHandler.getInstance().handleException(exp, "saveSchedulerDetail", SchedulerDAOHibernate.class);

		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [saveSchedulerDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveSchedulerDetail", SchedulerDAOHibernate.class);
		}
	}

	private String getIfUniquePathQuery(boolean isAddQuery) {
		String query;
		String OS = System.getProperty("os.name").toLowerCase();

		if (OS.contains("win")) {
			query = "from ScheduledReportParams s " +
					"where s.hostId = :hostId and s.outputFormat = :outputFormat " +
					"and upper(coalesce(s.outputLocation, '#!#')) = upper(coalesce(:outputLocation, '#!#')) " +
					"and upper(coalesce(s.fileNamePrefix, '#!#')) = upper(coalesce(:fileNamePrefix, '#!#')) ";
		} else {
			query = "from ScheduledReportParams s " +
					"where s.hostId = :hostId and s.outputFormat = :outputFormat " +
					"and coalesce(s.outputLocation, '#!#') = coalesce(:outputLocation, '#!#') " +
					"and coalesce(s.fileNamePrefix, '#!#') = coalesce(:fileNamePrefix, '#!#') ";
		}

		if (!isAddQuery) {
			query += "and s.scheduleId != :scheduleId";
		}

		return query;
	}

	/**
	 * @param scheduler -
	 *            Scheduler
	 * @exception SwtException
	 */
	public void updateSchedulerDetail(Scheduler scheduler) throws SwtException {
		List<?> records = null;
		List<?> records2 = null;
		ScheduledReportParams scheduledReportParams = scheduler.getScheduledReportParams();
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + "- [updateSchedulerDetail] - Entering");

			if (scheduledReportParams != null) {
				try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
					TypedQuery<ScheduledReportParams> query1 = session.createQuery(
							"from ScheduledReportParams s where s.hostId = :hostId and s.reportTypeId = :reportTypeId and s.reportName = :reportName and s.scheduleId != :scheduleId",
							ScheduledReportParams.class
					);
					query1.setParameter("hostId", scheduler.getHostId());
					query1.setParameter("reportTypeId", scheduledReportParams.getReportTypeId());
					query1.setParameter("reportName", scheduledReportParams.getReportName());
					query1.setParameter("scheduleId", scheduler.getScheduleId());
					records = query1.getResultList();

					String uniquePathQuery = getIfUniquePathQuery(false);
					Query<ScheduledReportParams> query2 = session.createQuery(uniquePathQuery, ScheduledReportParams.class);
					query2.setParameter("hostId", scheduler.getHostId());
					query2.setParameter("outputFormat", scheduledReportParams.getOutputFormat());
					query2.setParameter("outputLocation",
							SwtUtil.isEmptyOrNull(scheduledReportParams.getOutputLocation()) ? "" : scheduledReportParams.getOutputLocation());
					query2.setParameter("fileNamePrefix",
							SwtUtil.isEmptyOrNull(scheduledReportParams.getFileNamePrefix()) ? "" : scheduledReportParams.getFileNamePrefix());
					query2.setParameter("scheduleId", scheduler.getScheduleId());
					records2 = query2.getResultList();
				}

				if (records.isEmpty() && records2.isEmpty()) {
					interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
					try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
						tx = session.beginTransaction();
						session.update(scheduler);
						session.update(scheduler.getScheduledReportParams());
						tx.commit();
					}
				} else if (!records.isEmpty()) {
					throw new SwtException("errors.CouldNotSaveJobReportWithSameNameExceptioninAdd");
				} else if (!records2.isEmpty()) {
					throw new SwtException("errors.CouldNotSaveJobReportWithSameLocationPrefixExceptioninAdd");
				}
			} else {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
					tx = session.beginTransaction();
					session.update(scheduler);
					tx.commit();
				}
			}

			log.debug(this.getClass().getName() + "- [updateSchedulerDetail] - Exiting");

		} catch (SwtException exp) {
			if (!SwtErrorHandler.JOB_SAME_LOCATION_ERROR.equals(exp.getErrorCode())
				&& !SwtErrorHandler.JOB_SAME_NAME_ERROR.equals(exp.getErrorCode())) {
				log.error(this.getClass().getName() + "- [saveSchedulerDetail] - Exception " + exp.getMessage());
			}
			throw SwtErrorHandler.getInstance().handleException(exp, "saveSchedulerDetail", SchedulerDAOHibernate.class);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [updateSchedulerDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "updateSchedulerDetail", SchedulerDAOHibernate.class);
		}
	}

	public void deleteSchedulerDetail(Scheduler scheduler) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug("Entering deleteSchedulerDetail");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				
				// First check if the scheduler still exists
				Scheduler existingScheduler = session.get(Scheduler.class, scheduler.getScheduleId());
				if (existingScheduler != null) {
					ScheduledReportParams scheduledReportParams = scheduler.getScheduledReportParams();
					if (scheduledReportParams != null) {
						// Check if the params still exist
						ScheduledReportParams existingParams = session.get(ScheduledReportParams.class, scheduledReportParams.getScheduleId());
						if (existingParams != null) {
							session.delete(existingParams);
						} else {
							log.warn("ScheduledReportParams with ID " + scheduledReportParams.getScheduleId() + " already deleted");
						}
					}
					session.delete(existingScheduler);
				} else {
					log.warn("Scheduler with ID " + scheduler.getScheduleId() + " already deleted");
				}
				
				tx.commit();
			} catch (Exception e) {
				if (tx != null && tx.isActive()) {
					tx.rollback();
				}
				throw e;
			}

			log.debug("Exiting deleteSchedulerDetail");

		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [deleteSchedulerDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "deleteSchedulerDetail", SchedulerDAOHibernate.class);
		}
	}

	public Job getJobDetail(String hostId, String jobId) throws SwtException {
		Job job = null;
		log.debug("Entering 'getJobDetail' Method");

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String queryString = "FROM Job j WHERE j.id.hostId LIKE :hostId AND j.id.jobId LIKE :jobId";
			TypedQuery<Job> query = session.createQuery(queryString, Job.class);
			query.setParameter("hostId", hostId);
			query.setParameter("jobId", jobId);

			List<Job> results = query.getResultList();
			if (!results.isEmpty()) {
				job = results.get(0); // safely get the first match
			}

		} catch (HibernateException e) {
			log.debug("Problem in SchedulerDAOHibernate.getJobDetail method");
			e.printStackTrace();
		}

		log.debug("Returning from 'getJobDetail' Method with job --> " + job);
		return job;
	}


	/**
	 * This method used to get the reports job list
	 *
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<Job> getReportsJobList(String hostId) throws SwtException {
		ArrayList<Job> jobList = null;
		log.debug("Entering 'getReportsJobList' Method");
		Session session = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			jobList = new ArrayList<>();
			String queryString = "FROM Job j WHERE j.id.hostId = :hostId AND j.jobType = :jobType order by j.jobDescription asc";
			Query<Job> query = session.createQuery(queryString);
			query.setParameter("hostId", hostId);
			query.setParameter("jobType", "R");
			jobList.addAll(query.list());
		} catch (HibernateException e) {
			log.debug("Problem in SchedulerDAOHibernate.getReportsJobList method");
			e.printStackTrace();
		} finally {
			JDBCCloser.close(session);
		}
		log.debug("Returning from 'getReportsJobList' Method with job -->" + jobList.size());
		return jobList;
	}

	/**
	 * This method used to get the reports type list for the selected Job id
	 *
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<ScheduledReportType> getReportTypes(String hostId, String jobId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getReportTypes] - Entering");

		ArrayList<ScheduledReportType> resultList = new ArrayList<>();

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql;
			Query<ScheduledReportType> query;

			// Choose the appropriate query based on jobId
			if (SwtUtil.isEmptyOrNull(jobId) || "all".equalsIgnoreCase(jobId)) {
				hql = "from ScheduledReportType s where s.hostId = :hostId order by s.reportName asc";
				query = session.createQuery(hql, ScheduledReportType.class);
				query.setParameter("hostId", hostId);
			} else {
				hql = "from ScheduledReportType s where s.hostId = :hostId and s.jobId = :jobId order by s.reportName asc";
				query = session.createQuery(hql, ScheduledReportType.class);
				query.setParameter("hostId", hostId);
				query.setParameter("jobId", jobId);
			}

			// Execute the query and add results to the list
			List<ScheduledReportType> records = query.getResultList();
			if (records != null) {
				resultList.addAll(records);
			}
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [getReportTypes] - Exception " + exp.getMessage(), exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getReportTypes", SchedulerDAOHibernate.class);
		}

		log.debug(this.getClass().getName() + "- [getReportTypes] - Exiting");
		return resultList;
	}

	public JobStatus getJobStatus(Integer scheduleId) throws SwtException {
		log.debug("Entering 'getJobStatus' Method");

		JobStatus jobStatus = null;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from JobStatus u where u.scheduleId = :scheduleId";
			Query<JobStatus> query = session.createQuery(hql, JobStatus.class);
			query.setParameter("scheduleId", scheduleId);

			// Fetch the first result if it exists
			List<JobStatus> jobList = query.getResultList();
			if (!jobList.isEmpty()) {
				jobStatus = jobList.get(0);
			}
		} catch (HibernateException e) {
			log.error("Problem in SchedulerDAOHibernate.getJobStatus method: " + e.getMessage(), e);
		}

		return jobStatus;
	}

	/**
	 * This method is added for integration of Input System. as when we schedule
	 * an input job one row has to be inserted into S_JOB_STATUS table. This
	 * method is used for that purpose. Incase of System job we dont use this
	 * function as the is wrtten in the Scheduler's code.
	 *
	 * @param jobStatus
	 * @throws SwtException
	 */
	public void saveJobStatus(JobStatus jobStatus) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor;

		try {
			log.debug("Entering saveJobStatus");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				session.save(jobStatus);
				tx.commit();
			}

			log.debug("Exiting saveJobStatus");

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [saveJobStatus] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveJobStatus", SchedulerDAOHibernate.class);
		}
	}
	public void updateJobStatus(JobStatus jobStatus) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor;

		try {
			log.debug("Entering 'updateJobStatus' Method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				session.update(jobStatus);
				tx.commit();
			}

			log.debug("Exiting 'updateJobStatus' Method");

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [updateJobStatus] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "updateJobStatus", SchedulerDAOHibernate.class);
		}
	}

	public Scheduler getJobType(String hostId) throws SwtException {
		Scheduler scheduler = null;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getJobType] - Enter");

			TypedQuery<Scheduler> query = session.createQuery(
					"select s from Scheduler s, Job j " +
					"where s.hostId = :hostId " +
					"and j.id.jobId = s.jobId " +
					"and j.id.hostId = s.hostId " +
					"and j.programName = 'org.swallow.batchScheduler.MatchingProcess'",
					Scheduler.class
			);

			query.setParameter("hostId", hostId);
			List<Scheduler> list = query.getResultList();

			if (!list.isEmpty()) {
				scheduler = list.get(0);
			}

			log.debug(this.getClass().getName() + " - [getJobType] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getJobType] - Exception - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getJobType", SchedulerDAOHibernate.class);
		}

		return scheduler;
	}


	public Collection<String> getJobTypeList(String hostId) throws SwtException {
		log.debug("Entering getJobTypeList");

		List<String> jobTypes;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<String> query = session.createQuery(
					"select distinct s.jobType from Job s where s.id.hostId = :hostId",
					String.class
			);
			query.setParameter("hostId", hostId);
			jobTypes = query.getResultList();
		}

		log.debug("Exiting getJobTypeList");

		return jobTypes;
	}

	@Override
	public Collection<Job> getJobNameList(String hostId, String selectedJobType) throws SwtException {
		log.debug("Entering getJobNameList");

		List<Job> noofRecords;
		if("Report".equalsIgnoreCase(selectedJobType)) {
			selectedJobType = "R";
		}else if("Process".equalsIgnoreCase(selectedJobType)) {
			selectedJobType = "P";
		}
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Job> query = session.createQuery(
					"from Job s where s.id.hostId = :hostId and s.jobType = :jobType order by s.jobDescription asc",
					Job.class
			);
			query.setParameter("hostId", hostId);
			query.setParameter("jobType", selectedJobType);

			noofRecords = query.getResultList();
		}

		log.debug("Exiting getJobNameList");

		return noofRecords;
	}


	public Collection getUserList(String hostId, String userList) throws SwtException {
		List result;

		// Build the dynamic HQL query using safe string concatenation for IN clause
		String hqlQuery = "SELECT u FROM User u WHERE u.id.hostId = :hostId";
		if (!SwtUtil.isEmptyOrNull(userList)) {
			userList = "'" + userList.replaceAll(",", "','") + "'";
			hqlQuery += " AND u.id.userId IN (" + userList + ")";
		}
		hqlQuery += " ORDER BY u.id.userId";

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Query query = session.createQuery(hqlQuery);
			query.setParameter("hostId", hostId);
			result = query.getResultList();
		}

		return result;
	}

	public Collection getRoleList(String hostId, String roleList) throws SwtException {
		List result;

		// Build the dynamic HQL query using safe string concatenation for IN clause
		String hqlQuery = "SELECT r FROM Role r WHERE r.hostId = :hostId";
		if (!SwtUtil.isEmptyOrNull(roleList)) {
			roleList = "'" + roleList.replaceAll(",", "','") + "'";
			hqlQuery += " AND r.roleId IN (" + roleList + ")";
		}
		hqlQuery += " ORDER BY r.roleId";

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Query query = session.createQuery(hqlQuery);
			query.setParameter("hostId", hostId);
			result = query.getResultList();
		}

		return result;
	}

	public ScheduledReportParams getScheduledReportParams(Integer scheduleId) throws SwtException {
		ScheduledReportParams scheduledReportParams = null;
		Session session = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			String queryString = "FROM ScheduledReportParams s WHERE s.scheduleId = :scheduleId";
			Query<ScheduledReportParams> query = session.createQuery(queryString);
			query.setParameter("scheduleId", scheduleId);
			List<ScheduledReportParams> results = query.list();

			Iterator itr = results.iterator();
			while (itr.hasNext()) {
				scheduledReportParams = (ScheduledReportParams) itr.next();
			}
		} catch (HibernateException e) {
			e.printStackTrace();
		} finally {
			JDBCCloser.close(session);
		}
		return scheduledReportParams;
	}

	public ArrayList getScreenDetails(String menuItemId) throws SwtException {
		String query = "";

		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		ArrayList<String> screenDetails = null;
		try{
			log.debug(this.getClass().getName() + " - [getScreenDetails] - Entry");
			query = "SELECT distinct P.PROGRAM_NAME, M.WIDTH, M.HEIGHT " +
					"FROM S_PROGRAM P, P_MENU_ITEM M " +
					"WHERE M.MENU_ITEM_ID ='" + menuItemId + "'" +
					"AND P.PROGRAM_ID = M.PROGRAM_ID";
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			stmt.execute(query);
			rs = stmt.getResultSet();
			rs.next();
			screenDetails = new ArrayList<String>();
			// the first element in the rs is the program name
			screenDetails.add(rs.getString(1));
			// the second element in the rs is the height
			screenDetails.add(rs.getString(2));
			// the third element in the rs is the width
			screenDetails.add(rs.getString(3));
			log.debug(this.getClass().getName() + " - [getScreenDetails] - Exit");
		}catch (Exception swtEx){
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScreenDetails] method : - "
					+ swtEx.getMessage());
		}finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getScreenDetails",SchedulerDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getScreenDetails",SchedulerDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return screenDetails;
	}

	public ArrayList getScheduledReportTypeConfig(String selectedJobId) throws SwtException {
		List noofRecords;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<ScheduledReportType> query = session.createQuery(
					"from ScheduledReportType s where s.jobId = :jobId",
					ScheduledReportType.class
			);
			query.setParameter("jobId", selectedJobId);
			noofRecords = query.getResultList();
		}

		log.debug("exiting getSchedulerDetails");

		return (ArrayList) noofRecords;
	}

}