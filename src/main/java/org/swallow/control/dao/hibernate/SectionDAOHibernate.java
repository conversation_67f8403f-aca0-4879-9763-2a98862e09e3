/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;
import java.util.List;

import jakarta.persistence.Query;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.swallow.control.dao.SectionDAO;
import org.swallow.control.model.Section;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

import org.hibernate.Session;
import org.hibernate.Transaction;

import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;


@Repository ("sectionDAO")
@Transactional
public class SectionDAOHibernate extends CustomHibernateDaoSupport implements
		SectionDAO {
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SectionDAOHibernate.class);

	
	public SectionDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
	    super(sessionfactory, entityManager);
	}
	
	/**
	 * @param hostId
	 * @return Collection
	 */
	@SuppressWarnings("unchecked")
	public Collection getSectionList(String hostId) throws SwtException {
		log.debug("Entering getSectionList(Section sec): ");
		try {
			Session session = getHibernateTemplate().getSessionFactory().getCurrentSession();
			String hql = "from Section c where c.id.hostId = :hostId order by c.id.sectionId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			List list = query.getResultList();
			log.debug("noofRecords.size : " + list.size());
			log.debug("exiting getSectionList(Section sec): ");
			return list;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [getSectionList] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getSectionList", SectionDAOHibernate.class);
		}
	}


	/**
	 * @param section
	 *            -object
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public void saveSectionDetail(Section section) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			// Check if the Section already exists
			String hql = "from Section c where c.id.hostId = :hostId and c.id.sectionId = :sectionId";
			session = getHibernateTemplate().getSessionFactory().openSession();
			Query query = session.createQuery(hql);
			query.setParameter("hostId", section.getId().getHostId());
			query.setParameter("sectionId", section.getId().getSectionId());

			// Execute query and check if record exists
			List records = query.getResultList();

			if (records.isEmpty()) {
				log.debug("entering saveSectionDetail");
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate().getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(section);
				tx.commit();
				session.close();
				log.debug("exiting saveSectionDetail");
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [saveSectionDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveSectionDetail", SectionDAOHibernate.class);
		}
	}


	/**
	 * @param sec -
	 *            object
	 * @return
	 */
	public void updateSectionDetail(Section sec) throws SwtException {
		log.debug("Entring updateSectionDetail");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(sec);
			tx.commit();
			session.close();
			log.debug("exiting updateSectionDetail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ "- [updateSectionDetail] - Exception "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateSectionDetail", SectionDAOHibernate.class);
		}
	}

	/**
	 * @param
	 * 
	 */
	public void deleteSectionDetail(Section section) throws SwtException {
		log.debug("entering deleteSectionDetail");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(section);
			tx.commit();
			session.close();
			log.debug("exiting deleteSectionDetail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ "- [deleteSectionDetail] - Exception "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteSectionDetail", SectionDAOHibernate.class);
		}
	}

}
