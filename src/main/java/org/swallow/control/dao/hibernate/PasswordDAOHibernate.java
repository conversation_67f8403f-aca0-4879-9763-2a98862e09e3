/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;
import jakarta.persistence.Query;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;




import org.swallow.control.dao.*;
import org.swallow.control.model.*;
import java.util.Collection;
import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;


@Repository ("passwordDAO")
@Transactional
public class PasswordDAOHibernate extends CustomHibernateDaoSupport implements PasswordDAO {
	public PasswordDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}

	
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(PasswordDAOHibernate.class);
	/**
	 * @param hostId
	 * @return Collection
	 */
	public Collection getPasswordRules(String hostId) throws SwtException {
		log.debug("Entering getPasswordRules(Password pwd)");
		Session session = getSessionFactory().getCurrentSession();
		String hql = "from Password pwd where pwd.id.hostId = :hostId";
		Query query = session.createQuery(hql);
		query.setParameter("hostId", hostId);
		List list = query.getResultList();
		log.debug("exiting getPasswordRules(Password pwd)");
		return list;
	}


	/** 
	 * @param pwdRules
	 * 
	 */
	public void updatePasswordRules(Password pwdRules)  throws SwtException{
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("Entring updatePasswordRules");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(pwdRules);
			tx.commit();
			session.close();
			log.debug("exiting updatePasswordRules");	
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updatePasswordRules] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updatePasswordRules", PasswordDAOHibernate.class);
		}
	}
}