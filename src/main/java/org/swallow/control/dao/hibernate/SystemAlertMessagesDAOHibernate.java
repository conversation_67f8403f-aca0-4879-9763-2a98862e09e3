/*
 * @(#)SystemAlertMessagesDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.NoResultException;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.SystemAlertMessagesDAO;
import org.swallow.control.model.Role;
import org.swallow.control.model.SystemAlertMesages;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


@Repository("sysAlertMessagesDAO")
@Transactional
public class SystemAlertMessagesDAOHibernate extends CustomHibernateDaoSupport
        implements SystemAlertMessagesDAO {
    
    /**
     * Final log instance for logging this class
     */
    private final Log log = LogFactory.getLog(SystemAlertMessagesDAOHibernate.class);
    
    /**
     * Constructor with dependency injection
     * @param sessionfactory The session factory
     * @param entityManager The entity manager
     */
    public SystemAlertMessagesDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    /**
     * Collects the Alert message detail list from Database table P_ALERT 
     * @param hostId The host ID
     * @return Collection of alert messages
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection getAlertMsgDetailList(String hostId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getAlertMsgDetailList] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<SystemAlertMesages> query = session.createQuery(
                "from SystemAlertMesages alert where alert.id.hostId = :hostId", 
                SystemAlertMesages.class);
            query.setParameter("hostId", hostId);
            List<SystemAlertMesages> alertList = query.getResultList();
            
            log.debug(this.getClass().getName() + " - [getAlertMsgDetailList] - Exit");
            return alertList;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getAlertMsgDetailList: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getAlertMsgDetailList", this.getClass());
        }
    }

    /**
     * Collects the Role list from database table S_Role 
     * @param hostId The host ID
     * @return Collection of roles
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection getRollList(String hostId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getRollList] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Role> query = session.createQuery(
                "from Role role where role.hostId = :hostId order by role.roleId", 
                Role.class);
            query.setParameter("hostId", hostId);
            List<Role> roleList = query.getResultList();
            
            log.debug(this.getClass().getName() + " - [getRollList] - Exit");
            return roleList;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getRollList: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getRollList", this.getClass());
        }
    }

    /**
     * Update the values in SystemAlertMesages bean in the database table P_ALERT
     * @param alertmsg The alert message to update
     * @throws SwtException If an error occurs during update
     */
    public void updateAlertMsgDetail(SystemAlertMesages alertmsg) throws SwtException {
        log.debug(this.getClass().getName() + " - [updateAlertMsgDetail] - Entry");
        
        try {
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            
            try (Session session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession()) {
                Transaction tx = session.beginTransaction();
                try {
                    session.update(alertmsg);
                    tx.commit();
                } catch (Exception e) {
                    if (tx != null) tx.rollback();
                    throw e;
                }
            }
            
            log.debug(this.getClass().getName() + " - [updateAlertMsgDetail] - Exit");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [updateAlertMsgDetail] - Exception " + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp, "updateAlertMsgDetail", SystemAlertMessagesDAOHibernate.class);
        }
    }

    /**
     * Get the Editable fields from the Database table P_ALERT 
     * @param hostId The host ID
     * @param alertstage The alert stage
     * @return SystemAlertMesages object
     * @throws SwtException If an error occurs during retrieval
     */
    public SystemAlertMesages getEditableData(String hostId, String alertstage) throws SwtException {
        log.debug(this.getClass().getName() + " - [getEditableData] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<SystemAlertMesages> query = session.createQuery(
                "from SystemAlertMesages system where system.id.hostId = :hostId and system.id.alertstage = :alertstage", 
                SystemAlertMesages.class);
            query.setParameter("hostId", hostId);
            query.setParameter("alertstage", alertstage);
            
            SystemAlertMesages systemAlert = null;
            List<SystemAlertMesages> alertMsgList = query.getResultList();
            
            if (!alertMsgList.isEmpty()) {
                systemAlert = alertMsgList.get(0);
            }
            
            log.debug(this.getClass().getName() + " - [getEditableData] - Exit");
            return systemAlert;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getEditableData: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEditableData", this.getClass());
        }
    }

    /**
     * This message checks whether the passed Alert Stage is exit with enable
     * state or not.
     * 
     * @param hostId The host ID
     * @param alertStage The alert stage
     * @return boolean True if alert is allowed, false otherwise
     * @throws SwtException If an error occurs during check
     */
    public boolean alertAllow(String hostId, String alertStage) throws SwtException {
        log.debug(this.getClass().getName() + " - [alertAllow] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<SystemAlertMesages> query = session.createQuery(
                "from SystemAlertMesages sam where sam.id.hostId = :hostId " +
                "and sam.id.alertstage = :alertStage and sam.enableflg = 'A'", 
                SystemAlertMesages.class);
            query.setParameter("hostId", hostId);
            query.setParameter("alertStage", alertStage);
            
            SystemAlertMesages systemAlertMesages = null;
            try {
                systemAlertMesages = query.getSingleResult();
            } catch (NoResultException nre) {
                // No result found, which is a valid case
                log.debug("No alert found with the given criteria");
            }
            
            boolean flag = systemAlertMesages != null;
            log.debug(this.getClass().getName() + " - [alertAllow] - Exit");
            return flag;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in alertAllow: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "alertAllow", this.getClass());
        }
    }
}