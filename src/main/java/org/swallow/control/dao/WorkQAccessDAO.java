/*
 * Created on Dec 26, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao;

import org.swallow.control.model.*;
import org.swallow.control.dao.*;
import java.util.Collection;
import org.swallow.dao.*;
import org.swallow.exception.SwtException;
/**
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface WorkQAccessDAO extends DAO{
	public Collection getCurrencyList(String entityId, String hostId)  throws SwtException;
	public Collection getWorkQAccessDetails(String entityId, String hostId,String roleId)throws SwtException;
	public Collection  getDetailsByRoleId(String hostId,String roleId)throws SwtException;
}
