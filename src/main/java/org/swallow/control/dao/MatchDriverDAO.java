/*
 * Created on Apr 28, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao;

import java.util.Collection;

import org.swallow.control.model.SystemLog;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.MatchDriver;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public interface MatchDriverDAO extends DAO {
	public Collection getMatchDriverList(String hostId, String entityId)
			throws SwtException;

	public void updateMatchDriverDetail(MatchDriver matchDriver)
			throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	public Collection getMatchDriverList(String hostId, String entityId,
			String currencyCode) throws SwtException;

	// Method added by <PERSON><PERSON> for Mantis 1420 - Test date situation can
	// lead to matching process failing to start on 14-Apr-2011
	/**
	 * This method is used to save the system log details
	 * 
	 * @param SystemLog
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void saveSystemLog(SystemLog systemLog) throws SwtException;

}
