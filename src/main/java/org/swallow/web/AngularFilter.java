package org.swallow.web;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import jakarta.servlet.http.HttpSession;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
/**
 * This filter is actually used in dev mode only with Angular client
 * It avoids error appearing when requesting a cross site request inside the Angular client
 * It is mapped for every http request (.do, .action, .js, .html ...)
 * 
 * <AUTHOR> Chebka,
 *
 */
//@WebFilter(urlPatterns = "/*")
public class AngularFilter implements Filter{
	private static final Log log = LogFactory.getLog(AngularFilter.class);
	
	// set to true when requests are from Angular client
	public static String IS_HTML5_UNDOCK = "is_html5_undock";
	public static String SUCCESS_HTML5 = "success-html5";
	public static String HTML5_SWF_MODULE = "/swf/core/Html5Module.swf";

	private static ArrayList<String> JSON_RESPONSE_LIST = new ArrayList<String>(){
		{
			add("logon!getMenu.do");
			add("logon!ngxMessages.do");
		}
	};
	
	public void init(FilterConfig arg0) throws ServletException {
		
	}

	public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest httpservletrequest = (HttpServletRequest) req;
		// Angular 4 work purpose: Error is: No 'Access-Control-Allow-Origin' header is present on the requested resource. Origin 'http://localhost:4200' is therefore not allowed access
		if(httpservletrequest.isRequestedSessionIdValid() ){

			HttpServletResponse httpservletresponse = (HttpServletResponse) resp;
			String requestQueryStr = httpservletrequest.getQueryString();
			String requestUri = httpservletrequest.getRequestURI();
			boolean responseJson = "json".equalsIgnoreCase(httpservletrequest.getParameter("response")) || (requestQueryStr!=null && requestQueryStr.indexOf("response=json")!=-1);
			boolean responseBinary = "binary".equalsIgnoreCase(httpservletrequest.getParameter("response")) || (requestQueryStr!=null && requestQueryStr.indexOf("response=binary")!=-1);
			boolean isUploadRequest = httpservletrequest.getRequestURI().indexOf("core/file!upload.do")>-1;
			boolean isChangepwdRequest = httpservletrequest.getRequestURI().indexOf("system/changepassword!save.do")>-1;

			if(responseJson || responseBinary){
				
				//System.out.println("SessionID: "+httpservletrequest.getSession().getId());
				//  If HTML5 undock call
				if(httpservletrequest.getRequestURI().indexOf("undock.do")>-1){
					httpservletrequest.setAttribute(IS_HTML5_UNDOCK, SwtConstants.STR_TRUE);
				}

				if (!httpservletresponse.containsHeader("withCredentials")) {
					httpservletresponse.addHeader("withCredentials", "true");
				}

				if (!httpservletresponse.containsHeader("crossDomain")) {
					httpservletresponse.addHeader("crossDomain", "true");
				}

				if (!httpservletresponse.containsHeader("Access-Control-Allow-Headers")) {
					httpservletresponse.addHeader("Access-Control-Allow-Headers", "UploadfileFileName,Uploadfile,UploadfileContentType,fileName,uploadPath,downloadPath,deletePath,tableName,columnName,fileSize,set-cookie,cookie,withCredentials,Authorization,JSESSIONID");
				}

				if (!httpservletresponse.containsHeader("Access-Control-Allow-Credentials")) {
					httpservletresponse.addHeader("Access-Control-Allow-Credentials", "true");
				}

				if (!httpservletresponse.containsHeader("Access-Control-Expose-Headers")) {
					httpservletresponse.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
				}

				if (httpservletrequest.getHeader("Origin") != null && !httpservletresponse.containsHeader("Access-Control-Allow-Origin")) {
					httpservletresponse.addHeader("Access-Control-Allow-Origin", httpservletrequest.getHeader("Origin"));
				}


				if (isChangepwdRequest) {
					httpservletrequest.setAttribute("cbc-mode-encryption", "cbc");
				} 

				// New session is provided by Angular CLI (even if a proxy is set)
				CommonDataManager CDM = (CommonDataManager) httpservletrequest.getSession().getAttribute(
						SwtConstants.CDM_BEAN);
				// Assign the existing CDM to current session
				if(CDM == null){
					 ConcurrentHashMap<String, HttpSession> sessions= SessionManager.getInstance().getSessionMap();
					 for(HttpSession session:sessions.values()){
						// Hard copy of session attributes
						 CDM = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
						 if(CDM != null){
							 Enumeration attrNames = session.getAttributeNames();
							 while(attrNames.hasMoreElements()){
								 String attrName = (String)attrNames.nextElement();
								 httpservletrequest.getSession().setAttribute(attrName, session.getAttribute(attrName));
							 }
							 
							 // Forcer la session via le cookie JSESSIONID
							 Cookie cookie = new Cookie("JSESSIONID", session.getId());
						     cookie.setPath(httpservletrequest.getContextPath());
						     cookie.setSecure(false);
							 httpservletresponse.addCookie(cookie);
							 
							 break;
						 }
					 }
				}
				
				// Wrapping the response to allow transforming XML content into json (ONLY Angular responses that needs json, keep XML for Flex screens)
				boolean jsonResponse = !"true".equalsIgnoreCase(httpservletrequest.getParameter("source_isflex")) && !responseBinary; 

				// Override if url is in the list
				for(String response:JSON_RESPONSE_LIST){
					if(httpservletrequest.getRequestURI().indexOf(response) != 0){
						jsonResponse = true;
						break;
					}
				}
				if(jsonResponse){
					AngularResponseWrapper angWrapper =  new AngularResponseWrapper(httpservletrequest, httpservletresponse);
					chain.doFilter(req, angWrapper);
					angWrapper.finishResponse();
				}
				else{
					chain.doFilter(req, resp);
				}
			}
			else if (Html5Mapping.mappingById.get(httpservletrequest.getParameter("programId")) != null){
				if(requestUri.indexOf(HTML5_SWF_MODULE) == -1){
					RequestDispatcher rd= httpservletrequest.getRequestDispatcher(HTML5_SWF_MODULE);
					rd.forward(httpservletrequest, httpservletresponse);
				}
				else{
					chain.doFilter(req, resp);
				}
			}
			else{
				chain.doFilter(req, resp);
			}
		}
		else{
			chain.doFilter(req, resp);// sends request to next resource
		}
	}


	
	/**
	 * Angular response wrapper
	 * It allows transforming XML content into Json
	 * 
	 */
	private static class AngularResponseWrapper extends HttpServletResponseWrapper {
		boolean isXmlResponse = true;
		HttpServletRequest request = null;
		HttpServletResponse response = null;
		ServletOutputStream stream = null;
		PrintWriter writer = null;
		  
		/**
		 * Overriden constructor
		 * @param response
		 */
		public AngularResponseWrapper(HttpServletRequest request, HttpServletResponse response) {
			super(response);
			this.request = request;
			this.response = response;
		}
		
		@Override
		public void setContentType(String type) {
			// XML content type to be set as Json (text)
			isXmlResponse = "text/xml".equalsIgnoreCase(type);
			super.setContentType(type);
		}
	
		public void finishResponse() {
			try {
				if (writer != null) {
					writer.close();
				} else {
					if (stream != null) {
						stream.close();
					}
				}
			} catch (IOException e) {
			}
		}
		  
		@Override
		public void flushBuffer() throws IOException {
			stream.flush();
		}
		  
		@Override
		public void addHeader(String name, String value) {
			// Ignore it asking for: Content-Length on json responses
			// Sometimes json content is truncated due to it.
			if(!"Content-Length".equalsIgnoreCase(name)) {
				super.addHeader(name, value);
			}
		}
	
		@Override
		public ServletOutputStream getOutputStream() throws IOException {
			if (writer != null) {
				throw new IllegalStateException("getWriter() has already been called!");
			}

			if (stream == null) {
				stream = createOutputStream();
			}
			return (stream);
		}

		@Override
		public PrintWriter getWriter() throws IOException {
			if (writer != null) {
				return (writer);
			}

			if (stream != null) {
				throw new IllegalStateException("getOutputStream() has already been called!");
			}

			stream = createOutputStream();
			writer = new PrintWriter(new OutputStreamWriter(stream, "UTF-8"));
			return (writer);
		}
	    
		private ServletOutputStream createOutputStream() throws IOException {
			final ServletOutputStream ros = super.getOutputStream();
			if(isXmlResponse){
				return new ServletOutputStream() {
					final ByteArrayOutputStream tos = new ByteArrayOutputStream();
					boolean closed = false;

					@Override
					public void write(byte[] bytes) throws IOException {
						tos.write(bytes);
					}
	
					@Override
					public void write(int arg0) throws IOException {
						tos.write(arg0);
					}

					@Override
					public void println(String s) throws IOException {
						ros.println(isXmlResponse?xmlToJson(s):s);
					}
					
					@Override
					public void write(byte[] b, int off, int len) throws IOException {
						tos.write(b, off, len);
					}
					
					@Override
					public void close() throws IOException {
						if (closed) {
							try {
								tos.close();
							} catch (Exception e) {
								// TODO: handle exception
							}
							try {
								ros.close();
							} catch (Exception e) {
								// TODO: handle exception
							}
							return;
						}

						try {
							if (isXmlResponse) {
								byte[] bytes = tos.toByteArray();
								String content = new String(bytes, "UTF-8");
								content = xmlToJson(content);
								bytes = content.getBytes();
								ros.write(bytes);
								ros.flush();
								tos.close();
								response.setContentType("text/json");
							} else {
								byte[] bytes = tos.toByteArray();
								ros.write(bytes);
								ros.flush();
								tos.close();
							}
						} catch (Exception e) {
							try {
								log.error("Error on closing cutom ServletOutputStream, cause: "+e.getMessage(), e);
							} catch (Exception e2) {
								// TODO: handle exception
							}
							byte[] bytes = tos.toByteArray();
							ros.write(bytes);
							ros.flush();
							tos.close();
						}

						ros.close();
						closed = true;
					}
					
					
					
					/**
					 * Converts XML string into Json string
					 * @param content
					 * @return
					 * @throws IOException
					 */
					private String xmlToJson(String content) throws IOException{
						try {
							DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
							// API to obtain DOM Document instance
							DocumentBuilder builder = null;
							// Create DocumentBuilder with default configuration
							builder = factory.newDocumentBuilder();
//							System.err.println("-azeaze--"+content);
							content = content.trim();
							content = StringEscapeUtils.unescapeJava(content);
							
							// Get rid of error : The entity "nbsp" was referenced, but not declared. 
							content = content.replace("&nbsp;", " ");
							
							// Parse the content to Document object
//							System.err.println("-aass--"+content);
							if(SwtUtil.isEmptyOrNull(content)){
								return  "";
							}
							Document doc = builder.parse(new InputSource(new StringReader(content)));
							String regex = "[0-9,/.-]+";
							// locate the node(s)
//								    XPath xpath = XPathFactory.newInstance().newXPath();
//								    NodeList nodes = (NodeList)xpath.evaluate
//								        ("//employee/name[text()='John']", doc, XPathConstants.NODESET);
							String delimiter = "$||$";
							int delimterLenght = delimiter.length();
							XPath xpath = XPathFactory.newInstance().newXPath();
						NodeList nodes = (NodeList) xpath.evaluate(".//grid/rows/row/node()|.//grid/totals/total/node()|.//singletons/node()", doc,
									XPathConstants.NODESET);
							for (int idx = 0; idx < nodes.getLength(); idx++) {
								// we will add delimter to all numbers all numbers to protect them from json conversion
								if(nodes.item(idx).getTextContent().trim().matches(regex))
									nodes.item(idx).setTextContent(delimiter + nodes.item(idx).getTextContent());
							}

							TransformerFactory tf = TransformerFactory.newInstance();
							Transformer transformer = tf.newTransformer();
							transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
							StringWriter writer = new StringWriter();
							transformer.transform(new DOMSource(doc), new StreamResult(writer));
							String output = writer.getBuffer().toString().replaceAll("\n|\r|\t", "");

							JSONObject jsonObj = (JSONObject) SwtUtil.invokeStaticMethod("org.json.XML", "toJSONObject",
									output);

						
//						 JSONObject json = XML.toJSONObject(xml);
							JSONTokener tokener = new JSONTokener(jsonObj.toString()) {
								public Object nextValue() throws JSONException {
									Object nextValue = super.nextValue();
									if (nextValue instanceof String) {
										String value = (String) nextValue;
										if (value.startsWith(delimiter)) {
											value = value.substring(delimterLenght);
										}
										return value.toString();
									}
									return nextValue;
								}
							};
						    jsonObj = new JSONObject(tokener);
							content = (String)SwtUtil.invokeMethod(jsonObj, "toString");

						} catch (Exception e) {
							e.printStackTrace();
							throw new IOException("XML to Json conversion error: "+e.getMessage(), e);
						}
						return content;
					}

					public boolean isReady() {
						// TODO Auto-generated method stub
						return true;
					}

					public void setWriteListener(WriteListener listener) {
						// TODO Auto-generated method stub
						
					}
				};
			}
			else{
				return ros;
			}
		}
	}
	
	/**
	 * Html 5/Flex mapping: .swf into .html
	 * 
	 * <AUTHOR> Chebka, Swallowtech Tunisia
	 *
	 */
	private static class Html5Mapping{
		String programId;
		String programName;
		String html5ProgramName;
		
		static boolean initialized = false;
		
		static ConcurrentHashMap<String, Html5Mapping> mappingById = new ConcurrentHashMap<String, AngularFilter.Html5Mapping>(){
			public Html5Mapping get(Object key) {
				if(!initialized){
					initialize();
				}
				return key != null ?super.get(key): null;
			};
			
			public Html5Mapping put(String key, Html5Mapping value) {
				if(key != null){
					return super.put(key, value);
				}
				return null;
			};
		};
		
		static ConcurrentHashMap<String, Html5Mapping> mappingByName = new ConcurrentHashMap<String, AngularFilter.Html5Mapping>(){
			public Html5Mapping get(Object key) {
				if(!initialized){
					initialize();
				}
				return key != null ?super.get(key): null;
			};
			
			public Html5Mapping put(String key, Html5Mapping value) {
				if(key != null){
					return super.put(key, value);
				}
				return null;
			};
		};
		
		public Html5Mapping(String programId, String programName, String html5ProgramName) {
			this.programId = programId;
			this.programName = programName;
			this.html5ProgramName = html5ProgramName;
		}
		
		public static void initialize(){
			try {
				XPath mapXPath =  XPathFactory.newInstance().newXPath();
				DocumentBuilderFactory dbFactory  = DocumentBuilderFactory.newInstance();
				DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
				InputStream is = AngularFilter.class.getClassLoader().getResourceAsStream("html5-mapping.xml");
				// File html5-mapping.xml may not be present
				if(is != null)
				{
					Document doc = dBuilder.parse(is);
					
					NodeList programs = (NodeList) mapXPath.compile("/s_program/*").evaluate(doc, XPathConstants.NODESET);
	
		            for (int i = 0; i < programs.getLength(); i++){
		            	Node program = programs.item(i);
		            	String progId = program.getAttributes().item(0).getNodeValue();
						String progName = null;
						String html5ProgName = null;
		            	for (int j = 0; j < program.getChildNodes().getLength(); j++){
		            		Node mapping = program.getChildNodes().item(j);
		            		if(mapping.getNodeName().equalsIgnoreCase("program_name") && mapping.getFirstChild() != null){
		            			progName = mapping.getFirstChild().getNodeValue();
		            		}
		            		else if(mapping.getNodeName().equalsIgnoreCase("html5_program_name") && mapping.getFirstChild() != null){
		            			html5ProgName = mapping.getFirstChild().getNodeValue();
		            		}
		            	}
		            	
		            	Html5Mapping mapEntry = new Html5Mapping(progId, progName, html5ProgName);
						mappingById.put(progId, mapEntry);
						mappingByName.put(progName, mapEntry);					
		            }
				}
			} catch (Exception e) {
				e.printStackTrace();
			} finally{
				initialized = true;
			}
		}

		/**
		 * @return the programId
		 */
		public String getProgramId() {
			return programId;
		}

		/**
		 * @return the programName
		 */
		public String getProgramName() {
			return programName;
		}

		/**
		 * @return the html5ProgramName
		 */
		public String getHtml5ProgramName() {
			return html5ProgramName;
		}
		
		@Override
		public String toString() {
			return 	this.getClass().getSimpleName() + "(programId=" + getProgramId() +", programName="+ getProgramName() + ", html5ProgramName=" + getHtml5ProgramName() + ")";
		}
	}
	

}