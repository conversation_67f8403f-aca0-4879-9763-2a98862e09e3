package org.swallow.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.batchScheduler.SwtJobScheduler;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuItem;
import org.swallow.model.User;
import org.swallow.service.LogonManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserInfoThread;
import org.swallow.util.UserThreadLocalHolder;


public class RequestFilter implements Filter
{
	private final Log log = LogFactory.getLog(RequestFilter.class);
	private FilterConfig config;
	 private String fail;
	private String refresh;
	private String login;
	ArrayList<String> urlsToExcludeList = null;
    public RequestFilter()
    {
    	refresh=null;
    	login=null;
    	fail=null;
    	urlsToExcludeList = new ArrayList(Arrays.asList(XSSFilter.urlsToExcludeArr));
    }

    public void init(FilterConfig filterconfig) throws ServletException
    {
       	log.info("entering 'init' method");
//       	String appName = filterconfig.getServletContext().getInitParameter(SwtConstants.APP_NAME);
//       	SwtUtil.appName = appName;    
       	String appVersion = PropertiesFileLoader.getInstance().getPropertiesVersionValue(SwtConstants.VERSION);
       	SwtUtil.appVersion=appVersion;
        config = filterconfig;
       	initRoute();       	
       	log.info("SwtUtil.appName - " + SwtUtil.appName);
       	log.info("exiting  'init' method");

    }

    public void destroy()
    {
        config = null;
        SwtJobScheduler.getInstance().shutdown();
    }

    public FilterConfig getFilterConfig()
    {
        return config;
    }

    public void setFilterConfig(FilterConfig filterconfig)
    {
       	log.debug("entering 'setFilterConfig' method");
        config = filterconfig;
       	initRoute(); 
		log.debug("exiting 'setFilterConfig' method");
    }

	
    public void doFilter(ServletRequest servletrequest, ServletResponse servletresponse, FilterChain filterchain)
        throws IOException, ServletException
    {
		log.debug("entering 'doFilter' method");

        HttpServletRequest httpservletrequest = (HttpServletRequest)servletrequest;
        HttpSession httpsession = httpservletrequest.getSession(false);
        ServletContext servletcontext = config.getServletContext();
        if(log.isDebugEnabled())
        {
        	
        	
		log.debug("httpsession - " + httpsession);
		log.debug("httpservletrequest.getPathInfo() - " + httpservletrequest.getPathInfo());
		log.debug("httpservletrequest.getPathTranslated() - " + httpservletrequest.getPathTranslated());
		log.debug("httpservletrequest.getQueryString() - " + httpservletrequest.getQueryString());
		log.debug("httpservletrequest.getContextPath() - " + httpservletrequest.getContextPath());
		log.debug("httpservletrequest.getRequestURI() - " + httpservletrequest.getRequestURI());
		log.debug("httpservletrequest.getRequestURL() - " + httpservletrequest.getRequestURL());
        }
        //Added by Bouazizi Mefteh  for Mantis 2121: redirect to main.jsp after closing a screen mid-loading  or click on/refresh the url bar
        String sessionId = httpsession!=null?httpsession.getId():"";
		boolean isValidSession = SessionManager.getInstance().getSessionMap().containsKey(sessionId);
		boolean isRefreshRequest = httpservletrequest.getParameter("refresh")!=null && httpservletrequest.getParameter("refresh").equals("true");
		boolean isRedicrect = httpservletrequest.getParameter("redirect")!=null && httpservletrequest.getParameter("redirect").equals("true");
		boolean isPreLoginData = httpservletrequest.getParameter("method")!=null && (httpservletrequest.getParameter("method").equals("preLoginScreenData") || httpservletrequest.getParameter("method").equals("initiateLogin")) ;
		boolean isPreLoginScreen = httpservletrequest.getParameter("method")!=null && httpservletrequest.getParameter("method").equals("preLoginScreen");
		
		boolean isLogonRequest = XSSFilter.getPathToFilter(httpservletrequest).startsWith("logon.do");
		boolean isLogoutRequest = XSSFilter.getPathToFilter(httpservletrequest).startsWith("logout.do");
		boolean isPasswordChangeRequest  = httpservletrequest.getParameter("screen")!=null && httpservletrequest.getParameter("screen").equals("logon");
		boolean isDFAChallenge  =  httpservletrequest.getParameter("challenge")!=null && httpservletrequest.getParameter("challenge").equals("Y") && UserThreadLocalHolder.getUserSession() == null;
		
		boolean isLoginAttempt  =  httpservletrequest.getParameter("method")!=null && httpservletrequest.getParameter("method").equals("login");
		isLoginAttempt =  isLoginAttempt && httpservletrequest.getParameter("encpasswd")!=null;
		boolean isMFASuccess = httpservletrequest.getParameter("mfaLogin")!=null &&  "success".equalsIgnoreCase(httpservletrequest.getParameter("mfaLogin"))&& httpservletrequest.getParameter("auth")!=null;
		boolean fromPreLogin = httpservletrequest.getParameter("fromPreLogin")!=null;
		boolean isFromPreLoginScreen = httpservletrequest.getParameter("isFromPreLoginScreen")!=null;
		
		
		if(isLoginAttempt) {
			String cdmUserId = null;
			String requestUserId = null;
			requestUserId = httpservletrequest.getParameter("user.id.userId");
			CommonDataManager    cdm = null;
			if(httpsession != null) {
				cdm = (CommonDataManager) (httpsession
						.getAttribute(SwtConstants.CDM_BEAN));
				if (cdm != null) {
					cdmUserId = cdm.getUser().getId().getUserId();
					if(!cdmUserId.equals(requestUserId))
						isValidSession = false;
				}
			}
			
			try {
				HttpServletResponse httpservletresponse = ( HttpServletResponse ) servletresponse;
				Cookie uiColorCookie = new Cookie("dXNl-cm5h-bWU", SwtUtil.encode64(requestUserId));
				uiColorCookie.setPath("; HttpOnly;");
				uiColorCookie.setSecure(true);
			    
	    		httpservletresponse.addCookie(uiColorCookie);
			} catch (Exception e) {
			}
			
			// Adding the bearer cookie
			if(cdm != null && !SwtUtil.isEmptyOrNull(cdm.getBearerToken())) {
				addCookieToResponse((HttpServletResponse) servletresponse, SwtConstants.BEARERTOKEN, cdm.getBearerToken());
			}
		}

		if(isValidSession && !isRedicrect && !isDFAChallenge && (isLogonRequest || isRefreshRequest) && !isPreLoginData && !isMFASuccess && !isPreLoginScreen && !fromPreLogin && !isFromPreLoginScreen)
        {
			CommonDataManager    cdm = (CommonDataManager)httpsession.getAttribute(SwtConstants.CDM_BEAN);
			LogonBean logBean = new LogonBean();
			LogonManager logon = (LogonManager) (SwtUtil.getBean("logonManager"));
			
			// Adding the bearer cookie
			if(cdm != null && !SwtUtil.isEmptyOrNull(cdm.getBearerToken())) {
				addCookieToResponse((HttpServletResponse) servletresponse, SwtConstants.BEARERTOKEN, cdm.getBearerToken());
			}
						
			//When the session is valid (the user is connected successfully) and the CommonDataManager is not loaded create a new CommonDataManager and reset settings
			if(cdm == null && httpservletrequest.getParameter("user.id.userId") != null) {
				User userdb = null;
				// Setting the CommonDataManager in Session
				String userId = httpservletrequest.getParameter("user.id.userId");
				cdm = new CommonDataManager();
				try {
					userdb = logon.getUserDetail(CacheManager.getInstance().getHostId(), userId);
				} catch (SwtException e) {
				}
				httpservletrequest.getSession().setAttribute(SwtConstants.CDM_BEAN,
						cdm);
				cdm.setUser(userdb);
			}
			if(cdm != null) {
				logBean.afterLogonProcess(httpservletrequest, cdm.getUser(),logon);
				// Set User preferences and remove Movement locks
				SwtUtil.setUserPreferences(httpservletrequest);
		        RequestDispatcher requestdispatcher = servletcontext
				.getRequestDispatcher("/logon.do?redirect=true&method=reLogin");
		        requestdispatcher.forward(servletrequest, servletresponse);

			}else {
				//UserThreadLocalHolder.setUserInfo(null);
				
			    Enumeration attributeNames = httpsession.getAttributeNames();     
			    HashMap<String,Object> storedAttributes=new HashMap<String,Object>();  
			    while (attributeNames.hasMoreElements())
			    {
			      String key = (String)attributeNames.nextElement();
			      storedAttributes.put(key,httpsession.getAttribute(key));
			      httpsession.removeAttribute(key);      
			    }
			    httpsession.invalidate();
			    
			    httpsession=httpservletrequest.getSession(true);
			    for(Map.Entry attribue:storedAttributes.entrySet())
			    {
			    	httpsession.setAttribute((String)attribue.getKey(),attribue.getValue());  
			    	storedAttributes.remove(attribue);
			    }  
				 RequestDispatcher requestdispatcher = servletcontext
							.getRequestDispatcher(login);
					        requestdispatcher.forward(servletrequest, servletresponse);
			}
        }else{
	        CommonDataManager cdm = null;
	        if(httpsession != null && httpsession.getId() != null )
	        {
	        	cdm = (CommonDataManager)httpsession.getAttribute(SwtConstants.CDM_BEAN);
	        	log.debug("cdm - " + cdm);
				if(cdm != null)
				{
					if(isPasswordChangeRequest && isValidSession){
						// Set User preferences and remove Movement locks
						SwtUtil.setUserPreferences(httpservletrequest);
					}
					String userId = cdm.getUser().getId().getUserId();
					log.debug("userId - " + userId);
					UserInfoThread userInfo = new UserInfoThread(userId,httpsession, servletrequest.getRemoteAddr());
					UserThreadLocalHolder.setUserInfo(userInfo);
					
					// Adding the bearer cookie
					if(!SwtUtil.isEmptyOrNull(cdm.getBearerToken())) {
						addCookieToResponse((HttpServletResponse) servletresponse, SwtConstants.BEARERTOKEN, cdm.getBearerToken());
					}
				}else {
					//UserThreadLocalHolder.setUserInfo(null);
					
				    Enumeration attributeNames = httpsession.getAttributeNames();     
				    HashMap<String,Object> storedAttributes=new HashMap<String,Object>();  
				    while (attributeNames.hasMoreElements())
				    {
				      String key = (String)attributeNames.nextElement();
				      storedAttributes.put(key,httpsession.getAttribute(key));
				      httpsession.removeAttribute(key);      
				    }
				    httpsession.invalidate();
				    
				    httpsession=httpservletrequest.getSession(true);
				    
				    for(Map.Entry attribue:storedAttributes.entrySet())
				    {
				    	httpsession.setAttribute((String)attribue.getKey(),attribue.getValue());  
				    	storedAttributes.remove(attribue);
				    }  
				    
				   // Remove the bearer token cookie
				   // removeCookieFromResponse((HttpServletResponse) servletresponse, SwtConstants.BEARERTOKEN);
				}
				filterchain.doFilter(servletrequest, servletresponse);
	        }else {
	        	filterchain.doFilter(servletrequest, servletresponse);
	        }
		}
		log.debug("exiting 'doFilter' method");
    }

    /**
     * Adds a cookie to the http response
     * @param httpservletresponse
     * @param name
     * @param value
     */
    private void addCookieToResponse(HttpServletResponse httpservletresponse, String name, String value) {
    	try {
			Cookie cookie = new Cookie(name, value);
			cookie.setPath("; HttpOnly;");
			cookie.setSecure(true);
			//cookie.setPath("/swallowtech");
			cookie.setMaxAge(3600*2);
    		httpservletresponse.addCookie(cookie);
		} catch (Exception e) {
			//
		}
    }
    
    /**
     * Removes the cookie from http response
     * @param httpservletresponse
     * @param name
     */
    private void removeCookieFromResponse (HttpServletResponse httpservletresponse, String name) {
    	Cookie cookie = new Cookie(name, "");
		cookie.setPath("; HttpOnly;");
		cookie.setSecure(true);
		//cookie.setPath("/swallowtech");
		cookie.setMaxAge(0);
		httpservletresponse.addCookie(cookie);
    }
    
    /**
     * Added by Bouazizi Mefteh for Mantis 1534.
     * Init the fail parameter
     */
    public void initRoute() {
        ServletContext servletcontext = config.getServletContext();
        fail = config.getInitParameter("fail");
        if (fail == null) {
            log.warn("Request Filter:could not get an initial parameter fail");
        }
        refresh = config.getInitParameter("refresh");
        if (refresh == null) {
            log.warn("Request Filter:could not get an initial parameter refresh");
        }
        login = config.getInitParameter("login");
        if (login == null) {
        	log.warn("Request Filter:could not get an initial parameter login");
        }
       
    }

}