/*
 * @(#)FILENAME.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
/*
 * Created on May 15, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.web;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.io.UnsupportedEncodingException;
import java.security.Security;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.StringTokenizer;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;


/**
 * DOCUMENT ME!
 *
 * <AUTHOR> TODO To change the template for this generated type comment
 *         go to Window - Preferences - Java - Code Style - Code Templates
 */
public class Config {
    /** DOCUMENT ME!  */
    private static Config utilsInst = null;

    /** DOCUMENT ME!  */
    private final static Log log = LogFactory.getLog(Config.class);

    /** DOCUMENT ME!  */
    private Hashtable hshConfigValues = new Hashtable();

    /**
     * Creates a new Config object.
     *
     * @throws SwtException DOCUMENT ME!
     */
    private Config() throws SwtException {
        CacheManager cacheManagerInst;

        try {
            cacheManagerInst = CacheManager.getInstance();

            if (!(cacheManagerInst.getmaxUser() == null)) {
                hshConfigValues.put(SwtConstants.SWT_MAX_USER_ID,
                    cacheManagerInst.getmaxUser());
            }

            if (!(cacheManagerInst.getexpiryDate() == null)) {
                hshConfigValues.put(SwtConstants.SWT_EXPIRY_DATE_ID,
                    cacheManagerInst.getexpiryDate());
            }

            if (!(cacheManagerInst.getserverName() == null)) {
                hshConfigValues.put(SwtConstants.SWT_SERVER_ID,
                    cacheManagerInst.getserverName());
            }
            /* Start:Code added by venkat for mantis 1267 on 2-Dec-2010 */
            if (!(cacheManagerInst.getHost() == null)) {
                hshConfigValues.put(SwtConstants.SWT_HOST,
                    cacheManagerInst.getHost());
            }
            if (!(cacheManagerInst.getLicenseHashCode() == null)) {
                hshConfigValues.put(SwtConstants.LICENSE_HASHCODE,
                    cacheManagerInst.getLicenseHashCode());
            }            
            /* End:Code added by venkat for mantis 1267 on 2-Dec-2010 */            
        } catch (SwtException e) {
            e.printStackTrace();
        }
    }

    /**
     * DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     *
     * @throws SwtException DOCUMENT ME!
     */
    public static Config getInstance() throws SwtException {
        log.debug("entering 'getInstance()' method ");

        if (utilsInst == null) {
            log.debug("Singelton Instance is null");
            utilsInst = new Config();
        }

        log.debug("exiting 'getInstance()' method ");

        return utilsInst;
    }

    /**
     * Retrieves the specified parameter value.
     *
     * @param parameter The name of the parameter to retrieve.
     *
     * @return The parameter value, or null if it does not exist.
     */
    /* Start:code modified by venkat for mantis 1267 on 2-Dev-2010. */
    public String get(String parameter) {
        if ((parameter.equals("Users")) || (parameter.equals("ExpiryDate"))
                || (parameter.equals("ipAddress"))||(parameter.equals("Host"))||(parameter.equals("Code"))) {
            if (!(( String ) hshConfigValues.get(parameter) == null)) {
                return ( String ) hshConfigValues.get(parameter);
            } else {
                return null;
            }
        }
        else {
            return null;
        }
    }
    /* End:code modified by venkat for mantis 1267 on 2-Dev-2010. */    
    /* Start :  Added for Mantis Issue No : 430 */
    /* Code modified by venkat for mantis 1267 on 02-Dec-2010. */
    public ArrayList<String> getIPAddress(String parameter) {
        if (parameter.equals("Hostname")) {
            if (!(( ArrayList<String> ) hshConfigValues.get(parameter) == null)) {
                return ( ArrayList<String> ) hshConfigValues.get(parameter);
            } else {
                return null;
            }
        }
        else {
            return null;
        }
    }
    /* END   :  Added for Mantis Issue No : 430 */
    /**
     * DOCUMENT ME!
     *
     * @param inputString DOCUMENT ME!
     * @param teb DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     *
     * @throws Exception DOCUMENT ME!
     */
    protected byte[] encrypt(byte[] inputString, byte[] teb)
        throws Exception {
    	// Java has a list of built-in security providers already,
    	// so there is no need to add platform specific providers.
    	// Indeed adding this will make it fail on AIX for example.
    	// Security.addProvider(new com.sun.crypto.provider.SunJCE());

        //create and initalise our key and cipher objects
        SecretKey key = new SecretKeySpec(teb, "DES");
        Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");

        //Cipher cipher = Cipher.getInstance("DES");
        //Initialize the cipher to encryption mode
        cipher.init(Cipher.ENCRYPT_MODE, key);

        //get input string in bytes
        byte[] inputBytes = inputString;

        //do the encryption
        byte[] encryptedBytes = cipher.doFinal(inputBytes);

        //return the encrypted string
        return encryptedBytes;
    }

    /**
     * DOCUMENT ME!
     *
     * @param value DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     */
    public String encrypt(String value) {
        StringBuffer encStr = new StringBuffer();

        try {
            byte[] encBytes = encrypt(value.getBytes("ISO8859-1"));

            for (int i = 0; i < encBytes.length; ++i) {
                encStr.append(new Byte(encBytes[i]).longValue()).append(",");
            }

            encStr.setLength(encStr.length() - 1);

            return encStr.toString();
        } catch (UnsupportedEncodingException e) {
            log.error(e);

            return "";
        }
    }

    /**
     * DOCUMENT ME!
     *
     * @param str DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     */
    protected final byte[] encrypt(byte[] str) {
        try {
            return encrypt(str, this.getKey());
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     */
    private final byte[] getKey() {
        try {
            String source = "Thequickbrownfoxjumpsoverthelazydog";
            String tmp = source.substring(10, 11);
            tmp = tmp.concat(source.substring(3, 4));
            tmp = tmp.concat(source.substring(32, 33));
            tmp = tmp.concat(source.substring(18, 19));
            tmp = tmp.concat(source.substring(21, 22));
            tmp = tmp.concat(source.substring(3, 5));
            tmp = tmp.concat(source.substring(12, 13));

            return tmp.getBytes("ISO8859-1");
        } catch (java.io.UnsupportedEncodingException ex) {
            return null;
        }
    }

    /**
     * Retrieves the specified parameter value but decrypts it first. This is
     * used for passwords stored in a config file. These passwords are
     * actually stored as a comma seperated list of long values, so these must
     * be interpreted and then converted to a byte array before the decryption
     * can take place.
     *
     * @param parameter The name of the parameter to retrieve.
     *
     * @return The parameter value, or null if it does not exist.
     */
    public byte[] getPassword(String parameter) {
        try {
            //String str = new String((String)hshConfigValues.get(parameter));
            //create the byte[] array
            //System.out.println(" The value in getPassword() is "+parameter);
            log.debug("inside getPassword() method ");

            StringTokenizer splitPassword = new StringTokenizer(parameter, ",");
            int i = 0;

            while (splitPassword.hasMoreTokens()) {
                splitPassword.nextToken();
                i++;
            }

            byte[] pass = new byte[i];

            //now populate it
            splitPassword = new StringTokenizer(parameter, ",");
            i = 0;

            while (splitPassword.hasMoreTokens()) {
                pass[i++] = Byte.parseByte(splitPassword.nextToken());
            }

            //return the decrypted string
            return Config.decrypt(pass, this.getKey());
        } catch (Exception ex) {
            ex.printStackTrace();

            return null;
        }
    }

    /**
     * DOCUMENT ME!
     *
     * @param inputString DOCUMENT ME!
     * @param teb DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     *
     * @throws Exception DOCUMENT ME!
     */
    public static byte[] decrypt(byte[] inputString, byte[] teb)
        throws Exception {
    	// Java has a list of built-in security providers already,
    	// so there is no need to add platform specific providers.
    	// Indeed adding this will make it fail on AIX for example.
    	// Security.addProvider(new com.sun.crypto.provider.SunJCE());

        //create and initalise our key and cipher objects
        SecretKey key = new SecretKeySpec(teb, "DES");
        Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");

        //Cipher cipher = Cipher.getInstance("DES");
        //Initialize the cipher to decryption mode
        cipher.init(Cipher.DECRYPT_MODE, key);

        //get input string in bytes
        byte[] inputBytes = inputString;

        //do the decryption
        byte[] decryptedBytes = cipher.doFinal(inputBytes);

        //return the decrypted string
        return decryptedBytes;
    }

    /**
     * DOCUMENT ME!
     *
     * @param str DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     */
    protected final byte[] decrypt(byte[] str) {
        try {
            return decrypt(str, this.getKey());
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * DOCUMENT ME!
     *
     * @param value DOCUMENT ME!
     *
     * @return DOCUMENT ME!
     */
    public String decrypt(String value) {
        String decValue = "";
        byte[] decBytes = getPassword(value);

        for (int i = 0; i < decBytes.length; ++i) {
        	decValue += decBytes[i];
        }
        return new String(decBytes);
    }
}
