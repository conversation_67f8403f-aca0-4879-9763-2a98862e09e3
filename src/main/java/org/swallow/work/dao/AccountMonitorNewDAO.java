/*
 * @(#)AccountMonitorNewDAO.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Collection;
import java.util.Date;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.work.service.AccountMonitorNewDetailVO;

/**
 * <pre>
 * DAO layer for Account Monitor screen. This interface performs 
 * following tasks 
 *  - Gets account details and linked account list
 *  - Updates sum flag of accounts
 *  - Updates loro flag for loro accounts
 *  - Account monitor details
 * </pre>
 */
public interface AccountMonitorNewDAO {

	/**
	 * Interface definition which will return a collection of accountId and its
	 * linked accountId
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            accountId
	 * @return Collection<AcctMaintenance>
	 * @throws SwtException
	 */
	public Collection<AcctMaintenance> getAccountAndLinkedAccountsList(
			String hostId, String entityId, String currencyCode,
			String accountId) throws SwtException;

	/**
	 * Interface definition which will update monitor sum flag of accounts which
	 * are provided to it as inputs
	 * 
	 * @param Collection
	 *            <AcctMaintenance> colAcctMaintanance
	 * @throws SwtException
	 */
	public void updateMonitorSum(Collection<AcctMaintenance> colAcctMaintanance)
			throws SwtException;

	/**
	 * Interface definition which will update Loro_To_Predicted flag for the
	 * selected account
	 * 
	 * @param AcctMaintenance
	 *            accountMaintenance
	 * @throws SwtException
	 */
	public void updateLoroToPredictedFlag(AcctMaintenance accountMaintenance)
			throws SwtException;

	/**
	 * Interface definition which is used to get the predicted balances, loro
	 * balances,Unsettled balances,Unexpected balances,External balances Start
	 * of day balances and open unexpected balances. For that it invokes this
	 * stored procedure PKG_ACCOUNT_MONITOR.SP_ACCOUNT_MONITOR_JOB
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            accountType
	 * @param Date
	 *            dateParam
	 * @param boolean
	 *            isCacheSearch
	 * @param String
	 *            applyCurrencyThreshold
	 * @param String
	 *            accountClass
	 * @param String
	 *            hideZeroBalances
	 * @param String
	 *            roleId
	 * @return AccountMonitorNewDetailVO
	 * @throws SwtException
	 */
	public AccountMonitorNewDetailVO getAllBalancesUsingStoredProc(
			String hostId, String entityId, String currencyCode,
			String accountType, Date dateParam, boolean isCacheSearch,
			String applyCurrencyThreshold, String accountClass,
			String hideZeroBalances, String roleId) throws SwtException;

}
