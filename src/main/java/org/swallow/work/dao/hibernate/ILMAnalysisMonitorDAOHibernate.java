/*
 * @(#)ILMAnalysisMonitorDAOHibernate.java 1.0 29/11/2013
 *
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

/*
 * Created on Nov 29, 2013
 *
 * To change the template for this generated file go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataAccessResourceFailureException;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.control.model.Archive;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.pcm.report.model.PCReport;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.reports.model.ThroughputRatioReport;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.ILMAnalysisMonitorDAO;
import org.swallow.work.model.ILMSummaryRecord;
import org.swallow.work.model.ThroughputMonitorRecord;

import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;

/**
 * <pre>
 * DAO layer for ILM Analysis Monitor, Used to
 * -
 * -
 * -
 * </pre>
 */
@Repository("ilmAnalysisMonitorDAO")
@Transactional(propagation = Propagation.REQUIRED)
public class ILMAnalysisMonitorDAOHibernate extends CustomHibernateDaoSupport implements
        ILMAnalysisMonitorDAO {
    /**
     * Log object
     */
    private static Log log = LogFactory
            .getLog(ILMAnalysisMonitorDAOHibernate.class);

    public ILMAnalysisMonitorDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    public String getChartsData(String hostId, String entityId,
                                String currencyId, Date selectedDate, String userId, String dbLink,
                                String selectedFigures, String sumByCutOff, String includeOpenMvnts)
            throws SwtException {

        String datasets = null;

        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("select PKG_ILM.FN_GET_TIME_SERIES_ALL(?,?,?,?,?,?,?,?,?) from dual")
        ) {
            log.debug(this.getClass().getName() + " - [ getChartsData ]- Entry");

            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setDate(4, SwtUtil.truncateDateTime(selectedDate));
            cstmt.setString(5, userId);
            cstmt.setString(6, dbLink);
            cstmt.setString(7, selectedFigures);
            cstmt.setString(8, "true".equals(sumByCutOff) ? "Y" : "N");
            cstmt.setString(9, "true".equals(includeOpenMvnts) ? "Y" : "N");

            try (ResultSet rs = cstmt.executeQuery()) {
                if (rs.next()) {
                    datasets = rs.getString(1);
                }
            }
            log.debug(this.getClass().getName() + " - [ getChartsData ]- Exit");
        } catch (SQLException sqlException) {
            throw SwtErrorHandler.getInstance().handleException(sqlException,
                    "getChartsData", ILMAnalysisMonitorDAOHibernate.class);
        } catch (Exception exp) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [ getChartsData ] method : - "
                      + exp.getMessage());
            throw new SwtException(exp.getMessage());
        }
        return datasets;
    }

    public List<ILMAccountGroup> getGroupsDetailForGrid(String hostId, String entityId,
                                                        String currencyId, Date selectedDate, String userId,
                                                        String dbLink, String isGlobal, String includeOpenMvnts) throws SwtException {

        List<ILMAccountGroup> grpList = new ArrayList<>();
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{call PKG_ILM.SP_GET_GROUPS(?,?,?,?,?,?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName() + " - [ getGroupsDetailForGrid ]- Entry");

            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setDate(4, SwtUtil.truncateDateTime(selectedDate));
            cstmt.setString(5, userId);
            cstmt.setString(6, dbLink);
            cstmt.setString(7, isGlobal);
            cstmt.setString(8, "true".equals(includeOpenMvnts) ? "Y" : "N");
            cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();

            try (ResultSet rs = (ResultSet) cstmt.getObject(9)) {
                while (rs != null && rs.next()) {
                    ILMAccountGroup ilmGroup = new ILMAccountGroup();
                    ilmGroup.getId().setIlmGroupId(rs.getString(1));
                    ilmGroup.setIlmGroupName(rs.getString(2));
                    ilmGroup.setIlmGroupDescription(rs.getString(3));
                    ilmGroup.setExternalSOD(rs.getString(4));
                    ilmGroup.setForecastSOD(rs.getString(5));
                    ilmGroup.setExternalEOD(rs.getString(6));
                    ilmGroup.setForecastEOD(rs.getString(7));
                    ilmGroup.setThresholdMin1(rs.getString(8));
                    ilmGroup.setThresholdMin2(rs.getString(9));
                    ilmGroup.setThresholdMax1(rs.getString(10));
                    ilmGroup.setThresholdMax2(rs.getString(11));
                    ilmGroup.setPublicPrivate(rs.getString(12));
                    ilmGroup.setCreatedByUser(rs.getString(13));
                    ilmGroup.setLastUpdated(rs.getString(14));
                    ilmGroup.setGlobal(rs.getString(15));
                    ilmGroup.setOpenUnsett(rs.getString(16));
                    ilmGroup.setOpenUnexp(rs.getString(17));
                    ilmGroup.setDefaultLegendText(rs.getString(18));
                    grpList.add(ilmGroup);
                }
            }
            log.debug(this.getClass().getName() + "-[ getGroupsDetailForGrid ]- Exit");
        } catch (DataAccessResourceFailureException | IllegalStateException | HibernateException | SQLException e) {
            log.error(e.getClass().getSimpleName() + " caught in "
                      + this.getClass().getName()
                      + "-[getGroupsDetailForGrid]-" + e.getMessage());
            if (e instanceof SQLException && ((SQLException) e).getErrorCode() == 2019) {
                throw new SwtException(SwtConstants.ILM_DB_LINK_FAIL);
            }
            throw SwtErrorHandler.getInstance().handleException(e, "getGroupsDetailForGrid",
                    ILMAnalysisMonitorDAOHibernate.class);
        }
        return grpList;
    }

    public List<ILMScenario> getScenariosDetail(String hostId, String entityId,
                                                String currencyId, String userId, String isGlobal)
            throws SwtException {

        List<ILMScenario> scnList = new ArrayList<>();
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{call pkg_ilm.SP_GET_SCENARIOS(?,?,?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName() + " - [ getScenariosDetail ]- Entry");
            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setString(4, userId);
            cstmt.setString(5, isGlobal);
            cstmt.registerOutParameter(6, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();

            try (ResultSet rs = (ResultSet) cstmt.getObject(6)) {
                while (rs != null && rs.next()) {
                    ILMScenario ilmscen = new ILMScenario();
                    ilmscen.getId().setIlmScenarioId(rs.getString(1));
                    ilmscen.setIlmScenarioName(rs.getString(2));
                    ilmscen.setPublicPrivate(rs.getString(3));
                    ilmscen.setCreatedByUser(rs.getString(4));
                    scnList.add(ilmscen);
                }
            }
            log.debug(this.getClass().getName() + "-[ getScenariosDetail ]- Exit");
        } catch (DataAccessResourceFailureException | IllegalStateException | HibernateException | SQLException e) {
            log.error(e.getClass().getSimpleName() + " caught in "
                      + this.getClass().getName()
                      + "-[getScenariosDetail]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getScenariosDetail",
                    ILMAnalysisMonitorDAOHibernate.class);
        }
        return scnList;
    }

    public ILMAccountGroup getGroupDetails(String hostId, String entityId, String currencyId, String group) throws SwtException {

        ILMAccountGroup accountGroup = null;
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection connection = SwtUtil.connection(session);
                CallableStatement cstmt = connection.prepareCall("{call PKG_ILM.FN_GET_GROUP_THRESHOLDS(?,?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName() + " - [getGroupDetails] - " + "Entry");

            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setString(4, group);
            cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();

            try (ResultSet rs = (ResultSet) cstmt.getObject(5)) {
                if (rs != null && rs.next()) {
                    accountGroup = new ILMAccountGroup();
                    accountGroup.getId().setIlmGroupId(rs.getString(1));
                    accountGroup.setIlmGroupName(rs.getString(2));
                    accountGroup.setIlmGroupDescription(rs.getString(3));
                    accountGroup.setHostId(rs.getString(4));
                    accountGroup.setEntityId(rs.getString(5));
                    accountGroup.setThresholdMin1(SwtUtil.isEmptyOrNull(rs.getString(6)) ? "" : rs.getString(6));
                    accountGroup.setThresholdMin2(SwtUtil.isEmptyOrNull(rs.getString(7)) ? "" : rs.getString(7));
                    accountGroup.setThresholdMax1(SwtUtil.isEmptyOrNull(rs.getString(8)) ? "" : rs.getString(8));
                    accountGroup.setThresholdMax2(SwtUtil.isEmptyOrNull(rs.getString(9)) ? "" : rs.getString(9));
                    accountGroup.setFilterCondition(rs.getString(10));
                    accountGroup.setPublicPrivate(rs.getString(11));
                    accountGroup.setCreatedByUser(rs.getString(12));
                    accountGroup.setCreateDate(rs.getDate(13));
                }
            }
            log.debug(this.getClass().getName() + " - [getGroupDetails] - " + "Exit");
        } catch (SQLException sqlException) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getGroupDetails] method : - "
                      + sqlException.getMessage());
            throw SwtErrorHandler.getInstance().handleException(sqlException,
                    "getGroupDetails", ILMAnalysisMonitorDAOHibernate.class);
        } catch (Exception exp) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getGroupDetails] method : - "
                      + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                    "getGroupDetails",
                    ILMAnalysisMonitorDAOHibernate.class);
        }
        return accountGroup;
    }


    public HashMap<String, String> getProcessState(String hostId,
                                                   String entityId, String currencyId, Date selectedDate, String roleId, String userId, String dbLink)
            throws SwtException {

        HashMap<String, String> map = new LinkedHashMap<>();
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{call PKG_ILM.SP_GET_STATE_DATA (?,?,?,?,?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName() + " - [ getProcessState ]- Entry");

            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setDate(4, SwtUtil.truncateDateTime(selectedDate));
            cstmt.setString(5, roleId);
            cstmt.setString(6, userId);
            cstmt.setString(7, dbLink);
            cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();

            try (ResultSet rs = (ResultSet) cstmt.getObject(8)) {
                if (rs != null && rs.next()) {
                    map.put("ACCOUNTS_NUMBER", rs.getString(1));
                    map.put("ACCOUNTS_NEW_DATA", rs.getString(2));
                    map.put("ACCOUNTS_INCOMPLETE", rs.getString(3));
                    map.put("ACCOUNTS_INCONSISTENT", rs.getString(4));
                    map.put("END_DATE", rs.getString(5));
                    map.put("RECALCULATE", rs.getString(6));
                    map.put("ENTITY", entityId);
                    map.put("CURRENCY", currencyId);
                    map.put("IS_ALREADY_RUNNING", rs.getString(7));
                }
            }
            log.debug(this.getClass().getName() + " - [ getProcessState ]- Exit");
        } catch (SQLException sqlException) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getProcessState] method : - "
                      + sqlException.getMessage());
            throw SwtErrorHandler.getInstance().handleException(sqlException,
                    "getProcessState", ILMAnalysisMonitorDAOHibernate.class);
        } catch (Exception exp) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getProcessState] method : - "
                      + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                    "getProcessState",
                    ILMAnalysisMonitorDAOHibernate.class);
        }
        return map;
    }

    public String getCurrencyTimeframe(String entityId, String currencyId, Date selectedDate, String roleId) throws SwtException {

        String ccyTimframe = null;
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("select PKG_ILM.FN_GET_TIMEFRAME(?,?,?,?) from dual")
        ) {
            log.debug(this.getClass().getName() + " - [ getCurrencyTimeframe ]- Entry");
            cstmt.setString(1, entityId);
            cstmt.setString(2, currencyId);
            cstmt.setDate(3, SwtUtil.truncateDateTime(selectedDate));
            cstmt.setString(4, roleId);

            try (ResultSet rs = cstmt.executeQuery()) {
                if (rs.next()) {
                    ccyTimframe = rs.getString(1);
                }
            }
            log.debug(this.getClass().getName() + " - [ getCurrencyTimeframe ]- Exit");
            return ccyTimframe;
        } catch (HibernateException hibernateException) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getCurrencyTimeframe] method : - "
                      + hibernateException.getMessage());
            throw SwtErrorHandler.getInstance().handleException(
                    hibernateException, "getCurrencyTimeframe",
                    ILMAnalysisMonitorDAOHibernate.class);
        } catch (SQLException sqlException) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getCurrencyTimeframe] method : - "
                      + sqlException.getMessage());
            throw SwtErrorHandler.getInstance().handleException(sqlException,
                    "getCurrencyTimeframe", ILMAnalysisMonitorDAOHibernate.class);
        }
    }

    public String getCcyMultiplierAndDecimalPlaces(String entityId, String currencyId) throws SwtException {

        String returnString = null;
        String SQLQuery = "SELECT CM.DECIMAL_PLACES, DECODE (C.MONITOR_DISPLAY_MULTIPLIER, 'T', 1000, 'M', 1000000, 'B', 1000000000, 1) " +
                          "FROM s_currency C, s_currency_master CM " +
                          "WHERE C.currency_code = CM.currency_code " +
                          "AND CM.currency_code = '" + currencyId + "' " +
                          "AND C.entity_id = '" + entityId + "'";
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                Statement statement = conn.createStatement()
        ) {
            log.debug(this.getClass().getName() + " - [ getCcyMultiplierAndDecimalPlaces ]- Entry");
            try (ResultSet rs = statement.executeQuery(SQLQuery)) {
                if (rs != null && rs.next()) {
                    returnString = rs.getString(1) + "," + rs.getString(2);
                } else {
                    returnString = "2,1";
                }
            }
            log.debug(this.getClass().getName() + " - [ getCcyMultiplierAndDecimalPlaces ]- Exit");
            return returnString;
        } catch (HibernateException hibernateException) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getCcyMultiplierAndDecimalPlaces] method : - "
                      + hibernateException.getMessage());
            throw SwtErrorHandler.getInstance().handleException(
                    hibernateException, "getCcyMultiplierAndDecimalPlaces",
                    ILMAnalysisMonitorDAOHibernate.class);
        } catch (SQLException sqlException) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [getCcyMultiplierAndDecimalPlaces] method : - "
                      + sqlException.getMessage());
            throw SwtErrorHandler.getInstance().handleException(sqlException,
                    "getCcyMultiplierAndDecimalPlaces", ILMAnalysisMonitorDAOHibernate.class);
        }
    }

    public String getCurrentDbLink(String hostId) throws SwtException {
        log.debug(this.getClass().getName() + " - [ getCurrentDbLink ]- Entry");
        String dbLink = null;
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            List<Archive> dbColl = session.createQuery(
                            "from Archive a where a.id.hostId = :hostId and a.defaultDb = 'Y' and a.moduleId = 'Predict'",
                            Archive.class)
                    .setParameter("hostId", hostId)
                    .getResultList();
            log.debug(this.getClass().getName() + " - [ getCurrentDbLink ]- Exit");
            if (!dbColl.isEmpty()) {
                dbLink = dbColl.get(0).getDb_link();
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception Catched in [getCurrentDbLink] method : - " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrentDbLink", ILMAnalysisMonitorDAOHibernate.class);
        }
        return dbLink;
    }

    public String recalculateData(String hostId, String entityId,
                                  String currencyId, String selectedDateStr, String dbLink, String sequenceNumber, CommonDataManager CDM)
            throws SwtException {

        String result = null;
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{call PKG_ILM.ILM_MAIN_PROCESSING (?,?,?,?,?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName() + " - [ recalculateData ]- Entry");
            CDM.getIlmScreenConnectionDetails().put(sequenceNumber, cstmt);
            cstmt.setString(1, dbLink);
            cstmt.setString(2, sequenceNumber);
            cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.VARCHAR);
            cstmt.setString(4, hostId);
            cstmt.setString(5, entityId);
            cstmt.setString(6, currencyId);
            cstmt.setDate(7, SwtUtil.truncateDateTime(SwtUtil.parseDateGeneral(selectedDateStr)));
            cstmt.setString(8, CDM.getUser().getId().getUserId());

            cstmt.execute();
            result = cstmt.getString(3);
            log.debug(this.getClass().getName() + " - [ recalculateData ]- Exit");
        } catch (Exception exp) {
// Roll back data
            try {
                // If connection is available, rollback
                Connection conn = null;
                if (CDM.getIlmScreenConnectionDetails().get(sequenceNumber) instanceof CallableStatement) {
                    CallableStatement cstmt = (CallableStatement) CDM.getIlmScreenConnectionDetails().get(sequenceNumber);
                    conn = cstmt.getConnection();
                }
                if (conn != null) {
                    conn.rollback();
                }
            } catch (SQLException e1) {
                // ignore
            }
// log error when isn't a cancellation request 
            if (CDM.getIlmScreenConnectionDetails().get(sequenceNumber) != null) {
                log.error(this.getClass().getName()
                          + " - Exception Catched in [ recalculateData ] method : - "
                          + exp.getMessage());
                throw SwtErrorHandler.getInstance().handleException(exp, "recalculateData", ILMAnalysisMonitorDAOHibernate.class);
            }
        } finally {
// Clean up after error
            try {
                cleanUpProcessDriver(sequenceNumber, CDM.getUser().getId().getUserId());
            } catch (Exception e) {
            }
// remove sequence from 
            try {
                CDM.getIlmScreenConnectionDetails().remove(sequenceNumber);
            } catch (Exception e) {
            }
        }
        return result;
    }

    /**
     * Calls for
     * PKG_ILM.GET_CURRENT_DATE(
     * P_HOST_ID           IN  P_ILM_ACC_GROUP.HOST_ID%TYPE,
     * P_ENTITY_ID         IN  P_ILM_ACC_GROUP.ENTITY_ID%TYPE,
     * P_CURRENCY_CODE     IN  P_ILM_ACC_GROUP.CURRENCY_CODE%TYPE,
     * CURRENCY_DATE       OUT DATE,
     * ENTITY_DATE         OUT DATE);
     * <p>
     * To get system current date expressed in currency and in entity timeframe.
     */
    public String[] getNowDates(String entityId, String currencyId) throws SwtException {
        String[] result = new String[]{null, null};
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{call PKG_ILM.GET_CURRENT_DATE(?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName() + " - [ getNowDates ]- Entry");

            cstmt.setString(1, entityId);
            cstmt.setString(2, currencyId);
            cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.VARCHAR);
            cstmt.registerOutParameter(4, oracle.jdbc.OracleTypes.VARCHAR);
            cstmt.execute();

            result[0] = cstmt.getString(3);
            result[1] = cstmt.getString(4);

            log.debug(this.getClass().getName() + " - [ getNowDates ]- Exit");
        } catch (Exception exp) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [ getNowDates ] method : - "
                      + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                    "getNowDates", ILMAnalysisMonitorDAOHibernate.class);
        }
        return result;
    }


    public Collection<CurrencyAccessTO> getEntityCurrency(String entityId,
                                                          String roleId) throws SwtException {
        Collection<CurrencyAccessTO> colCurrency = new ArrayList<>();
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{call PKG_ILM.SP_GET_CCYS_PARAM(?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName()
                      + " - [getEntityCurrency] - Enter");

            cstmt.setString(1, CacheManager.getInstance().getHostId());
            cstmt.setString(2, entityId);
            cstmt.setString(3, roleId);
            cstmt.registerOutParameter(4, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();

            try (ResultSet rsCurency = (ResultSet) cstmt.getObject(4)) {
                while (rsCurency != null && rsCurency.next()) {
                    colCurrency.add(new CurrencyAccessTO(entityId, rsCurency.getString(1), rsCurency.getString(2),
                            SwtConstants.CURRENCYGRP_FULL_ACCESS));
                }
            }
            return colCurrency;
        } catch (HibernateException e) {
            log.error("HibernateException caught in " + this.getClass().getName()
                      + "-[getEntityCurrency]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEntityCurrency",
                    ILMAnalysisMonitorDAOHibernate.class);

        } catch (SQLException e) {
            log.error("SQLException caught in " + this.getClass().getName()
                      + "-[getEntityCurrency]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEntityCurrency",
                    ILMAnalysisMonitorDAOHibernate.class);

        }
    }

    public String isAllEntityAvailable(String hostId, String currencyId, String roleId)
            throws SwtException {

        String isAvailable = null;
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("select PKG_ILM.FN_IS_ALL_ENTITY_SELECTABLE(?,?,?) from dual")
        ) {
            log.debug(this.getClass().getName() + " - [ getChartsData ]- Entry");

            cstmt.setString(1, hostId);
            cstmt.setString(2, currencyId);
            cstmt.setString(3, roleId);
            try (ResultSet rs = cstmt.executeQuery()) {
                if (rs.next()) {
                    isAvailable = rs.getString(1);
                }
            }
            log.debug(this.getClass().getName() + " - [ getChartsData ]- Exit");
        } catch (SQLException sqlException) {
            throw SwtErrorHandler.getInstance().handleException(sqlException,
                    "getChartsData", ILMAnalysisMonitorDAOHibernate.class);
        } catch (Exception exp) {
            log.error(this.getClass().getName()
                      + " - Exception Catched in [ getChartsData ] method : - "
                      + exp.getMessage());
            throw new SwtException(exp.getMessage());
        }
        return isAvailable;
    }

    public Collection<EntityUserAccess> getEntitiesHasCurrencies(String hostId,
                                                                 String roleId) throws SwtException {

        Collection<EntityUserAccess> colEntity = new ArrayList<>();
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{call PKG_ILM.SP_GET_LIST_ENTITIES(?,?,?)}")
        ) {
            log.debug(this.getClass().getName()
                      + " - [getEntitiesHasCurrencies] - Enter");

            cstmt.setString(1, CacheManager.getInstance().getHostId());
            cstmt.setString(2, roleId);
            cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();

            try (ResultSet rsEntity = (ResultSet) cstmt.getObject(3)) {
                while (rsEntity != null && rsEntity.next()) {
                    colEntity.add(new EntityUserAccess(rsEntity.getString(1), rsEntity.getString(2), null, 0));
                }
            }
            return colEntity;
        } catch (HibernateException e) {
            log.error("HibernateException caught in " + this.getClass().getName()
                      + "-[getEntitiesHasCurrencies]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEntitiesHasCurrencies",
                    ILMAnalysisMonitorDAOHibernate.class);

        } catch (SQLException e) {
            log.error("SQLException caught in " + this.getClass().getName()
                      + "-[getEntitiesHasCurrencies]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEntitiesHasCurrencies",
                    ILMAnalysisMonitorDAOHibernate.class);

        }
    }

    public HashMap<String, String> getGroupsAndScenarioNames(String entityId, String currencyId)
            throws SwtException {

        HashMap<String, String> returnString = new HashMap<>();
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                Statement statement = conn.createStatement()
        ) {
            String SQLQuery = "SELECT 'GRP_'|| ILM_GROUP_ID , ILM_GROUP_NAME FROM P_ILM_ACC_GROUP " +
                              "WHERE currency_code = '" + currencyId + "' " +
                              "AND entity_id = '" + entityId + "'";
            statement.execute(SQLQuery);
            try (ResultSet rs = statement.getResultSet()) {
                while (rs != null && rs.next()) {
                    returnString.put(rs.getString(1), rs.getString(2));
                }
            }

            SQLQuery = "SELECT 'SCR_'||ILM_SCENARIO_ID , ILM_SCENARIO_NAME FROM P_ILM_SCENARIO " +
                       "WHERE currency_code = '" + currencyId + "' " +
                       "AND entity_id = '" + entityId + "'";
            statement.execute(SQLQuery);
            try (ResultSet rs = statement.getResultSet()) {
                while (rs != null && rs.next()) {
                    returnString.put(rs.getString(1), rs.getString(2));
                }
            }
            return returnString;
        } catch (HibernateException hibernateException) {
            log.error("Problem in accessing Hibernate properties");
            throw SwtErrorHandler.getInstance().handleException(
                    hibernateException, "getGroupsAndScenarioNames",
                    ILMAnalysisMonitorDAOHibernate.class);
        } catch (SQLException sqlException) {
            log.error("Problem in executing Query");
            throw SwtErrorHandler.getInstance().handleException(sqlException,
                    "getGroupsAndScenarioNames", ILMAnalysisMonitorDAOHibernate.class);
        }
    }

    public void cleanUpProcessDriver(String uniqueSequenceId, String userId) throws SwtException {

        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = (uniqueSequenceId != null)
                        ? conn.prepareCall("{call  PKG_ILM.SP_CLEANUP_PROCESS_DRIVER(?,?)}")
                        : conn.prepareCall("{call  PKG_ILM.SP_CLEANUP_PROCESS_DRIVER(?)}")
        ) {
            log.debug(this.getClass().getName()
                      + " - [cleanUpProcessDriver] - Enter");
            if (uniqueSequenceId != null) {
                cstmt.setString(1, userId);
                cstmt.setString(2, uniqueSequenceId);
            } else {
                cstmt.setString(1, userId);
            }
            cstmt.execute();
            conn.commit();
        } catch (HibernateException e) {
            log.error("HibernateException caught in " + this.getClass().getName()
                      + "-[cleanUpProcessDriver]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "cleanUpProcessDriver",
                    ILMAnalysisMonitorDAOHibernate.class);

        } catch (SQLException e) {
            log.error("SQLException caught in " + this.getClass().getName()
                      + "-[cleanUpProcessDriver]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "cleanUpProcessDriver",
                    ILMAnalysisMonitorDAOHibernate.class);

        }
    }


    public ILMScenario getILMScenarioDetails(String ilmScenarioId) throws SwtException {
        ILMScenario scenario = null;
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            log.debug(this.getClass().getName() + " - [getILMScenarioDetails] - Entry");
            List<ILMScenario> acctGrpList = session.createQuery(
                            "from ILMScenario scen where scen.id.ilmScenarioId = :ilmScenarioId", ILMScenario.class)
                    .setParameter("ilmScenarioId", ilmScenarioId)
                    .getResultList();

            for (ILMScenario scen : acctGrpList) {
                scenario = scen;
            }
            log.debug(this.getClass().getName() + " - [getILMScenarioDetails] - Exit");
            return scenario;
        } catch (Exception exp) {
            log.debug(this.getClass().getName()
                      + " - Exception Catched in [getILMScenarioDetails] method : - "
                      + exp.getMessage());

            log.error(this.getClass().getName()
                      + " - Exception Catched in [getILMScenarioDetails] method : - "
                      + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                    "getILMScenarioDetails", ILMAnalysisMonitorDAOHibernate.class);
        }
    }

    public ILMAccountGroup getILMGroupDetails(String ilmGroupId) throws SwtException {
        ILMAccountGroup accountGroup = null;
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            log.debug(this.getClass().getName() + " - [getILMGroupDetails] - Entry");
            List<ILMAccountGroup> acctGrpList = session.createQuery(
                            "from ILMAccountGroup grp where grp.id.ilmGroupId = :ilmGroupId", ILMAccountGroup.class)
                    .setParameter("ilmGroupId", ilmGroupId)
                    .getResultList();

            for (ILMAccountGroup grp : acctGrpList) {
                accountGroup = grp;
            }
            log.debug(this.getClass().getName() + " - [getILMGroupDetails] - Exit");
            return accountGroup;
        } catch (Exception exp) {
            log.debug(this.getClass().getName()
                      + " - Exception Catched in [getILMGroupDetails] method : - "
                      + exp.getMessage());

            log.error(this.getClass().getName()
                      + " - Exception Catched in [getILMGroupDetails] method : - "
                      + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                    "getILMGroupDetails", ILMAnalysisMonitorDAOHibernate.class);
        }
    }

    public ArrayList<ThroughputMonitorRecord> getIlmThroughPutRatioData(String defaultEntityId, String defaultCcyId,
                                                                        String selectedAccountGroup, String selectedScenario, Date selectedDate, String hostId, String roleId, String dbLink, String calculateAs) throws SwtException {

        ArrayList<ThroughputMonitorRecord> resultList = new ArrayList<>();
        String[] data = null;

        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{ ? = call pkg_ilm_rep.FN_GET_THRU_RATIO_DATA(?,?,?,?,?,?,?,?,?)}")
        ) {
            log.debug(this.getClass().getName() + " - [getEntitiesHasCurrencies] - Enter");

            cstmt.setString(2, CacheManager.getInstance().getHostId());
            cstmt.setString(3, defaultEntityId);
            cstmt.setString(4, defaultCcyId);
            cstmt.setDate(5, SwtUtil.truncateDateTime(selectedDate));
            cstmt.setString(6, selectedAccountGroup);
            cstmt.setString(7, selectedScenario);
            cstmt.setString(8, roleId);
            cstmt.setString(9, dbLink);
            cstmt.setString(10, calculateAs);
            cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);

            cstmt.execute();

            try (ResultSet rsEntity = (ResultSet) cstmt.getObject(1)) {
                while (rsEntity != null && rsEntity.next()) {
                    ThroughputMonitorRecord record = new ThroughputMonitorRecord();
                    record.getId().setEntityId(rsEntity.getString(1));
                    record.getId().setCurrencyCode(rsEntity.getString(2));
                    record.getId().setIlmGroup(rsEntity.getString(3));
                    record.setForecastedInflow(rsEntity.getDouble(5));
                    record.setForecastedOutflows(rsEntity.getDouble(6));
                    record.setActualsInflow(rsEntity.getDouble(7));
                    record.setActualsOutflows(rsEntity.getDouble(8));
                    record.setUnsetteledOutflows(rsEntity.getDouble(9));
                    record.setThreshold1ActualPercent(rsEntity.getDouble(12));
                    record.setThreshold2ActualPercent(rsEntity.getDouble(15));
                    record.setThreshold1Percent(rsEntity.getDouble(11));
                    record.setThreshold2Percent(rsEntity.getDouble(14));
                    record.setCurrent(rsEntity.getDouble(17));
                    record.setThreshold1Time(rsEntity.getString(10));
                    record.setThreshold2Time(rsEntity.getString(13));
                    record.setCurrentTime(rsEntity.getString(16));
                    if (!SwtUtil.isEmptyOrNull(rsEntity.getString(18))) {
                        data = rsEntity.getString(18).split("\\|");
                        record.setChartsTimeData(data[0].replaceAll(";", ","));
                        record.setChartsPercentData(data[1].replaceAll(";", ","));
                    }
                    record.setAlerting(rsEntity.getString(19));
                    resultList.add(record);
                }
            }
            return resultList;
        } catch (HibernateException e) {
            log.error("HibernateException caught in " + this.getClass().getName()
                      + "-[getEntitiesHasCurrencies]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEntitiesHasCurrencies",
                    ILMAnalysisMonitorDAOHibernate.class);

        } catch (SQLException e) {
            log.error("SQLException caught in " + this.getClass().getName()
                      + "-[getEntitiesHasCurrencies]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEntitiesHasCurrencies",
                    ILMAnalysisMonitorDAOHibernate.class);

        }
    }

    public JasperPrint getBlockPaymentReport(PCReport pcReport) throws SwtException {

        JasperPrint jasperPrint = null;
        List<?> pagesize = null;
        SystemFormats sysformat = null;
        String currencyFormat = null;
        boolean showCuttOffSubReport = false;

        try (
                Session session = SwtUtil.pcSessionFactory.openSession();
                Connection connection = SwtUtil.connection(session)
        ) {
            sysformat = SwtUtil.getCurrentSystemFormats(pcReport.getRequest().getSession());
            currencyFormat = sysformat.getCurrencyFormat();
            Map<String, Object> parms = new HashMap<>();

            JasperReport jasperReport = null;
            try {
                jasperReport = JasperCompileManager.compileReport(pcReport.getRequest()
                                                                          .getServletContext().getRealPath("/")
                                                                  + PCMConstant.PCM_BLOCK_PAYMENT_REPORTS_FILE);
            } catch (JRException exp) {
                exp.printStackTrace();
            }
            log.debug("report complied");
            HashMap<String, String> parmsLabel = new HashMap<>();
            parmsLabel.put("pgrandTotal", "Grand-Total");
            parmsLabel.put("pReportTitleBlockedPayment", "Blocked Payments (End of Day)");
            parmsLabel.put("subTotal", "Sub-Total");
            parmsLabel.put("pValueDate", "Value Date");
            parmsLabel.put("pCurrency", "Currency");
            parmsLabel.put("pAccount Grp", "Account Grp");
            parmsLabel.put("pEntity", "Entity");
            parmsLabel.put("pSource", "Source");
            parmsLabel.put("valueDate", "Value Date");
            parmsLabel.put("pReportDateTime", "Report Date/Time");
            parmsLabel.put("pCuttOffNotReached", "See final page for currencies where cut-off has not yet been reached");
            parmsLabel.put("pcurrencyNotReachedCutOff", "Currencies where cut-off has not yet been reached for value: " + SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())));
            parmsLabel.put("pAccount", "Account");
            parmsLabel.put("pAmount", "Amount");
            parmsLabel.put("pSourceRef", "Source Ref");
            parmsLabel.put("pPRID", "PR ID");
            parmsLabel.put("pMsgType", "MsgType");
            parmsLabel.put("pStopBlockRule", "Stop/Block Rule");

            String toDateAsString = SwtUtil.formatDate(pcReport.getToDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
            String sysDateAsString = SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));

            parms.put("pHost_Id", SwtUtil.getCurrentHostId());
            parms.put("p_selectedCurrency", pcReport.getCurrencyCode());
            parms.put("p_selectedCurrencyName", pcReport.getCurrencyName());
            parms.put("p_selectedAccountGroup", pcReport.getAccountGroup());
            parms.put("p_selectedAccountGrpName", pcReport.getAccountGroupName());
            parms.put("p_UseCcyMultiplier", pcReport.getUseCurrencyMultiplier());
            parms.put("p_selectedEntity", pcReport.getEntityId());
            parms.put("p_selectedSource", pcReport.getSource());
            parms.put("p_selectedSourceName", pcReport.getSourceName());
            parms.put("p_selectedEntityName", pcReport.getEntityName());
            parms.put("p_ReportDateTime", SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()) + " HH:mm"));

            parms.put("p_selectedFromDate", SwtUtil.truncateDateTime(pcReport.getFromDate()));
            if (pcReport.getToDate() == null) {
                parms.put("p_selectedToDateAsString", SwtUtil.formatDate(pcReport.getFromDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())));
            } else {
                parms.put("p_selectedToDateAsString", toDateAsString);
            }
            parms.put("p_selectedToDate", SwtUtil.truncateDateTime(pcReport.getToDate()));
            parms.put("p_selectedFromDateAsString", SwtUtil.formatDate(pcReport.getFromDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())));
            parms.put("p_dateFormat", SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
            parms.put("SUBREPORT_DIR", JasperCompileManager.compileReport(SwtUtil.contextRealPath + PCMConstant.PCM_BLOCK_PAYMENT_SUB_REPORTS_FILE));

            if (pcReport.getToDate() == null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(pcReport.getFromDate());

                Calendar cal2 = Calendar.getInstance();
                cal2.setTime(SwtUtil.getSystemDatewithoutTime());
                if (cal.equals(cal2)) {
                    showCuttOffSubReport = true;
                }

            } else {
                Calendar cal2 = Calendar.getInstance();
                cal2.setTime(pcReport.getToDate());

                Calendar cal3 = Calendar.getInstance();
                cal3.setTime(SwtUtil.getSystemDatewithoutTime());

                if (cal2.equals(cal3) || cal3.before(cal2)) {
                    showCuttOffSubReport = true;
                }
            }

            parms.put("p_showRemainingCutoff", showCuttOffSubReport ? "true" : "false");
            parms.put("p_Dictionary_Data", parmsLabel);
            parms.put("pCurrencyPattern", currencyFormat);

            jasperPrint = JasperFillManager.fillReport(jasperReport, parms, connection);
            log.debug("Report Filled");
            pagesize = jasperPrint.getPages();
            if (pagesize.size() == 0) {
                jasperPrint = JasperFillManager.fillReport(jasperReport, parms, new JREmptyDataSource(1));
            }

        } catch (SwtException swtexp) {
            log.debug(this.getClass().getName()
                      + " - Exception Catched in [getBlockPaymentReport] method - "
                      + swtexp.getMessage());

            log.error(this.getClass().getName()
                      + " - Exception Catched in [getBlockPaymentReport] method - "
                      + swtexp.getMessage());
            SwtUtil.logException(swtexp, pcReport.getRequest(), "");

        } catch (Exception exp) {
            SwtUtil.logException(
                    SwtErrorHandler.getInstance().handleException(exp,
                            "getBlockPaymentReport",
                            ILMAnalysisMonitorDAOHibernate.class), pcReport.getRequest(), "");
            throw new SwtException(exp.getMessage());
        }
        log.debug(this.getClass().getName()
                  + "- [getTurnoverReport] - Exiting ");
        return jasperPrint;
    }


    public JasperPrint getILMThroughputRatioReport(ThroughputRatioReport pcReport) throws SwtException {

        JasperPrint jasperPrint = null;
        List<?> pagesize = null;
        SystemFormats sysformat = null;
        String currencyFormat = null;
        boolean showCuttOffSubReport = false;

        try (
                Session session = SwtUtil.sessionFactory.openSession();
                Connection connection = SwtUtil.connection(session)
        ) {
            sysformat = SwtUtil.getCurrentSystemFormats(pcReport.getRequest().getSession());
            currencyFormat = sysformat.getCurrencyFormat();
            Map<String, Object> parms = new HashMap<>();

            JasperReport jasperReport = null;
            try {
                jasperReport = JasperCompileManager.compileReport(pcReport.getRequest()
                                                                          .getServletContext().getRealPath("/")
                                                                  + SwtConstants.ILM_THROUGHPUT_TEMPLATE);
            } catch (JRException exp) {
                exp.printStackTrace();
            }
            log.debug("report complied");
            HashMap<String, String> parmsLabel = new HashMap<>();
            parmsLabel.put("pgrandTotal", "Grand-Total");
            parmsLabel.put("pReportTitle", "ILM ThroughPut Monitor Report");
            parmsLabel.put("pSubReportTitle", "Intraday Throughput");
            parmsLabel.put("pScenario", "Scenario");
            parmsLabel.put("pValueDate", "Value Date");
            parmsLabel.put("pCurrency", "Currency");
            parmsLabel.put("pAccount Grp", "Account Grp");
            parmsLabel.put("pAccount_Grp_Desc", "Account Grp Desc");
            parmsLabel.put("pEntity", "Entity");
            parmsLabel.put("valueDate", "Value Date");
            parmsLabel.put("pReportDateTime", "Report Date/Time");
            parmsLabel.put("p_entity", "Entity");
            parmsLabel.put("p_ccy", "Ccy");
            parmsLabel.put("p_ilm_group", "ILM Group");
            parmsLabel.put("p_forcinf", "Inflows");
            parmsLabel.put("p_forcout", "Outflows");
            parmsLabel.put("p_unsetout", "Unsettled Out.");
            parmsLabel.put("p_threshold1", "Threshold 1");
            parmsLabel.put("p_threshold2", "Threshold 2");
            parmsLabel.put("p_threshold", "Threshold");
            parmsLabel.put("p_out_perc", "Throughput");
            parmsLabel.put("p_timeslot", "Time");
            parmsLabel.put("p_current", "Latest");
            parmsLabel.put("p_actuals", "Actuals");
            parmsLabel.put("p_forecasted", "Forecasted");
            parmsLabel.put("p_throughputratios", "Throughput Ratios");
            parmsLabel.put("pcalculateAs", "Calculate As");

            if (SwtUtil.isEmptyOrNull(pcReport.getCalculateAs()) || "1".equals(pcReport.getCalculateAs())) {
                parmsLabel.put("pcalculateAsValue", SwtUtil.getMessage("ilmthroughputActTvsForecT", pcReport.getRequest()));
            } else if ("2".equals(pcReport.getCalculateAs())) {
                parmsLabel.put("pcalculateAsValue", SwtUtil.getMessage("ilmthroughputActTvsForecL", pcReport.getRequest()));
            } else {
                parmsLabel.put("pcalculateAsValue", SwtUtil.getMessage("ilmthroughputActTvsActT", pcReport.getRequest()));
            }

            String toDateAsString = SwtUtil.formatDate(pcReport.getValueDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));
            String sysDateAsString = SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));

            parms.put("p_hostID", SwtUtil.getCurrentHostId());
            parms.put("p_selectedCurrency", pcReport.getCurrencyCode());
            parms.put("p_selectedCurrencyName", pcReport.getCurrencyName());
            parms.put("p_selectedAccountGroup", pcReport.getAccountGroup());
            parms.put("p_selectedAccountGrpName", pcReport.getAccountGroupName());
            parms.put("p_UseCcyMultiplier", pcReport.getUseCurrencyMultiplier());
            parms.put("p_selectedEntity", pcReport.getEntityId());
            parms.put("p_selectedScenario", pcReport.getScenarioId());
            parms.put("p_selectedScenarioName", pcReport.getScenarioId());
            parms.put("p_selectedEntityName", pcReport.getEntityName());
            parms.put("p_CalculateAs", pcReport.getCalculateAs());
            parms.put("p_ReportDateTime", SwtUtil.formatDate(SwtUtil.getSysParamDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()) + " HH:mm"));

            parms.put("p_selectedDate", SwtUtil.truncateDateTime(pcReport.getValueDate()));
            if (pcReport.getValueDate() == null) {
                parms.put("p_selectedDateAsString", SwtUtil.formatDate(pcReport.getValueDate(), SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession())));
            } else {
                parms.put("p_selectedDateAsString", toDateAsString);
            }
            parms.put("p_dateFormat", SwtUtil.getCurrentDateFormat(pcReport.getRequest().getSession()));

            parms.put("p_Dictionary_Data", parmsLabel);
            parms.put("p_ChartsData", pcReport.getChartsData());
            parms.put("pCurrencyPattern", currencyFormat);
            parms.put("p_selectedDateISO", SwtUtil.formatDate(pcReport.getValueDate(), "yyyy-MM-dd"));
            parms.put("p_dbLink", "");
            parms.put("p_currencyFormat", pcReport.getCurrencyFormat());
            parms.put("p_roleId", "VendorSuppor");
            parms.put("chartImageBase64", pcReport.getChartsData().get("MAINEURRABONL2U"));

            parms.put("SUBREPORT_DIR", JasperCompileManager.compileReport(SwtUtil.contextRealPath + SwtConstants.ILM_THROUGHPUT_SUB_TEMPLATE));
            parms.put("SUBREPORT_CHART_GRID_DIR", JasperCompileManager.compileReport(SwtUtil.contextRealPath + SwtConstants.ILM_THROUGHPUT_SUB_GRID_CHART_TEMPLATE));

            jasperPrint = JasperFillManager.fillReport(jasperReport, parms, connection);
            log.debug("Report Filled");
            pagesize = jasperPrint.getPages();
            if (pagesize.size() == 0) {
                jasperPrint = JasperFillManager.fillReport(jasperReport, parms, new JREmptyDataSource(1));
            }

        } catch (SwtException swtexp) {
            log.debug(this.getClass().getName()
                      + " - Exception Catched in [getBlockPaymentReport] method - "
                      + swtexp.getMessage());

            log.error(this.getClass().getName()
                      + " - Exception Catched in [getBlockPaymentReport] method - "
                      + swtexp.getMessage());
            SwtUtil.logException(swtexp, pcReport.getRequest(), "");

        } catch (Exception exp) {
            SwtUtil.logException(
                    SwtErrorHandler.getInstance().handleException(exp,
                            "getBlockPaymentReport",
                            ILMAnalysisMonitorDAOHibernate.class), pcReport.getRequest(), "");
            throw new SwtException(exp.getMessage());
        }

        log.debug(this.getClass().getName()
                  + "- [getTurnoverReport] - Exiting ");
        return jasperPrint;
    }


    Integer convertDoubleToInt(Double d) {
        //round down
        return (d == null) ? null : d % 1 == 0 ? Integer.valueOf(d.intValue()) : Integer.valueOf(d.intValue());
    }


    public HashMap<String, ILMSummaryRecord> getILMSummaryGridData(String hostId, String userFilter, Date valueDate,
                                                                   String dbLink, String includeSOD, String includeCR, String includeOpenMovements, String includeSumByCutoff,
                                                                   String hideNonSumAcct, String applyCcyMultiplier, HashMap<String, Integer> orderMap, String ccyPattern, String userId) throws SwtException {

        HashMap<String, ILMSummaryRecord> result = new HashMap<>();
        String entityId = null;
        String ccyCode = null;
        String accountGroup = null;
        String accountId = null;
        String currencyCode = null;
        String[] data = null;

        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{ call PKG_ILM.SP_GET_SUMMARY_TAB ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,? )}")
        ) {
            log.debug(this.getClass().getName() + " - [getEntitiesHasCurrencies] - Enter");

            cstmt.setString(1, CacheManager.getInstance().getHostId());
            cstmt.setString(2, userFilter);
            cstmt.setDate(3, SwtUtil.truncateDateTime(valueDate));
            cstmt.setString(4, includeSOD);
            cstmt.setString(5, includeCR);
            cstmt.setString(6, includeOpenMovements);
            cstmt.setString(7, includeSumByCutoff);
            cstmt.setString(8, hideNonSumAcct);
            cstmt.setString(9, applyCcyMultiplier);
            cstmt.setString(10, userId);
            cstmt.registerOutParameter(11, oracle.jdbc.OracleTypes.CURSOR);

            cstmt.execute();

            try (ResultSet rsEntity = (ResultSet) cstmt.getObject(11)) {
                while (rsEntity.next()) {
                    ILMSummaryRecord record = new ILMSummaryRecord();

                    accountGroup = rsEntity.getString("ILM_GROUP_ID");
                    accountId = rsEntity.getString("account_id");
                    entityId = rsEntity.getString("ENTITY_ID");
                    currencyCode = rsEntity.getString("CURRENCY_CODE");

                    record.setCurrencyCode(rsEntity.getString("CURRENCY_CODE"));
                    record.setAccountId(rsEntity.getString("account_id"));
                    record.setAccountName(rsEntity.getString("ACCOUNT_NAME"));

                    record.setActualSod(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("ACTUAL_SOD"), ccyPattern));
                    record.setAvailable(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("AVAILABLE"), ccyPattern));
                    record.setCutOff(rsEntity.getString("CUT_OFF"));
                    record.setCutOffExceeded("Y".equals(rsEntity.getString("NON_SUM_ACCOUNT")));
                    record.setSum((rsEntity.getString("MONITOR_SUM")));
                    record.setMinBalColor((rsEntity.getString("MINBAL_COL")));
                    record.setDpctThru(rsEntity.getString("D_PCT_THRU"));
                    record.setdPctThruColor(rsEntity.getString("D_PCT_THRU_COLOR"));
                    record.setPctConfCr(rsEntity.getString("PCT_CONF_CR"));
                    record.setEntityId(rsEntity.getString("ENTITY_ID"));
                    record.setExternalBal(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("EXTERNAL_BAL"), ccyPattern));
                    record.setExternalBalCol(rsEntity.getString("EXTERNAL_BAL_COL"));
                    record.setHostId(rsEntity.getString("HOST_ID"));
                    record.setIlmGroupId(rsEntity.getString("ILM_GROUP_ID"));
                    record.setCurrencyMultiplierLabel(rsEntity.getString("CCY_MULT_FLAG"));
                    record.setMinbal(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("MINBAL"), ccyPattern));
                    record.setMinBalTime(rsEntity.getString("MINBAL_TIME"));
                    record.setPreadvices(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("PREADVICES"), ccyPattern));
                    record.setPredictedBalance(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("PREDICTED_BALANCE"), ccyPattern));
                    record.setPredictBalCol(rsEntity.getString("PREDICT_BAL_COL"));
                    record.setSodBalCol(rsEntity.getString("SOD_BAL_COL"));
                    record.setTurnover(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("TURNOVER"), ccyPattern));
                    record.setUnexpectedBalance(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("UNEXPECTED_BALANCE"), ccyPattern));
                    record.setUnsettledBalance(SwtUtil.formatCurrency(currencyCode, rsEntity.getBigDecimal("UNSETTLED_BALANCE"), ccyPattern));
                    record.setScenarioHighlighted(rsEntity.getString("SCENARIO_HIGHLIGHTING"));

                    if ("All".equals(entityId) && "All".equals(accountId) && "All".equals(accountGroup)) {
                        if (orderMap.get(currencyCode) != null)
                            record.setOrder(orderMap.get(currencyCode));
                        result.put(currencyCode, record);
                    } else if ("All".equals(accountId) && "All".equals(accountGroup)) {
                        record.setIlmGlobalAccountGroupId(rsEntity.getString("GLOBAL_GROUP_ID"));
                        if (orderMap.get(currencyCode + "&&" + entityId) != null)
                            record.setOrder(orderMap.get(currencyCode + "&&" + entityId));
                        if (result.get(currencyCode) != null) {
                            result.get(currencyCode).getSubILMSummaryList().put(entityId, record);
                        } else {
                            ILMSummaryRecord ccyRecord = new ILMSummaryRecord();
                            ccyRecord.getSubILMSummaryList().put(entityId, record);
                            result.put(currencyCode, ccyRecord);
                        }
                    } else if ("All".equals(accountId)) {
                        if (result.get(currencyCode) != null) {
                            if (result.get(currencyCode).getSubILMSummaryList().get(entityId) != null) {
                                result.get(currencyCode).getSubILMSummaryList().get(entityId).getSubILMSummaryList().put(accountGroup, record);
                            } else {
                                ILMSummaryRecord entityRecord = new ILMSummaryRecord();
                                entityRecord.getSubILMSummaryList().put(accountGroup, record);
                                result.get(currencyCode).getSubILMSummaryList().put(entityId, entityRecord);
                            }
                        } else {
                            ILMSummaryRecord entityRecord = new ILMSummaryRecord();
                            entityRecord.getSubILMSummaryList().put(accountGroup, record);

                            ILMSummaryRecord ccyRecord = new ILMSummaryRecord();
                            ccyRecord.getSubILMSummaryList().put(entityId, entityRecord);
                            result.put(currencyCode, ccyRecord);
                        }
                    } else {
                        if (result.get(currencyCode) != null) {
                            if (result.get(currencyCode).getSubILMSummaryList().get(entityId) != null) {
                                if (result.get(currencyCode).getSubILMSummaryList().get(entityId).getSubILMSummaryList().get(accountGroup) != null) {
                                    result.get(currencyCode).getSubILMSummaryList().get(entityId).getSubILMSummaryList().get(accountGroup).getSubILMSummaryList().put(accountId, record);
                                } else {
                                    ILMSummaryRecord accountGroupRecord = new ILMSummaryRecord();
                                    accountGroupRecord.getSubILMSummaryList().put(accountId, record);

                                    result.get(currencyCode).getSubILMSummaryList().get(entityId).getSubILMSummaryList().put(accountGroup, accountGroupRecord);
                                }
                            } else {
                                ILMSummaryRecord accountGroupRecord = new ILMSummaryRecord();
                                accountGroupRecord.getSubILMSummaryList().put(accountId, record);

                                ILMSummaryRecord entityRecord = new ILMSummaryRecord();
                                entityRecord.getSubILMSummaryList().put(accountGroup, accountGroupRecord);

                                result.get(currencyCode).getSubILMSummaryList().put(entityId, entityRecord);
                            }
                        } else {
                            ILMSummaryRecord accountGroupRecord = new ILMSummaryRecord();
                            accountGroupRecord.getSubILMSummaryList().put(accountId, record);

                            ILMSummaryRecord entityRecord = new ILMSummaryRecord();
                            entityRecord.getSubILMSummaryList().put(accountGroup, accountGroupRecord);

                            ILMSummaryRecord ccyRecord = new ILMSummaryRecord();
                            ccyRecord.getSubILMSummaryList().put(entityId, entityRecord);
                            result.put(currencyCode, ccyRecord);
                        }
                    }
                }
            }
        } catch (HibernateException | SQLException e) {
            log.error(e.getClass().getSimpleName() + " caught in " + this.getClass().getName()
                      + "-[getEntitiesHasCurrencies]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEntitiesHasCurrencies",
                    ILMAnalysisMonitorDAOHibernate.class);
        }
        return result;
    }

    public HashMap<String, ILMSummaryRecord> getILMOptionGridData(HashMap<String, Integer> orderMap, String roleId) throws SwtException {

        HashMap<String, ILMSummaryRecord> result = new HashMap<>();
        String entityId = null;
        String ccyCode = null;
        String accountGroup = null;
        String currencyCode = null;
        String[] data = null;

        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("{ call PKG_ILM.SP_GET_SUMMARY_OPT_GRPS ( ?, ?)}")
        ) {
            log.debug(this.getClass().getName() + " - [getILMOptionGridData] - Enter");

            cstmt.setString(1, roleId);
            cstmt.registerOutParameter(2, oracle.jdbc.OracleTypes.CURSOR);

            cstmt.execute();

            try (ResultSet rsEntity = (ResultSet) cstmt.getObject(2)) {
                while (rsEntity.next()) {
                    ILMSummaryRecord record = new ILMSummaryRecord();

                    accountGroup = rsEntity.getString("ILM_GROUP_ID");
                    entityId = rsEntity.getString("ENTITY_ID");
                    currencyCode = rsEntity.getString("CURRENCY_CODE");

                    record.setIlmGlobalAccountGroupId(rsEntity.getString("IS_GLOBAL_ALT_CENT"));
                    record.setCurrencyCode(rsEntity.getString("CURRENCY_CODE"));
                    record.setIlmAccountGroupName(rsEntity.getString("ILM_GROUP_NAME"));
                    record.setIlmAccountGroupDesc(rsEntity.getString("ILM_GROUP_DESCRIPTION"));

                    if (result.get(currencyCode) != null) {
                        if (result.get(currencyCode).getSubILMSummaryList().get(entityId) != null) {
                            result.get(currencyCode).getSubILMSummaryList().get(entityId).getSubILMSummaryList().put(accountGroup, record);
                        } else {
                            ILMSummaryRecord entityRecord = new ILMSummaryRecord();
                            entityRecord.getSubILMSummaryList().put(accountGroup, record);
                            result.get(currencyCode).getSubILMSummaryList().put(entityId, entityRecord);
                        }
                    } else {
                        ILMSummaryRecord entityRecord = new ILMSummaryRecord();
                        entityRecord.getSubILMSummaryList().put(accountGroup, record);

                        ILMSummaryRecord ccyRecord = new ILMSummaryRecord();
                        ccyRecord.getSubILMSummaryList().put(entityId, entityRecord);
                        result.put(currencyCode, ccyRecord);
                    }
                }
            }
        } catch (HibernateException | SQLException e) {
            log.error(e.getClass().getSimpleName() + " caught in " + this.getClass().getName()
                      + "-[getILMOptionGridData]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getILMOptionGridData",
                    ILMAnalysisMonitorDAOHibernate.class);
        }
        return result;
    }

    public String getDataState(String hostId, String entityId, String currencyId, Date selectedDate, String dbLink) throws SwtException {
        String result = null;
        try (
                Session session = getHibernateTemplate().getSessionFactory().openSession();
                Connection conn = SwtUtil.connection(session);
                CallableStatement cstmt = conn.prepareCall("select pkg_ilm.FN_IS_GRP_CALC_DONE(?,?,?,?,?) from dual")
        ) {
            log.debug(this.getClass().getName() + " - [getDataState] - Enter");

            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setDate(4, SwtUtil.truncateDateTime(selectedDate));
            cstmt.setString(5, dbLink);

            try (ResultSet rs = cstmt.executeQuery()) {
                if (rs.next()) {
                    result = rs.getString(1);
                }
            }
        } catch (HibernateException | SQLException e) {
            log.error(e.getClass().getSimpleName() + " caught in " + this.getClass().getName()
                      + "-[getDataState]-" + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getDataState",
                    ILMAnalysisMonitorDAOHibernate.class);
        }
        return result;
    }


}
