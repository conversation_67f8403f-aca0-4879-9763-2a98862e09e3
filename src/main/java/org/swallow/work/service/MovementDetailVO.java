/*
 * Created on Jan 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.service;

import java.util.Collection;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MovementDetailVO {
	private Collection posLevelDetail;
	private Collection movementDetail;
	
	
	public MovementDetailVO(){
	}
	public MovementDetailVO(Collection posLevelDetail, Collection movementDetail){
		this.posLevelDetail = posLevelDetail;
		this.movementDetail = movementDetail;
	}
	
	
	/**
	 * @return Returns the movementDetail.
	 */
	public Collection getMovementDetail() {
		return movementDetail;
	}
	/**
	 * @param movementDetail The movementDetail to set.
	 */
	public void setMovementDetail(Collection movementDetail) {
		this.movementDetail = movementDetail;
	}
	/**
	 * @return Returns the posLevelDetail.
	 */
	public Collection getPosLevelDetail() {
		return posLevelDetail;
	}
	/**
	 * @param posLevelDetail The posLevelDetail to set.
	 */
	public void setPosLevelDetail(Collection posLevelDetail) {
		this.posLevelDetail = posLevelDetail;
	}
}
