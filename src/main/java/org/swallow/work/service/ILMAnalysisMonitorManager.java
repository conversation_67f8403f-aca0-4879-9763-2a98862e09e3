/*
 * @(#) ILMAnalysisMonitorManager .java 1.0 2013/11/29
 * 
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.pcm.report.model.PCReport;
import org.swallow.reports.model.ThroughputRatioReport;
import org.swallow.util.CommonDataManager;
import org.swallow.work.dao.ILMAnalysisMonitorDAO;
import org.swallow.work.model.ILMSummaryRecord;
import org.swallow.work.model.ThroughputMonitorRecord;

import net.sf.jasperreports.engine.JasperPrint;

/**
 * This is the Manager interface which contains prototypes of all the manager
 * functions.
 * 
 */
public interface ILMAnalysisMonitorManager {
	/**
	 * setter method for ilmAnalysisMonitorDao
	 * 
	 * @param ilmAnalysisMonitorDao
	 */
	void setIlmAnalysisMonitorDAO(ILMAnalysisMonitorDAO ilmAnalysisMonitorDao);

	public String getChartsData(String hostId, String entityId, String currencyId,
			Date selectedDate, String userId, String dbLink,
			String selectedFigures, String sumByCutOff, String includeOpenMvnts) throws SwtException;
	 
	public List getGroupsDetailForGrid(String hostId, String entityId, String currencyId, Date selectedDate, String userId, String dbLink, String isGlobal, String includeOpenMvnts) throws SwtException;
	
	/**
	 * Get the scenarios list with its details
	 * @param hostId
	 * @param entityId
	 * @param currencyId
	 * @param userId
	 * @param isGlobal
	 * @return List
	 * @throws SwtException
	 */
	public List getScenariosDetail(String hostId, String entityId, String currencyId, String userId, String isGlobal) throws SwtException;

	public ILMAccountGroup getGroupDetails(String hostId, String entityId, String currencyId, String group) throws SwtException;
	
	public HashMap<String, String> getProcessState(String hostId, String entityId, String currencyId,
			Date selectedDate, String roleId,String userId, String dbLink) throws SwtException;

	public String getCurrencyTimeframe(String entityId, String currencyId, Date selectedDate, String roleId) throws SwtException;

	public String getCcyMultiplierAndDecimalPlaces(String entityId, String currencyId) throws SwtException;

	public String getCurrentDbLink(String hostId) throws SwtException;
	
	public String recalculateData( String hostId, String entityId, String currencyId ,String selectedDateStr, String dbLink, String uniqueSequence, CommonDataManager CDM) throws SwtException ;

	public String[] getNowDates(String entityId, String currencyId) throws SwtException;
	
	public Collection<CurrencyAccessTO> getEntityCurrency(String entityId, String roleId) throws SwtException;
	
	public Collection<EntityUserAccess> getEntitiesHasCurrencies(String hostId, String roleId) throws SwtException;
	
	public String isAllEntityAvailable(String hostId, String currencyId, String roleId) throws SwtException;
	
	public HashMap<String, String> getGroupsAndScenarioNames(String entityId , String currencyId  ) throws SwtException ;

	public void cleanUpProcessDriver(String uniqueSequenceId, String userId) throws SwtException;
	
	/**
	 * Get the ilm group details according to a given ilm group ID 
	 * @param ilmGroupId
	 * @return ILMAccountGroup
	 * @throws SwtException
	 */
	public ILMAccountGroup getILMGroupDetails(String ilmGroupId) throws SwtException;
	
	/**
	 * Get the ilm scenario details according to a given ilm scenario ID 
	 * @param ilmScenarioId
	 * @return ILMScenario
	 * @throws SwtException
	 */
	public ILMScenario getILMScenarioDetails(String ilmScenarioId) throws SwtException;
	/**
	 * Get the Ilm ThroughPut Ratio Data + charts data
	 * @param defaultEntityId
	 * @param defaultCcyId
	 * @param selectedAccountGroup
	 * @param selectedScenario
	 * @param sysDateAsString
	 * @return
	 */
	public ArrayList<ThroughputMonitorRecord> getIlmThroughPutRatioData(String defaultEntityId, String defaultCcyId,
			String selectedAccountGroup, String selectedScenario, Date selectedDate, String hostId, String roleId, String dbLink, String calculateAs) throws SwtException;
	
	public JasperPrint getILMThroughputRatioReport(ThroughputRatioReport report)  throws SwtException;
	
	public HashMap<String, ILMSummaryRecord>getILMSummaryGridData(String hostId, String userFilter, Date valueDate , String dbLink, String includeSOD, String includeCR, String includeOpenMovements,
			String includeSumByCutoff, String hideNonSumAcct, String applyCcyMultiplier, HashMap<String, Integer> orderMap, String ccyPattern,String userId) throws SwtException;

	public String getDataState(String hostId, String entityId, String currencyId, Date selectedDate, String dbLink) throws SwtException;

	public HashMap<String, ILMSummaryRecord> getILMOptionGridData(HashMap<String, Integer> orderMap, String roleId) throws SwtException;
}
