/*
 * @(#)MetagroupMonitorManagerImpl.java 1.0 04/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.MetagroupMonitorDAO;
import org.swallow.work.dao.hibernate.MetagroupMonitorDAOHibernate;
import org.swallow.work.service.MetagroupMonitorManager;

/**
 * <pre>
 * Manager layer for MetaGroupMonitor Screen
 * Class used to
 *  - Display Meta Group Monitor Details
 *  - Display Group Monitor Details
 *  - Display Book Group Monitor Details
 *  - Get BookGroupMonitor JobStatusFlag  
 * </pre>
 */
@Component("metagroupMonitorManager")
public class MetagroupMonitorManagerImpl implements MetagroupMonitorManager {
	/**
	 * Log Instance
	 */
	private static final Log log = LogFactory
			.getLog(MetagroupMonitorManagerImpl.class);

	/**
	 * DAO instance
	 */
	@Autowired
	private MetagroupMonitorDAO metagroupMonitorDAO = null;

	/**
	 * This method sets the DAO which will cater to this manager class
	 * 
	 * @param metagroupMonitorDAO
	 *            instance of metagroupMonitorDAO
	 */
	public void setMetagroupMonitorDAO(MetagroupMonitorDAO metagroupMonitorDAO) {
		this.metagroupMonitorDAO = metagroupMonitorDAO;
	}

	/**
	 * This method returns the predicted balance categorized by meta group code
	 * and the total for a particular currency, entity ID and date
	 * 
	 * @param String
	 *            entity Id
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            date-the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param String
	 *            refreshFlag - flag for checking whether the refresh button has
	 *            been clicked
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */
	public Collection getMetagroupMonitorDetailsNew(String entityId,
			String currencyCode, String date, SystemFormats format,
			String refreshFlag, String roleId, String locationId)
			throws SwtException {

		// ArrayList instance to hold metagroupMonitorDetails
		ArrayList<Object> metagroupMonitorDetails = null;
		// variable to hold date
		Date valueDate = null;
		// ArrayList instance to hold metagroupMonitorDetailsTotalList
		ArrayList metagroupMonitorDetailsTotalList = null;
		// ArrayList instance to hold arrCol
		ArrayList arrCol = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getMetagroupMonitorDetailsNew ] - " + " Entry ");
			valueDate = SwtUtil.parseDate(date, format.getDateFormatValue());
			metagroupMonitorDetailsTotalList = new ArrayList();

			/*
			 * Calls metagroupMonitorDAO
			 * getMetagroupMonitorDetailsUsingStoredProc and Returns
			 * metagroupMonitorDetailsList Object
			 */
			arrCol = (ArrayList) metagroupMonitorDAO
					.getMetagroupMonitorDetailsUsingStoredProc(entityId,
							currencyCode, valueDate, format,
							metagroupMonitorDetailsTotalList, roleId,
							locationId);

			metagroupMonitorDetails = new ArrayList();
			// To add the Object for arraylist
			metagroupMonitorDetails.add((Collection) arrCol.get(0));
			// To add the Object for arraylist
			metagroupMonitorDetails.add(metagroupMonitorDetailsTotalList);
			// To add the Object for arraylist
			metagroupMonitorDetails.add((String) arrCol.get(1));

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [ getMetagroupMonitorDetailsNew ] - " + "Exception  "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMetagroupMonitorDetailsNew",
					MetagroupMonitorManagerImpl.class);
		} finally {

			valueDate = null;
			metagroupMonitorDetailsTotalList = null;
			arrCol = null;
			log.debug(this.getClass().getName()
					+ " - [ getMetagroupMonitorDetailsNew ] - " + " Exit ");

		}

		return metagroupMonitorDetails;
	}

	/**
	 * This method returns the predicted balance categorised by group code and
	 * the total for a particular currency, entity ID and date
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            currency code
	 * @param Date
	 *            date - the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param String
	 *            refreshFlag - flag for checking whether the refresh button has
	 *            been clicked
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @param String
	 *            metagroupId
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */

	public Collection getGroupMonitorDetailsNew(String entityId,
			String currencyCode, String date, SystemFormats format,
			String refreshFlag, String roleId, String locationId,
			String metagroupId) throws SwtException {

		// ArrayList instance to hold groupMonitorDetails
		ArrayList<Object> groupMonitorDetails = null;
		// variable to hold date
		Date valueDate = null;
		// ArrayList instance to hold groupMonitorDetailsTotalList
		ArrayList groupMonitorDetailsTotalList = null;
		// ArrayList instance to hold arrCol
		ArrayList arrCol = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getGroupMonitorDetailsNew ] - " + " Entry ");
			valueDate = SwtUtil.parseDate(date, format.getDateFormatValue());

			groupMonitorDetailsTotalList = new ArrayList();

			/*
			 * Calls metagroupMonitorDAO getGroupMonitorDetailsUsingStoredProc
			 * and Returns groupMonitorDetailsList Object
			 */
			arrCol = (ArrayList) metagroupMonitorDAO
					.getGroupMonitorDetailsUsingStoredProc(entityId,
							currencyCode, valueDate, format,
							groupMonitorDetailsTotalList, roleId, locationId,
							metagroupId);

			groupMonitorDetails = new ArrayList();
			// To add the Object for arraylist
			groupMonitorDetails.add((Collection) arrCol.get(0));
			// To add the Object for arraylist
			groupMonitorDetails.add(groupMonitorDetailsTotalList);
			// To add the Object for arraylist
			groupMonitorDetails.add((String) arrCol.get(1));

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [ getGroupMonitorDetailsNew ] - " + "Exception  "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getGroupMonitorDetailsNew",
					MetagroupMonitorManagerImpl.class);
		} finally {
			valueDate = null;
			groupMonitorDetailsTotalList = null;
			arrCol = null;
			log.debug(this.getClass().getName()
					+ " - [ getGroupMonitorDetailsNew ] - " + " Exit ");

		}

		return groupMonitorDetails;
	}

	/**
	 * This method returns the predicted balance categorised by book code and
	 * the total for a particular currency, entity ID and date
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            currency code
	 * @param Date
	 *            date - the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param String
	 *            refreshFlag - flag for checking whether the refresh button has
	 *            been clicked
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @param String
	 *            groupCode
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */

	public Collection getBookMonitorDetailsNew(String entityId,
			String currencyCode, String date, SystemFormats format,
			String refreshFlag, String roleId, String locationId,
			String groupCode) throws SwtException {

		// ArrayList instance to hold bookMonitorDetails
		ArrayList<Object> bookMonitorDetails = null;
		// ArrayList instance to hold bookMonitorDetailsTotalList
		ArrayList bookMonitorDetailsTotalList = null;
		// variable to hold date
		Date valueDate = null;
		// ArrayList instance to hold arrCol
		ArrayList arrCol = null;

		try {

			log.debug(this.getClass().getName()
					+ " - [ getBookMonitorDetailsNew ] - " + " Entry ");
			valueDate = SwtUtil.parseDate(date, format.getDateFormatValue());
			bookMonitorDetailsTotalList = new ArrayList();
			/*
			 * Calls metagroupMonitorDAO getBookMonitorDetailsUsingStoredProc
			 * and Returns bookMonitorDetailsList Object
			 */
			arrCol = (ArrayList) metagroupMonitorDAO
					.getBookMonitorDetailsUsingStoredProc(entityId,
							currencyCode, valueDate, format,
							bookMonitorDetailsTotalList, roleId, locationId,
							groupCode);

			bookMonitorDetails = new ArrayList<Object>();
			// To add the Object for arraylist
			bookMonitorDetails.add((Collection) arrCol.get(0));
			// To add the Object for arraylist
			bookMonitorDetails.add(bookMonitorDetailsTotalList);
			// To add the Object for arraylist
			bookMonitorDetails.add((String) arrCol.get(1));

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [ getBookMonitorDetailsNew ] - " + "Exception  "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBookMonitorDetailsNew",
					MetagroupMonitorManagerImpl.class);
		} finally {
			bookMonitorDetailsTotalList = null;
			valueDate = null;
			arrCol = null;
			log.debug(this.getClass().getName()
					+ " - [ getBookMonitorDetailsNew ] - " + "Exit");

		}

		return bookMonitorDetails;
	}

	/**
	 * This method is used to return Job Status Flag.
	 * 
	 * @return
	 * @throws SwtException
	 */
	public boolean getBookGroupMonitorJobFlag(String entityId)
			throws SwtException {
		/* Variable Declaration for jobFlag. */
		boolean jobFlag = false;
		try {
			log.debug(this.getClass().getName()
					+ " - [ getBookGroupMonitorJobFlag ] - Entry ");
			// code modified by Bala on 3-10-2011 for Mantis 1600:DataBuild in
			// progress not coming properly
			jobFlag = SwtUtil.getMonitorJobFlag(entityId,
					SwtConstants.PROCESS_GROUP_MONITOR);
			log.debug(this.getClass().getName()
					+ " - [ getBookGroupMonitorJobFlag ] - Exit ");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getBookGroupMonitorJobFlag] method : - "
							+ e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getBookGroupMonitorJobFlag",
					MetagroupMonitorDAOHibernate.class);
			throw swtexp;
		}
		return jobFlag;
	}
}