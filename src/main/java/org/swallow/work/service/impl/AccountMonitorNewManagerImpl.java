/*
 * @(#)AccountMonitorNewManagerImpl.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.AccountMonitorNewDAO;
import org.swallow.work.model.AccountMonitorNew;
import org.swallow.work.model.AccountMonitorTotalCacheValue;
import org.swallow.work.model.Movement;
import org.swallow.work.service.AccountMonitorNewDetailVO;
import org.swallow.work.service.AccountMonitorNewManager;
import org.swallow.work.service.MovementManager;

/**
 * <pre>
 * Manager layer for Account Monitor screen. Used to 
 *  - Display Account Monitor details
 *  - Updates sum flag of accounts
 *  - Updates loro flag for loro accounts
 *  - Get Monitor Job flag 
 * </pre>
 */
@Component("acctmonitorNewManager")
public class AccountMonitorNewManagerImpl implements AccountMonitorNewManager {

	/**
	 * Instance of Log
	 */
	private static final Log logger = LogFactory
			.getLog(AccountMonitorNewManagerImpl.class);

	/**
	 * Implementation of AccountMonitorNewDAO, to perform account monitor
	 * related database operation
	 */
	@Autowired
	private AccountMonitorNewDAO acctmonitorNewDAO = null;

	/**
	 * Setter method for acctmonitorNewDAO
	 * 
	 * @param acctmonitorNewDAO
	 *            AccountMonitorNewDAO
	 */
	public void setAcctmonitorNewDAO(AccountMonitorNewDAO acctmonitorNewDAO) {
		this.acctmonitorNewDAO = acctmonitorNewDAO;
	}

	/**
	 * Method which is used to get the predicted balances, loro
	 * balances,Unsettled balances,Unexpected balances,External balances Start
	 * of day balances and open unexpected balances.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyId
	 * @param String
	 *            accountType
	 * @param Date
	 *            dateParam
	 * @param boolean
	 *            isCacheSearch
	 * @param String
	 *            applyCurrencyThreshold
	 * @param String
	 *            accountClassParm
	 * @param String
	 *            hideZeroBalances
	 * @param OpTimer
	 *            opTimer
	 * @return AccountMonitorNewDetailVO object
	 * @throws SwtException
	 */
	public AccountMonitorNewDetailVO getAccountMonitorDetails(String hostId,
			String entityId, String currencyCode, String accountType,
			Date date, boolean isCacheSearch, String applyCurrencyThreshold,
			String accountClass, String hideZeroBalances, OpTimer opTimer,
			String roleId) throws SwtException {
		// To get account monitor details, which will have grid data, totals
		// value, no of unexpected movement, start of day balance and etc
		AccountMonitorNewDetailVO acctMonitorVO = null;
		// To get Totals value
		AccountMonitorTotalCacheValue acctMonitorTotal = null;
		// Flag, whether the job is running or not
		boolean jobFlag;

		try {
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ getAccountMonitorDetails ] - Entry ");
			// Start job timer (To find out time taken for this process)
			opTimer.start("jobflag");
			// Find out whether the monitor job is running or not
			jobFlag = SwtUtil.getMonitorJobFlag(entityId,
					SwtConstants.JOB_PROCESS_MONITOR);
			// Stop job timer
			opTimer.stop("jobflag");

			// If true means. job is not running (already processed). So get
			// values from processed table. Otherwise run the job (sending
			// another request)
			if (jobFlag) {
				// Start data timer
				opTimer.start("data");
				// Job is not running, so get values by invoking SP.
				acctMonitorVO = acctmonitorNewDAO
						.getAllBalancesUsingStoredProc(hostId, entityId,
								currencyCode, accountType, date, isCacheSearch,
								applyCurrencyThreshold, accountClass,
								hideZeroBalances, roleId);
				// Stop data timer
				opTimer.stop("data");
			} else {
				// Job is running, so set empty value. Send another request to
				// check whether the job is completed or not. If completed fetch
				// records from processed table.

				// Initialize object to hold account monitor screen details
				acctMonitorVO = new AccountMonitorNewDetailVO();
				// Initialize object to hold totals value
				acctMonitorTotal = new AccountMonitorTotalCacheValue();
				// Set properties
				acctMonitorTotal.setAccumulatedSODBalAsString("");
				acctMonitorTotal.setOpenUnexpectedBalTotalAsString("");
				acctMonitorTotal.setPredictedBalTotalAsString("");
				acctMonitorTotal.setExternalBalTotalAsString("");
				acctMonitorTotal.setLoroBalTotalAsString("");
				acctMonitorTotal.setUnsettledBalTotalAsString("");
				acctMonitorTotal.setUnexpectedBalTotalAsString("");
				acctMonitorTotal.setPredictedBalTotal(new Double("0"));
				acctMonitorTotal.setExternalBalTotal(new Double("0"));
				acctMonitorTotal.setLoroBalTotal(new Double("0"));
				acctMonitorTotal.setUnexpectedBalTotal(new Double("0"));
				acctMonitorTotal.setUnsettledBalTotal(new Double("0"));
				acctMonitorTotal.setAccumulatedSODBal(new Double("0"));
				acctMonitorTotal.setOpenUnexpectedBalTotal(new Double("0"));

				// Set empty records
				acctMonitorVO
						.setSummaryDetails(new ArrayList<AccountMonitorNew>());
				acctMonitorVO.setTotalDetails(acctMonitorTotal);
				acctMonitorVO.setJobFlag("Y");
			}
		} catch (SwtException ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ "getAccountMonitorDetails - SwtException - "
					+ ex.getMessage());
			// re-throw exception
			throw ex;
		} catch (Exception ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ "getAccountMonitorDetails - Exception - "
					+ ex.getMessage());
			// re-throw exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountMonitorDetails",
					AccountMonitorNewManagerImpl.class);
		} finally {
			// nullify object(s)
			acctMonitorTotal = null;
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ getAccountMonitorDetails ] - Exit ");
		}
		return acctMonitorVO;
	}

	/**
	 * This function updates the monitor sum flag and updates MONITOR_SUM flag
	 * in p_account table
	 * 
	 * @param String -
	 *            hostId
	 * @param String -
	 *            entityId
	 * @param String -
	 *            currencyCode
	 * @param String -
	 *            accountId
	 * @param String -
	 *            operation. Specifies the inclusion or exclusion of account
	 *            from total calculations
	 * @throws SwtException -
	 *             SwtException object
	 */
	public void updateMonitorSum(String hostId, String entityId,
			String currencyCode, String accountId, String operation)
			throws SwtException {
		// List of accounts to be updated
		Collection<AcctMaintenance> colAccountsToBeUpdated = null;
		// To hold account details as well as linked account list
		Collection<AcctMaintenance> colAccount = null;
		// Iterate through list and updates account
		Iterator<AcctMaintenance> itrAccount = null;
		// To update sum flag
		AcctMaintenance acctMaintenance = null;

		try {
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ updateMonitorSum ] - Entry ");
			/*
			 * Calls AccountMonitorNewDAO getAccountMonitorDetails to get the
			 * Linked accounts for the account Id which you have selected in the
			 * screen
			 */
			colAccount = acctmonitorNewDAO.getAccountAndLinkedAccountsList(
					hostId, entityId, currencyCode, accountId);
			// Initialize list to hole list of accounts to be updated
			colAccountsToBeUpdated = new ArrayList<AcctMaintenance>();

			if (colAccount != null) {
				/*
				 * For the operation IncludeInTotals and accountColl not Null
				 * then it will iterate the account & Linked account List
				 */
				if (operation.equals("IncludeInTotals")) {
					// Operation is IncludeInTotals. If account is not included
					// in sum calculation, then add the account in sum
					// calculation to change the status to yes

					// Get iterator, and iterate through account list check if
					// account is included. If not change the status to yes and
					// add account to "to be updated list"
					itrAccount = colAccount.iterator();
					while (itrAccount.hasNext()) {
						// Get account details
						acctMaintenance = itrAccount.next();

						// If not included then change it to yes
						if ((acctMaintenance.getAcctMonitorSum() != null)
								&& acctMaintenance.getAcctMonitorSum().equals(
										SwtConstants.NO)) {
							acctMaintenance.setAcctMonitorSum(SwtConstants.YES);
							/* Condition to check secondary forecast is null */
							if (acctMaintenance.getSecondaryForecast() == null) {
								/* Setting secondary forecast as none */
								acctMaintenance.setSecondaryForecast("N");
							}
							/* Condition to check secondary external is null */
							if (acctMaintenance.getSecondaryExternal() == null) {
								/* Setting secondary external as none */
								acctMaintenance.setSecondaryExternal("N");
							}
							// Add account to "to be updated list"
							colAccountsToBeUpdated.add(acctMaintenance);
						}
					}
				} else if (operation.equals("ExcludeFromTotals")) {
					// Operation is ExcludeFromTotals. If account is included in
					// sum calculation, then remove the account from sum
					// calculation to change the status to no

					// Get iterator, and iterate through account list check if
					// account is included. If yes change the status to no and
					// add account to "to be updated list"
					itrAccount = colAccount.iterator();
					while (itrAccount.hasNext()) {
						// Get account details
						acctMaintenance = itrAccount.next();

						// If included then change it to no
						if ((acctMaintenance.getAcctMonitorSum() != null)
								&& acctMaintenance.getAcctMonitorSum().equals(
										SwtConstants.YES)) {
							acctMaintenance.setAcctMonitorSum(SwtConstants.NO);
							/* Condition to check secondary forecast is null */
							if (acctMaintenance.getSecondaryForecast() == null) {
								/* Setting secondary forecast as none */
								acctMaintenance.setSecondaryForecast("N");
							}
							/* Condition to check secondary external is null */
							if (acctMaintenance.getSecondaryExternal() == null) {
								/* Setting secondary external as none */
								acctMaintenance.setSecondaryExternal("N");
							}
							// Add account to "to be updated list"
							colAccountsToBeUpdated.add(acctMaintenance);
						}
					}
				}
			}
			/*
			 * Calls AccountMonitorNewDAO updateMonitorSum to update the
			 * MONITOR_SUM value based on the operation 1.ExcludeFromTotals
			 * 2.IncludeInTotals in the database
			 */
			acctmonitorNewDAO.updateMonitorSum(colAccountsToBeUpdated);
		} catch (SwtException ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [updateMonitorSum] - SwtException - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateMonitorSum", AccountMonitorNewManagerImpl.class);
		} catch (Exception ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [updateMonitorSum] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateMonitorSum", AccountMonitorNewManagerImpl.class);
		} finally {
			// nullify objects
			colAccountsToBeUpdated = null;
			colAccount = null;
			itrAccount = null;
			acctMaintenance = null;
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ updateMonitorSum ] - Exit ");
		}
	}

	/**
	 * This function counts Number of movements available for a particular
	 * balanceType
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            accountId
	 * @param String
	 *            accountType
	 * @param Date
	 *            valueDate
	 * @param String
	 *            balanceType
	 * @param String
	 *            applyCurrencyThreshold
	 * @throws SwtException
	 */
	public int getNoOfMovements(String hostId, String entityId,
			String currencyCode, String accountId, String accountType,
			Date valueDate, String balanceType, String applyCurrencyThreshold)
			throws SwtException {
		// To get total no of movements
		MovementManager movementManager = null;
		// Current filter condition
		String currentFilter = null;
		// Current sort field
		String currentSort = null;
		// Filter condition and sort field (concatenate of currentFilter and
		// currentSort)
		String filterSortStatus = null;
		// No of records to be displayed per page
		int pageSize;
		// Current page no
		int currentPage;
		// Total no of movements
		int totalCount = 0;
		// Totals returned from the SQL call
		HashMap<String, Object> totalMap = null; 
		try {
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ getNoOfMovements ] - Entry ");

			// Get no of records to be displayed on the page
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			// Set default values
			// Set current page to 1 (first page)
			currentPage = 1;
			/* Assigning value 'all' to currentFilter String variable */
			currentFilter = "all";
			/* Assigning value '2|false|' to currentSort String variable */
			currentSort = "2|false|";
			/*
			 * Assigning value '2|false|'with 'all' as concatenated to
			 * filterSortStatus String variable
			 */
			filterSortStatus = currentFilter + "," + currentSort;

			// Get implementaion of MovementManager interface from Spring
			// context
			movementManager = (MovementManager) SwtUtil
					.getBean("movementManager");
			// Get total no of movements
			// Start:Code Added By ASBalaji for mantis 2032 on
						// 15-08-2012
			// Modified to send openMovementFlag as a separate parameter instead
			// of concatenating with isAllstr.
			totalMap = movementManager.getMonitorMovements(hostId, entityId,
					currencyCode, accountId, valueDate, accountType,
					balanceType, Integer.valueOf(0), "", "", pageSize,
					currentPage, "N", "", new ArrayList<Movement>(),
					filterSortStatus, SwtConstants.ACCOUNT_MONITOR, "N",
					applyCurrencyThreshold, UserThreadLocalHolder.getUser(), "<None>");
			// End:Code Added By ASBalaji for mantis 2032 on
						// 15-08-2012
			if(totalMap != null && totalMap.get("totalCount") != null )
				totalCount = Integer.parseInt(String.valueOf(totalMap.get("totalCount")));
			
			return totalCount;
		} catch (SwtException ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [getNoOfMovements] - SwtException - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getNoOfMovements", AccountMonitorNewManagerImpl.class);
		} catch (Exception ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [getNoOfMovements] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getNoOfMovements", AccountMonitorNewManagerImpl.class);
		} finally {
			// nullify objects
			movementManager = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ getNoOfMovements ] - Exit ");
		}
	}

	/**
	 * This function will return Number of linked accounts available for a
	 * particular Account Id
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            accountId
	 * @param String
	 *            accountId
	 * @return int
	 * @throws SwtException
	 */
	public int getNoOfLinkAccounts(String hostId, String entityId,
			String currencyCode, String accountId) throws SwtException {
		// To hold account details as well as linked account list
		Collection<AcctMaintenance> colAccount = null;

		try {
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ getNoOfLinkAccounts ] - Entry ");
			// Get account and linked account list
			colAccount = acctmonitorNewDAO.getAccountAndLinkedAccountsList(
					hostId, entityId, currencyCode, accountId);

			// Calculate no of linked account (size - 1, as one is main account
			// and rest of them are linked account)
			if (colAccount == null || colAccount.size() == 0) {
				return 0;
			} else {
				return colAccount.size() - 1;
			}
		} catch (SwtException ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [getNoOfLinkAccounts] - SwtException - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getNoOfLinkAccounts", AccountMonitorNewManagerImpl.class);
		} catch (Exception ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [getNoOfLinkAccounts] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getNoOfLinkAccounts", AccountMonitorNewManagerImpl.class);
		} finally {
			// nullify object(s)
			colAccount = null;
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ getNoOfLinkAccounts ] - Exit ");
		}
	}

	/**
	 * This function updates the LORO_TO_PREDICTED flag for the selected account
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            selectedAccountId
	 * @param String
	 *            operation
	 * @throws SwtException
	 */
	public void updateLoroToPredictedFlag(String hostId, String entityId,
			String currencyCode, String selectedAccountId, String operation)
			throws SwtException {
		// To hold account details as well as linked account details
		Collection<AcctMaintenance> colAccount = null;
		// To get matched account
		Iterator<AcctMaintenance> itrAccount = null;
		// Account maintenance details
		AcctMaintenance accountMaintenance = null;
		// To update loro flag
		AcctMaintenance acctMaintenance = null;

		try {
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ updateLoroToPredictedFlag ] - Entry ");
			// Get account details as well as linked account list
			colAccount = acctmonitorNewDAO.getAccountAndLinkedAccountsList(
					hostId, entityId, currencyCode, selectedAccountId);
			/* If collection has value iterate through the values */
			if (colAccount != null) {
				// Iterate through account list, and get matched account
				itrAccount = colAccount.iterator();
				while (itrAccount.hasNext()) {
					// Get account details from collection
					acctMaintenance = itrAccount.next();
					// Get matched account
					if (acctMaintenance.getId().getHostId().equals(hostId)
							&& acctMaintenance.getId().getEntityId().equals(
									entityId)
							&& acctMaintenance.getId().getAccountId().equals(
									selectedAccountId)) {
						// Account details matched, so break the loop.
						accountMaintenance = acctMaintenance;
						break;
					}
				}
			}
			// If account details not found in the list, set default value
			// TODO check whether this is really required?
			if (accountMaintenance == null) {
				// Initialize account maintenance object to hold default
				// properties
				accountMaintenance = new AcctMaintenance();
				// Set properties
				accountMaintenance.getId().setHostId(hostId);
				accountMaintenance.getId().setEntityId(entityId);
				accountMaintenance.getId().setAccountId(selectedAccountId);
			}

			// Based on the operation, set flag(direction)
			if (operation.equals("MoveToLoro")) {
				logger
						.debug(this.getClass().getName()
								+ " - [ updateLoroToPredictedFlag ] - Moves to LORO account");
				//accountMaintenance.setLoroToPredicted(SwtConstants.NO);
			} else if (operation.equals("MoveToPredicted")) {
				logger
						.debug(this.getClass().getName()
								+ " - [ updateLoroToPredictedFlag ] - Moves to Predicted account");
				//accountMaintenance.setLoroToPredicted(SwtConstants.YES);
			}

			// Update Loro to predicted flag(direction)
			acctmonitorNewDAO.updateLoroToPredictedFlag(accountMaintenance);
		} catch (SwtException ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [updateLoroToPredictedFlag] - SwtException - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateLoroToPredictedFlag",
					AccountMonitorNewManagerImpl.class);
		} catch (Exception ex) {
			// log error message
			logger.error(this.getClass().getName()
					+ " - [updateLoroToPredictedFlag] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateLoroToPredictedFlag",
					AccountMonitorNewManagerImpl.class);
		} finally {
			// nullify objects
			colAccount = null;
			itrAccount = null;
			accountMaintenance = null;
			acctMaintenance = null;
			// log debug message
			logger.debug(this.getClass().getName()
					+ " - [ updateLoroToPredictedFlag ] - Exit ");
		}
	}
}