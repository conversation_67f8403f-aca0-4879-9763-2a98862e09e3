/*
 * Created on Jul 31, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 *  * This class contains getters and setters for Sweep Prior Cut-Off
 */
public class SweepPriorCutOff extends BaseObject implements
		org.swallow.model.AuditComponent {
	private Id id = new Id();
	private Double predictBalance;
	/* Start:Code Added for Mantis 1370 by Chinna On 9-MAR-2011 */
	private Double externalBalance= null;;
	/* End:Code Added for Mantis 1370 by Chinna On 9-MAR-2011 */
	private Double targetBalance;
	private Double sweepAmount;
	private String manualSweepFlag;
	private String autoSweepSwitchFlag;
	private String cutOff;
	private Date valuedate;
	private String currcode;
	private String valuedateAsString;
	private String predictBalanceAsString;
	/* Start:Code Added for Mantis 1370 by Chinna On 9-MAR-2011 */
	private String externalBalanceAsString=null;
	/* End:Code Added for Mantis 1370 by <PERSON>na On 9-MAR-2011 */
	private String targetBalanceAsString;
	private String sweepAmountAsString;
	private String acctType;
	private String acctname;
	private String minSweepAmt;
	private String sweepDays;
	private int leadTime;
	private int extendDisplayTimeBy;
	private String extendDisplayTime;
	private boolean targetBalanceNegative;
	private boolean predictedBalanceNegative;
	/* Start:Code Added for Mantis 1370 by Chinna On 9-MAR-2011 */
	private boolean externalBalanceNegative;
	/* End:Code Added for Mantis 1370 by Chinna On 9-MAR-2011 */
	private String mainAccountId;
	// Sweepfrm balance for Mantis 1370
	private String sweepFrmbal;
	private String subAcctim = null;
	
	private String mainAcccoutName;

	/**
	 * @return the subAcctim
	 */
	public String getSubAcctim() {
		return subAcctim;
	}

	/**
	 * @param subAcctim the subAcctim to set
	 */
	public void setSubAcctim(String subAcctim) {
		this.subAcctim = subAcctim;
	}

	/**
	 * @return the sweepFrmbal
	 */
	public String getSweepFrmbal() {
		return sweepFrmbal;
	}

	/**
	 * @param sweepFrmbal the sweepFrmbal to set
	 */
	public void setSweepFrmbal(String sweepFrmbal) {
		this.sweepFrmbal = sweepFrmbal;
	}

		/**
	 * @return Returns the MainAcccoutName.
	 */
	public String getMainAcccoutName() {
		return mainAcccoutName;
	}

	/**
	 * @param MainAcccoutName
	 *            The MainAcccoutName to set.
	 */
	public void setMainAcccoutName(String mainAcccoutName) {
		this.mainAcccoutName = mainAcccoutName;
	}

	
	/**
	 * @return Returns the autoSweepSwitchFlag.
	 */
	public String getAutoSweepSwitchFlag() {
		return autoSweepSwitchFlag;
	}

	/**
	 * @param autoSweepSwitchFlag
	 *            The autoSweepSwitchFlag to set.
	 */
	public void setAutoSweepSwitchFlag(String autoSweepSwitchFlag) {
		this.autoSweepSwitchFlag = autoSweepSwitchFlag;
	}

	/**
	 * @return Returns the currcode.
	 */
	public String getCurrcode() {
		return currcode;
	}

	/**
	 * @param currcode
	 *            The currcode to set.
	 */
	public void setCurrcode(String currcode) {
		this.currcode = currcode;
	}

	/**
	 * @return Returns the cuttOff.
	 */
	public String getCutOff() {
		return cutOff;
	}

	/**
	 * @param cuttOff
	 *            The cuttOff to set.
	 */
	public void setCutOff(String cutOff) {
		this.cutOff = cutOff;
	}

	/**
	 * @return Returns the manualSweepFlag.
	 */
	public String getManualSweepFlag() {
		return manualSweepFlag;
	}

	/**
	 * @param manualSweepFlag
	 *            The manualSweepFlag to set.
	 */
	public void setManualSweepFlag(String manualSweepFlag) {
		this.manualSweepFlag = manualSweepFlag;
	}

	/**
	 * @return Returns the predictBalanceAsString.
	 */
	public String getPredictBalanceAsString() {
		return predictBalanceAsString;
	}

	/**
	 * @param predictBalanceAsString
	 *            The predictBalanceAsString to set.
	 */
	public void setPredictBalanceAsString(String predictBalanceAsString) {
		this.predictBalanceAsString = predictBalanceAsString;
	}

	/**
	 * @return Returns the sweepAmountAsString.
	 */
	public String getSweepAmountAsString() {
		return sweepAmountAsString;
	}

	/**
	 * @param sweepAmountAsString
	 *            The sweepAmountAsString to set.
	 */
	public void setSweepAmountAsString(String sweepAmountAsString) {
		this.sweepAmountAsString = sweepAmountAsString;
	}

	/**
	 * @return Returns the targetBalanceAsString.
	 */
	public String getTargetBalanceAsString() {
		return targetBalanceAsString;
	}

	/**
	 * @param targetBalanceAsString
	 *            The targetBalanceAsString to set.
	 */
	public void setTargetBalanceAsString(String targetBalanceAsString) {
		this.targetBalanceAsString = targetBalanceAsString;
	}

	/**
	 * @return Returns the valuedate.
	 */
	public Date getValuedate() {
		return valuedate;
	}

	/**
	 * @param valuedate
	 *            The valuedate to set.
	 */
	public void setValuedate(Date valuedate) {
		this.valuedate = valuedate;
	}

	/**
	 * @return Returns the valuedateAsString.
	 */
	public String getValuedateAsString() {
		return valuedateAsString;
	}

	/**
	 * @param valuedateAsString
	 *            The valuedateAsString to set.
	 */
	public void setValuedateAsString(String valuedateAsString) {
		this.valuedateAsString = valuedateAsString;
	}

	/**
	 * @return Returns the predictBalance.
	 */
	public Double getPredictBalance() {
		return predictBalance;
	}

	/**
	 * @param predictBalance
	 *            The predictBalance to set.
	 */
	public void setPredictBalance(Double predictBalance) {
		this.predictBalance = predictBalance;
	}

	/**
	 * @return Returns the sweepAmount.
	 */
	public Double getSweepAmount() {
		return sweepAmount;
	}

	/**
	 * @param sweepAmount
	 *            The sweepAmount to set.
	 */
	public void setSweepAmount(Double sweepAmount) {
		this.sweepAmount = sweepAmount;
	}

	/**
	 * @return Returns the targetBalance.
	 */
	public Double getTargetBalance() {
		return targetBalance;
	}

	/**
	 * @param targetBalance
	 *            The targetBalance to set.
	 */

	public void setTargetBalance(Double targetBalance) {
		this.targetBalance = targetBalance;
	}

	/**
	 * @return Returns the acctType.
	 */
	public String getAcctType() {
		return acctType;
	}

	/**
	 * @param acctType
	 *            The acctType to set.
	 */
	public void setAcctType(String acctType) {
		this.acctType = acctType;
	}

	public static class Id extends BaseObject {
		private String hostId;
		private String entityId;
		private String accountId;

		public Id(String hostId, String entityId, String accountId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.accountId = accountId;

		}

		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		public Id() {
		}

		/**
		 * @return Returns the accountId.
		 */
		public String getAccountId() {
			return accountId;
		}

		/**
		 * @param accountId
		 *            The accountId to set.
		 */
		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}
	}

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the acctname.
	 */
	public String getAcctname() {
		return acctname;
	}

	/**
	 * @param acctname
	 *            The acctname to set.
	 */
	public void setAcctname(String acctname) {
		this.acctname = acctname;
	}

	/**
	 * @return Returns the minSweepAmt.
	 */
	public String getMinSweepAmt() {
		return minSweepAmt;
	}

	/**
	 * @param minSweepAmt
	 *            The minSweepAmt to set.
	 */
	public void setMinSweepAmt(String minSweepAmt) {
		this.minSweepAmt = minSweepAmt;
	}

	/**
	 * @return Returns the sweepDays.
	 */
	public String getSweepDays() {
		return sweepDays;
	}

	/**
	 * @param sweepDays
	 *            The sweepDays to set.
	 */
	public void setSweepDays(String sweepDays) {
		this.sweepDays = sweepDays;
	}

	/**
	 * @return Returns the leadTime.
	 */
	public int getLeadTime() {
		return leadTime;
	}

	/**
	 * @param leadTime
	 *            The leadTime to set.
	 */
	public void setLeadTime(int leadTime) {
		this.leadTime = leadTime;
	}

	/**
	 * @return Returns the extendDisplayTime.
	 */
	public String getExtendDisplayTime() {
		return extendDisplayTime;
	}

	/**
	 * @param extendDisplayTime
	 *            The extendDisplayTime to set.
	 */
	public void setExtendDisplayTime(String extendDisplayTime) {
		this.extendDisplayTime = extendDisplayTime;
	}

	/**
	 * @return Returns the extendDisplayTimeBy.
	 */
	public int getExtendDisplayTimeBy() {
		return extendDisplayTimeBy;
	}

	/**
	 * @param extendDisplayTimeBy
	 *            The extendDisplayTimeBy to set.
	 */
	public void setExtendDisplayTimeBy(int extendDisplayTimeBy) {
		this.extendDisplayTimeBy = extendDisplayTimeBy;
	}

	/**
	 * @return Returns the predictedBalanceNegative.
	 */
	public boolean isPredictedBalanceNegative() {
		return predictedBalanceNegative;
	}

	/**
	 * @param predictedBalanceNegative
	 *            The predictedBalanceNegative to set.
	 */
	public void setPredictedBalanceNegative(boolean predictedBalanceNegative) {
		this.predictedBalanceNegative = predictedBalanceNegative;
	}

	/**
	 * @return Returns the targetBalanceNegative.
	 */
	public boolean isTargetBalanceNegative() {
		return targetBalanceNegative;
	}

	/**
	 * @param targetBalanceNegative
	 *            The targetBalanceNegative to set.
	 */
	public void setTargetBalanceNegative(boolean targetBalanceNegative) {
		this.targetBalanceNegative = targetBalanceNegative;
	}

	/**
	 * @return Returns the mainAccountId.
	 */
	public String getMainAccountId() {
		return mainAccountId;
	}

	/**
	 * @param mainAccountId
	 *            The mainAccountId to set.
	 */
	public void setMainAccountId(String mainAccountId) {
		this.mainAccountId = mainAccountId;

	}

	/* Start:Code Added for Mantis 1370 by Chinna On 9-MAR-2011 */

	/**
	 * @return the externalBalance
	 */
	public Double getExternalBalance() {
		return externalBalance;
	}

	/**
	 * @param externalBalance
	 *            the externalBalance to set
	 */
	public void setExternalBalance(Double externalBalance) {
		this.externalBalance = externalBalance;
	}

	/**
	 * @return the externalBalanceAsString
	 */
	public String getExternalBalanceAsString() {
		return externalBalanceAsString;
	}

	/**
	 * @param externalBalanceAsString
	 *            the externalBalanceAsString to set
	 */
	public void setExternalBalanceAsString(String externalBalanceAsString) {
		this.externalBalanceAsString = externalBalanceAsString;
	}

	/**
	 * @return the externalBalanceNegative
	 */
	public boolean isExternalBalanceNegative() {
		return externalBalanceNegative;
	}

	/**
	 * @param externalBalanceNegative
	 *            the externalBalanceNegative to set
	 */
	public void setExternalBalanceNegative(boolean externalBalanceNegative) {
		this.externalBalanceNegative = externalBalanceNegative;
	}
	/* End:Code Added for Mantis 1370 by Chinna On 9-MAR-2011 */

	
}
