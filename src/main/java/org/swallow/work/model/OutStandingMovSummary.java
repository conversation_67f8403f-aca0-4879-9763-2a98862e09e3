/*
 * Created on Jan 15, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class OutStandingMovSummary implements Serializable{
	private String currencyCode;
	private String currencyName;
	
	private String posLevelFirst ;
	private String posLevelInterim;
	private String posLevelFinal;
	private String posLevelTotal;
	private String entityId ;
	private String date;
	private Map<String,String> summaryTotal;
	
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return Returns the currencyName.
	 */
	public String getCurrencyName() {
		return currencyName;
	}
	/**
	 * @param currencyName The currencyName to set.
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	/**
	 * @return Returns the posLevelFinal.
	 */
	public String getPosLevelFinal() {
		return posLevelFinal;
	}
	/**
	 * @param posLevelFinal The posLevelFinal to set.
	 */
	public void setPosLevelFinal(String posLevelFinal) {
		this.posLevelFinal = posLevelFinal;
	}
	/**
	 * @return Returns the posLevelFirst.
	 */
	public String getPosLevelFirst() {
		return posLevelFirst;
	}
	/**
	 * @param posLevelFirst The posLevelFirst to set.
	 */
	public void setPosLevelFirst(String posLevelFirst) {
		this.posLevelFirst = posLevelFirst;
	}
	/**
	 * @return Returns the posLevelInterim.
	 */
	public String getPosLevelInterim() {
		return posLevelInterim;
	}
	/**
	 * @param posLevelInterim The posLevelInterim to set.
	 */
	public void setPosLevelInterim(String posLevelInterim) {
		this.posLevelInterim = posLevelInterim;
	}
	/**
	 * @return Returns the posLevelTotal.
	 */
	public String getPosLevelTotal() {
		return posLevelTotal;
	}
	/**
	 * @param posLevelTotal The posLevelTotal to set.
	 */
	public void setPosLevelTotal(String posLevelTotal) {
		this.posLevelTotal = posLevelTotal;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the summaryTotal.
	 */
	public Map getSummaryTotal() {
		summaryTotal = new HashMap();
		summaryTotal.put("currencyCode",currencyCode);
		summaryTotal.put("currencyName",currencyName);
		summaryTotal.put("entityId",entityId);
		summaryTotal.put("date",date);		
		return summaryTotal;
	}
	/**
	 * @param summaryTotal The summaryTotal to set.
	 */
	public void setSummaryTotal(Map summaryTotal) {
		this.summaryTotal = summaryTotal;
	}
	
	
	private String summaryTotalUrlParams = new String();
	public String getSummaryTotalUrlParams() {
		getSummaryTotal();
		summaryTotalUrlParams =  summaryTotal.entrySet().stream()
		          .map(p -> p.getKey() + "=" + p.getValue())
		          .reduce((p1, p2) -> p1 + "&" + p2)
		          .map(s -> "" + s)
		          .orElse("");
		return summaryTotalUrlParams;
	}
	/**
	 * @return Returns the date.
	 */
	public String getDate() {
		return date;
	}
	/**
	 * @param date The date to set.
	 */
	public void setDate(String date) {
		this.date = date;
	}
}
