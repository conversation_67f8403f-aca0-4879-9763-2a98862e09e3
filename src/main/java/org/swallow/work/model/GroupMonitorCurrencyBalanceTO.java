/*
 * @(#)GroupMonitorCurrencyBalanceTO .java  11/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.Serializable;
import java.util.Date;
public class GroupMonitorCurrencyBalanceTO implements Serializable{
    /** stores the instance of log object */
    private final Log log = LogFactory.getLog(GroupMonitorCurrencyBalanceTO.class);

    /**
     * the total
     */
    private String total;

    /**
     * Flag for checking whether the total amount is negative
     */
    private boolean totalNegativeFlag = false;

    /**
     * the timestamp
     */
    private Date timeStamp;

    /**
     * Constructor for the class
     * Creates a new GroupMonitorCurrencyBalanceTO object.
     * @param total the total
     * @param timeStamp the time stamp at which this value is being stored in cache
     * @param totalNegativeFlag the flag to check whether the total is negative
     */
    public GroupMonitorCurrencyBalanceTO(String total, Date timeStamp,
        boolean totalNegativeFlag) {
        this.total = total;
        this.totalNegativeFlag = totalNegativeFlag;
        this.timeStamp = timeStamp;
    }

    /**
     * This function overrides the equals function of Object class.
     * @param obj the object to check
     * @return boolean value indicating whether the value exists in cache
     */

    public boolean equals(Object obj) {
        log.debug("entering equals method");

        boolean retValue = false;

        if ((obj != null) && obj instanceof GroupMonitorCurrencyBalanceTO) {
            log.debug("obj - " + obj);
            retValue = total.equals(((GroupMonitorCurrencyBalanceTO) obj)
                    .getTotal())
                && new Boolean(totalNegativeFlag).equals(new Boolean(
                        ((GroupMonitorCurrencyBalanceTO) obj)
                        .isTotalNegativeFlag()));
        }

        log.debug("retValue - " + retValue);
        log.debug("exiting equals method");

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     * @return hashcode
     */
    public int hashCode() {
        return total.hashCode() + new Boolean(totalNegativeFlag).hashCode();
    }

    /**
     * @return Returns the predictedBalance.
     */
    public String getTotal() {
        return total;
    }

    /**
     * @return Returns the timeStamp.
     */
    public Date getTimeStamp() {
        return timeStamp;
    }

    /**
     * @return Returns the totalNegativeFlag.
     */
    public boolean isTotalNegativeFlag() {
        return totalNegativeFlag;
    }
}
