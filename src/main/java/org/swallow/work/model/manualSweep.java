/*
 * @(#) manualSweep.java
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;
import java.util.Date;
import org.swallow.model.BaseObject;

public class manualSweep extends BaseObject{
 
	private Double predictBalance;
	private Double targetBalance;
	private Double sweepAmount;
	private String level;
	private String displayLevel; // Level to be displayed in JSP
    /**
     * @return Returns the displayLevel.
     */
    public String getDisplayLevel() {
        if( level!=null)
        {
			if( level.equalsIgnoreCase("m")){
			     displayLevel="Main"; 
			}else if (level.equalsIgnoreCase("s")){
			    displayLevel="Sub";
			}		 
	    }
        else
        {
            displayLevel="";
        }
    return displayLevel;
    }
    /**
     * @param displayLevel The displayLevel to set.
     */
    public void setDisplayLevel(String displayLevel) {
        setLevel(displayLevel);
        this.displayLevel = displayLevel;
    }
	private String cuttOff;
	private Date valuedate ;
	private String currcode;
	private String valuedateAsString;
	private String predictBalanceAsString;
	private String targetBalanceAsString;
	private String sweepAmountAsString;
	private String accountId;
	private String signFlagForPredictBal;
	private String signFlagForSweepAmount;
	
	private String hostId ;
	private String entityId;
	
	
	/**
	 * @return Returns the signFlagForPredictBal.
	 */
	public String getSignFlagForPredictBal() {
		return signFlagForPredictBal;
	}
	/**
	 * @param signFlagForPredictBal The signFlagForPredictBal to set.
	 */
	public void setSignFlagForPredictBal(String signFlagForPredictBal) {
		this.signFlagForPredictBal = signFlagForPredictBal;
	}
	/**
	 * @return Returns the currcode.
	 */
	public String getCurrcode() {
		return currcode;
	}
	/**
	 * @param currcode The currcode to set.
	 */
	public void setCurrcode(String currcode) {
		this.currcode = currcode;
	}
	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}
	/**
	 * @param accountId The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	/**
	 * @return Returns the cuttOff.
	 */
	public String getCuttOff() {
		return cuttOff;
	}
	/**
	 * @param cuttOff The cuttOff to set.
	 */
	public void setCuttOff(String cuttOff) {
		this.cuttOff = cuttOff;
	}
	/**
	 * @return Returns the level.
	 */
	public String getLevel() {
		return level;
	}
	/**
	 * @param level The level to set.
	 */
	public void setLevel(String level) {
		this.level = level;
	}
	/**
	 * @return Returns the predictBalance.
	 */
	public Double getPredictBalance() {
		return predictBalance;
	}
	/**
	 * @param predictBalance The predictBalance to set.
	 */
	public void setPredictBalance(Double predictBalance) {
		this.predictBalance = predictBalance;
	}
	/**
	 * @return Returns the sweepAmount.
	 */
	public Double getSweepAmount() {
		return sweepAmount;
	}
	/**
	 * @param sweepAmount The sweepAmount to set.
	 */
	public void setSweepAmount(Double sweepAmount) {
		this.sweepAmount = sweepAmount;
	}
	/**
	 * @return Returns the targetBalance.
	 */
	public Double getTargetBalance() {
		return targetBalance;
	}
	/**
	 * @param targetBalance The targetBalance to set.
	 */
	public void setTargetBalance(Double targetBalance) {
		this.targetBalance = targetBalance;
	}
	/**
	 * @return Returns the predictBalanceAsString.
	 */
	public String getPredictBalanceAsString() {
		return predictBalanceAsString;
	}
	/**
	 * @param predictBalanceAsString The predictBalanceAsString to set.
	 */
	public void setPredictBalanceAsString(String predictBalanceAsString) {
		this.predictBalanceAsString = predictBalanceAsString;
	}
	/**
	 * @return Returns the sweepamountAsString.
	 */
	public String getSweepAmountAsString() {
		return sweepAmountAsString;
	}
	/**
	 * @param sweepamountAsString The sweepamountAsString to set.
	 */
	public void setSweepAmountAsString(String sweepAmountAsString) {
		this.sweepAmountAsString = sweepAmountAsString;
	}
	/**
	 * @return Returns the targetBalanceAsString.
	 */
	public String getTargetBalanceAsString() {
		return targetBalanceAsString;
	}
	/**
	 * @param targetBalanceAsString The targetBalanceAsString to set.
	 */
	public void setTargetBalanceAsString(String targetBalanceAsString) {
		this.targetBalanceAsString = targetBalanceAsString;
	}
	/**
	 * @return Returns the valuedate.
	 */
	public Date getValuedate() {
		return valuedate;
	}
	/**
	 * @param valuedate The valuedate to set.
	 */
	public void setValuedate(Date valuedate) {
		this.valuedate = valuedate;
	}
	/**
	 * @return Returns the valuedateAsString.
	 */
	public String getValuedateAsString() {
		return valuedateAsString;
	}
	/**
	 * @param valuedateAsString The valuedateAsString to set.
	 */
	public void setValuedateAsString(String valuedateAsString) {
		this.valuedateAsString = valuedateAsString;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	
	
	/**
	 * @return Returns the signFlagForSweepAmount.
	 */
	public String getSignFlagForSweepAmount() {
		return signFlagForSweepAmount;
	}
	/**
	 * @param signFlagForSweepAmount The signFlagForSweepAmount to set.
	 */
	public void setSignFlagForSweepAmount(String signFlagForSweepAmount) {
		this.signFlagForSweepAmount = signFlagForSweepAmount;
	}
}
