/*
 * @(#)MessageArchive.java  
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;
import java.util.Date;
import org.swallow.model.BaseObject;
public class MessageArchive extends BaseObject implements org.swallow.model.AuditComponent{
	
	private Date startTime;	
	private Long  seqNumber;
	private String status;
	

	/**
	 * @return Returns the seqNumber.
	 */
	public Long getSeqNumber() {
		return seqNumber;
	}
	/**
	 * @param seqNumber The seqNumber to set.
	 */
	public void setSeqNumber(Long seqNumber) {
		this.seqNumber = seqNumber;
	}
	
	
	/**
	 * @return Returns the startTime.
	 */
	public Date getStartTime() {
		return startTime;
	}
	/**
	 * @param startTime The startTime to set.
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * @return Returns the status.
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * @param status The status to set.
	 */
	public void setStatus(String status) {
		this.status = status;
	}
}
