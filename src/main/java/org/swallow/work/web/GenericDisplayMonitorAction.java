/*
 * @(#)GenericDisplayMonitorAction.java 1.0 29/11/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.io.IOException;
import java.util.ArrayList;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.SwtDynamicReportImpl;
import org.swallow.model.ExportObject;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CommonDataManager;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.XmlEncoder;
import org.swallow.work.model.ColumnMetadata;
import org.swallow.work.model.GenericDisplayPageDTO;
import org.swallow.work.model.QueryResult;
import org.swallow.work.service.GenericDisplayMonitorManager;
import org.w3c.dom.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;





import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/genericdisplay", "/genericdisplay.do"})
public class GenericDisplayMonitorAction extends BaseController {
    private static final Map<String, String> viewMap = new HashMap<>();
    static {
        viewMap.put("fail", "error");
        viewMap.put("data", "jsp/data");
        viewMap.put("dataerror", "jsp/work/genericdisplaymonitorflexdataerror");
        viewMap.put("genericdisplay", "jsp/work/genericdisplaymonitor");
    }

    private String getView(String resultName) {
        return viewMap.getOrDefault(resultName, "error");
    }

    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
    public String execute(@RequestParam(value = "method", required = false) String method,
                          HttpServletRequest request, HttpServletResponse response) throws SwtException {
        method = String.valueOf(method);
        switch (method) {
            case "testBaseQuery":
                return testBaseQuery();
            case "getGenericDisplayData":
                return getGenericDisplayData();
            case "refreshGenericDisplayData":
                return refreshGenericDisplayData();
            case "genericDisplay":
                return genericDisplay();
            case "unspecified":
                return unspecified();
            case "exportGenericDetails":
                return exportGenericDetails();
            case "getScreenDetails":
                return getScreenDetails();
            case "getFacilityAccess":
                return getFacilityAccess();
        }


    return unspecified();
}




	/**
	 * Initializing opTimer object
	 */
	private OpTimer opTimer = new OpTimer();

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(GenericDisplayMonitorAction.class);
	
	@Autowired
private GenericDisplayMonitorManager genericDisplayMonitorManager = null;

	/**
	 * @param genericDisplayMonitorManager the genericDisplayMonitorManager to set
	 */
	public void setGenericDisplayMonitorManager(
			GenericDisplayMonitorManager genericDisplayMonitorManager) {
		this.genericDisplayMonitorManager = genericDisplayMonitorManager;
	}

	/**
	 * Test if any record exists in database for the base query
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String testBaseQuery() throws SwtException {
HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();

		
		String baseQuery="";
		String result = null;
		String testResult = null;
		long startTime;
		long endTime;
		String replacedQuery = null;
		String replQueryAfterSysTablesCheck = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		try{
			log.debug(this.getClass().getName() + "- [testBaseQuery] - Enter");
			baseQuery = request.getParameter("baseQuery");
			baseQuery = SwtUtil.decode64(baseQuery);
			if(SwtUtil.isEmptyOrNull(baseQuery)){
				baseQuery="-";
			}
			startTime = System.currentTimeMillis();
			result = genericDisplayMonitorManager.getRecords(baseQuery);
			endTime = System.currentTimeMillis();
			try{
				// Test if the query contain any select to a "System" table to protect DATABASE
				replacedQuery = SwtUtil.uncommentSQLQuery(baseQuery);
				replQueryAfterSysTablesCheck = replacedQuery.replaceAll("(?mi)((\\s*)(sys)(\\s)*(\\.))|(\\s*)\"(SYS)\"(\\s)*(\\.)", " ");
				if(!SwtUtil.isEmptyOrNull(replQueryAfterSysTablesCheck) && !SwtUtil.isEmptyOrNull(replacedQuery) && replacedQuery.length() == replQueryAfterSysTablesCheck.length()) {
					replacedQuery = replQueryAfterSysTablesCheck.replace("where", "where 1=2 AND ").replace("WHERE", "WHERE 1=2 AND ");
					testResult = genericDisplayMonitorManager.getRecords(replacedQuery);
					// if after replacement an error occurred then we will replace the current query result with error.
					if(testResult.split(SwtConstants.SEPARATOR_RECORD_REGEX)[0].equals("-1")) {
						result = testResult;
					}
					responseHandler = new ResponseHandler();
					responseConstructor = new SwtResponseConstructor();
					xmlWriter = responseConstructor.getXMLWriter();
					xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
					xmlWriter.startElement(SwtConstants.SINGLETONS);
					responseConstructor.createElement("result", result + SwtConstants.SEPARATOR_RECORD + (endTime-startTime));
					xmlWriter.endElement(SwtConstants.SINGLETONS);
					xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
					request.setAttribute("data", xmlWriter.getData());
					
				}
			}catch(Exception e){
				// Empty exception bloc to ensure that everything will work fine even when any exception rised 
				// in the security test
			}

			// set the access to response
			//response.getWriter().print(result + SwtConstants.SEPARATOR_RECORD + (endTime-startTime));			
			log.debug(this.getClass().getName() + " - [testBaseQuery] - " + "Exit");
			return getView("data");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [testBaseQuery] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "testBaseQuery", GenericDisplayMonitorAction.class), request, "");
			return getView("fail");
		}
	}
	
	/**
	 * Get the data of generic display related on the base query done by the user. 
	 * It will be executed only when loading the Generic display screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getGenericDisplayData() throws SwtException {
HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();

		
		String baseQuery="";
		QueryResult queryResult=null;
		XmlEncoder xmlEncoder = new XmlEncoder();
		Document xmlResult = null;
		String columnsWidth="";
		String columnsOrder="";
		String refColumns= null;
		String facilityRefColumns= null;
		String refParams= null;
		String facilityRefPrams= null;
		try{
			log.debug(this.getClass().getName() + "- [getGenericDisplayData] - Enter");
			
			/* Get baseQuery from the request, We retrieve the query filled by the user in advanced details 
			 * screen which could contain the '+' encoded character
			 */
			baseQuery = request.getParameter("basequery");
			refColumns = SwtUtil.decode64(request.getParameter("refColumns"));
			facilityRefColumns = request.getParameter("facilityRefColumns");
			refParams = request.getParameter("refParams");
			facilityRefPrams = request.getParameter("facilityRefParams");
			baseQuery = SwtUtil.decode64(baseQuery);
			
			columnsWidth = request.getParameter("columnsWidth");
			columnsOrder = request.getParameter("columnsOrder");
			
			opTimer.start(SwtConstants.OPTIMER_START_ALL);
			opTimer.start(SwtConstants.FETCH_MESSAGE);
			// Fill the instance of QueryResult class, we receive the result of baseQuery from the database 
			queryResult = genericDisplayMonitorManager.getGenericDisplayData(baseQuery);
			queryResult.getPage().setFacilityRefColumns(facilityRefColumns);
			queryResult.getPage().setScenarioRefColumns(refColumns);
			queryResult.getPage().setFacilityRefParams(facilityRefPrams);
			queryResult.getPage().setScenarioRefParams(refParams);
			
			opTimer.stop(SwtConstants.FETCH_MESSAGE);
			
			// Encode the queryResult to an xml
			if(queryResult!=null)
				xmlResult =  xmlEncoder.encode(queryResult, request, opTimer, columnsWidth, columnsOrder);
                  	else
				throw new SwtException("Exception Catched in [getGenericDisplayData] method nullPointerException");
			
			// Set the xml to response which will be maintained in Flex part
			response.getWriter().print(xmlEncoder.toXmlString(xmlResult));
			
			log.debug(this.getClass().getName() + " - [getGenericDisplayData] - " + "Exit");

			return null;
		} catch (SwtException exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGenericDisplayData] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGenericDisplayData] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "getGenericDisplayData", GenericDisplayMonitorAction.class), request, "");
			return getView("fail");
		}
	}
	
	/**
	 * Refresh the data in Generic Display screen when sorting, filtering or changing the page view
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String refreshGenericDisplayData() throws SwtException {
HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();

		
		String baseQuery="";
		QueryResult queryResult=null;
		XmlEncoder xmlEncoder = new XmlEncoder();
		Document xmlResult = null;
		String columnsWidth="";
		String columnsOrder="";
		String cancelExport="";
		String refColumns="";
		String facilityRefColumns="";
		String refParams= null;
		String facilityRefPrams= null;
		String filter= null;
		String roleId = null;
		String scenarioId = null;
		String selectedFilter = null;
		String selectedSort = null;
		String selectedCurrencyGroup = null;
		String applyCurrencyThreshold = null;
		
		try{
			log.debug(this.getClass().getName() + "- [refreshGenericDisplayData] - Enter");
			
			/* Decode the basequery. We have a problem with '+' character.
			   That's why we encode it in jsp part and then undo it in this part */
			baseQuery = request.getParameter("basequery");
			if(baseQuery!=null)
				baseQuery = SwtUtil.decode64(baseQuery);
			
			columnsWidth = request.getParameter("columnsWidth");
			columnsOrder = request.getParameter("columnsOrder");
			refColumns = SwtUtil.decode64(request.getParameter("refColumns"));
			facilityRefColumns = request.getParameter("facilityRefColumns");
			refParams = request.getParameter("refParams");
			facilityRefPrams = request.getParameter("facilityRefParams");
			selectedCurrencyGroup = request.getParameter("selectedCurrencyGroup")!=null?request.getParameter("selectedCurrencyGroup"):null;
			applyCurrencyThreshold = request.getParameter("applyCurrencyThreshold")!=null?request.getParameter("applyCurrencyThreshold"):null;
			
			if (request.getParameter("filter")!=null)
				filter = SwtUtil.decode64(request.getParameter("filter"));
			else 
				filter = "";
			
			if(request.getParameter("fromSummaryScreen")!=null && request.getParameter("fromSummaryScreen").equals(SwtConstants.STR_TRUE)) {
				/* Getting the User's Role Id from the session object */
				roleId = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();
				scenarioId = request.getParameter("scenarioID");
			}
		

			/* Set the cancelExport value from Flex part.
			   It will be 'true' only when the user exports more than 300 pages, and then cancels the export before accomplishment*/ 
			cancelExport = request.getParameter("cancelExport");
 
			CommonDataManager CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			CDM.setCancelExport(cancelExport);
			opTimer.start(SwtConstants.OPTIMER_START_ALL);
			opTimer.start(SwtConstants.FETCH_MESSAGE);
			
			// Create an object 'page'. It conserves all the properties of pagination, filtering and sorting 
			GenericDisplayPageDTO page  = new GenericDisplayPageDTO();
			
			// Set the selectedFilter value, retrieved from the request, in 'page' object
			selectedFilter = request.getParameter("selectedFilter");

			if(!SwtUtil.isEmptyOrNull(selectedFilter))
					{
						selectedFilter = SwtUtil.decode64(request.getParameter("selectedFilter").toString());
						page.setSelectedFilter(selectedFilter.concat("|"+filter));
					}
			else {
				page.setSelectedFilter(filter);
			}
			selectedSort = request.getParameter("selectedSort");
			// Set the selectedSort value, retrieved from the request, in 'page' object
			if(!SwtUtil.isEmptyOrNull(selectedSort))
				page.setSelectedSort(SwtUtil.decode64(request.getParameter("selectedSort").toString()));
			else 
				page.setSelectedSort("1");
			
			page.setAllPages(false);
			
			// Set the pageNumber value, retrieved from the request, in 'page' object
			if(request.getParameter("pageNoValue")!=null)
				page.setPageNumber(Integer.parseInt(request.getParameter("pageNoValue").toString()));
			else if(request.getParameter("currentPage")!=null)
				page.setPageNumber(Integer.parseInt(request.getParameter("currentPage").toString()));
			else {
				page.setPageNumber(1);
			}
			// Set recordsPerPage in 'page' object. It is a constant set in default_predict.properties
			page.setRecordsPerPage(SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE));	
			
			page.setFacilityRefColumns(facilityRefColumns);
			page.setScenarioRefColumns(refColumns);
			page.setFacilityRefParams(facilityRefPrams);
			page.setScenarioRefParams(refParams);
			//
			// Fill the instance of QueryResult class, we receive the result of baseQuery from the database 
			queryResult = genericDisplayMonitorManager.getQueryResultPage(page, opTimer, baseQuery, scenarioId,  roleId,selectedCurrencyGroup,applyCurrencyThreshold);
			opTimer.stop(SwtConstants.FETCH_MESSAGE);
			page.setSelectedFilter(selectedFilter!=null?selectedFilter:"");
			// Encode the queryResult to an XML
			if(queryResult!=null)
				xmlResult =  xmlEncoder.encode(queryResult, request, opTimer, columnsWidth, columnsOrder);
			else
				throw new SwtException("Exception Catched in [refreshGenericDisplayData] method nullPointerException");
			
			// Set the XML to response which will be maintained in Flex part
			response.getWriter().print(xmlEncoder.toXmlString(xmlResult));
			log.debug(this.getClass().getName() + " - [refreshGenericDisplayData] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [refreshGenericDisplayData] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "refreshGenericDisplayData", GenericDisplayMonitorAction.class), request, "");
			return getView("fail");
		}
	}
	
	/**
	 * Method to set the mapping between advanced details and generic display screen  
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward
	 */
	public String genericDisplay() {
HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + " - [genericDisplay] - "
					+ "Entry");
			request.setAttribute("scenarioID", request.getParameter("scenarioID"));
			request.setAttribute("fromSummaryScreen", request.getParameter("fromSummaryScreen"));
			request.setAttribute("scenarioTitle", request.getParameter("scenarioTitle"));
			request.setAttribute("basequery", request.getParameter("basequery"));
			request.setAttribute("facilityID", request.getParameter("facilityID"));
			request.setAttribute("facilityName", request.getParameter("facilityName"));
			request.setAttribute("refColumns", request.getParameter("refColumns"));
			request.setAttribute("facilityRefColumns", request.getParameter("facilityRefColumns"));
			request.setAttribute("facilityRefParams", request.getParameter("facilityRefParams"));
			request.setAttribute("refParams", request.getParameter("refParams"));
			request.setAttribute("filter", request.getParameter("filter"));
			request.setAttribute("selectedCurrencyGroup", request.getParameter("selectedCurrencyGroup"));
			request.setAttribute("applyCurrencyThreshold", request.getParameter("applyCurrencyThreshold"));
			request.setAttribute("method", "genericDisplay");
			// Add max pages to be exported
			request.setAttribute("exportMaxPages", PropertiesFileLoader
								.getInstance().getPropertiesValue(
										SwtConstants.EXPORT_MAX_PAGE_SIZE));
			opTimer.start("all");
			return getView("genericdisplay");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [genericDisplay] method : - "
					+ exp.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "genericDisplay", GenericDisplayMonitorAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * Default method returns Flex page
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 */
	public String unspecified() {
HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [unspecified] - " + " Entry");
		return getView("flex");
	}
	
	/**
	 * This Method is used to Export Data in Corresponding format for generic details
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * @throws IOException
	 */
	public String exportGenericDetails() throws SwtException {
		String currentFilter = null;
		String currentSort = null;
		String currPageStr = null;
		String exportType = null;
		ArrayList<FilterDTO> filterData = null;
		FilterDTO fDTO = null;
		String fileName = null;
		int currentPage = 0;
		int pageSize = 0;
		String titleSuffix = "";
		String scenarioID = "";
		String scenarioTitle = "";
		String baseQuery = "";
		String facilityID = "";
		ColumnMetadata metadata;
		QueryResult queryResult;
		ArrayList<ColumnDTO> columnData = null;
		ColumnDTO cDTO = null;
		XmlEncoder xmlEncoder = new XmlEncoder();
		SwtDynamicReportImpl dynamicReport = null;
		// Token for download corresponding file
		String tokenForDownload = null;
		String filter = null;
		String roleId = null;
		String selectedCurrencyGroup = null;
		String applyCurrencyThreshold = null;
		ActionMessages errors = null;
		CommonDataManager CDM = null;
		// hold the cancelExport value in CDM
		String cancelExport = ""; 
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			errors= new ActionMessages();
				CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
				log.debug(this.getClass().getName()
						+ " - [exportGenericDetails] - " + "Entry");
				scenarioID = request.getParameter("scenarioID");
				scenarioTitle = request.getParameter("scenarioTitle");
				baseQuery = request.getParameter("basequery");
				selectedCurrencyGroup = request.getParameter("selectedCurrencyGroup")!=null?request.getParameter("selectedCurrencyGroup"):null;
				applyCurrencyThreshold = request.getParameter("applyCurrencyThreshold")!=null?request.getParameter("applyCurrencyThreshold"):null;
				if(baseQuery!=null)
						baseQuery= SwtUtil.decode64(baseQuery);
				
				facilityID = request.getParameter("facilityID");

				// Add a cookie to the response. It is the same as come from jsp part.
				// downloadTokenValue will have been provided in the form submit via the hidden input field
				// Source scan tools may report a "HTTP response splitting" vulnerability. A generic solution of
				// ignoring text after CRLFs is implemented in XSSFilter class, so can safely ignore the report.
				tokenForDownload = request.getParameter("tokenForDownload");
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|OK")); 

				if (request.getParameter("filter")!=null)
					filter = SwtUtil.decode64(request.getParameter("filter"));
				else 
					filter = "";
				
				// initialize current page
				currentPage = 1;
				// get page Size from properties file.
				pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE);
				// check currentFilter whether it is null or empty, set as all.
				currentFilter = (request.getParameter("selectedFilter") == null || request
						.getParameter("selectedFilter").trim().length() == 0) ? "all"
						: SwtUtil.decode64(request.getParameter("selectedFilter"));
				/*
				 * check currentSort whether it is null or empty, set as default
				 * value.
				 */
				currentSort = (request.getParameter("selectedSort") == null || request
						.getParameter("selectedSort").trim().length() == 0) ? "1"
						:  SwtUtil.decode64(request.getParameter("selectedSort"));
				// get currentPage from request parameter. 
				currPageStr = request.getParameter("currentPage");
				// get exportType from request parameter. 
				exportType = request.getParameter("exportType");
				// check currPageStr whether not null, convert string to int.
				if (currPageStr != null) {
					currentPage = Integer.parseInt(currPageStr);
				}
				// check pageCount whether not null, calculate page size.
				if (request.getParameter("pageCount") != null) {
					pageSize = pageSize
							* Integer.parseInt(request.getParameter("pageCount"));
				}
				// Create an object 'page'. It conserves all the properties of pagination, filtering and sorting 
				GenericDisplayPageDTO page  = new GenericDisplayPageDTO();
				// Set the selectedFilter value, retrieved from the request, in 'page' object
				page.setSelectedFilter(currentFilter.concat("|" + filter));
				// Set the selectedSort value, retrieved from the request, in 'page' object
				page.setSelectedSort(currentSort);
				// Set the currentPage value, retrieved from the request, in 'page' object
				page.setPageNumber(currentPage);
				
				// Set recordsPerPage in 'page' object. It is a constant set in default_predict.properties
				page.setRecordsPerPage(SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE));	
	
				if (!request.getParameter("pageCount").equals("1"))
					page.setAllPages(true);
				else
					page.setAllPages(false);
				
				if(request.getParameter("fromSummaryScreen")!=null && request.getParameter("fromSummaryScreen").equals(SwtConstants.STR_TRUE)) {
					/* Getting the User's Role Id from the session object */
					roleId = ((CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN)).getUser()
							.getRoleId();
				}
					
				// get data from db
				queryResult = genericDisplayMonitorManager.getQueryResultPage(page, opTimer, baseQuery,scenarioID,roleId,selectedCurrencyGroup,applyCurrencyThreshold,true);
				
				cancelExport = CDM.getCancelExport();
				if(SwtUtil.isEmptyOrNull(cancelExport)||(!cancelExport.equals("true")))
					cancelExport="false";
				if(cancelExport.equals("true")){
					response.setContentType("text/html");
					response.setHeader("Content-disposition","inline");
					return null;
				}
				columnData = new ArrayList<ColumnDTO>();
				// column for date
				for (int i=0;i<queryResult.getMetadataDetails().size();i++){
					cDTO = new ColumnDTO();
					metadata = (ColumnMetadata) queryResult.getMetadataDetails().get(i);
					cDTO.setHeading(metadata.getColumnLabel());
					cDTO.setType(xmlEncoder.getDataType(metadata.getColumnType()));
					cDTO.setDataElement(metadata.getColumnName());
					columnData.add(cDTO);
				}
				
				filterData = new ArrayList<FilterDTO>();
				fDTO = new FilterDTO();
				
				// Set scenario ID filter Name and value in Export screen. 
				fDTO.setName("Scenario ID");
				fDTO.setValue(scenarioID);
				filterData.add(fDTO);
	
				// set scenario title filter Name and value in Export screen.
				fDTO = new FilterDTO();
				fDTO.setName("Scenario Title");
				fDTO.setValue(scenarioTitle);
				filterData.add(fDTO);
				
				// set query filter Name and value in Export screen.
				fDTO = new FilterDTO();
				fDTO.setName("Base Query");
				fDTO.setValue(baseQuery);
				filterData.add(fDTO);
				
				// set facility ID filter Name and value in Export screen.
				fDTO = new FilterDTO();
				fDTO.setName("Facility ID");
				fDTO.setValue(facilityID);
				filterData.add(fDTO);
				
				dynamicReport= new SwtDynamicReportImpl();
				ArrayList<ArrayList<ExportObject>> data  = new ArrayList<ArrayList<ExportObject>>();										
				if(cancelExport.equals("true")){
					response.setContentType("text/html");
					response.setHeader("Content-disposition","inline");
					return null;
				}
				fileName = request.getParameter("screen").replaceAll(" ", "");
				titleSuffix = PropertiesFileLoader.getInstance()
						.getPropertiesValue("windows.title.suffix");
	
				// Check whether titleSuffix is null and set empty string 
				if (titleSuffix == null)
					titleSuffix = "";
				// Export type for csv
				if (exportType.equalsIgnoreCase("excel")) {
					// Content for excel
					response.setContentType("application/vnd.ms-excel");
					// Column Heading
					response.setHeader("Content-disposition",
							"attachment; filename=" + fileName + titleSuffix + "_"
									+ SwtUtil.FormatCurrentDate() + ".xls");
					// Object Conversion to XLS
					//excelObjGen.convertObject(request, response, columnData, filterData, data, null, null, fileName);
					dynamicReport.convertObject(request, response, columnData, filterData, baseQuery, queryResult,null, fileName,SwtDynamicReportImpl.XLS_EXPORT, null);

				// Export type for pdf
				} else if (exportType.equalsIgnoreCase("pdf")) {
					// Content for pdf
					response.setContentType("application/pdf");
					// Column Heading
					response.setHeader("Content-disposition",
							"attachment; filename=" + fileName + titleSuffix + "_"
									+ SwtUtil.FormatCurrentDate() + ".pdf");
					// Object Conversion to PDF
					dynamicReport.convertObject(request, response, columnData, filterData, baseQuery, queryResult,null, fileName,SwtDynamicReportImpl.PDF_EXPORT, null);
				// Export type for csv
				} else if (exportType.equalsIgnoreCase("csv")) {
					// Content for csv
					response.setContentType("application/vnd.ms-excel");
					// Column Heading
					response.setHeader("Content-disposition",
							"attachment; filename=" + fileName + titleSuffix + "_"
									+ SwtUtil.FormatCurrentDate() + ".csv");
					// Object Conversion to CSV
					/* If csvResponse is true then we have to cancel the export of the SCV file. It becomes true only
					   when the user exports more than 300 pages (as default property in Predict) and then cancels it*/
					dynamicReport.convertObject(request, response, columnData, filterData, baseQuery, queryResult, null, fileName,SwtDynamicReportImpl.CSV_EXPORT, null);
				}
				
				log.debug(this.getClass().getName() + "- [exportGenericDetails] - Exit");
				return null;
				}catch(OutOfMemoryError exp){
				if (errors != null) {
					errors.add("", new ActionMessage(exp.getMessage()));
				}
				saveErrors(request, errors);
				// Source scan tools may report a "HTTP response splitting" vulnerability, a generic solution works by ignoring text after CRLFs is implemented XSSFilter class. 
				tokenForDownload = request.getParameter("tokenForDownload");
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|KO")); 
				response.setContentType("text/html");
				response.setHeader("Content-disposition","inline");
				return null;
			} catch (SwtException swtexp) {
				if (swtexp.getErrorCode().contains(
						"errors.OutOfMemoryError")) {
					if (errors != null) {
						errors.add("", new ActionMessage(swtexp.getErrorCode()));
					}
					saveErrors(request, errors);
					// Source scan tools may report a "HTTP response splitting" vulnerability, a generic solution works by ignoring text after CRLFs is implemented XSSFilter class. 
					tokenForDownload = request.getParameter("tokenForDownload");
					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|KO")); 
					response.setContentType("text/html");
					response.setHeader("Content-disposition","inline");
					return null;
				}else{	
				log.error(this.getClass().getName()
						+ " - Exception Catched in [exportGenericDetails] method : - "
						+ swtexp.getMessage());
				SwtUtil.logException(swtexp, request, "");
				return getView("fail");
				}
				
				
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [exportGenericDetails] method : - "
						+ e.getMessage());
				SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
						e, "exportErrors", GenericDisplayMonitorAction.class), request, "");
				return getView("fail");
				
			} 
		finally {
				if (CDM != null) {
					//CDM.setCancelExport("false");
				}
				// Nullifying the objects for already created objects
				currentFilter = null;
				currentSort = null;
				currPageStr = null;
				exportType = null;
				filterData = null;
				fDTO = null;
				fileName = null;
				titleSuffix = null;
				scenarioID = null;
				scenarioTitle = null;
				baseQuery = null;
				facilityID = null;
				metadata = null;
				queryResult = null;
				columnData = null;
				cDTO = null;
				xmlEncoder = new XmlEncoder();
			}
	}
	
	
	/**
	 * Get the screen details (the program name, height and width) for a defined facility   
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getScreenDetails() throws SwtException {
HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();

		
		String facilityId = "";
		String screenDetails = "";
		try{
			log.debug(this.getClass().getName() + "- [getScreenDetails] - Enter");
			facilityId = request.getParameter("facilityId");
			// Get a string like: 'program name','height','width'
			screenDetails = genericDisplayMonitorManager.getScreenDetails(facilityId);
			// Set the screenDetails to response
			response.getWriter().print(screenDetails);
			log.debug(this.getClass().getName() + " - [getScreenDetails] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScreenDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getScreenDetails", GenericDisplayMonitorAction.class), request, "");
			return getView("fail");
		}
	}
	
	/**
	 * Return the facility access of the required facility screen. The user has access to the facility screen if he: 
	 * - Has access to the menu item 
	 * - Has access to the entity id
	 * - Has access to the currency id 
	 * It returns a string which contains:
	 * - 0: full access
	 * - 1: view access
	 * - 2: no access 
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getFacilityAccess() throws SwtException {
HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();

		
		String facilityId = null;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String roleId = null;
		String access = null;
		try{
			log.debug(this.getClass().getName() + "- [getFacilityAccess] - Enter");
			// Get role id from request
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			facilityId = request.getParameter("facilityId");
			hostId = request.getParameter("hostId");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			// Get the access of a facility screen related to its facility id
			access = genericDisplayMonitorManager.getFacilityAccess(hostId,
					roleId, entityId, currencyCode, facilityId);
			// Add the access to the response
			response.getWriter().print(access);
			log.debug(this.getClass().getName() + " - [getFacilityAccess] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFacilityAccess] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getFacilityAccess", GenericDisplayMonitorAction.class), request, "");
			return getView("fail");
		}
	}
}
