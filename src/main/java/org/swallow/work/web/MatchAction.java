/*
 * @(#)MatchAction.java 1.0 23/01/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Match;
import org.swallow.work.model.MatchQueue;
import org.swallow.work.service.MatchDetailVO;
import org.swallow.work.service.MatchManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR> Tripathi
 * @version 11th Jan 2007
 *
 * <pre>
 * Action Layer for AMtch
 * Used to
 *  - Display Offered / Suspend / Confirmed queues
 * </pre>
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/movementmatch", "/movementmatch.do"})
public class MatchAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("summary", "jsp/work/movementmatchsummarydisplay");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/work/movementmatchqueueselection");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
		}


		return unspecified();
	}


	private MatchQueue matchQueue;
	public MatchQueue getMatchQueue() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		matchQueue = RequestObjectMapper.getObjectFromRequest(MatchQueue.class, request, "matchQueue");
		return matchQueue;
	}

	public void setMatchQueue(MatchQueue matchQueue) {
		this.matchQueue = matchQueue;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("matchQueue", matchQueue);
	}
	private Match match;
	public Match getMatch() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		match = RequestObjectMapper.getObjectFromRequest(Match.class, request, "match");
		return match;
	}

	public void setMatch(Match match) {
		this.match = match;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("match", match);
	}


	@Autowired
	private MatchManager matchManager = null;

	private final Log log = LogFactory.getLog(MatchAction.class);

	/**
	 * @param matchManager
	 */
	public void setMatchManager(MatchManager matchManager) {
		this.matchManager = matchManager;
	}

	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		request.getSession().setAttribute("status",
				request.getParameter("value"));
		return display();
	}

	/**
	 * This method has been written to shift the screen data logic on backend.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward
	 * @throws Exception
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		/* Local variable declaration */
		// To hold the hostId
		String hostId = null;
		// To hold entityId
		String entityId = null;
		// To hold the currency group id
		String currGrpId = null;
		// To hold role id
		String roleId = null;
		// To hold the match status
		String status = null;
		// To hold the applyCurrencyThreshold value
		String applyCurrencyThreshold = null;
		// To hold number of IncludedMovements in a Matche
		String noIncludedMovementMatches = null;
		// To hold the variable to indicate number of included movement matches
		String noIncludedMovementMatchesInd = null;
		// DynaValidatorForm instance
		// DynaValidatorForm dyForm = null;
		// variable to hold the currencyCode
		String currencyCode = null;
		// SystemFormats object
		SystemFormats sysFormat = null;
		// String variable to hold searchDateAsString
		String searchDateAsString = null;
		// Object to get the system format
		SystemFormats format = null;
		// Variable to hold value date
		Date valueDate = null;
		// Date flag to check whether the user selected value and value date are
		// equal
		boolean dateFlag = true;
		// MatchQueue instance
		MatchQueue matchQueue = null;
		// Match instance
		Match match = null;
		// Variable to hold Tab Flag
		String tabFlag = null;
		// Variable to hold database date
		String dbDate = null;
		// To hold applyCurrencyThreshold indicator
		String applyCurrencyThresholdInd = null;
		// To hold currency group access
		int currGrpAccess;
		// To hold selected tab index
		String selectedTabIndex = null;
		// To hold Today Date
		String todayDateAsString = null;
		// To hold Today Date + plus one
		String datePlusOneAsString = null;
		// To hold Today Date + plus two
		String todayDatePlusTwoAsString = null;
		// To hold Today Date + plus three
		String todayDatePlusThreeAsString = null;
		// To hold Today Date + plus four
		String todayDatePlusFourAsString = null;
		// To hold Today Date + plus five
		String todayDatePlusFiveAsString = null;
		// To hold Today Date + plus six
		String todayDatePlusSixAsString = null;
		// To hold on-load date
		Date dateOnLoad = null;
		// To hold formatted test date
		Date testDateFormatted = null;
		// MatchDetailVO instance
		MatchDetailVO matchDetailsVO = null;
		// To hold matchlistdetails
		ArrayList matchListDetailsAll = null;
		// To hold the system date
		Date systemDBDate = null;
		/* Variable Declaration for calledFrom */
		String calledFrom = null;
		try {
			log.debug(this.getClass().getName() + "-[display]- Entry");
			// Initialize entityId
			entityId = SwtConstants.EMPTY_STRING;
			// Initialize the currency group id
			currGrpId = SwtConstants.EMPTY_STRING;
			// Initialize role id
			roleId = SwtConstants.EMPTY_STRING;
			// Initialize the status
			status = SwtConstants.EMPTY_STRING;
			// Initialize the applyCurrencyThreshold
			applyCurrencyThreshold = SwtConstants.EMPTY_STRING;
			// Initialize noIncludedMovementMatches
			noIncludedMovementMatches = SwtConstants.EMPTY_STRING;
			// Initialize noIncludedMovementMatchesInd
			noIncludedMovementMatchesInd = SwtConstants.EMPTY_STRING;
			// get the // DynaValidatorForm
			/* This is used to get the current system formats from session */
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get the matchQueue from // DynaValidatorForm
			matchQueue = (MatchQueue) getMatchQueue();
			// get the match from // DynaValidatorForm
			match = (Match) getMatch();
			// Modified for mantis 1443
			// get the hostid from cache manager
			if (!SwtUtil.isEmptyOrNull(request.getParameter("hostId")))
				hostId = request.getParameter("hostId");
			else
				hostId = CacheManager.getInstance().getHostId();
			// get the role Id
			roleId = SwtUtil.getCurrentUser(request.getSession()).getRoleId();
			// get the entityId from request
			entityId = request.getParameter("entityId");
			// get the entityid from match object if entity is null
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = match.getId().getEntityId();
			}
			// get the current entityid if entity is null
			if (SwtUtil.isEmptyOrNull(entityId)) {
				// Get the user current entity id
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			// get the system db date
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);

			// Start:Code modified by Vivekanandan A on 17/07/2012 for mantis
			// 1991
			// get the system date with format
			dbDate = SwtUtil.getSysDateWithFmt(systemDBDate);
			// set db date in request
			request.setAttribute("dbDate", dbDate);
			// End:Code modified by Vivekanandan A on 17/07/2012 for mantis 1991
			// set the entityId in match object
			match.getId().setEntityId(entityId);
			/*
			 * if screen is excuted from genric than append selectedCurrencyGroup from currencyGroup of the selected currencyId
			 */
			calledFrom = request.getParameter("calledFrom");
			if(calledFrom!=null&&calledFrom.equals("generic")){

				currGrpId =  SwtUtil.getCurrencyGroup(entityId,request
						.getParameter("currencyId" ) );
			}else {
				// get the currencyGroupId from request
				currGrpId = request.getParameter("currencyGroupId");
			}
			// get the currGrpId from matchQueue object if currGrpId is null
			if (SwtUtil.isEmptyOrNull(currGrpId)) {
				// get the currency group code from match queue
				currGrpId = matchQueue.getCurrencyGrpCode();
			}
			// get the current currGrpId if currGrpId is null
			if (SwtUtil.isEmptyOrNull(currGrpId)) {
				// get the current user from swtutil
				currGrpId = SwtUtil.getCurrentUser(request.getSession())
						.getCurrentCcyGrpId();
			}
			// get the status from request
			status = request.getParameter("status");
			// checks the status is null
			if (status == null) {
				// get the status form request
				status = (String) request.getSession().getAttribute("status");
			}
			// set attribute for status in request
			request.setAttribute("status", status);
			// get the applyCurrencyThreshold from request
			applyCurrencyThreshold = request
					.getParameter("applyCurrencyThreshold");
			// get the applyCurrencyThreshold from matchQueue object if
			// applyCurrencyThreshold is null
			if (SwtUtil.isEmptyOrNull(applyCurrencyThreshold)) {
				applyCurrencyThreshold = matchQueue.getApplyCurrencyThreshold();
			}
			// set 'Y' for applyCurrencyThreshold if applyCurrencyThreshold is
			// null
			if (SwtUtil.isEmptyOrNull(applyCurrencyThreshold)) {
				applyCurrencyThreshold = SwtConstants.YES;
			}
			// set the applyCurrencyThreshold in matchQueue
			matchQueue.setApplyCurrencyThreshold(applyCurrencyThreshold);
			// get the applyCurrencyThresholdInd from request
			applyCurrencyThresholdInd = request
					.getParameter("applyCurrencyThresholdInd");
			// set the applyCurrencyThresholdInd
			if (applyCurrencyThresholdInd != null
				&& applyCurrencyThresholdInd.equals("1")) {
				if (matchQueue.getApplyCurrencyThreshold().equals(
						SwtConstants.YES)) {
					applyCurrencyThreshold = SwtConstants.NO;
				} else if (matchQueue.getApplyCurrencyThreshold().equals(
						SwtConstants.NO)) {
					applyCurrencyThreshold = SwtConstants.YES;
				}
			}
			// set the applyCurrencyThresholdInd in the matchQueue
			matchQueue.setApplyCurrencyThreshold(applyCurrencyThreshold);
			// set the attribute for applyCurrencyThreshold in request
			request.setAttribute("applyCurrencyThreshold",
					applyCurrencyThreshold);
			/*
			 * Maintaining "No-Included Movement Matches" check box of screen.
			 */
			noIncludedMovementMatches = request
					.getParameter("noIncludedMovementMatches");
			noIncludedMovementMatchesInd = request
					.getParameter("noIncludedMovementMatchesInd");
			// set the noIncludedMovementMatches as "N" flag
			if (SwtUtil.isEmptyOrNull(noIncludedMovementMatches)) {
				noIncludedMovementMatches = SwtConstants.NO;
			}
			// set the noIncludedMovementMatchesInd based on condition check
			if (noIncludedMovementMatchesInd != null
				&& noIncludedMovementMatchesInd.equals("1")) {
				if (noIncludedMovementMatches.equals(SwtConstants.YES)) {
					noIncludedMovementMatches = SwtConstants.NO;
				} else if (noIncludedMovementMatches.equals(SwtConstants.NO)) {
					noIncludedMovementMatches = SwtConstants.YES;
				}
			}
			request.setAttribute("noIncludedMovementMatches",
					noIncludedMovementMatches);
			// set the noIncludedMovementMatchesInd in matchQueue
			matchQueue.setNoIncludedMovementMatches(noIncludedMovementMatches);
			// get the status from matchQueue
			if (status == null) {
				status = matchQueue.getStatus();
				if (status == null) {
					status = (String) request.getSession().getAttribute(
							"status");
				}
			}
			// set the CurrencyCode for matchQueue
			matchQueue.setCurrencyCode("All");
			// set the currGrpId based on the currency group access
			currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);
			if (currGrpAccess == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				currGrpId = "All";
			}
			// set the currency group code in matchQueue
			matchQueue.setCurrencyGrpCode(currGrpId);
			// put the currency group list in request
			putCurrencyGroupListInReq(request, hostId, entityId);
			// get the selectedTabIndex from request and set into attribute
			selectedTabIndex = request.getParameter("selectedTabIndex");
			if (selectedTabIndex == null) {
				selectedTabIndex = "2";
			}
			request.setAttribute("selectedTabIndex", selectedTabIndex);
			// To get the current system format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			/* Puts the today date in request object */
			putTodayDateInReq(request);
			// Get the datePlusOneAsString value
			// Get the todayDateAsString
			// get the system date.
			todayDateAsString = SwtUtil.getSysDateWithFmt(systemDBDate);
			// Get the datePlusOneAsString value
			datePlusOneAsString = SwtUtil.getDBSysDatewithoutTimeAsString(1,
					systemDBDate);
			// Get the todayDatePlusTwoAsString value
			todayDatePlusTwoAsString = SwtUtil.getDBSysDatewithoutTimeAsString(
					2, systemDBDate);
			// Get the todayDatePlusThreeAsString value
			todayDatePlusThreeAsString = SwtUtil
					.getDBSysDatewithoutTimeAsString(3, systemDBDate);
			// Get the todayDatePlusFourAsString value
			todayDatePlusFourAsString = SwtUtil
					.getDBSysDatewithoutTimeAsString(4, systemDBDate);
			// Get the todayDatePlusFiveAsString value
			todayDatePlusFiveAsString = SwtUtil
					.getDBSysDatewithoutTimeAsString(5, systemDBDate);
			// Get the todayDatePlusSixAsString value
			todayDatePlusSixAsString = SwtUtil.getDBSysDatewithoutTimeAsString(
					6, systemDBDate);
			// set value the dateOnLoad & testDateFormatted
			dateOnLoad = SwtUtil.parseDate(dbDate, format.getDateFormatValue());
			// get formated date from swtutil
			testDateFormatted = SwtUtil.parseDate(todayDateAsString, format
					.getDateFormatValue());
			// Get the Tabs label
			/*
			 * Getting the selectedValueDate from request. If it is not equal to
			 * null and greater than zero ,then it is assigned that value .
			 * otherwise it sets the null value.
			 */
			searchDateAsString = ((request.getParameter("selectedValueDate") != null) && (request
																								  .getParameter("selectedValueDate").trim().length() > 0)) ? request
					.getParameter("selectedValueDate")
					: null;
			// Start:Code modified by Vivekanandan A on 17/07/2012 for
			// mantis1991
			// checks the existing entity id is not empty and not null
			if (!SwtUtil
					.isEmptyOrNull(request.getParameter("existingEntityId"))
				&& !request.getParameter("existingEntityId").equals(
					entityId)) {
				// set the date flag as false
				dateFlag = false;
				// assigning the search date to today date
				searchDateAsString = todayDateAsString;
				// set the selected tab index as one in request
				request.setAttribute("selectedTabIndex", "1");
				// set the selected tab name in request
				request
						.setAttribute("selectedTabName",
								"MatchQueueTodayParent");
				/* Sets the searchDateAsString as valueDateAsString */
				matchQueue.setValueDateAsString(todayDateAsString);
			}
			request.setAttribute("existingEntityId", entityId);
			// End:Code modified by Vivekanandan A on 17/07/2012 for mantis 1991
			// connection
			// exhaust issue
			// Set the ValueDateAsString to matchQueue
			matchQueue.setValueDateAsString(SwtUtil
					.getSysDateWithFmt(systemDBDate));
			/*
			 * If the selected value date as String is not equal to null and
			 * greater than zero, it will compare the dates and sets the values
			 * in request object.
			 */
			if ((searchDateAsString != null)
				&& (searchDateAsString.trim().length() > 0)) {
				/*
				 * If the selected index is 8, then sets the value 8 in
				 * selectedTabIndex request object and MatchQueueAllParent in
				 * tab name request object.
				 */
				if (selectedTabIndex.equals("8")) {
					/*
					 * Start: Code Modified by Vivekanandan A for mantis 1991 on
					 * 23-07-2012
					 */
					// Condition to check entity is changed to not select all
					// tab
					if (dateFlag) {
						request.setAttribute("selectedTabIndex", "8");
						request.setAttribute("selectedTabName",
								"MatchQueueAllParent");
						/* Sets the todayDate as valueDateAsString */
						matchQueue.setValueDateAsString(todayDateAsString);
					}
					/*
					 * End: Code Modified by Vivekanandan A for mantis 1991 on
					 * 23-07-2012
					 */
				}
				/*
				 * If the selected value date as String is equal to todayDate,
				 * then sets the value 1 in selectedTabIndex request object and
				 * MatchQueueTodayParent in tab name request object.
				 */
				else if (searchDateAsString.equals(todayDateAsString)) {
					request.setAttribute("selectedTabIndex", "1");
					request.setAttribute("selectedTabName",
							"MatchQueueTodayParent");
					/* Sets the todayDate as valueDateAsString */
					matchQueue.setValueDateAsString(todayDateAsString);
				}
				/*
				 * If the selected value date as String is equal to
				 * MatchQueueTodayPlusOneParent, then sets the value 2 in
				 * selectedTabIndex request object and todayPlusOne in
				 * selectedtabname request object.
				 */
				else if (searchDateAsString.equals(datePlusOneAsString)) {
					request.setAttribute("selectedTabIndex", "2");
					request.setAttribute("selectedTabName",
							"MatchQueueTodayPlusOneParent");
					/* Sets the todayDatePlusOne as valueDateAsString */
					matchQueue.setValueDateAsString(datePlusOneAsString);
				}
				/*
				 * If the selected value date as String is equal to
				 * MatchQueueTodayPlusTwoParent, then sets the value 3 in
				 * selectedTabIndex request object and todayPlusTwo in
				 * selectedtabname request object.
				 */
				else if (searchDateAsString.equals(todayDatePlusTwoAsString)) {
					request.setAttribute("selectedTabIndex", "3");
					request.setAttribute("selectedTabName",
							"MatchQueueTodayPlusTwoParent");
					/* Sets the todayDatePlusTwo as valueDateAsString */
					matchQueue.setValueDateAsString(todayDatePlusTwoAsString);

				}
				/*
				 * If the selected value date as String is equal to
				 * MatchQueueTodayPlusThreeParent, then sets the value 4 in
				 * selectedTabIndex request object and todayPlusThree in
				 * selectedtabname request object.
				 */
				else if (searchDateAsString.equals(todayDatePlusThreeAsString)) {
					request.setAttribute("selectedTabIndex", "4");
					request.setAttribute("selectedTabName",
							"MatchQueueTodayPlusThreeParent");
					/* Sets the todayDatePlusTwo as valueDateAsString */
					matchQueue.setValueDateAsString(todayDatePlusThreeAsString);
				}
				/*
				 * If the selected value date as String is equal to
				 * MatchQueueTodayPlusFourParent, then sets the value 5 in
				 * selectedTabIndex request object and todayPlusFour in
				 * selectedtabname request object.
				 */
				else if (searchDateAsString.equals(todayDatePlusFourAsString)) {
					request.setAttribute("selectedTabIndex", "5");
					request.setAttribute("selectedTabName",
							"MatchQueueTodayPlusFourParent");
					/* Sets the todayDatePlusTwo as valueDateAsString */
					matchQueue.setValueDateAsString(todayDatePlusFourAsString);
				}
				/*
				 * If the selected value date as String is equal to
				 * MatchQueueTodayPlusFiveParent, then sets the value 6 in
				 * selectedTabIndex request object and todayPlusFive in
				 * selectedtabname request object.
				 */
				else if (searchDateAsString.equals(todayDatePlusFiveAsString)) {
					request.setAttribute("selectedTabIndex", "6");
					request.setAttribute("selectedTabName",
							"MatchQueueTodayPlusFiveParent");
					/* Sets the todayDatePlusTwo as valueDateAsString */
					matchQueue.setValueDateAsString(todayDatePlusFiveAsString);
				}
				/*
				 * If the selected value date as String is equal to
				 * MatchQueueTodayPlusSixParent, then sets the value 7 in
				 * selectedTabIndex request object and todayPlusSix in
				 * selectedtabname request object.
				 */
				else if (searchDateAsString.equals(todayDatePlusSixAsString)) {
					request.setAttribute("selectedTabIndex", "7");
					request.setAttribute("selectedTabName",
							"MatchQueueTodayPlusSixParent");
					/* Sets the todayDatePlusTwo as valueDateAsString */
					matchQueue.setValueDateAsString(todayDatePlusSixAsString);

				}
				/*
				 * If the selected value date as String is equal to Selected,
				 * then sets the value 9 or 1 in selectedTabIndex request object
				 * and today or Selected in selectedtabname request object.
				 */
				else {
					Calendar cal = Calendar.getInstance();
					cal.setTime(dateOnLoad);
					cal.add(Calendar.DATE, 7);
					if (dbDate.equals(todayDateAsString)
						|| (selectedTabIndex.equalsIgnoreCase("9") && (dateOnLoad
																			   .before(testDateFormatted) && cal.getTime()
																			   .after(testDateFormatted)))) {
						request.setAttribute("selectedTabIndex", "9");
						request.setAttribute("selectedTabName",
								"MatchQueueSelectedParent");
						/* Sets the searchDateAsString as valueDateAsString */
						matchQueue.setValueDateAsString(searchDateAsString);
					} else {
						dateFlag = false;
						request.setAttribute("selectedTabIndex", "1");
						request.setAttribute("selectedTabName",
								"MatchQueueTodayParent");
						/* Sets the searchDateAsString as valueDateAsString */
						matchQueue.setValueDateAsString(todayDateAsString);
					}
				}
				// Get the selectedTabIndex from request
				selectedTabIndex = request.getAttribute("selectedTabIndex")
						.toString();
			} else {
				/*
				 * This method is used to set the Tab Information in request
				 * object
				 */
				bindTabInfoInRequest(request);
				/* Getting the selectedTabIndex value from request Object */
				selectedTabIndex = request.getAttribute("selectedTabIndex")
						.toString();
				matchQueue.setValueDateAsString(todayDateAsString);
			}
			// set the value date from matchQueue
			valueDate = SwtUtil.parseDate(matchQueue.getValueDateAsString(),
					format.getDateFormatValue());
			// Instantiate the matchListDetailsAll
			matchListDetailsAll = new ArrayList();
			// get the match screen data
			matchDetailsVO = matchManager.getMatchScreenData(entityId, hostId,
					currGrpId, status, roleId, selectedTabIndex,
					applyCurrencyThreshold, noIncludedMovementMatches,
					valueDate);
			// To get the tabflag value for bean object
			tabFlag = matchDetailsVO.getTabFlag();
			// To set the tab label
			SwtUtil.getTabsLabel(request, entityId, hostId, currencyCode,
					sysFormat, true, tabFlag);
			// get the match details list
			matchListDetailsAll = (ArrayList) matchDetailsVO
					.getMatchListDetailsForSelectedTab();
			// set the attribute for matchQualityDetailsToday in request
			request.setAttribute("matchQualityDetailsToday",
					matchListDetailsAll);
			// put entity list in request
			putEntityListInReq(request);
			// get the menu entity currency group access
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			// set the attribute for lastRefTime in request
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			setMatch(match);
			setMatchQueue(matchQueue);
			log.debug(this.getClass().getName() + "-[display]- Exit");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in MatchAction.display method: "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MatchAction.display method: "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", MatchAction.class), request, "");
			return getView("fail");
		} finally {
			// set the selected tab name and index in request
			if (request.getParameter("selectedTabIndex") != null
				&& request.getParameter("selectedTabIndex").equals("9")
				&& dateFlag) {
				bindTabInfoInRequest(request);
			}
			/* null the objects created already. */
			hostId = null;
			entityId = null;
			currGrpId = null;
			roleId = null;
			status = null;
			applyCurrencyThreshold = null;
			noIncludedMovementMatches = null;
			noIncludedMovementMatchesInd = null;
			currencyCode = null;
			sysFormat = null;
			searchDateAsString = null;
			format = null;
			valueDate = null;
			matchQueue = null;
			match = null;
			tabFlag = null;
			dbDate = null;
			applyCurrencyThresholdInd = null;
			selectedTabIndex = null;
			todayDateAsString = null;
			datePlusOneAsString = null;
			todayDatePlusTwoAsString = null;
			todayDatePlusThreeAsString = null;
			todayDatePlusFourAsString = null;
			todayDatePlusFiveAsString = null;
			todayDatePlusSixAsString = null;
			dateOnLoad = null;
			testDateFormatted = null;
			matchDetailsVO = null;
			matchListDetailsAll = null;
			systemDBDate = null;
		}
	}

	/**
	 * This method is used to put today date into request object.
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putTodayDateInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				  + "- [putTodayDateInReq] - Entering ");
		/* Date variable to hold the test date */
		Date testDate = null;
		/* String variable to hold the today date */
		String today = null;
		/* START: Code change for date range restriction */
		/* Returns the test date used by the system */
		testDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
		/* Used to concatenate the day,month and year */
		today = testDate.getDate() + "/" + (testDate.getMonth() + 1) + "/"
				+ (testDate.getYear() + 1900);
		/* Sets the today date in request object */
		request.setAttribute("today", today);
		log.debug(this.getClass().getName()
				  + "- [putTodayDateInReq] - Exiting ");
	}

	/**
	 * This method is used to set the TAB attribute of screen into request.
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void bindTabInfoInRequest(HttpServletRequest request)
			throws SwtException {
		String selectedTabIndex = request.getParameter("selectedTabIndex");
		String selectedTabName = request.getParameter("selectedTabName");
		if (SwtUtil.isEmptyOrNull(selectedTabIndex)) {
			request.setAttribute("selectedTabIndex", "1");
			request.setAttribute("selectedTabName", "MatchQueueTodayParent");
		} else {
			request.setAttribute("selectedTabIndex", selectedTabIndex);
			request.setAttribute("selectedTabName", selectedTabName);
		}
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	private String putCurrencyGroupListInReq(HttpServletRequest request,
											 String hostId, String entityId) throws SwtException {
		log.debug("Inside MatchAction.putCurrencyGroupListInReq method");

		String defaultCurrencyGroup = new String();

		Collection currencyGroupList = new ArrayList();

		CurrencyGroup currencyGroup = new CurrencyGroup();

		String roleId = SwtUtil.getCurrentUser(request.getSession())
				.getRoleId();

		Collection groupList = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupViewORFullAcess(roleId, entityId);
		// to be deleted later--starts

		Iterator fullAccessItr = groupList.iterator();

		// to be deleted later--ends
		Iterator itGroupList = groupList.iterator();
		currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));
		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();
			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}
		request.setAttribute("currencyGroupList", currencyGroupList);

		if ((currencyGroupList != null) && (currencyGroupList.size() > 1)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(1);

			defaultCurrencyGroup = labelValueBean.getValue();
		}

		return defaultCurrencyGroup;
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering MatchAction.'putEntityListInReq' method");

		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);

		log.debug("exiting MatchAction.'putEntityListInReq' method");
	}

}
