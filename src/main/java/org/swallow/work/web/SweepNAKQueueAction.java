package org.swallow.work.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.SweepNAKQueue;
import org.swallow.work.service.SweepNAKQueueManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionErrors;


/**
 * This class is used for menu option work > sweep > Exception.
 *
 * <AUTHOR> has rewritten this class on 31/07/2007
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/sweepNAKqueue", "/sweepNAKqueue.do"})
public class SweepNAKQueueAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("messageSummary", "jsp/work/NAKmessagedisplay");
		viewMap.put("success", "jsp/work/sweepNAKqueue");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "displayMessageQueue":
				return displayMessageQueue();
			case "showMessages":
				return showMessages();
		}


		return unspecified();
	}


	private SweepNAKQueue sweepNAKQueue;
	public SweepNAKQueue getSweepNAKQueue() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		sweepNAKQueue = RequestObjectMapper.getObjectFromRequest(SweepNAKQueue.class, request);
		return sweepNAKQueue;
	}

	public void setSweepNAKQueue(SweepNAKQueue sweepNAKQueue) {
		this.sweepNAKQueue = sweepNAKQueue;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("sweepNAKQueue", sweepNAKQueue);
	}

	/**
	 * Manager class object provided by Struts for this action class.
	 */
	@Autowired
	private SweepNAKQueueManager sweepNAKQueueManager = null;

	/**
	 * Initializing logger object for logging details into server.log
	 */
	private final Log log = LogFactory.getLog(SweepNAKQueueAction.class);

	/**
	 * Final type string used to initialize various string object
	 */
	private final String EMPTY_STRING = "";

	/*
	 * Initializing Manager class object.
	 */
	public void setSweepNAKQueueManager(
			SweepNAKQueueManager sweepNAKQueueManager) {
		this.sweepNAKQueueManager = sweepNAKQueueManager;
	}

	/**
	 * Default method implementation as per struts setting.
	 *
	 * @return actionForword
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		return displayMessageQueue();
	}

	/**
	 * Private method used to put entity list into request.
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);
	}

	/**
	 * Private method used to put currency list into request.
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	private String putCcyGroupDetailsInReuest(HttpServletRequest request,
											  String entityId) throws SwtException {
		ArrayList currrencyGroupList = new ArrayList();
		User user = SwtUtil.getCurrentUser(request.getSession());
		String roleId = user.getRoleId();
		String firstCurrGroupId = "All";
		currrencyGroupList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupFullORViewAcessLVL(roleId, entityId);

		if ((currrencyGroupList != null) && (currrencyGroupList.size() != 0)) {
			currrencyGroupList.add(0, new LabelValueBean("All", "All"));
		}

		request.setAttribute("currencyGroupList", currrencyGroupList);

		return firstCurrGroupId;
	}

	/**
	 * This method is used to display the Message Queue.
	 *
	 * @return
	 */
	public String displayMessageQueue() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		ActionErrors errors = new ActionErrors();
		// DynaValidatorForm SweepNAKQueue sweepNAKQueue = null;
		String reqChangedValue = EMPTY_STRING;
		Collection sweepNAKQueueDetails = new ArrayList();
		String entityId = null;
		String currGrp = null;
		String roleId = null;
		String hostId = null;
		String currencyCode = null;
		/* Variable Declaration for calledFrom */
		String calledFrom = null;
		try {
			log.debug(this.getClass().getName() + " - [displayMessageQueue()] - "+ "Entry");
			sweepNAKQueue = (SweepNAKQueue) getSweepNAKQueue();
			reqChangedValue = request.getParameter("changedValue");

			entityId = sweepNAKQueue.getEntityId();
			calledFrom = request.getParameter("calledFrom");
			if(calledFrom!=null && calledFrom.equals("generic")){
				entityId = request.getParameter("entityId") != null ? request
						.getParameter("entityId") : "All";
				currencyCode = request.getParameter("currencyId") != null ? request
						.getParameter("currencyId") : "All";
				currGrp =  SwtUtil.getCurrencyGroup(entityId,currencyCode);
			}else {
				currGrp = sweepNAKQueue.getCurrGrp();
			}
			User user = SwtUtil.getCurrentUser(request.getSession());
			roleId = user.getRoleId();
			// Modified for mantis 1443

			if (!SwtUtil.isEmptyOrNull(request.getParameter("hostId")))
				hostId = request.getParameter("hostId");
			else
				hostId = CacheManager.getInstance().getHostId();

			// Coming from workflow Monitor
			if (entityId == null) {
				entityId = request.getParameter("entityId");
			}

			// Coming from workflow Monitor
			if (currGrp == null) {
				currGrp = request.getParameter("currCode");
			}

			// Handling if entityId is still missing
			if (!((entityId != null) && (entityId.trim().length() > 0))) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			// Handling if currencyGroupId is still missing
			if (!((currGrp != null) && (currGrp.trim().length() > 0))) {
				currGrp = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getCurrentCcyGrpId();
			}

			/*
			 * TODO : Check this logic wheather we really need itor not.
			 */
			String firstCurrGrp = putCcyGroupDetailsInReuest(request, entityId);

			if ((reqChangedValue != null) && reqChangedValue.equals("entity")) {
				currGrp = firstCurrGrp;
			}
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show
			 * 'Last refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			/* Populating the data for JSP */
			sweepNAKQueue.setEntityId(entityId);
			sweepNAKQueue.setCurrGrp(currGrp);
			sweepNAKQueueDetails = sweepNAKQueueManager.getNAKQueueDetails(
					hostId, entityId, currGrp, roleId, new Date());
			request.setAttribute("sweepNAKQueueDetails", sweepNAKQueueDetails);

			putEntityListInReq(request);
			setSweepNAKQueue(sweepNAKQueue);
			log.debug(this.getClass().getName() + " - [displayMessageQueue()] - "+ "Exit");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepNAKQueueAction.'displayMessageQueue' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepNAKQueueAction.'displayMessageQueue' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayMessageQueue", SweepNAKQueueAction.class),
					request, "");

			return getView("fail");
		} finally {
			entityId = null;
			currGrp = null;
			roleId = null;
			hostId = null;
		}

		return getView("success");
	}

	/**
	 *
	 * @return
	 */
	public String showMessages()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionErrors errors = new ActionErrors();
		SystemFormats format = null;

		try {
			String entityId = request.getParameter("entityId");
			String currCode = request.getParameter("currencyCode");
			String linkSource = request.getParameter("linkSource");
			format = SwtUtil.getCurrentSystemFormats(request.getSession());

			List collMsgDetails = sweepNAKQueueManager.getMessageDetails(
					entityId, currCode, format, linkSource);
			request.setAttribute("NAKMessageColl", collMsgDetails);
		} catch (SwtException swtexp) {
			log
					.debug("SwtException Catch in SweepNAKQueueAction.'showMessages' method : "
							+ swtexp.getMessage());
			swtexp.printStackTrace();
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("fail");
		} catch (Exception exp) {
			log
					.debug("Exception Catch in SweepNAKQueueAction.'showMessages' method : "
							+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "showMessages", SweepNAKQueueAction.class), request,
					"");

			return getView("fail");
		}

		return getView("messageSummary");
	}
}
