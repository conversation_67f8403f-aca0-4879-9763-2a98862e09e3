/*
 * @(#)WorkflowMonitorAction.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.json.JSONObject;
import org.swallow.config.springMVC.BaseController;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.OracleTimeDTO;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.service.WorkflowMonitorManager;
import org.swallow.work.web.form.MonitorTabBean;
import org.swallow.work.web.form.WorkflowMonitorBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * This is the action class which caters to requests made from front end
 *
 * <AUTHOR> Tripathi
 * @version 03 Jan 2008
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/workflowmonitor", "/workflowmonitor.do"})
public class WorkflowMonitorAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/work/workflowmonitordata");
		viewMap.put("dataerror", "jsp/work/workflowmonitordataerror");
		viewMap.put("success", "jsp/work/workFlowMonitor");
		viewMap.put("flex", "jsp/work/workflowmonitorflex");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "flex":
				return flex();
			case "data":
				return data();
			case "unspecified":
				return unspecified();
			case "saveColumnWidth":
				return saveColumnWidth();
			case "saveColumnOrder":
				return saveColumnOrder();
			case "saveColumnHidden":
				return saveColumnHidden();
			case "saveUserSettings":
				return saveUserSettings();
		}


		return flex();
	}


	/**
	 * WorkflowMonitorManager Object
	 */
	@Autowired
	private WorkflowMonitorManager workflowMonitorManager = null;
	// Added for mantis 1443
	/**
	 * Initializing menuItemId.
	 */
	private final String menuItemId = "" + SwtConstants.SCREEN_WORKFLOWMONITOR;
	/**
	 * Log object
	 */
	private static final Log log = LogFactory
			.getLog(WorkflowMonitorAction.class);

	/**
	 * Setter method for workflow manager
	 *
	 * @param workflowMonitorManager
	 */
	public void setWorkflowMonitorManager(
			WorkflowMonitorManager workflowMonitorManager) {
		this.workflowMonitorManager = workflowMonitorManager;
	}

	public String flex()
			throws SwtException {
		// variable to hold host Id
		String hostId = null;
		// variable to hold role Id
		String roleId = null;
		// variable to hold user Id
		String userId = null;
		// variable to hold default Entity Id
		String defaultEntityId = null;
		// variable to hold default currency group id
		String defaultCcyGrpId = null;
		// variable to hold system date
		Date sysDate = null;
		// variable to hold system date as string
		String sysDateAsString = null;
		// variable to hold screen id
		String screenId = SwtConstants.WORKFLOW_MONITOR_ID;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {
			log.debug(this.getClass().getName() + "- [flex] - starting ");
			// get the host id from swtutil
			hostId = SwtUtil.getCurrentHostId();
			// get the role id from common data manager
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the current user id from swtutil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the default entity id from swtutil
			defaultEntityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			// get the default currency group id from swtutil
			defaultCcyGrpId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();
			// get the system date from swtutil
			sysDate = SwtUtil.getSystemDatewithoutTime();
			// get the formatted system date form swtutil
			sysDateAsString = SwtUtil.formatDate(sysDate, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			/* Used to get the instance from Configuration file */
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Initialising the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// Setting the rate in request
			request.setAttribute("autoRefreshRate", new Integer(screenOption
					.getPropertyValue()));
			// Setting the hostId in request
			request.setAttribute("hostId", hostId);
			// Setting the default entity id in request
			request.setAttribute("defaultEntityId", defaultEntityId);
			// Setting the default currency group id in request
			request.setAttribute("defaultCcyGrpId", defaultCcyGrpId);
			// Setting the role id in request
			request.setAttribute("roleId", roleId);
			// Setting the user id in request
			request.setAttribute("userId", userId);
			// Setting the item id in request
			request.setAttribute("itemId", SwtConstants.WORKFLOW_MONITOR_ID);
			// Setting the system date as string in request
			request.setAttribute("sysDateAsString", sysDateAsString);
			// Start: Code modified for Mantis 1991 by Vivekanandan A on
			// 19-07-2012
			// Setting the existing Entit Id in request
			request.setAttribute("existingEntityId", defaultEntityId);
			// End: Code modified for Mantis 1991 by Vivekanandan A on
			// 19-07-2012
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					defaultEntityId, defaultCcyGrpId);
			log.debug(this.getClass().getName() + "- [flex] - Exiting ");
		} catch (Exception exception) {
			log.error("Exception caught in" + this.getClass().getName()
					  + "[flex] " + exception.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(exception,
					"flex", MetagroupMonitorAction.class);
		}
		return getView("flex");
	}

	/**
	 * Method to get Work flow Monitor Data
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String data()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Variable Declaration for opTimer */
		OpTimer opTimer = null;
		/* Variable Declaration for userId */
		String userId = null;
		/* Variable Declaration for screenId */
		String screenId = null;
		/* Variable Declaration for entityId */
		String entityId = null;
		/* Variable Declaration for currencyGrpId */
		String currencyGrpId = null;
		/* Variable Declaration for roleId */
		String roleId = null;
		/* Variable Declaration for selectedDate */
		Date selectedDate = null;
		/* Variable Declaration for selectedDateStr */
		String selectedDateStr = null;
		/* Variable Declaration for selectedCategory */
		String selectedCategory = null;
		/* Variable Declaration for applyCurrencyThreshold */
		String applyCurrencyThreshold = null;
		/* Variable Declaration for screenOptionManager */
		ScreenOptionManager screenOptionManager = null;
		/* Variable Declaration for screenOption */
		ScreenOption screenOption = null;
		/* Variable Declaration for workflowDetails */
		WorkflowMonitorBean workflowDetails = null;
		/* Variable Declaration for workFlowMgr */
		WorkflowMonitorManager workFlowMgr = null;
		/* Variable Declaration for entitys list */
		Collection<LabelValueBean> entitysLblValue = null;
		// Collection of user entity access list
		Collection collUserEntityAccess = null;
		// Collection of label value of entity access
		Collection<LabelValueBean> entityAccessLvlColl = null;
		// Collection of label value of currency group
		Collection<LabelValueBean> currrencyGroupList = null;
		// Collection of label value of currency group access
		Collection<LabelValueBean> currGroupAccessColl = null;
		// OracleTimeDTO instance
		OracleTimeDTO oraSystemTime = null;
		/* Variable Declaration for entityTimeOffset */
		String entityTimeOffset = null;
		/* Variable Declaration for isBusinessDay */
		boolean isBusinessDay = false;
		// To hold the tab list
		ArrayList<MonitorTabBean> tabList = null;
		// To hold the tab category list
		ArrayList<MonitorTabBean> tabCategoryNamesList = null;
		// Get the calendar calendar
		Calendar systemDateCal = null;
		// index for looping
		int i = 1;
		// index for buisness days
		int j = 1;
		// Flag to contorl index
		boolean indexFlag = true;
		// Variable to hold Tab Flag
		String tabFlag = null;
		// To hold the system date
		Date systemDBDate = null;
		String propertyValue = null;
		String isClosed=null;
		String isThresholdToBeApplied=null;
		String hostId = null;
		ScreenOption WorkflowScreenOption = null;
		String dateFormat = null;
		String currencyFormat = null;
		try {
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			log.debug(this.getClass().getName() + "-[data() ]-Entry");
			// Instantiate the OpTimer
			opTimer = new OpTimer();
			opTimer.start("all");
			/* get current userId from session. */
			userId = SwtUtil.getCurrentUserId(request.getSession());
			/* get screenId. */
			screenId = SwtConstants.WORKFLOW_MONITOR_ID;
			/* get entityId from request. */
			entityId = request.getParameter("entityid");
			/* get currencyGrpId from request. */
			currencyGrpId = request.getParameter("currencygrpid");
			/* get roleId from request. */
			roleId = request.getParameter("roleid");
			/* get selectedDateStr from request. */
			selectedDateStr = request.getParameter("selectedDate");
			selectedCategory = request.getParameter("selectedCategory");
			currencyFormat= SwtUtil.getCurrentCurrencyFormat(request.getSession());

			/* get applyCurrencyThreshold from request. */
			applyCurrencyThreshold = request
					.getParameter("applycurrencythreshold");

			// get the divider status from the database if it is closed or opened
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			WorkflowScreenOption = new ScreenOption();
			/* Used to get the instance from Configuration file */
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			// check if user has already workflowt setting or not
			propertyValue = screenOptionManager.getPropertyValue(hostId, userId, SwtConstants.WORKFLOW_MONITOR_ID,
					SwtConstants.WORKFLOW_DIVIDER_OPTION);
			if (!SwtUtil.isEmptyOrNull(propertyValue) && !propertyValue.equals("null")) {

				JSONObject prefJSON = new JSONObject(propertyValue);
				isClosed = (String) prefJSON.get("ISCLOSED");
			} else {
				isClosed = "false";
			}

			propertyValue = screenOptionManager.getPropertyValue(hostId, userId, SwtConstants.WORKFLOW_MONITOR_ID,
					SwtConstants.WORKFLOW_APPLY_THRESHOLD_OPTION);
			if (!SwtUtil.isEmptyOrNull(propertyValue)) {
				isThresholdToBeApplied = propertyValue;
			}else {
				isThresholdToBeApplied = "E";
			}
			/* check whether entityId is null and set entityid from session. */
			if (entityId == null)
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			// Initialising the ScreenOption instance
			screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// Setting the rate in request
			request.setAttribute("autoRefreshRate", new Integer(screenOption
					.getPropertyValue()));
			// get the current role id and set into request
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			request.setAttribute("roleId", roleId);
			// set the currencyGrpId to All if null
			if (currencyGrpId == null) {
				currencyGrpId = "All";
			}

			// Start: Code modified for Mantis 1991 by Vivekanandan A on
			// 19-07-2012
			// Modified for mantis 1443
			// set the selected date based on condition check
			if (SwtUtil.isEmptyOrNull(selectedDateStr)) {
				// get the selected date from swtutil
				selectedDate = SwtUtil.getSysParamDateWithEntityOffset(entityId
						.equalsIgnoreCase(SwtConstants.ALL_VALUE) ? SwtUtil
						.getUserCurrentEntity(request.getSession()) : entityId);
			} else {
				selectedDate = SwtUtil.parseDate(selectedDateStr, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}
			// checks the existing entity id not null
			if (!SwtUtil
					.isEmptyOrNull(request.getParameter("existingEntityId"))
				&& !request.getParameter("existingEntityId").equals(
					entityId)) {
				// get the selected date from util
				selectedDate = SwtUtil.getSysParamDateWithEntityOffset(entityId
						.equalsIgnoreCase(SwtConstants.ALL_VALUE) ? SwtUtil
						.getUserCurrentEntity(request.getSession()) : entityId);
				// assign the selected date as string as null
				selectedDateStr = null;
			}

			request.setAttribute("existingEntityId", entityId);
			// End: Code modified for Mantis 1991 by Vivekanandan A on
			// 19-07-2012
			// set the applyCurrencyThreshold to Y if null
			if (applyCurrencyThreshold == null) {
				if("E".equalsIgnoreCase(isThresholdToBeApplied)) {
					applyCurrencyThreshold = SwtConstants.YES;
				}else {
					applyCurrencyThreshold = SwtConstants.NO;
				}
			}
			// Instantiate the WorkflowMonitorBean
			workflowDetails = new WorkflowMonitorBean();
			// get the WorkflowMonitorManager from bean
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			// get the workflowmonitor details
			workflowDetails = workFlowMgr.getWorkflowMonitorDetails(entityId,
					currencyGrpId, roleId, selectedDate,
					applyCurrencyThreshold, userId);
			// set the entity id and currency group id in workflowdetails bean
			workflowDetails.setSelectedEntity(entityId);
			workflowDetails.setSelectedCurr(currencyGrpId);
			// set attribute for workbean,applyCurrencyThreshold in request
			request.setAttribute("workbean", workflowDetails);
			request.setAttribute("applyCurrencyThreshold",
					applyCurrencyThreshold);
			// Instantiate the LabelValueBean
			entitysLblValue = new ArrayList<LabelValueBean>();
			// get the collection of user entity access
			collUserEntityAccess = SwtUtil
					.getUserEntityAccessListForWorkFlow(roleId);
			// get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVL(
					collUserEntityAccess, null);
			// get the WorkflowMonitorManager from bean
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			// set the label value bean of entity
			if (workFlowMgr.getAllEntityOption(roleId)) {
				entitysLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL,
						SwtConstants.ALL_VALUE));
				entitysLblValue.addAll(entityAccessLvlColl);
			} else {
				entitysLblValue = entityAccessLvlColl;
			}
			// set attribute for entity label value
			request.setAttribute("entitys", entitysLblValue);
			// Instantiate the currrencyGroupList
			currrencyGroupList = new ArrayList<LabelValueBean>();
			// add the all label into currrencyGroupList
			currrencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
			// get the collection of currency group access
			currGroupAccessColl = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupFullORViewAcessLVL(roleId, entityId);
			if (collUserEntityAccess != null) {
				currrencyGroupList.addAll(currGroupAccessColl);
			}
			// set attribute for crrgrp,reply_status_ok,reply_message in request
			request.setAttribute("crrgrp", currrencyGroupList);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch ok");
			// get the oracle system time
			oraSystemTime = workFlowMgr.getOracleSystemTime();
			// set attribute for serverTime,entityOffset,opTimes in request
			request.setAttribute("serverTime", oraSystemTime);
			entityTimeOffset = workFlowMgr.getEntityOffset(entityId);
			request.setAttribute("entityOffset", entityTimeOffset);
			opTimer.stop("all");
			request.setAttribute("opTimes", opTimer.getOpTimes());
			// set false for business day
			isBusinessDay = false;
			// get the system date
			// Start : Method modified by Balaji for Mantis 1991
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(entityId
					.equalsIgnoreCase(SwtConstants.ALL_VALUE) ? SwtUtil
					.getUserCurrentEntity(request.getSession()) : entityId);
			// End : Method modified by Balaji for Mantis 1991
			// Instantiate the array list
			tabList = new ArrayList<MonitorTabBean>();
			// Instantiate the array list
			tabCategoryNamesList = new ArrayList<MonitorTabBean>();
			// Get the calendar instance
			systemDateCal = Calendar.getInstance();
			// Set the time for calendar
			systemDateCal.setTime(SwtUtil.getDBSysDatewithTime(systemDBDate));
			// Get the Tab Flag value form bean object
			tabFlag = workflowDetails.getTabFlag();
			for (i = 1; i <= 5; i++) {
				// Instantiate MonitorTabBean
				MonitorTabBean acctMonitor = new MonitorTabBean();
				// set the tabDate as String
				acctMonitor.setTabDateAsString(SwtUtil.formatDate(systemDateCal
						.getTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue()));
				if (!SwtUtil.isEmptyOrNull(selectedDateStr)
					&& acctMonitor.getTabDateAsString().equals(
						selectedDateStr) && indexFlag) {
					indexFlag = false;
					request.setAttribute("selectedIndex", i - 1);
					request.setAttribute("selectedTabIndex", j - 1);
				} else {
					if (indexFlag)
					{
						request.setAttribute("selectedIndex", "0");
						request.setAttribute("selectedTabIndex", "0");
					}
				}

				// To check the tab flag is normal day or weekend
				if (!SwtUtil.isEmptyOrNull(tabFlag)
					&& tabFlag.charAt(i - 1) == 'N')
					isBusinessDay = true;
				else
					isBusinessDay = false;

				// set the BusinessDay
				if (isBusinessDay) {
					acctMonitor.setBusinessDay(SwtConstants.STR_TRUE);
				} else {
					acctMonitor.setBusinessDay("false");
				}
				// increment the calendar date
				systemDateCal.add(Calendar.DATE, 1);
				// add to list only Business Day

				tabList.add(acctMonitor);
				//increment Business Day counter
				j++;
			}


			String tabName1 = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB1_NAME);
			String tabName2 = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB2_NAME);

			int k = 1;
			for (int h = 1; h <= 2; h++) {
				// Instantiate MonitorTabBean
				MonitorTabBean acctMonitor = new MonitorTabBean();
				// set the tabDate as String
				if(h==1) {
					acctMonitor.setTabNameAsString(tabName1);
					acctMonitor.setCount(0);
					acctMonitor.setTabName(tabName1);
				}else {
					acctMonitor.setTabNameAsString(tabName2);
					acctMonitor.setTabName(tabName2);
					acctMonitor.setCount(0);

				}
				acctMonitor.setTabId(h);

				if (!SwtUtil.isEmptyOrNull(selectedCategory)
					&& acctMonitor.getTabName().equals(
						selectedCategory)) {
					request.setAttribute("selectedIndexCategory", h - 1);
					request.setAttribute("selectedTabIndexCategory", k - 1);
				} else {
					request.setAttribute("selectedIndexCategory", "0");
					request.setAttribute("selectedTabIndexCategory", "0");
				}

				tabCategoryNamesList.add(acctMonitor);
				//increment Business Day counter
				k++;
			}
			/* Set Last Reference Time in request. */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));

			request.setAttribute("displayedDate", (SwtUtil.formatDate(SwtUtil
					.getSystemDatewithoutTime(), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue())));
			request.setAttribute("dateFormat", dateFormat);
			request.setAttribute("currencyFormat", currencyFormat);
			// set the list to request
			request.setAttribute("tabDetails", tabList);
			request.setAttribute("tabCategoryDetails", tabCategoryNamesList);
			request.setAttribute("isClosed", isClosed);
			request.setAttribute("isThresholdToBeApplied", isThresholdToBeApplied);

			log.debug(this.getClass().getName() + "-[data]-Exit");
			return getView("data");
		} catch (SwtException e) {
			String message = e.getStackTrace()[0].getClassName() + "."
							 + e.getStackTrace()[0].getMethodName() + ":"
							 + e.getStackTrace()[0].getLineNumber() + " "
							 + e.getMessage();
			log.error("Exception caught in " + this.getClass().getName()
					  + "data" + message);
			SwtUtil.logException(e, request, "");

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());

			return getView("dataerror");
		} catch (Exception e) {
			String message = e.getStackTrace()[0].getClassName() + "."
							 + e.getStackTrace()[0].getMethodName() + ":"
							 + e.getStackTrace()[0].getLineNumber() + " "
							 + e.getMessage();
			log.error("Exception caught in " + this.getClass().getName()
					  + "data" + message);
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "data", this.getClass()), request, "");

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());

			return getView("dataerror");
		} finally {
			// nullify objects
			opTimer = null;
			userId = null;
			screenId = null;
			entityId = null;
			currencyGrpId = null;
			roleId = null;
			selectedDate = null;
			selectedDateStr = null;
			applyCurrencyThreshold = null;
			screenOptionManager = null;
			screenOption = null;
			workflowDetails = null;
			workFlowMgr = null;
			entitysLblValue = null;
			collUserEntityAccess = null;
			entityAccessLvlColl = null;
			currrencyGroupList = null;
			currGroupAccessColl = null;
			oraSystemTime = null;
			entityTimeOffset = null;
			tabList = null;
			systemDateCal = null;
			tabFlag = null;
			systemDBDate = null;
		}
	}

	/**
	 * This method directs control to workflowmonitor.jsp
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String hostId = null;
		String roleId = null;
		String userId = null;
		String defaultEntityId = null;
		String defaultCcyGrpId = null;
		Date sysDate = null;
		String sysDateAsString = null;
		String screenId = SwtConstants.WORKFLOW_MONITOR_ID;
		try {
			log
					.debug("Entering into WorkkflowMonitorAction.unspecified() method");
			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			defaultEntityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			defaultCcyGrpId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();

			sysDate = SwtUtil.getSystemDatewithoutTime();
			sysDateAsString = SwtUtil.formatDate(sysDate, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			/* Used to get the instance from Configuration file */
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Initialising the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// Setting the rate in request
			request.setAttribute("autoRefreshRate", new Integer(screenOption
					.getPropertyValue()));

			// Added for mantis 1443
			//bind column widht and column order in request
			bindColumnWidthInRequest(request);
			bindColumnOrderInRequest(request,defaultEntityId);

			request.setAttribute("hostId", hostId);
			request.setAttribute("defaultEntityId", defaultEntityId);
			request.setAttribute("defaultCcyGrpId", defaultCcyGrpId);
			request.setAttribute("roleId", roleId);
			request.setAttribute("userId", userId);
			request.setAttribute("sysDateAsString", sysDateAsString);
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					defaultEntityId, defaultCcyGrpId);
			log
					.debug("Exiting from WorkflowMonitorAction.unspecified() method");

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in WorkflowMonitorAction.unspecified method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in WorkflowMonitorAction.unspecified method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "unspecified", WorkflowMonitorAction.class), request,
					"");
			return getView("fail");
		}
		return getView("success");
	}

	/**
	 * Method to set column width in request attribute
	 *
	 * @param request
	 */
	// Added for mantis 1443
	private void bindColumnWidthInRequest(HttpServletRequest request) {
		/* Method's local variable declaration */
		String width = null;
		HashMap<String, String> widths = null;
		String[] props = null;
		try {

			log.debug(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - " + "Entry");
			/* Read the user preferences for column width from property value */
			width = SwtUtil.getPropertyValue(request, menuItemId, "display",
					"column_width");
			/* Condition to set default column width */
			/* Set default width for columns */
			if(SwtUtil.isEmptyOrNull(width)){
				width = "maingroup=30,entity=132,ccy=132,count=100,divider=900";
			}

			widths = new HashMap<String, String>();
			/* Get column width for each column */
			props = width.split(",");
			/* Loop to separate column and width value in hash map */
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			log.debug(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - " + "Exit");
			request.setAttribute("column_width", widths);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - " + e.getMessage());
		}finally{
			//nullify the objects
			width = null;
			props = null;
		}
	}
	/**
	 * Method to save column width in user preferences list
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @param SwtException
	 */
	// Added for mantis 1443
	public String saveColumnWidth() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		log.debug(this.getClass().getName() + " - [saveColumnWidth] - "
				  + "Entry");
		/* Method's local variable declaration */
		String width = null;
		String isScenInstance = null;
		String scenarioId= null;
		try {
			/* Read width values from request */
			width = URLDecoder.decode(request.getParameter("width"), "UTF-8");
			scenarioId= request.getParameter("scenarioId");
			isScenInstance= request.getParameter("isScenInstance");
			/* Condition to check width is not null */
			if (width != null) {

				if("Y".equalsIgnoreCase(isScenInstance)) {
					SwtUtil.setPropertyValue(request, menuItemId,
							"display", "column_width_"+scenarioId, width);
				}else {

					/* Set width value for the screen in user preference list */
					SwtUtil.setPropertyValue(request, menuItemId, "display",
							"column_width", width);
				}
				request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "Column width saved ok");
			} else {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message",
						"Width parameter not sent");
			}
			log.debug(this.getClass().getName() + " - [saveColumnWidth] - "
					  + "Exit");
		} catch (Exception e) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [saveColumnWidth] method : - "
					  + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "saveColumnWidth", WorkflowMonitorAction.class),
					request, "");
		}

		return getView("statechange");
	}


	/**
	 * This method is used to bind the column order in request object
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @throws SwtException
	 */
	// Added for mantis 1443
	private void bindColumnOrderInRequest(HttpServletRequest request,
										  String entityId) throws SwtException {
		// To get column order from DB (Comma separated value)
		String columnOrder = null;
		String columnHidden = null;
		// To hold grid column order
		ArrayList<String> alColumnOrder = null;
		// Property value (split by Comma)
		String[] props = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ bindColumnOrderInRequest ] - Entry ");
			// Get column order from DB (User preference)
			columnOrder = SwtUtil.getPropertyValue(request, entityId,
					menuItemId, "display", "column_order");

			// If user preference not found in DB, set default order
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				columnOrder = "maingroup,entity,ccy,count";
			}
			/*
			 * Comma special character is used to split and put in String array
			 * variable
			 */
			props = columnOrder.split(",");
			// Initialize list to hold grid column order
			alColumnOrder = new ArrayList<String>(props.length);
			for (String prop : props) {
				/* Adding the Column values to ArrayList */
				alColumnOrder.add(prop);
			}

			columnHidden = SwtUtil.getPropertyValue(request, entityId,
					menuItemId, "display", "column_hidden");
			if(SwtUtil.isEmptyOrNull(columnHidden)) {
				columnHidden = "";
			}

			/*
			 * Setting the Column orders value in request object to show in
			 * screen
			 */
			request.setAttribute("column_order", alColumnOrder);
			request.setAttribute("column_hidden", alColumnOrder);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - [bindColumnOrderInRequest] - Exception - "
					  + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"bindColumnOrderInRequest", AccountMonitorNewAction.class);
		} finally {
			// nullify objects
			columnOrder = null;
			alColumnOrder = null;
			props = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ bindColumnOrderInRequest ] - Exit ");
		}

	}

	/**
	 * This method is used to save the dataGrid column's order in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	// Added for mantis 1443
	public String saveColumnOrder() {
		// To hold column order (comma separated value)
		String columnOrder = null;
		// Entity id
		String entityId = null;
		String isScenInstance = null;
		String scenarioId= null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Entry ");
			// If column order and entity id found in request, save the same in
			// DB (user preference comma separated value)
			entityId =  SwtUtil.getUserCurrentEntity(request.getSession());
			scenarioId= request.getParameter("scenarioId");
			isScenInstance= request.getParameter("isScenInstance");
			/* Condition to check width is not null */
			if ((columnOrder = request.getParameter("order")) != null
				&& (entityId != null)) {
				if("Y".equalsIgnoreCase(isScenInstance)) {
					SwtUtil.setPropertyValue(request, entityId, menuItemId,
							"display", "column_order_"+scenarioId, columnOrder);
				}else {

					/* Set width value for the screen in user preference list */
					SwtUtil.setPropertyValue(request, entityId, menuItemId,
							"display", "column_order", columnOrder);
				}
			}
			/* Setting the reply_status_ok,reply_message in request object */
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			/*
			 * Setting the reply_status_ok,reply_message,reply_location in
			 * request object
			 */
			log.error(this.getClass().getName()
					  + "- [ saveColumnOrder() ] - " + e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify object(s)
			columnOrder = null;
			entityId = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Exit ");
		}
		/* Return Type of this Struts Action and returns to flexstatechange.jsp */
		return getView("statechange");
	}


	/**
	 * This method is used to save the dataGrid's column hidden in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnHidden() {
		// Column width - comma separated value
		String screenName = null;
		String columnHidden = null;
		String entityId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Entry ");
			// If column order and entity id found in request, save the same in
			// DB (user preference comma separated value)
			entityId =  SwtUtil.getUserCurrentEntity(request.getSession());
			if ((columnHidden = request.getParameter("hidden")) != null	&& (entityId != null)) {
				SwtUtil.setPropertyValue(request, entityId, menuItemId,
						"display", "column_hidden", columnHidden);
			}


			/* Setting the reply_status_ok,reply_message value in request object */
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
		} catch (Exception e) {
			// log error message
			log.error(this.getClass().getName()
					  + "- [ saveColumnWidth() ] - " + e);
			/*
			 * Setting the reply_status_ok,reply_message value,reply_location in
			 * request object
			 */
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify objects
			columnHidden = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnWidth ] - Exit");
		}
		/* Return Type of this Struts Action and returns to a JSP page */
		return getView("statechange");
	}

	//Save the divider status in the database if it is closed or opened
	public String saveUserSettings() throws SwtException {

		String hostId = null;
		String userId = null;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption WorkflowScreenOption = null;
		ScreenOption WorkflowScreenOptionThreshold = null;
		String isClosed = null;
		String applyThresholdValue= null;
		String oldPropertyOption =null;
		Collection<ScreenOption> option =null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + "- [saveUserSettings] - starting ");
			WorkflowScreenOption = new ScreenOption();
			WorkflowScreenOptionThreshold = new ScreenOption();
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			isClosed = request.getParameter("isClosed");
			applyThresholdValue = request.getParameter("applyThreshold");

			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			WorkflowScreenOption.getId().setHostId(hostId);
			WorkflowScreenOption.getId().setUserId(userId);
			WorkflowScreenOption.getId().setScreenId(SwtConstants.WORKFLOW_MONITOR_ID);
			WorkflowScreenOption.getId().setPropertyName(SwtConstants.WORKFLOW_DIVIDER_OPTION);



			// Save user preferences
			JSONObject sampleObject = new JSONObject();
			sampleObject.put("ISCLOSED", isClosed);
			WorkflowScreenOption.setPropertyValue(sampleObject.toString());
			option = screenOptionManager.getScreenOption(WorkflowScreenOption);

			if (option != null && option.size() > 0) {
				oldPropertyOption = (screenOptionManager.getScreenOption(WorkflowScreenOption)).iterator().next()
						.getPropertyValue();
				if (!oldPropertyOption.equals(sampleObject.toString())) {
					// update workflow options
					screenOptionManager.saveWorkflowOptions(WorkflowScreenOption, true);
				}

			} else {
				// save workflow options
				screenOptionManager.saveWorkflowOptions(WorkflowScreenOption, false);

			}


			WorkflowScreenOptionThreshold.getId().setHostId(hostId);
			WorkflowScreenOptionThreshold.getId().setUserId(userId);
			WorkflowScreenOptionThreshold.getId().setScreenId(SwtConstants.WORKFLOW_MONITOR_ID);
			WorkflowScreenOptionThreshold.getId().setPropertyName(SwtConstants.WORKFLOW_APPLY_THRESHOLD_OPTION);



			// Save user preferences
			WorkflowScreenOptionThreshold.setPropertyValue(applyThresholdValue);
			option = screenOptionManager.getScreenOption(WorkflowScreenOptionThreshold);

			if (option != null && option.size() > 0) {
				oldPropertyOption = (screenOptionManager.getScreenOption(WorkflowScreenOptionThreshold)).iterator().next()
						.getPropertyValue();
				if (!oldPropertyOption.equals(applyThresholdValue)) {
					// update workflow options
					screenOptionManager.saveWorkflowOptions(WorkflowScreenOptionThreshold, true);
				}

			} else {
				// save workflow options
				screenOptionManager.saveWorkflowOptions(WorkflowScreenOptionThreshold, false);

			}


			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + "- [saveUserSettings] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveUserSettings] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
												   + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "saveUserSettings", WorkflowMonitorAction.class);

		} finally {
			WorkflowScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
		}
		return getView("statechange");
	}


}