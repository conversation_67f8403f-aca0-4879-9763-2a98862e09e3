/*
 * @(#)MovementSearchAction.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.Archive;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Movement;
import org.swallow.work.service.MovementSearchManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/movementsearch", "/movementsearch.do"})
public class MovementSearchAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("search", "jsp/work/movementsummarydisplay");
		viewMap.put("archiveSearch", "jsp/work/archivesearchparam");
		viewMap.put("success", "jsp/work/movementsearchparam");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "archiveSearch":
				return archiveSearch();
			case "populate":
				return populate();
			case "archivePopulateAcc":
				return archivePopulateAcc();
			case "populateAcc":
				return populateAcc();
			case "search":
				return search();
			case "searchFromOutStanding":
				return searchFromOutStanding();
			case "searchFromMatch":
				return searchFromMatch();
			case "searchAccFromMatch":
				return searchAccFromMatch();
		}


		return unspecified();
	}



	private final Log log = LogFactory.getLog(MovementSearchAction.class);
	@Autowired
	private MovementSearchManager movementsearchManager;

	public void setMovementsearchManager(
			MovementSearchManager movementsearchManager) {
		this.movementsearchManager = movementsearchManager;
	}
	private Movement movementsearch = null;

	public Movement getMovementsearch() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		movementsearch = RequestObjectMapper.getObjectFromRequest(Movement.class, request,	"movementsearch");
		return movementsearch;
	}

	public void setMovementsearch(Movement movementsearch) {
		this.movementsearch = movementsearch;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("movementsearch", movementsearch);
	}

	private Map<String, String> referencemap = null;


	public Map<String, String> getReferencemap() {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		referencemap = RequestObjectMapper.getObjectFromRequest(Map.class, request,"referencemap");
		if (referencemap == null) {
			referencemap = new HashMap<String, String>();
		}


		return referencemap;
	}
	public void setReferencemap(Map<String, String> referencemap) {
		this.referencemap = referencemap;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("referencemap", referencemap);
	}

	private Movement movement;
	public Movement getMovement() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		movement = RequestObjectMapper.getObjectFromRequest(Movement.class, request,"movement");
		return movement;
	}

	public void setMovement(Movement movement) {
		this.movement = movement;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("movement", movement);
	}

	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug("exiting 'unspecified' method");

		return populate();
	}

	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putEntityListInReq' method");

		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		List<LabelValueBean> list = new ArrayList<>(coll);
		list.add(0, new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));

		request.setAttribute("entities", list);

		log.debug("exiting 'putEntityListInReq' method");
	}


	/**
	 * @param entityId
	 * @param request
	 * @throws SwtException
	 */
	private void putLanguageListInRequest(HttpServletRequest request,
										  String entityId) throws SwtException {
		log.debug("Inside the putLanguageListInRequest");
		Collection coll = CacheManager.getInstance().getMiscParamsLVL(
				"POSLEVEL", entityId);

		Collection positionLevelList = new ArrayList();
		positionLevelList.add(new LabelValueBean("", ""));

		if (coll != null) {
			Iterator itr = coll.iterator();

			while (itr.hasNext()) {

				LabelValueBean labelValue = (LabelValueBean) (itr.next());
				String label = labelValue.getLabel();
				String value = labelValue.getValue();

				if (!label.equalsIgnoreCase("") && !value.equalsIgnoreCase("")) {
					positionLevelList.add(new LabelValueBean(label, value));
				}
			}
		}
		request.setAttribute("collLang", positionLevelList);


		log.debug("Outside the putLanguageListInRequest");
	}

	/**
	 * This method is used to display archive search Details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String archiveSearch()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold DynaValidatorForm
		// DynaValidatorForm dyForm = null;
		// Variable to hold movement Search
		Movement movementSearch = null;
		// Variable to hold entity id
		String entityId = null;
		// Variable to hold initial input screen
		String initialInputScreen = null;
		// Variable to hold cache manager list
		CacheManager cacheManagerInst = null;
		// Variable to hold host id
		String hostId = null;
		// Collection declared to currency in the list
		Collection currList = null;
		// Collection declared to book in the list
		Collection bookList = null;
		// Collection declared to group in the list
		Collection groupList = null;
		// Collection declared to meta group in the list
		Collection metaGroupList = null;
		// collection to hold account list
		Collection accountlist = null;
		// variable to hold currency code
		String currCode = null;
		// variable to hold position level names
		ArrayList collPositionLvlNames = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [archiveSearch]  - Entering ");
			// get the dyna validator form
			// get movement search form
			movementSearch = (Movement) (getMovementsearch());
			// get entity id from movement search
			entityId = movementSearch.getId().getEntityId();
			// get initial input screen from request
			initialInputScreen = request.getParameter("initialinputscreen");
			// put entity list in request
			putEntityListInReq(request);
			// put archive list in request
			putArchiveListinRequest(request);
			// get instance from cache manager
			cacheManagerInst = CacheManager.getInstance();
			// get host id form cache manager instance
			hostId = cacheManagerInst.getHostId();
			// check entity id equal to null if null set the user current entity
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				movementSearch.getId().setEntityId(entityId);
			}
			// get currency list
			currList = getCurrencyList(request, hostId, entityId);
			// set currency details in request
			request.setAttribute("currencydetails", currList);

			// Populating the bookcode combo box
			bookList = movementsearchManager.getBookCodeDetails(hostId,
					entityId);
			// set book details in request
			request.setAttribute("bookdetails", bookList);
			// Populating the group combo box
			groupList = movementsearchManager.getGroupDetails(hostId, entityId);
			// set group detials in request
			request.setAttribute("groupdetails", groupList);
			// Populating the metagroup combo box
			metaGroupList = movementsearchManager.getMetaGroupDetails(hostId,
					entityId);
			// set meta group details in request
			request.setAttribute("metagroupdetails", metaGroupList);
			// get currency code from movment search
			currCode = movementSearch.getCurrencyCode();
			/*
			 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			// if currency code is null set it currency code is All
			if (SwtUtil.isEmptyOrNull(currCode)) {
				currCode = SwtConstants.ALL_VALUE;
			}
			/*
			 * End:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			// Populating the account combo box

			if (currCode.equalsIgnoreCase(SwtConstants.ALL_VALUE) && (currList.size()>0)) {
				accountlist = movementsearchManager.getAccountDetails(hostId,
						entityId);
				request.setAttribute("accountdetails", accountlist);
			} else {
				accountlist = movementsearchManager.getAccountDetails(hostId,
						entityId, currCode);

				request.setAttribute("accountdetails", accountlist);
			}
			// set the default value date

			movementSearch.setValueFromDateAsString(SwtUtil.formatDate(
					SwtUtil.getSysParamDateWithEntityOffset(entityId), SwtUtil
							.getCurrentSystemFormats(request.getSession())
							.getDateFormatValue()));

			if("All".equalsIgnoreCase(entityId)) {
				collPositionLvlNames = movementsearchManager.getPositionLevelList(hostId);
			}else {
				collPositionLvlNames = (ArrayList) SwtUtil.getSwtMaintenanceCache()
						.getEntityPositionLevelObjectLVL(entityId);
			}


			collPositionLvlNames.add(0, new LabelValueBean(
					SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			// set position level name ,initial input screen,method in request
			request.setAttribute("collLang", collPositionLvlNames);
			request.setAttribute("initialinputscreen", initialInputScreen);
			request.setAttribute("methodName", "search");
			request.setAttribute("method", "search");
			request.setAttribute("extraFilter", request
					.getParameter("extraFilter"));
			// put swift message list in request
			putSwiftMessageListInReq(request);
			setMovementsearch(movementSearch);
			// get menu entity currency group access form swtutil
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
			log.debug(this.getClass().getName()
					  + " - [archiveSearch]  - Exiting ");
			return getView("archiveSearch");
		} catch (SwtException swtexp) {
			log.debug("SwtException in MovementSearchAction.archiveSearch "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception in MovementSearchAction.archiveSearch "
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "archiveSearch", MovementSearchAction.class), request,
					"");
			return getView("fail");
		} finally {
			movementSearch = null;
			entityId = null;
			initialInputScreen = null;
			cacheManagerInst = null;
			hostId = null;
			currList = null;
			bookList = null;
			groupList = null;
			metaGroupList = null;
			currCode = null;
			collPositionLvlNames = null;
		}
	}

	/**
	 * @param request
	 * @throws SwtException
	 *
	 */
	private void putArchiveListinRequest(HttpServletRequest request)
			throws SwtException {
		log.debug("Entering putArchiveListinRequest method");

		List archiveList = movementsearchManager.getArchiveList();
		Iterator itr = archiveList.iterator();
		Collection archiveColl = new ArrayList();
		Archive arch = new Archive();

		while (itr.hasNext()) {
			arch = (Archive) itr.next();
			archiveColl.add(new LabelValueBean(arch.getArchiveName(), arch
					.getId().getArchiveId()));
		}

		request.setAttribute("archives", archiveColl);
	}

	/**
	 * This method is used to display movement search Details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String populate()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold DynaValidatorForm
		// DynaValidatorForm dyForm = null;
		// variable to hold movement search
		Movement movementSearch = null;
		// variable to hold entity id
		String entityId = null;
		// variable to hold initial input screen
		String initialInputScreen = null;
		// variable to hold cache manager instant
		CacheManager cacheManagerInst = null;
		// variable to hold host id
		String hostId = null;
		// Collection declared to currency in the list
		Collection currList = null;
		// Collection declared to Book Code Details
		Collection bookList = null;
		// Collection declared to Group Details
		Collection groupList = null;
		// Collection declared to Meta Group Details
		Collection metaGroupList = null;
		// variable to hold currency code
		String currCode = null;
		// variable to hold instance for array list
		ArrayList accountList = null;
		// collection to hold position level names
		ArrayList collPositionLvlNames = null;
		// variable to hold label value bean
		LabelValueBean labelValueBean = null;
		boolean firstLoad = false;
		try {
			log.debug(this.getClass().getName() + " - [populate]  - Entering ");
			// get the dyna validator form
			// getting movement search form
			movementSearch = (Movement) (getMovementsearch());
			// get the entity id from movement search
			entityId = movementSearch.getId().getEntityId();
			// get the initial input screen value from request
			initialInputScreen = request.getParameter("initialinputscreen");
			// put entity list in request
			putEntityListInReq(request);
			// get instance from cache manager
			cacheManagerInst = CacheManager.getInstance();
			// get the host id from cache manager
			hostId = cacheManagerInst.getHostId();
			// check entity id equal to null if null set the user current entity
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				movementSearch.getId().setEntityId(entityId);
				firstLoad = true;
			}
			// get the currency list
			currList = getCurrencyList(request, hostId, entityId);
			// set the currency list in request
			request.setAttribute("currencydetails", currList);

			// Populating the book code combo box
			bookList = movementsearchManager.getBookCodeDetails(hostId,
					entityId);
			// set book code details in request
			request.setAttribute("bookdetails", bookList);
			// Populating the group combo box
			groupList = movementsearchManager.getGroupDetails(hostId, entityId);
			// set group details in request
			request.setAttribute("groupdetails", groupList);
			// Populating the meta group combo box
			metaGroupList = movementsearchManager.getMetaGroupDetails(hostId,
					entityId);
			// set meta group details in request
			request.setAttribute("metagroupdetails", metaGroupList);
			// get currency code from movement search
			currCode = movementSearch.getCurrencyCode();
			/*
			 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			// if currency code is null set it currency code is All
			if (SwtUtil.isEmptyOrNull(currCode)) {
				currCode = SwtConstants.ALL_VALUE;
			}
			/*
			 * End:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			// instance for Array list
			accountList = new ArrayList();
			// checks the currency code is All.if All get all the account
			// details else based on the currency
			if (currCode.equalsIgnoreCase(SwtConstants.ALL_VALUE) && (currList.size()>0)) {
				accountList = movementsearchManager.getAccountDetails(hostId,
						entityId);
				// set account details in request
				request.setAttribute("accountdetails", accountList);
			} else {
				accountList = movementsearchManager.getAccountDetails(hostId,
						entityId, currCode);
				// set account details in request
				request.setAttribute("accountdetails", accountList);
			}
			// check vale date is null or empty.if null or empty get date from

			movementSearch.setValueFromDateAsString(SwtUtil.formatDate(
					SwtUtil.getSysParamDateWithEntityOffset(entityId), SwtUtil
							.getCurrentSystemFormats(request.getSession())
							.getDateFormatValue()));

			Map<String, String> refMap = new HashMap<String, String>();
			if(firstLoad) {
				//Fill reference checkbox with the default values
				refMap.put("includeRefFlag", "N");
				refMap.put("includeRef1Flag", "Y");
				refMap.put("includeRef2Flag", "Y");
				refMap.put("includeRef3Flag", "Y");
				refMap.put("includeRef4Flag", "Y");
				refMap.put("excludeRefFlag", "N");
				refMap.put("excludeRef1Flag", "Y");
				refMap.put("excludeRef2Flag", "Y");
				refMap.put("excludeRef3Flag", "Y");
				refMap.put("excludeRef4Flag", "Y");


				setReferencemap(refMap);
			}else {

				//Fill reference checkbox with the default values
				refMap.put("includeRef", ""+request.getParameter("referencemap.includeRef"));
				refMap.put("includeRefFlag", ""+request.getParameter("referencemap.includeRefFlag"));
				refMap.put("includeRef1Flag", ""+request.getParameter("referencemap.includeRef1Flag"));
				refMap.put("includeRef2Flag", ""+request.getParameter("referencemap.includeRef2Flag"));
				refMap.put("includeRef3Flag", ""+request.getParameter("referencemap.includeRef3Flag"));
				refMap.put("includeRef4Flag", ""+request.getParameter("referencemap.includeRef4Flag"));

				refMap.put("excludeRef", ""+request.getParameter("referencemap.excludeRef"));
				refMap.put("excludeRefFlag", ""+request.getParameter("referencemap.excludeRefFlag"));
				refMap.put("excludeRef1Flag", ""+request.getParameter("referencemap.excludeRef1Flag"));
				refMap.put("excludeRef2Flag", ""+request.getParameter("referencemap.excludeRef2Flag"));
				refMap.put("excludeRef3Flag", ""+request.getParameter("referencemap.excludeRef3Flag"));
				refMap.put("excludeRef4Flag", ""+request.getParameter("referencemap.excludeRef4Flag"));

				setReferencemap(refMap);
			}
			// Populating the position level combo box
			if("All".equalsIgnoreCase(entityId)) {
				collPositionLvlNames = movementsearchManager.getPositionLevelList(hostId);
			}else {
				collPositionLvlNames = (ArrayList) SwtUtil.getSwtMaintenanceCache()
						.getEntityPositionLevelObjectLVL(entityId);
			}
			// instance for label value bean
			labelValueBean = new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE);
			// add the position level name in collection
			collPositionLvlNames.add(0, labelValueBean);
			// set position level names in request
			request.setAttribute("collLang", collPositionLvlNames);
			// set initial input screen in request
			request.setAttribute("initialinputscreen", initialInputScreen);
			// set method name in request
			request.setAttribute("methodName", "search");
			request.setAttribute("method", "search");
			request.setAttribute("extraFilter", request
					.getParameter("extraFilter"));
			// put message list in request
			putSwiftMessageListInReq(request);
			setMovementsearch(movementSearch);
			log.debug(this.getClass().getName() + " - [populate]  - Exiting ");
			return getView("success");
		} catch (SwtException swtexp) {
			log.debug("SwtException in MovementSearchAction.populate "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception in MovementSearchAction.populate "
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "populate", MovementSearchAction.class), request, "");

			return getView("fail");
		} finally {
			// nullify object(s)
			movementSearch = null;
			entityId = null;
			initialInputScreen = null;
			cacheManagerInst = null;
			hostId = null;
			currList = null;
			bookList = null;
			groupList = null;
			metaGroupList = null;
			currCode = null;
			accountList = null;
			collPositionLvlNames = null;
			labelValueBean = null;

		}
	}

	/**
	 * This method is used to populate the archive search Details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String archivePopulateAcc() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold DynaValidatorForm
		// DynaValidatorForm dyForm = null;
		// Variable to hold movement search
		Movement movementSearch = null;
		// variable to hold entity id
		String entityId = null;
		// variable to hold initial input screen
		String initialInputScreen = null;
		// variable to hold cache manager
		CacheManager cacheManagerInst = null;
		// variable to hold host id
		String hostId = null;
		// Collection declared to currency in the list
		Collection currList = null;
		// Collection declared to book in the list
		Collection bookList = null;
		// Collection declared to group in the list
		Collection groupList = null;
		// Collection declared to meta group in the list
		Collection metaGroupList = null;
		// collection to hold account list
		Collection accountList = null;
		// variable to hold position level names
		ArrayList collPositionLvlNames = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [archivePopulateAcc]  - Entering ");
			// get the dyna validator form
			// get movement search form
			movementSearch = (Movement) (getMovementsearch());
			// set match status in movement search
			movementSearch.setMatchStatus(movementSearch.getMatchStatus());
			// get entity id from movement search
			entityId = movementSearch.getId().getEntityId();
			// get initial input screen from request
			initialInputScreen = request.getParameter("initialinputscreen");
			// put entity list in request
			putEntityListInReq(request);
			// put archive list in request
			putArchiveListinRequest(request);
			// get instance from cache manager
			cacheManagerInst = CacheManager.getInstance();
			// get host id from cache manager
			hostId = cacheManagerInst.getHostId();
			// check entity id equal to null if null set the user current entity
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				movementSearch.getId().setEntityId(entityId);
			}
			// populating the currency details in combo box
			currList = getCurrencyList(request, hostId, entityId);
			// set currency details in request
			request.setAttribute("currencydetails", currList);

			// Populating the book code combo box
			bookList = movementsearchManager.getBookCodeDetails(hostId,
					entityId);
			// set book list in request
			request.setAttribute("bookdetails", bookList);
			// Populating the group combo box
			groupList = movementsearchManager.getGroupDetails(hostId, entityId);
			// set group list in request
			request.setAttribute("groupdetails", groupList);
			// Populating the meta group combo box
			metaGroupList = movementsearchManager.getMetaGroupDetails(hostId,
					entityId);
			// set meta group details in request
			request.setAttribute("metagroupdetails", metaGroupList);
			// Populating the account combo box
			/*
			 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			if(currList.size()>0)
			{
				accountList = movementsearchManager.getAccountClassDetails(hostId,
						entityId, movementSearch.getAccountClass(), movementSearch
								.getCurrencyCode());
			}else{
				accountList = new ArrayList();
				accountList.add(new LabelValueBean(SwtConstants.EMPTY_STRING,
						SwtConstants.EMPTY_STRING));
			}
			/*
			 * End:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			// set account details list in request
			request.setAttribute("accountdetails", accountList);
			// Populating the swift message type combo box - TODO
			// Populating the position level combo box

			if("All".equalsIgnoreCase(entityId)) {
				collPositionLvlNames = movementsearchManager.getPositionLevelList(hostId);
			}else {
				collPositionLvlNames = (ArrayList) SwtUtil.getSwtMaintenanceCache()
						.getEntityPositionLevelObjectLVL(entityId);
			}

			collPositionLvlNames.add(0, new LabelValueBean("", ""));
			request.setAttribute("collLang", collPositionLvlNames);
			request.setAttribute("initialinputscreen", initialInputScreen);
			request.setAttribute("methodName", "search");
			request.setAttribute("method", "search");
			request.setAttribute("methodName", "populateAcc");
			request.setAttribute("extraFilter", request
					.getParameter("extraFilter"));
			setMovementsearch(movementSearch);
			putSwiftMessageListInReq(request);
			log.debug(this.getClass().getName()
					  + " - [archivePopulateAcc]  - Exiting ");
			return getView("archiveSearch");
		} catch (SwtException swtexp) {
			log
					.debug("SwtException in MovementSearchAction.archivePopulateAcc "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception in MovementSearchAction.archivePopulateAcc "
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "archivePopulateAcc", MovementSearchAction.class),
					request, "");

			return getView("fail");
		} finally {
			movementSearch = null;
			entityId = null;
			initialInputScreen = null;
			cacheManagerInst = null;
			hostId = null;
			currList = null;
			bookList = null;
			groupList = null;
			metaGroupList = null;
			accountList = null;
			collPositionLvlNames = null;
		}
	}

	/**
	 * This mehtod is used to populate account class details in Movement search screen
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String populateAcc()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold DynaValidatorForm
		// DynaValidatorForm dyForm = null;
		// variable to hold movement search
		Movement movementSearch = null;
		// variable to hold entity id
		String entityId = null;
		// variable to hold initial input screen
		String initialInputScreen = null;
		// variable to hold cache manager
		CacheManager cacheManagerInst = null;
		// variable to hold host id
		String hostId = null;
		// Collection declared to currency in the list
		Collection currList = null;
		// Collection declared to book in the list
		Collection bookList = null;
		// Collection declared to group in the list
		Collection groupList = null;
		// Collection declared to meta group in the list
		Collection metaGroupList = null;
		// collection to hold account list
		Collection accountList = null;
		// variable to hold position level names
		ArrayList collPositionLvlNames = null;
		// variable to hold label value bean
		LabelValueBean labelValueBean = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [populateAcc]  - Entering ");
			// get the dyna validator form
			// get movement search from form
			movementSearch = (Movement) (getMovementsearch());
			// set match status in movement search
			movementSearch.setMatchStatus(movementSearch.getMatchStatus());
			// get entity id from movement search
			entityId = movementSearch.getId().getEntityId();
			// get intial input screen from request
			initialInputScreen = request.getParameter("initialinputscreen");
			// put entity list in request
			putEntityListInReq(request);
			// get cache manager instance
			cacheManagerInst = CacheManager.getInstance();
			// get host id from cache manager
			hostId = cacheManagerInst.getHostId();
			// check entity id equal to null if null set the user current entity
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				movementSearch.getId().setEntityId(entityId);
			}
			// populating the currency list combo box
			currList = getCurrencyList(request, hostId, entityId);
			// set currency details in request
			request.setAttribute("currencydetails", currList);

			// Populating the bookcode combo box
			bookList = movementsearchManager.getBookCodeDetails(hostId,
					entityId);
			// set book code details in request
			request.setAttribute("bookdetails", bookList);
			// Populating the group combo box
			groupList = movementsearchManager.getGroupDetails(hostId, entityId);
			// set group details in request
			request.setAttribute("groupdetails", groupList);
			// Populating the metagroup combo box
			metaGroupList = movementsearchManager.getMetaGroupDetails(hostId,
					entityId);
			// set meta group details in request
			request.setAttribute("metagroupdetails", metaGroupList);
			// Populating the account combo box
			/*
			 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			if(currList.size()>0)
			{
				accountList = movementsearchManager.getAccountClassDetails(hostId,
						entityId, movementSearch.getAccountClass(), movementSearch
								.getCurrencyCode());
			}else{
				accountList = new ArrayList();
				accountList.add(new LabelValueBean(SwtConstants.EMPTY_STRING,
						SwtConstants.EMPTY_STRING));
			}
			/*
			 *End:code modified by Sandeep kumar for Mantis 1894:Movement:
			 * Account drop down displays other currency accounts
			 */
			// set account list in request
			request.setAttribute("accountdetails", accountList);
			// get the position level names
			if("All".equalsIgnoreCase(entityId)) {
				collPositionLvlNames = movementsearchManager.getPositionLevelList(hostId);
			}else {
				collPositionLvlNames = (ArrayList) SwtUtil.getSwtMaintenanceCache()
						.getEntityPositionLevelObjectLVL(entityId);
			}
			// instance for label value bean
			labelValueBean = new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE);
			// Adding the position level names
			collPositionLvlNames.add(0, labelValueBean);
			// set position level names,initial input screen ,method names in
			// request

			Map<String, String> refMap = new HashMap<String, String>();

			//Fill reference checkbox with the default values
			refMap.put("includeRef", ""+request.getParameter("referencemap.includeRef"));
			refMap.put("includeRefFlag", ""+request.getParameter("referencemap.includeRefFlag"));
			refMap.put("includeRef1Flag", ""+request.getParameter("referencemap.includeRef1Flag"));
			refMap.put("includeRef2Flag", ""+request.getParameter("referencemap.includeRef2Flag"));
			refMap.put("includeRef3Flag", ""+request.getParameter("referencemap.includeRef3Flag"));
			refMap.put("includeRef4Flag", ""+request.getParameter("referencemap.includeRef4Flag"));


			refMap.put("excludeRef", ""+request.getParameter("referencemap.excludeRef"));
			refMap.put("excludeRefFlag", ""+request.getParameter("referencemap.excludeRefFlag"));
			refMap.put("excludeRef1Flag", ""+request.getParameter("referencemap.excludeRef1Flag"));
			refMap.put("excludeRef2Flag", ""+request.getParameter("referencemap.excludeRef2Flag"));
			refMap.put("excludeRef3Flag", ""+request.getParameter("referencemap.excludeRef3Flag"));
			refMap.put("excludeRef4Flag", ""+request.getParameter("referencemap.excludeRef4Flag"));

			setReferencemap(refMap);
			request.setAttribute("collLang", collPositionLvlNames);
			request.setAttribute("initialinputscreen", initialInputScreen);
			request.setAttribute("methodName", "search");
			request.setAttribute("method", "search");
			request.setAttribute("methodName", "populateAcc");
			request.setAttribute("extraFilter", request
					.getParameter("extraFilter"));
			// put swift message list in request
			putSwiftMessageListInReq(request);
			setMovementsearch(movementSearch);
			log.debug(this.getClass().getName()
					  + " - [populateAcc]  - Exiting ");
			return getView("success");
		} catch (SwtException swtexp) {
			log.debug("SwtException in MovementSearchAction.populateAcc "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception in MovementSearchAction.populateAcc "
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "populateAcc", MovementSearchAction.class), request, "");

			return getView("fail");
		} finally {
			movementSearch = null;
			entityId = null;
			initialInputScreen = null;
			cacheManagerInst = null;
			hostId = null;
			currList = null;
			bookList = null;
			groupList = null;
			metaGroupList = null;
			accountList = null;
			collPositionLvlNames = null;
			labelValueBean = null;
		}
	}

	public String search()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Inside the search method");

			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();

			// DynaValidatorForm
			Movement movementsearch =  (Movement) (getMovementsearch());
			SystemFormats formats = SwtUtil.getCurrentSystemFormats(request
					.getSession());

			// Fetching all the values from the dynaform
			String status = movementsearch.getMatchStatus();
			String entityName = request.getParameter("selectedEntityName");
			String entityid = movementsearch.getId().getEntityId();
			String initialinputscreen = request
					.getParameter("initialinputscreen");
			String movementtype = movementsearch.getMovementType();
			String sign = movementsearch.getSign();
			String predictstatus = movementsearch.getPredictStatus();
			if ("".equals(movementsearch.getAmountoverasstring())) {
				movementsearch.setAmountover(new Double(0.0));
			} else {
				movementsearch.setAmountover(SwtUtil.parseCurrency(
						movementsearch.getAmountoverasstring(), formats
								.getCurrencyFormat()));
			}

			if ("".equals(movementsearch.getAmountunderasstring())) {
				movementsearch.setAmountunder(new Double(0.0));
			} else {
				movementsearch.setAmountunder(SwtUtil.parseCurrency(
						movementsearch.getAmountunderasstring(), formats
								.getCurrencyFormat()));
			}

			if (movementsearch.getCurrencyCode() == null) {
				movementsearch.setCurrencyCode("");
			}
			String beneficiaryId = movementsearch.getBeneficiaryId();
			String custodianId = movementsearch.getCustodianId();
			Integer poslevel = movementsearch.getPositionLevel();
			String accountId = movementsearch.getAccountId();

			String group = movementsearch.getGroup();
			String metaGroup = movementsearch.getMetaGroup();
			String bookCode = movementsearch.getBookCode();

			String sortorder = movementsearch.getSortorder();


			String valuefromdate = movementsearch.getValueFromDateAsString();
			String valuetodate = movementsearch.getValueToDateAsString();

			String timefrom = movementsearch.getTimefrom();
			String timeto = movementsearch.getTimeto();

			String str = "";
			str = str.concat(valuefromdate + valuetodate + timefrom + timeto);
			String msgtype = movementsearch.getMessageId();
			String inputdate = movementsearch.getInputDateAsString();
			String counterparty = movementsearch.getCounterPartyId();
			ArrayList movlist = new ArrayList();
			java.util.Iterator itr = movlist.iterator();

			while (itr.hasNext()) {
				Movement movement = (Movement) itr.next();
			}
			Movement movement = new Movement();
			movement.getId().setEntityId(entityid);
			movementsearch.getId().setEntityId(entityid);
			setMovement(movement);
			int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request,
					entityid, null);
			if (accessInd == 0) {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			String date = "All";
			putEntityListInReq(request);
			request.setAttribute("date", date);
			request.setAttribute("entityId", entityid);
			request.setAttribute("entityName", entityName);

			request.setAttribute("currencyCode", movementsearch
					.getCurrencyCode());
			request.setAttribute("OutStandingMovementSummaryDetails", movlist);
			request.setAttribute("method", "search");
			request.setAttribute("initialinputscreen", initialinputscreen);
			putSwiftMessageListInReq(request);
			setMovementsearch(movementsearch);
			return getView("search");
		} catch (SwtException swtexp) {
			log.debug("SwtException in MovementSearchAction.search "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception in MovementSearchAction.search "
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "search", PreAdviceInputAction.class), request, "");

			return getView("fail");
		}
	}

	public String searchFromOutStanding() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Entering searchFromOutStanding method");

			// populating the currencylist combo box
			// DynaValidatorForm Movement movementsearch = (Movement) (getMovementsearch());
			Movement movementsearch =  (Movement) (getMovementsearch());
			String entityid = request.getParameter("entityCode");
			movementsearch.getId().setEntityId(entityid);
			putEntityListInReq(request);
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			Collection currlist = movementsearchManager.getCurrencyDetails(
					hostId, entityid);
			request.setAttribute("currencydetails", currlist);

			// Populating the bookcode combo box
			Collection booklist = movementsearchManager.getBookCodeDetails(
					hostId, entityid);
			request.setAttribute("bookdetails", booklist);

			// Populating the group combo box
			Collection grouplist = movementsearchManager.getGroupDetails(
					hostId, entityid);
			request.setAttribute("groupdetails", grouplist);
			// Populating the metagroup combo box
			Collection metagrouplist = movementsearchManager
					.getMetaGroupDetails(hostId, entityid);
			request.setAttribute("metagroupdetails", metagrouplist);

			// Populating the account combo box
			Collection accountlist = movementsearchManager.getAccountDetails(
					hostId, entityid);
			request.setAttribute("accountdetails", accountlist);

			// Populating the swift message type combo box - TODO

			// Populating the position level combo box
			putLanguageListInRequest(request, entityid);

			request.setAttribute("methodName", "search");
			putSwiftMessageListInReq(request);
			setMovementsearch(movementsearch);
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.debug("SwtException in MovementSearchAction.searchFromOutStanding "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			log
					.debug("Exception in MovementSearchAction.searchFromOutStanding "
						   + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "searchFromOutStanding", MovementSearchAction.class),
					request, "");

			return getView("fail");
		}
	}

	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String searchFromMatch() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Entering searchFromMatch method");

			// DynaValidatorForm Movement movementsearch = (Movement) (getMovementsearch());
			Movement movementsearch =  (Movement) (getMovementsearch());
			String initialinputscreen = request
					.getParameter("initialinputscreen");
			String entityid = request.getParameter("entityId");
			String currencyCode = request.getParameter("currencyCode");
			SystemFormats sysforma = SwtUtil.getCurrentSystemFormats(request
					.getSession());
			Date today = SwtUtil.getSysParamDateWithEntityOffset(entityid);
			String sysDateAsstring = SwtUtil.formatDate(today, sysforma
					.getDateFormatValue());
			movementsearch.getId().setEntityId(entityid);
			movementsearch.setCurrencyCode(currencyCode);
			movementsearch.setValueFromDateAsString(SwtUtil.formatDate(today,
					sysforma.getDateFormatValue()));
			movementsearch.setValueToDateAsString(SwtUtil.formatDate(today,
					sysforma.getDateFormatValue()));

			putEntityListInReq(request);

			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();

			if ((entityid == null) || (entityid.trim().length() <= 0)) {
				entityid = SwtUtil.getUserCurrentEntity(request.getSession());
				movementsearch.getId().setEntityId(entityid);
			}

			Collection currlist = movementsearchManager.getCurrencyDetails(
					hostId, entityid);
			request.setAttribute("currencydetails", currlist);

			// Populating the bookcode combo box
			Collection booklist = movementsearchManager.getBookCodeDetails(
					hostId, entityid);
			request.setAttribute("bookdetails", booklist);

			// Populating the group combo box
			Collection grouplist = movementsearchManager.getGroupDetails(
					hostId, entityid);
			request.setAttribute("groupdetails", grouplist);
			Collection metagrouplist = movementsearchManager
					.getMetaGroupDetails(hostId, entityid);
			request.setAttribute("metagroupdetails", metagrouplist);
			// Populating the account combo box
			Collection accountlist = movementsearchManager.getAccountDetails(
					hostId, entityid, currencyCode);
			request.setAttribute("accountdetails", accountlist);
			// Populating the position level combo box
			Collection collPositionLvlNames = SwtUtil.getSwtMaintenanceCache()
					.getEntityPositionLevelObjectLVL(entityid);
			request.setAttribute("collLang", collPositionLvlNames);


			Map<String, String> refMap = new HashMap<String, String>();
			//Fill reference checkbox with the default values
			refMap.put("includeRefFlag", "N");
			refMap.put("includeRef1Flag", "Y");
			refMap.put("includeRef2Flag", "Y");
			refMap.put("includeRef3Flag", "Y");
			refMap.put("includeRef4Flag", "Y");
			refMap.put("excludeRefFlag", "N");
			refMap.put("excludeRef1Flag", "Y");
			refMap.put("excludeRef2Flag", "Y");
			refMap.put("excludeRef3Flag", "Y");
			refMap.put("excludeRef4Flag", "Y");


			setReferencemap(refMap);

			setMovementsearch(movementsearch);
			request.setAttribute("sysDateAsstring", sysDateAsstring);
			request.setAttribute("methodName", "search");
			request.setAttribute("method", "search");
			request.setAttribute("initialinputscreen", initialinputscreen);
			request.setAttribute("isAmountDiffer", request
					.getParameter("isAmountDiffer"));
			request.setAttribute("selectedMovementsAmount", request
					.getParameter("selectedMovementsAmount"));
			putSwiftMessageListInReq(request);
			return getView("success");
		} catch (SwtException swtexp) {
			log.debug("SwtException in MovementSearchAction.searchFromMatch "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception in MovementSearchAction.searchFromMatch "
					  + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "searchFromMatch", MovementSearchAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * This mehtod is used to display the match details
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String searchAccFromMatch() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold DynaValidatorForm
		// DynaValidatorForm dyForm = null;
		// Variable to hold movment search
		Movement movementSearch = null;
		// Variable to hold initial input screen
		String initialInputScreen = null;
		// variable to hold entity id
		String entityId = null;
		// varibale to hold system formats
		SystemFormats sysFormat = null;
		// variable to hold date
		Date todayDate = null;
		// varible to hold system date as string
		String sysDateAsstring = null;
		// varible to hold cache manager
		CacheManager cacheManagerInst = null;
		// varible to hold host id
		String hostId = null;
		// Collection declared to currency in the list
		Collection currList = null;
		// Collection declared to Book Code Details
		Collection bookList = null;
		// Collection declared to Group Details
		Collection groupList = null;
		// Collection declared to Meta Group Details
		Collection metaGroupList = null;
		// Collection declared to the account details
		Collection accountList = null;
		// varible to hold position level names
		ArrayList collPositionLvlNames = null;
		// varible to hold label value bean
		LabelValueBean labelValueBean = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [searchAccFromMatch]  - Entering ");
			// get the dyna validator form
			// get movement search form
			movementSearch = (Movement) (getMovementsearch());
			// get initial input screen from request
			initialInputScreen = request.getParameter("initialinputscreen");
			// get entity id from request
			entityId = movementSearch.getId().getEntityId();
			// get current system format from swtutil
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get today date from swtutil
			todayDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);
			// get format date as string from swtutil
			sysDateAsstring = SwtUtil.formatDate(todayDate, sysFormat
					.getDateFormatValue());
			// set value date as string in movment search
			movementSearch.setValueFromDateAsString(SwtUtil.formatDate(
					todayDate, sysFormat.getDateFormatValue()));
			movementSearch.setValueToDateAsString(SwtUtil.formatDate(todayDate,
					sysFormat.getDateFormatValue()));
			// put entity list in request
			putEntityListInReq(request);
			// get instance for cache manager
			cacheManagerInst = CacheManager.getInstance();
			// get host id from cache manager
			hostId = cacheManagerInst.getHostId();
			// check entity id equal to null if null set the user current entity
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				movementSearch.getId().setEntityId(entityId);
			}
			// populating the currency details combo box
			currList = movementsearchManager.getCurrencyDetails(hostId,
					entityId);
			// set currency details in request
			request.setAttribute("currencydetails", currList);

			// Populating the bookcode combo box
			bookList = movementsearchManager.getBookCodeDetails(hostId,
					entityId);
			// set book code details in request
			request.setAttribute("bookdetails", bookList);
			// Populating the group combo box
			groupList = movementsearchManager.getGroupDetails(hostId, entityId);
			// set group details in request
			request.setAttribute("groupdetails", groupList);
			// Populating the metagroup combo box
			metaGroupList = movementsearchManager.getMetaGroupDetails(hostId,
					entityId);
			// set meta group details in request
			request.setAttribute("metagroupdetails", metaGroupList);
			if (!(movementSearch.getAccountClass()
					.equals(SwtConstants.ALL_VALUE))) {
				/*
				 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
				 * Account drop down displays other currency accounts
				 */
				accountList = movementsearchManager.getAccountClassDetails(
						hostId, entityId, movementSearch.getAccountClass(),
						movementSearch.getCurrencyCode());
				/*
				 *End:code modified by Sandeep kumar for Mantis 1894:Movement:
				 * Account drop down displays other currency accounts
				 */
			} else {
				accountList = movementsearchManager.getAccountDetails(hostId,
						entityId);
			}
			// set account details in request
			request.setAttribute("accountdetails", accountList);
			// Populating the position level combo box
			if("All".equalsIgnoreCase(entityId)) {
				collPositionLvlNames = movementsearchManager.getPositionLevelList(hostId);
			}else {
				collPositionLvlNames = (ArrayList) SwtUtil.getSwtMaintenanceCache()
						.getEntityPositionLevelObjectLVL(entityId);
			}
			// instance for label value bean
			labelValueBean = new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE);
			// Adding label value bean values
			collPositionLvlNames.add(0, labelValueBean);
			// set position level,date,mehtod name,initial input screen in
			// request
			request.setAttribute("collLang", collPositionLvlNames);


			Map<String, String> refMap = new HashMap<String, String>();

			//Fill reference checkbox with the default values
			refMap.put("includeRef", ""+request.getParameter("referencemap.includeRef"));
			refMap.put("includeRefFlag", ""+request.getParameter("referencemap.includeRefFlag"));
			refMap.put("includeRef1Flag", ""+request.getParameter("referencemap.includeRef1Flag"));
			refMap.put("includeRef2Flag", ""+request.getParameter("referencemap.includeRef2Flag"));
			refMap.put("includeRef3Flag", ""+request.getParameter("referencemap.includeRef3Flag"));
			refMap.put("includeRef4Flag", ""+request.getParameter("referencemap.includeRef4Flag"));


			refMap.put("excludeRef", ""+request.getParameter("referencemap.excludeRef"));
			refMap.put("excludeRefFlag", ""+request.getParameter("referencemap.excludeRefFlag"));
			refMap.put("excludeRef1Flag", ""+request.getParameter("referencemap.excludeRef1Flag"));
			refMap.put("excludeRef2Flag", ""+request.getParameter("referencemap.excludeRef2Flag"));
			refMap.put("excludeRef3Flag", ""+request.getParameter("referencemap.excludeRef3Flag"));
			refMap.put("excludeRef4Flag", ""+request.getParameter("referencemap.excludeRef4Flag"));

			setReferencemap(refMap);


			setMovementsearch(movementSearch);
			request.setAttribute("sysDateAsstring", sysDateAsstring);
			request.setAttribute("methodName", "searchAccFromMatch");
			request.setAttribute("method", "search");
			request.setAttribute("initialinputscreen", initialInputScreen);
			request.setAttribute("isAmountDiffer", request
					.getParameter("isAmountDiffer"));
			request.setAttribute("selectedMovementsAmount", request
					.getParameter("selectedMovementsAmount"));
			// put swift message list in request
			putSwiftMessageListInReq(request);
			log.debug(this.getClass().getName()
					  + " - [searchAccFromMatch]  - Exiting ");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.debug("SwtException in MovementSearchAction.searchAccFromMatch "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception in MovementSearchAction.searchAccFromMatch "
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "searchAccFromMatch", MovementSearchAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			movementSearch = null;
			initialInputScreen = null;
			entityId = null;
			sysFormat = null;
			todayDate = null;
			sysDateAsstring = null;
			cacheManagerInst = null;
			hostId = null;
			currList = null;
			bookList = null;
			groupList = null;
			metaGroupList = null;
			accountList = null;
			collPositionLvlNames = null;
			labelValueBean = null;
		}
	}

	private void putSwiftMessageListInReq(HttpServletRequest request)
			throws SwtException {
		Collection collSwiftMessage = new ArrayList();
		Iterator itr = PropertiesFileLoader.getInstance()
				.getSwiftMessageValues().iterator();
		collSwiftMessage.add(new LabelValueBean("", ""));

		while (itr.hasNext()) {
			String swiftVal = itr.next().toString();
			collSwiftMessage.add(new LabelValueBean(swiftVal, swiftVal));
		}
		Collections.sort((List) collSwiftMessage);
		request.setAttribute("swiftMessages", collSwiftMessage);
	}


	/**
	 *
	 * This function returns collection of currencies
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return - collection of currencies
	 * @throws SwtException -
	 *             SwtException object
	 */
	private Collection getCurrencyList(HttpServletRequest request,
									   String hostId, String entityId) throws SwtException {
		//variable to hold role id
		String roleId = null;
		//variable to hold currency list
		ArrayList currencyList = null;
		//variable to hold currency
		Collection currrencyListWithAll = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [ getCurrencyList ] - Entry ");

			/* Getting the User's Role Id from the session object */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			/* Returns the currency Access List based on the Role */
			currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcessLVL(roleId, entityId);

			/* Check for currency List not NULL */
			if (currencyList != null) {

				/*
				 * Removes the LabelValueBean object for the Key as 'Default' from
				 * the collection
				 */
				currencyList.remove(new LabelValueBean("Default", "*"));

				/* Assigning the new ArrayList object to a new Collection Object */
				currrencyListWithAll = new ArrayList();


				/*
				 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
				 * Account drop down displays other currency accounts
				 */
				// Condition to check currency list is not empty to Add All
				if(currencyList.size()>0)
				{
					/*
					 * Adding a new LabelValueBean object with the Key as 'ALL' and
					 * value as 'ALL'
					 */
					if(!"All".equalsIgnoreCase(entityId)) {
						currrencyListWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
								SwtConstants.ALL_VALUE));
					}


				}
				/*
				 * End:code modified by Sandeep kumar for Mantis 1894:Movement:
				 * Account drop down displays other currency accounts
				 */
				/* Adding the currencyList object to collection object */
				currrencyListWithAll.addAll(currencyList);
			}
			log.debug(this.getClass().getName() + " - [ getCurrencyList ] - Exit ");
		}catch (Exception e) {
			log
					.error(this.getClass().getName()
						   + " - [getCurrencyList] - Exception -"
						   + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			roleId = null;
		}
		return currrencyListWithAll;
	}

}
