/*
 * @(#)CentralBankMonitorAction.java 1.0 19/03/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.util.CacheManager;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.CentralBankMonitor;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.CentralBankMonitorManager;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.web.form.CentralBankMonitorForm;
import org.swallow.work.web.form.CentralBankRecords;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * CentralBankMonitorAction.java
 *
 * CentralBankMonitorAction class is used for CentralBankMonitor screen
 *
 * <AUTHOR> B
 * @date Mar 19, 2010
 */




import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.work.web.form.MetagroupMonitorForm;

@Scope("prototype")
@Controller
@RequestMapping(value = {"/centralBankMonitor", "/centralBankMonitor.do"})
public class CentralBankMonitorAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("dataerror", "jsp/work/centralmonitorflexdataerror");
		viewMap.put("success", "jsp/work/centralmonitorflexdata");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("options", "jsp/work/centralmonitoroptionsflex");
		viewMap.put("flexobject", "jsp/work/centralmonitorflex");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "flex":
				return flex();
			case "saveColumnWidth":
				return saveColumnWidth();
			case "unspecified":
				return unspecified();
			case "displayCentralMonitorDetails":
				return displayCentralMonitorDetails();
			case "countMovements":
				return countMovements();
			case "displayCentralMonitorOptions":
				return displayCentralMonitorOptions();
			case "saveCentralMonitorDetails":
				return saveCentralMonitorDetails();
			case "deleteCentralMonitorDetails":
				return deleteCentralMonitorDetails();
			case "saveCentralMonitorOptions":
				return saveCentralMonitorOptions();
		}


		return flex();
	}


	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(CentralBankMonitorAction.class);
	/**
	 * An instance of CentralBankMonitorManager to be used across the
	 * application
	 */
	@Autowired
	private CentralBankMonitorManager centralMonitorManager = null;
	/**
	 * menuItemId to be displayed
	 */
	private final String menuItemId = "" + SwtConstants.SCREEN_CENTRALMONITOR;

	/**
	 * @param centralMonitorManager
	 *            the centralMonitorManager to set
	 */
	public void setCentralMonitorManager(
			CentralBankMonitorManager centralMonitorManager) {
		this.centralMonitorManager = centralMonitorManager;
	}

	/**
	 * This method is used across this action class to add the entity into
	 * request
	 *
	 * @param request
	 *            HttpServletRequest request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				  + "- [putEntityListInReq] - Entering ");
		// HttpSession variable to hold the session
		HttpSession session = null;
		// Collection to hold the entity
		Collection coll = null;
		// Get the entity access list, and access list as label value bean
		session = request.getSession();
		coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);
		log
				.debug(this.getClass().getName()
					   + " - [putEntityListInReq] - Exit ");
	}

	/**
	 * Loads the flex object into the client
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flex()
			throws SwtException {
		log.debug(this.getClass().getName() + " - [flex] - Entering ");
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Get the host id from CacheManager
		String hostId = CacheManager.getInstance().getHostId();
		// Get the user id from SwtUtil
		String userId = SwtUtil.getCurrentUserId(request.getSession());
		// Get the screen id from SwtConstants
		String itemId = SwtConstants.CENTRAL_BANK_MONITOR_ID;
		// set the hostId
		request.setAttribute("hostId", hostId);
		// set the userId
		request.setAttribute("userId", userId);
		// set the itemId
		request.setAttribute("itemId", itemId);
		request.setAttribute("method", "flex");
		// Put selected font size in request scope
		this.setFontSize(request);
		log.debug(this.getClass().getName() + " - [flex] - Exit ");
		return getView("flexobject");
	}

	/**
	 * Used for binding the column width into request.
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @param int
	 *            dynamicColumnCount
	 * @param int
	 *            totalColumnCount
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request,
										  String entityId, int dynamicColumnCount, int totalColumnCount) {
		log.debug(this.getClass().getName()
				  + " - [bindColumnWidthInRequest] - Entering ");
		String width = SwtUtil.getPropertyValue(request, entityId, menuItemId,
				"display", "column_width");
		if (width.equalsIgnoreCase("")) { // default condition
			width = "balance=162";
			for (int i = 1; i <= dynamicColumnCount; i++) { // generate the
				// correct number of
				// dynamic width @
				width += ",predictdate" + i + "=140";
			}
		}
		// Instantiate the hash map
		HashMap<String, String> widths = new HashMap<String, String>();
		String[] props = width.split(",");
		for (int i = 0; i < props.length; i++) {
			if (props[i].indexOf("=") != -1) {
				String[] propval = props[i].split("=");
				widths.put(propval[0], propval[1]);
			}
		}
		if (widths.size() < totalColumnCount) { // the numbers of cols required
			// has increased from the number
			// stored
			for (int colNum = widths.size() + 1; colNum <= totalColumnCount; colNum++) {
				int dynamicNum = colNum
								 - (totalColumnCount - dynamicColumnCount); // this
				// absolute column number - number of fixed columns
				widths.put("predictdate" + dynamicNum, "140");
			}
		}
		request.setAttribute("column_width", widths);
		log.debug(this.getClass().getName()
				  + " - [bindColumnWidthInRequest] - Exit ");
	}

	/**
	 * Method to save the column width as per the user preferences
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 */
	public String saveColumnWidth() {
		// String variable to hold the column width
		String width = null;
		// Entity Id
		String entityid = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					  + " - [saveColumnWidth] - Entering ");
			// get the width
			if ((width = request.getParameter("width")) != null
				&& (entityid = request.getParameter("entityid")) != null) {
				SwtUtil.setPropertyValue(request, entityid, menuItemId,
						"display", "column_width", width);
			} else {
				throw new Exception(
						"You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			// set the reply message
			request.setAttribute("reply_message", "Column width saved ok");
			log.debug(this.getClass().getName()
					  + " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		}

		return getView("statechange");
	}

	/**
	 * This method is called when the response is returned to flex screen and
	 * then submitted.
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 *
	 * @return ActionForward
	 *
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {

		log.debug(this.getClass().getName()
				  + " - [unspecified] - Enter/Exit - Call to "
				  + this.getClass().getName()
				  + " - [displayCentralMonitorDetails]");
		return displayCentralMonitorDetails();
	}
	private CentralBankMonitorForm centralMonitor = null;

	public CentralBankMonitorForm getCentralMonitor() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		centralMonitor = RequestObjectMapper.getObjectFromRequest(CentralBankMonitorForm.class, request,"centralMonitor");
		return centralMonitor;
	}


	public void setCentralMonitor(CentralBankMonitorForm centralMonitor) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("centralMonitor", centralMonitor);
		this.centralMonitor = centralMonitor;
	}






	// Start : modified by Balaji for Mantis 1991
	/**
	 * This method calls the Manager class to get the Central bank monitor
	 * details and forwards the request to display it.
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayCentralMonitorDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// instantiate the opTimer
		OpTimer opTimer = null;
		/* Variable Declaration for format */
		SystemFormats format = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		/* Variable Declaration for userId */
		String userId = null;
		/* Variable Declaration for screenId */
		String screenId = null;
		/* Variable Declaration for centralMonitorOption */
		ScreenOption centralMonitorOption = null;
		/* Variable Declaration for scrOption */
		ScreenOption scrOption = null;
		/* Variable Declaration for startDate */
		String startDate = null;
		/* Variable Declaration for endDate */
		String endDate = null;
		/* Variable Declaration for entityId */
		String entityId = null;
		/* Variable Declaration for sessionId */
		String sessionId = null;
		/* Variable Declaration for multiplier */
		String multiplier = null;
		/* Variable Declaration for currencyLimit */
		BigDecimal currencyLimit = null;
		/* Variable Declaration for centralMonitor */
		CentralBankMonitorForm centralMonitor = null;
		/* Variable Declaration for monitorRecords */
		List<CentralBankRecords> monitorRecords = null;
		/* Variable Declaration for entityManager */
		EntityManager entityManager = null;
		/* Variable Declaration for entity */
		Entity entity = null;
		/* Variable Declaration for entityFetched */
		Entity entityFetched = null;
		/* Variable Declaration for session */
		HttpSession session = null;
		/* variable declaration for screenOptionManager */
		ScreenOptionManager screenOptionManager = null;
		/* variable declaration for userSession */
		HttpSession userSession = null;
		/* variable declaration for calDate */
		Calendar calDate = null;
		// Date variable to hold the temporary last date
		Date lastDate = null;
		/* Date variable to hold the lastCalDate object */
		Date lastCalDate = null;
		/* Variable Declaration for daysChanged */
		String daysChanged = null;
		/* Variable Declaration for message */
		String message = null;
		/* Variable Declaration for dynamicColumnCount */
		int dynamicColumnCount = 0;
		/* Variable Declaration for totalColumnCount */
		int totalColumnCount = 0;
		/* Variable Declaration for monitorSize */
		int monitorSize = 0;
		// holds the entityId from session
		String entityIdFromSession = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [displayCentralMonitorDetails] - Enter");
			opTimer = new OpTimer();
			opTimer.start("all");
			/* Assigns the Action Form to DynaValidatorForm */
			centralMonitor = getCentralMonitor();
			// get the current system formats
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Get the start date from the currency monitor bean
			startDate = centralMonitor.getFromDateAsString();
			// Get the end date from currency monitor bean
			endDate = centralMonitor.getToDateAsString();
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			screenId = SwtConstants.CENTRAL_BANK_MONITOR_ID;
			// Initialising the screen option instance
			scrOption = new ScreenOption();
			// Setting the hostId
			scrOption.getId().setHostId(hostId);
			// Setting the userId
			scrOption.getId().setUserId(userId);
			// Setting the screenId
			scrOption.getId().setScreenId(screenId);
			// Creating a ScreenOptionManager instance
			screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Get the central bank monitor options
			centralMonitorOption = screenOptionManager
					.getCentralBankMonitorOptions(scrOption);
			// Get the entity id from the form
			entityId = centralMonitor.getEntityId();
			// If entity id is null, get the entity from user session
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			// Get the last entityId used from session
			entityIdFromSession = (String) request.getSession().getAttribute(
					"centralBankMonitorCurrentEntity");
			// If the entity id from session and the current entity id are
			// different set the start date as null, So the start date will be
			// calculated for the entity considering the day of the week set for
			// the entity and the entity offset time applied
			if (!SwtUtil.isEmptyOrNull(entityIdFromSession)
				&& !entityId.equalsIgnoreCase(entityIdFromSession))
				startDate = null;

			// Set currenct used entity is session
			request.getSession().setAttribute(
					"centralBankMonitorCurrentEntity", entityId);
			// get the session for current user
			userSession = SessionManager.getInstance().getUserSession(
					SwtUtil.getCurrentHostId(request.getSession()), userId);
			if(userSession == null)
				return null;
			// Make an instance of calendar
			calDate = Calendar.getInstance();
			// The following condition is executed when the start date is null
			// The start date is null only when the screen is loaded for the
			// first time
			// All other times, the start date is populated in the form bean
			if (startDate == null) {
				// As the start date is null, get the start day from entity as
				// start date
				startDate = SwtUtil.formatDate(centralMonitorManager
						.getStartDay(entityId), format.getDateFormatValue());
				// Set the start date as calendar date
				calDate.setTime(SwtUtil.parseDate(startDate, format
						.getDateFormatValue()));
				// Add the default days to the calendar
				calDate.add(Calendar.DATE, Integer
												   .parseInt(centralMonitorOption.getNumberOfDays()) - 1);
				// Calculate the endDate and assign it to end date
				lastDate = calDate.getTime();
				endDate = SwtUtil.formatDate(lastDate, format
						.getDateFormatValue());
				// get session in request
				session = request.getSession();
				// Setting the Default days
				session.setAttribute("unchangedDays", centralMonitorOption
						.getNumberOfDays());

			} else {
				// get startdate from centralbankmonitor form
				startDate = centralMonitor.getFromDateAsString();
				// get endDate from centralbankmonitor form
				endDate = centralMonitor.getToDateAsString();
				// get session in request
				session = request.getSession();
				if (session != null) {
					daysChanged = (String) session.getAttribute("daysChanged");
					// check the condition dayschanged is not equal to null
					if (daysChanged != null) {
						// check dayschanged flag is Y
						if (daysChanged.equals("Y")) {

							centralMonitorOption.setNumberOfDays(session
									.getAttribute("changedDays").toString());

							// Set the start date as calendar date
							calDate.setTime(SwtUtil.parseDate(startDate, format
									.getDateFormatValue()));
							// Add the default days to the calendar
							calDate.add(Calendar.DATE, Integer
															   .parseInt(centralMonitorOption
																	   .getNumberOfDays()) - 1);
							lastCalDate = calDate.getTime();
							endDate = SwtUtil.formatDate(lastCalDate, format
									.getDateFormatValue());
							// Setting the Default dayschanged flag is N
							session.setAttribute("daysChanged", "N");
							// Setting the Default days
							session.setAttribute("unchangedDays",
									centralMonitorOption.getNumberOfDays());
						}
					}
				}
			}
			// Setting the values of start date and end date as Date and String
			// values respectively to form
			centralMonitor.setFromDateAsString(startDate);
			// set the from date
			centralMonitor.setFromDate(SwtUtil.parseDate(startDate, format
					.getDateFormatValue()));
			// set the to date string
			centralMonitor.setToDateAsString(endDate);
			// set the to date
			centralMonitor.setToDate(SwtUtil.parseDate(endDate, format
					.getDateFormatValue()));
			// set the entity id in the form
			centralMonitor.setEntityId(entityId);
			// Add the entity list in request
			putEntityListInReq(request);
			// Create an instance of entity manager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			// Create a instance of entity
			entity = new Entity();
			// Set the host id in the entity
			entity.getId().setHostId(CacheManager.getInstance().getHostId());
			// Set the entity id in the entity
			if (entityId != null
				&& !entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				entity.getId().setEntityId(entityId);
			} else {
				// set the current user entity
				entity.getId().setEntityId(
						SwtUtil.getUserCurrentEntity(request.getSession()));
			}
			// get the entity details
			entityFetched = entityManager.getEntityDetail(entity);
			// If Central Bank Parameters is empty then display alert and close
			// window
			if (SwtUtil.isEmptyOrNull(entityFetched.getCentralBankAccount())
				|| entityFetched.getCrrLimitFromDate() == null
				|| entityFetched.getCrrLimitToDate() == null
				|| entityFetched.getCrrLimit() == null
				|| SwtUtil.isEmptyOrNull(entityFetched.getStartDay())) {
				// set the status for reply
				request.setAttribute("reply_status_ok", "false");
				// set the reply message
				request.setAttribute("reply_message", SwtUtil
						.getMessage(SwtConstants.ALERT_START_CENTRALMONITOR, request));
				// set the close window flag true
				request.setAttribute("close_window", "true");
				return getView("dataerror");
			}
			// get the currency limit
			currencyLimit = entityFetched.getCrrLimit();
			// set the currency limit
			centralMonitor.setCurrencyLimit(SwtUtil.formatCurrency(
					entityFetched.getDomesticCurrency(), currencyLimit));
			// set the session Id
			sessionId = centralMonitor.getCurrTimeStamp() + userId
						+ userSession.getId();
			// Call the CentralBankMonitorManagerImpl and get the central
			// monitor details
			monitorRecords = centralMonitorManager
					.getCentralBankMonitorDetails(entityId, entityFetched
									.getCentralBankAccount(), currencyLimit, sessionId,
							userId, startDate, endDate, format, opTimer,
							centralMonitorOption.getHideWeekends());
			// Checking the monitor records is null
			if (monitorRecords != null) {
				if (monitorRecords.size() > 0) {
					// get the monitor size
					monitorSize = monitorRecords.get(0).getCentralMonitor()
							.size();
					// get the multiplier type
					multiplier = monitorRecords.get(0).getMultiplierType();
				}
			} else {
				monitorRecords = new ArrayList<CentralBankRecords>();
				// set jobFlagStatus in request
				request.setAttribute("jobFlagStatus", SwtConstants.YES);
			}
			// set the central bank records
			centralMonitor.setMonitorRecords(monitorRecords);

			// set the centralMonitor form
			setCentralMonitor(centralMonitor);
			request.setAttribute("centralMonitor", centralMonitor);
			// Put selected font size in request scope
			this.setFontSize(request);
			if (entityFetched.getCrrLimitFromDate() != null) {
				// set the entity form date
				request.setAttribute("entityFromDate", SwtUtil.formatDate(
						entityFetched.getCrrLimitFromDate(), SwtUtil
								.getCurrentDateFormat(request.getSession())));
			} else {
				// set the entity form date
				request.setAttribute("entityFromDate", "");
			}
			if (entityFetched.getCrrLimitToDate() != null) {
				// set the entity to date
				request.setAttribute("entityToDate", SwtUtil.formatDate(
						entityFetched.getCrrLimitToDate(), SwtUtil
								.getCurrentDateFormat(request.getSession())));
			} else {
				// set the entity to date
				request.setAttribute("entityToDate", "");
			}
			// dynamic Column count
			dynamicColumnCount = new Integer(monitorSize);
			// get total no of column
			totalColumnCount = new Integer(monitorSize + 1);
			// set request attribute for multiplier
			request.setAttribute("multiplier", multiplier == null ? ""
					: multiplier);

			// set autoRefreshRate
			if (centralMonitorOption.getPropertyValue() != null) {
				request.setAttribute("autoRefreshRate", new Integer(
						centralMonitorOption.getPropertyValue()));
			} else {
				request.setAttribute("autoRefreshRate", new Integer(
						PropertiesFileLoader.getInstance().getPropertiesValue(
								SwtConstants.DEFAULT_REFRESH_RATE)));
			}
			// set the default days
			request.setAttribute("defaultDays", new Integer(
					centralMonitorOption.getNumberOfDays()));
			// call the bind column width
			this.bindColumnWidthInRequest(request, entityId,
					dynamicColumnCount, totalColumnCount);
			// set the Domestic Currency
			request.setAttribute("currencyCode", entityFetched
					.getDomesticCurrency());
			// set the account Id for entity
			request.setAttribute("accountId", entityFetched
					.getCentralBankAccount());
			// set the reply status
			request.setAttribute("reply_status_ok", "true");
			// set the reply message
			request.setAttribute("reply_message", "Data fetch ok");
			opTimer.stop("all");
			// set request attribute for opTimes
			request.setAttribute("opTimes", opTimer.getOpTimes());
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			log.debug(this.getClass().getName()
					  + " - [displayCentralMonitorDetails] - Exit");
		} catch (SwtException e) {
			message = e.getStackTrace()[0].getClassName() + "."
					  + e.getStackTrace()[0].getMethodName() + ":"
					  + e.getStackTrace()[0].getLineNumber() + " "
					  + e.getMessage();
			log.error(this.getClass().getName()
					  + " - [displayCentralMonitorDetails] - Exception -"
					  + message);
			saveErrors(request, SwtUtil.logException(e, request, ""));
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} catch (Exception e) {
			message = e.getStackTrace()[0].getClassName() + "."
					  + e.getStackTrace()[0].getMethodName() + ":"
					  + e.getStackTrace()[0].getLineNumber() + " "
					  + e.getMessage();
			log.error(this.getClass().getName()
					  + " - [displayCentralMonitorDetails] - Exception -"
					  + message);
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayCentralMonitorDetails",
					CentralBankMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());

			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} finally {
			// null the objects created already
			opTimer = null;
			format = null;
			hostId = null;
			userId = null;
			screenId = null;
			centralMonitorOption = null;
			startDate = null;
			endDate = null;
			entityId = null;
			sessionId = null;
			currencyLimit = null;
			centralMonitor = null;
			monitorRecords = null;
			entityManager = null;
			entity = null;
			entityFetched = null;
			userSession = null;
			calDate = null;
			daysChanged = null;
			lastCalDate = null;
			message = null;
			entityIdFromSession = null;
		}
		return getView("success");
	}

	// End : modified by Balaji for Mantis 1991

	/**
	 * The method returns the no Of movements present for the entity, currency
	 * and date selected. This method is called from the central bank monitor
	 * screen. Click on break down by "Movements" and click any currency date
	 * wise various balance.
	 *
	 * @param mapping
	 *            Action Mapping
	 * @param form
	 *            ActionForm
	 * @param request
	 *            HttpServletRequest
	 * @param response
	 *            HttpServletResponse
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String countMovements()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Variable declaration
		// String variable to hold the number of movements
		String noOfMovmts = null;
		// String variable to hold the host id
		String hostId = null;
		// String variable to hold the entity id
		String entityId = null;
		// String variable to hold the currency code
		String currencyCode = null;
		// String variable to hold the value date as String
		String valueDateAsString = null;
		// String variable to hold the break down
		String breakdown = null;
		// String variable to hold the account Id
		String accountId = null;
		// String variable to hold the balance name
		String balanceName = null;
		// Date variable to hold the value date
		Date valueDate = null;
		try {
			log
					.debug(this.getClass().getName()
						   + " - [countMovements] - Entry");
			// Get the current host id from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Get the entity id from request
			entityId = request.getParameter("entityId");
			// Get the currency code from request
			currencyCode = request.getParameter("currencyCode");
			// Get the value date from request
			valueDateAsString = request.getParameter("valueDate");
			// Get the account Id from request
			accountId = request.getParameter("accountId");
			// Get the break down from request
			breakdown = request.getParameter("breakdown");
			// Get the balance name from request
			balanceName = request.getParameter("balanceName");
			// Check if entity id and currency code and value date is not null
			if ((entityId != null) && (currencyCode != null)
				&& (valueDateAsString != null)) {
				// Get the value date from the value date as string
				valueDate = SwtUtil.parseDateGeneral(valueDateAsString);
				// Get the number of movements by calling the
				// CurrencyMonitorNewManagerImpl
				noOfMovmts = centralMonitorManager.getNoOfMovements(hostId,
						entityId, currencyCode, accountId, balanceName,
						valueDate, breakdown)
							 + "";
			}

			// Write back the number of movements into response
			response.getWriter().print(Integer.parseInt(noOfMovmts));

			log.debug(this.getClass().getName() + " - [countMovements] - Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + " - [countMovements] - SwtException -"
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - [countMovements] - GenericException -"
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "countMovements", CentralBankMonitorAction.class),
					request, "");

			return null;
		} finally {
			// null the objects created. Though this may not improve the
			// performance drastically, it makes some difference.
			noOfMovmts = null;
			hostId = null;
			entityId = null;
			currencyCode = null;
			valueDateAsString = null;
			breakdown = null;
		}

		return null;
	}
	private ScreenOption centralMonitorOptions;

	public ScreenOption getCentralMonitorOptions() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		centralMonitorOptions = RequestObjectMapper.getObjectFromRequest(ScreenOption.class, request,"centralMonitorOptions");
		return centralMonitorOptions;
	}


	public void setCentralMonitorOptions(ScreenOption centralMonitorOptions) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("centralMonitorOptions", centralMonitorOptions);
		this.centralMonitorOptions = centralMonitorOptions;
	}





	/**
	 * This method is used to display existing options for the central
	 * monitor(if any) Click on the "Options" button from the central bank
	 * monitor screen
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 *
	 * @return ActionForward
	 *
	 * @throws SwtException
	 */
	public String displayCentralMonitorOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// String variable to hold host id
		String hostId = null;
		// String variable to hold user id
		String userId = null;
		// String variable to hold screen id
		String screenId = null;
		ScreenOption centralMonitorOption = null;
		ScreenOption scrOption = null;
		// Entity Manager
		EntityManager entityManager = null;
		// Entity object holder
		Entity entity = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [displayCentralMonitorOptions] - Entry");
			// Get instance of entityManager from SwtUtil
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			// Create a instance of Entity
			entity = new Entity();
			// Setting the host id in newly entity object
			entity.getId().setHostId(CacheManager.getInstance().getHostId());
			// Setting entity id in the newly created entity
			entity.getId().setEntityId(request.getParameter("entityId"));
			// Get the entity detail by calling getEntityDetail in
			// EntityManagerImpl
			entity = entityManager.getEntityDetail(entity);
			// Get host id from the CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get screen id from SwtConstants
			screenId = SwtConstants.CENTRAL_BANK_MONITOR_ID;
			// Initialising the screen option instance
			scrOption = new ScreenOption();
			// Setting the hostId
			scrOption.getId().setHostId(hostId);
			// Setting the userId
			scrOption.getId().setUserId(userId);
			// Setting the screenId
			scrOption.getId().setScreenId(screenId);
			// Creating a ScreenOptionManager instance
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			centralMonitorOption = screenOptionManager
					.getCentralBankMonitorOptions(scrOption);
			// Set the values
			setCentralMonitorOptions(centralMonitorOption);
			request.setAttribute("centralMonitorOptions", this.centralMonitorOptions);
			// Put selected font size in request scope
			this.setFontSize(request);
			// set the currency Limit as string
			if (entity.getCrrLimit() != null) {
				entity.setCrrLimitAsString(SwtUtil.formatCurrency(entity
						.getDomesticCurrency(), entity.getCrrLimit()));
			} else {
				entity.setCrrLimitAsString("");
			}
			// set the from date
			if (entity.getCrrLimitFromDate() != null) {
				entity.setCrrLimitFromDateAsString(SwtUtil.formatDate(entity
						.getCrrLimitFromDate(), SwtUtil
						.getCurrentDateFormat(request.getSession())));
			} else {
				entity.setCrrLimitFromDateAsString("");
			}
			// set the to date
			if (entity.getCrrLimitToDate() != null) {
				entity.setCrrLimitToDateAsString(SwtUtil.formatDate(entity
						.getCrrLimitToDate(), SwtUtil
						.getCurrentDateFormat(request.getSession())));
			} else {
				entity.setCrrLimitToDateAsString("");
			}
			// set the currencyLimit
			request.setAttribute("currencyLimit", entity.getCrrLimitAsString());
			// set the limit from date
			request.setAttribute("limitFrom", entity
					.getCrrLimitFromDateAsString());
			// set the limit to date
			request.setAttribute("limitTo", entity.getCrrLimitToDateAsString());
			// Set the request attributes
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Data fetch ok");
			log.debug(this.getClass().getName()
					  + " - [displayCentralMonitorOptions] - Exit");
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					  + " - [displayCentralMonitorOptions] - SwtException -"
					  + e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());

			return getView("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [displayCentralMonitorOptions] - GenericException -"
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayCentralMonitorOptions",
					CentralBankMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());

			return getView("dataerror");
		} finally {
			// null all the objects except the dyForm
			hostId = null;
			userId = null;
			screenId = null;
			centralMonitorOption = null;
			entityManager = null;
			entity = null;
		}
		return getView("options");
	}

	/**
	 * Method is used to save the CentralBank Monitor Details
	 *
	 * @param mapping
	 *            ActionMapping
	 * @param form
	 *            ActionForm
	 * @param request
	 *            HttpServletRequest
	 * @param response
	 *            HttpServletResponse
	 * @return a Result of type Action forward
	 */
	public String saveCentralMonitorDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// value date
		String valueDateStr = null;
		// Amount
		String amount = null;
		// user Id
		String userId = null;
		// user SessionId
		String userSessionId = null;
		// System Formats
		SystemFormats format = null;
		// CentralBankMonitor
		CentralBankMonitor centralMonitor = null;
		String entityId = null;
		// result Flag
		String resultFlag = null;
		// Check for save or update operation
		Boolean isCheckNeeded = false;
		// Amount
		String valueBeforeEdit = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [saveCentralMonitorDetails] - Entry");
			// Instantiate the centralMonitor
			centralMonitor = new CentralBankMonitor();
			// get the value date
			valueDateStr = request.getParameter("valueDate");
			// get the amount
			amount = request.getParameter("amount");
			// Gets the entityId, valueBeforeEdit , isCheckNeeded from request
			entityId = request.getParameter("entityId");
			valueBeforeEdit = request.getParameter("valueBeforeEdit");
			isCheckNeeded = Boolean.parseBoolean(request
					.getParameter("isCheckNeeded"));
			// get the current user Id
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the session for current user
			HttpSession userSession = SessionManager.getInstance()
					.getUserSession(
							SwtUtil.getCurrentHostId(request.getSession()),
							userId);
			// get the session Id
			userSessionId = userSession.getId();
			// get the system format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			centralMonitor.getId().setEntityId(entityId);
			centralMonitor.getId().setUserId("SYSTEM");
			// set the value date
			centralMonitor.getId().setValueDate(
					SwtUtil
							.parseDate(valueDateStr, format
									.getDateFormatValue()));
			// set the amount
			centralMonitor
					.setAmount(SwtUtil.isEmptyOrNull(amount) ? new BigDecimal(
							0.0) : SwtUtil.parseCurrencyBig(amount, format
							.getCurrencyFormat()));
			centralMonitor.setAmtBeforeEdit(SwtUtil
					.isEmptyOrNull(valueBeforeEdit) ? new BigDecimal(0.0)
					: SwtUtil.parseCurrencyBig(valueBeforeEdit, format
					.getCurrencyFormat()));
			centralMonitor.setUpdateDate(SwtUtil.getSystemDatewithoutTime());
			centralMonitor.setUpdateUser(userId);
			centralMonitor.setIsCheckNeeded(isCheckNeeded);
			// save the central bank monitor details .
			resultFlag = centralMonitorManager
					.saveCentralBankMonitorDetails(centralMonitor);
			// set the result to response writer
			response.getWriter().print(resultFlag);
			log.debug(this.getClass().getName()
					  + " - [saveCentralMonitorDetails] - Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + " - [saveCentralMonitorDetails] - SwtException -"
					  + swtexp.getMessage());
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - [saveCentralMonitorDetails] - GenericException -"
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveCentralMonitorDetails",
					CentralBankMonitorAction.class), request, "");
		} finally {
			// nullify objects
			valueDateStr = null;
			amount = null;
			userId = null;
			userSessionId = null;
			format = null;
			centralMonitor = null;
		}
		return null;
	}

	/**
	 * Method is used to delete the CentralBank Monitor Details when window is
	 * closed
	 *
	 * @param mapping
	 *            ActionMapping
	 * @param form
	 *            ActionForm
	 * @param request
	 *            HttpServletRequest
	 * @param response
	 *            HttpServletResponse
	 * @return a Result of type Action forward
	 */
	public String deleteCentralMonitorDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// current TimeStamp
		String currTimeStamp = null;
		// user Id
		String userId = null;
		// user SessionId
		String userSessionId = null;
		// CentralBankMonitor
		CentralBankMonitor centralMonitor = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [deleteCentralMonitorDetails] - Entry");
			// Instantiate the centralMonitor
			centralMonitor = new CentralBankMonitor();
			// get the current timestamp
			currTimeStamp = request.getParameter("currTimeStamp");
			// get the current user Id
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the session for current user
			HttpSession userSession = SessionManager.getInstance()
					.getUserSession(
							SwtUtil.getCurrentHostId(request.getSession()),
							userId);
			// get the session Id
			userSessionId = userSession.getId();
			// delete the central bank monitor details
			centralMonitor.setSessionId(userSessionId);
			centralMonitorManager
					.deleteCentralBankMonitorDetails(centralMonitor);
			log.debug(this.getClass().getName()
					  + " - [deleteCentralMonitorDetails] - Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + " - [deleteCentralMonitorDetails] - SwtException -"
					  + swtexp.getMessage());
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - [deleteCentralMonitorDetails] - GenericException -"
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "deleteCentralMonitorDetails",
					CentralBankMonitorAction.class), request, "");
		} finally {
			// nullify objects
			currTimeStamp = null;
			userId = null;
			userSessionId = null;
			centralMonitor = null;
		}
		return null;
	}

	/**
	 * This method is used to save the options or preferences in central bank
	 * monitor screen. From central bank monitor screen, click on "Options"
	 * set/reset the preference and "Save". This method is called on the save
	 * action.
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveCentralMonitorOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// ScreenOption object
		ScreenOption centralBankMonitorOption = null;
		// String object to hold hostId
		String hostId = null;
		// String object to hold userId
		String userId = null;
		// String object to hold screenId
		String screenId = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [saveCentralMonitorOptions] - Entry");
			// Object for DynaValidatorForm
			centralBankMonitorOption = getCentralMonitorOptions();
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get screen id from SwtConstants
			screenId = SwtConstants.CENTRAL_BANK_MONITOR_ID;
			// Set the host id in the ScreenOption form
			centralBankMonitorOption.getId().setHostId(hostId);
			// Set the user id in ScreenOption form
			centralBankMonitorOption.getId().setUserId(userId);
			// Set the screen id in ScreenOption form
			centralBankMonitorOption.getId().setScreenId(screenId);
			// Creating a ScreenOptionManager instance
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Save the central monitor option changes, by calling the
			// ScreenOptionManagerImpl
			screenOptionManager
					.saveCentralBankMonitorOptions(centralBankMonitorOption);
			// Set the request attribute to refresh the central bank monitor
			// screen
			// for refresh
			request.setAttribute("parentFormRefresh", "Y");

			Integer changedDays = Integer.parseInt(centralBankMonitorOption
					.getNumberOfDays());
			HttpSession session = request.getSession(false);
			Integer unchangedDays = Integer.parseInt((String) session
					.getAttribute("unchangedDays"));

			session.setAttribute("changedDays", changedDays);

			if (!unchangedDays.equals(changedDays))
				session.setAttribute("daysChanged", "Y");
			else
				session.setAttribute("daysChanged", "N");

			setCentralMonitorOptions(centralBankMonitorOption);
			request.setAttribute("centralMonitorOptions", centralBankMonitorOption);

			log.debug(this.getClass().getName()
					  + " - [saveCentralMonitorOptions] - Exit");
			return getView("options");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + " - [saveCentralMonitorOptions] - SwtException -"
					  + swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("success");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - [saveCentralMonitorOptions] - GenericException -"
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveCentralMonitorOptions",
					CentralBankMonitorAction.class), request, "");
			return getView("fail");
		} finally {
			// null all objects except ActionErrors and DynaValidatorForm
			centralBankMonitorOption = null;
			hostId = null;
			userId = null;
			screenId = null;
		}
	}
	/**
	 * This method gets font size for the user and the screen from database and
	 * put it in request scope.
	 *
	 * @param request
	 * @throws SwtException
	 * <AUTHOR>
	 * @updated Imed B
	 */
	private void setFontSize(HttpServletRequest request) throws SwtException {
		// To get font size (user preference)
		ScreenOptionManager screenOptionManager = null;
		// To hold font size (user preference)
		ScreenOption screenOption = null;
		// To build error message
		String errorMessage = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [setFontSize] - Entry");

			// Initialize object to get font size
			screenOption = new ScreenOption();
			// Set properties to get font size (user preferennce)
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			screenOption.getId().setUserId(
					SwtUtil.getCurrentUserId(request.getSession()));
			screenOption.getId().setScreenId(SwtConstants.CENTRAL_BANK_MONITOR_ID);
			// Get implementation of ScreenOptionManager from spring context
			screenOptionManager = (ScreenOptionManager) SwtUtil
					.getBean("screenOptionManager");
			// Gets the font size for central bank monitor screen and put the same
			// in request scope
			screenOption = screenOptionManager.getFontSize(screenOption);
			request.setAttribute("fontSize", screenOption.getPropertyValue());
		} catch (SwtException ex) {
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "."
						   + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " "
						   + ex.getMessage();
			// log error message
			log.error(this.getClass().getName()
					  + " - [setFontSize] - Exception -" + errorMessage);
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "."
						   + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " "
						   + ex.getMessage();
			// log error message
			log.error(this.getClass().getName()
					  + " - [setFontSize] - Exception -" + errorMessage);
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"setFontSize", CentralBankMonitorAction.class);
		} finally {
			// nullify objects
			screenOptionManager = null;
			screenOption = null;
			errorMessage = null;
			// log debug message
			log.debug(this.getClass().getName() + "- [setFontSize] - Exit");
		}
	}
}
