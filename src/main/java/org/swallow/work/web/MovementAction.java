/*
 * @(#)MovementAction.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.RoleTO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityPositionLevel;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.work.model.CrossReference;
import org.swallow.work.model.MatchDriver;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementExt;
import org.swallow.work.model.MovementMessage;
import org.swallow.work.model.MovementNote;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.PreAdviceInputManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.TokenHelper;

import org.swallow.work.web.form.MetagroupMonitorForm;

/**
 * <pre>
 *
 * This MovementAction class is used to perform movement related actions
 *  - display movement for given movement id.
 * 	- generate the new movement
 * 	- update the old movement details.
 * 	- open to display the movement message
 * 	- used to update the movement status to reconciled
 * 	- used to update the movement status to open/unopen
 *  - open to display the Cross Reference for corresponding movement
 *
 *   Modified by Karthik on 15-Nov-2011
 * </pre>
 *
 */




import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/movement", "/movement.do"})
public class MovementAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("alert", "jsp/work/enhancedalert");
		viewMap.put("success", "jsp/work/movement");
		viewMap.put("crossReference", "jsp/work/crossreferencedisplay");
		viewMap.put("errorDisplay", "jsp/errorDisplay");
		viewMap.put("rollover", "jsp/work/movementrollover");
		viewMap.put("movMessage", "jsp/work/movementmessagedisplay");
		viewMap.put("copyFrom", "jsp/work/copyfrommanualinput");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "displayMovement":
				return displayMovement();
			case "add":
				return add();
			case "update":
				return update();
			case "displayListByEntity":
				return displayListByEntity();
			case "copyFrom":
				return copyFrom();
			case "save":
				return save();
			case "copy":
				return copy();
			case "showMovementDetails":
				return showMovementDetails();
			case "clearSession":
				return clearSession();
			case "mvmntMessageDisplay":
				return mvmntMessageDisplay();
			case "updateMatchStatusToReconciled":
				return updateMatchStatusToReconciled();
			case "updateOpenUnopenFlag":
				return updateOpenUnopenFlag();
			case "checkMovementId":
				return checkMovementId();
			case "change":
				return change();
			case "getMvmntMessageCount":
				return getMvmntMessageCount();
			case "checkMovementStatus":
				return checkMovementStatus();
			case "crossReference":
				return crossReference();
			case "checkExternalPositionLevels":
				return checkExternalPositionLevels();
			case "getAccountsList":
				return getAccountsList();
			case "getBookCodeList":
				return getBookCodeList();
			case "openAlerting":
				return openAlerting();
		}


		return unspecified();
	}





	private Movement movement;
	public Movement getMovement() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		movement = RequestObjectMapper.getObjectFromRequest(Movement.class, request, "movement");
		return this.movement;
	}
	public void setMovement(Movement movement) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		// Remove the existing attribute if it exists
		request.removeAttribute("movement");

		// Set the new attribute
		request.setAttribute("movement", movement);
		this.movement = movement;


	}

	/**
	 * Reference Variable of Movement Manager
	 */
	@Autowired
	private MovementManager movementManager;

	/**
	 * Reference variable of log factory
	 */
	private Log log = LogFactory.getLog(MovementAction.class);

	/**
	 * @param movementManager
	 *            The movementManager to set.
	 */
	public void setMovementManager(MovementManager movementManager) {
		this.movementManager = movementManager;
	}

	/**
	 *
	 * This action method is invoked while opening the full input & movement
	 * display screen and based on the manual input param it will return the
	 * action forward
	 *
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To hold manual input parameter
		String manualInput = null;
		/* Variable Declaration for token. */
		String token = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [unspecified]  - Entering ");
			// get the manual input from request
			manualInput = request.getParameter("inputManual");
			// Setting required attributes
			if (manualInput.equalsIgnoreCase("y")) {
				// set the identifier for which screen is opened in request and
				// set notes details in session
				request.getSession().setAttribute("sessionNotesDetails", null);
				request.setAttribute("screenIdentifier", "manualInput");
				/*
				 * Generate token and assign token value in session attribute
				 * for later validation.
				 */
				token  = TokenHelper.setToken();
				request.getSession().setAttribute(TokenHelper.TOKEN_NAME_FIELD,
						token);
				// mapping for manual input screen
				return add();
			} else {
				if ((!SwtUtil.isEmptyOrNull(request.getParameter("calledFrom")))
					&& (request.getParameter("calledFrom").equals("generic"))){
					return displayMovement();
				}else{
					// set the identifier for which screen is opened in request
					request.setAttribute("screenIdentifier", "movementDisplay");
					// mapping for movement display screen
					return display();
				}
			}
//				log
//						.debug(this.getClass().getName()
//								+ " - [unspecified] - Existing");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'unspecified' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'unspecified' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "unspecified", MovementAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify object
			manualInput = null;
			token = null;

		}

	}

	/**
	 *
	 * This method is called from unspecified action method and used to display
	 * the movement display screen with empty movement details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Movement instance
		Movement movement = null;
		// Form Validator
		// DynaValidatorForm dynaForm = null;
		// collection for MovementNote
		Collection<MovementNote> sessionNotesDetails = null;
		try {
			log.debug(this.getClass().getName() + " - [display] - Entering");
			// Instantiate Movement object
			movement = new Movement();
			// get the // DynaValidatorForm
			// set the empty string value for predict,extract,external balance
			// status & movement type in the movement bean its used to maintain
			// no selection for
			// these radio button in screen
			movement.setPredictStatus("");
			movement.setMovementType("");
			movement.setExtractStatus("");
			movement.setExtBalStatus("");
			// set the screenFieldsStatus for movement display screen which is
			// used
			// to disable the component .
			request.setAttribute("screenFieldsStatus", "true");
			// set the enable/disable status for
			// change,open,match,notes,log,message,close button in movement
			// display
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			// based on showMovementId flag the components will be created for
			// movement display
			request.setAttribute("showMovementId", "yes");
			// get the session notes details from the session
			sessionNotesDetails = (Collection<MovementNote>) request
					.getSession().getAttribute("sessionNotesDetails");
			// remove the session notes details from the session
			if (sessionNotesDetails != null) {
				request.getSession().removeAttribute("sessionNotesDetails");
			}
			// set the attribute to identify which screen is opened
			request.setAttribute("screenIdentifier", "movementDisplay");
			// set the movement bean into form to display the form values.
			setMovement(movement);
			// set the access for menu ,entity, currency group
			getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName() + " - [display] - Existing");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'display' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MovementAction.'display' method : "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", MovementAction.class), request, "");

			return getView("fail");
		} finally {
			// nullify objects
			movement = null;
			sessionNotesDetails = null;
		}
	}

	/**
	 * This method is invoked while press enter/tab key on movement id text box
	 * in movement display screen after enter valid movement id and used to
	 * display the details of entered movement
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayMovement() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// dyna validator form to get and set the form values
		// DynaValidatorForm dynaForm = null;
		// To hold Movement Id as String
		String movementIdAsString = null;
		// To hold the Host Id for application
		String hostId = null;
		// To hold Entity Id
		String entityId = null;
		// Movement bean used to get the form values
		Movement movement = null;
		// position level collection
		Collection<String> posLevelColl = null;
		// used to iterate the position level collection
		Iterator<String> posLevelItr = null;
		// used to set the movement details
		Movement movDetail = null;
		// variable to hold role id
		String roleId = null;
		// To get Entity Access list
		Collection<EntityUserAccess> entityUserAccessList = null;
		// To iterate entity access list collection
		Iterator<EntityUserAccess> entityUserAccessItr = null;
		// To get EntityUserAccess
		EntityUserAccess entityUserAccess = null;
		// To Get Entity Access
		String currEntityOrCurrencyAccess = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [displayMovement] - Entering");
			// get the DynaValidatorForm to get all form values
			// get the movement details from form
			movement = (Movement) getMovement();
			// get the movement id to display the details
			movementIdAsString = movement.getId().getMovementIdAsString();
			// get movement id from request
			if (movementIdAsString == null) {
				movementIdAsString = (!SwtUtil.isEmptyOrNull(request
						.getParameter("selectedMovementId"))) ? request
						.getParameter("selectedMovementId").trim() : "0";
			}
			// get the host id
			hostId = CacheManager.getInstance().getHostId();
			// get the movement details for entered movement
			movement = movementManager.getMovementDetails(hostId, new Long(
					movementIdAsString), new SystemInfo(), SwtUtil
					.getCurrentSystemFormats(request.getSession()));
			// Gets the role id
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the entityId which is used to get the position
			// level,paychannel,party collection
			if (movement != null) {
				if(SwtUtil.isEmptyOrNull(movement.getIlmFcastStatus())){
					movement.setIlmFcastStatus("E");
				}
				entityId = movement.getId().getEntityId();
				// set the attribute to currGrpAccess
				request.setAttribute("currGrpAccess", SwtUtil
						.getSwtMaintenanceCache().getCurrencyAccess(roleId,
								movement.getId().getEntityId(),
								movement.getCurrencyCode()));
			}
			// Get the role entity user access list
			entityUserAccessList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(new RoleTO(roleId));
			// Set default value as no
			currEntityOrCurrencyAccess = SwtConstants.NO;
			// Checks entity user access list
			if (movement != null && entityUserAccessList != null) {
				// Gets the iterator
				entityUserAccessItr = entityUserAccessList.iterator();
				while (entityUserAccessItr.hasNext()) {
					entityUserAccess = entityUserAccessItr.next();
					// Checks current entity id match with user accessible
					// entity list
					if (entityUserAccess.getEntityId().equals(entityId)) {
						currEntityOrCurrencyAccess = SwtConstants.YES;
						break;
					} else {
						currEntityOrCurrencyAccess = SwtConstants.NO;
					}
				}
				// Check currency access
				if (currEntityOrCurrencyAccess == SwtConstants.YES
					&& SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
						roleId, movement.getId().getEntityId(),
						movement.getCurrencyCode()) == SwtConstants.CURRENCYGRP_NO_ACCESS) {
					currEntityOrCurrencyAccess = SwtConstants.NO;
				}
			}
			// check movement is null
			if (movement != null
				&& currEntityOrCurrencyAccess.equals(SwtConstants.YES)) {
				// set the button info
				setButtonInfo(request,  movement);
				// set the movement into dyForm to display the movement details
				setMovement(movement);
				// set the posting date for movement
				movement.setPostingDateAsString((SwtUtil.formatDate(movement
						.getPostingDate(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue())));
				// set the ExpectedSettlementDateTime for movement
				movement.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(movement
						.getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
				// set the SettlementDateTime for movement
				movement.setSettlementDateTimeAsString((SwtUtil.formatDate(movement
						.getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
				// put the position level,entity,sign,pay
				// channel,description,party list in request to populate the
				// corresponding select box in movement display screen.
				putPositionLevelListInReq(request, entityId, movement
						.getInputSource());
				putEntityListInReq(request);
				putSignListInReq(request);
				putDescriptionsInReq(request, movement);
				putPartyListInReq(request, hostId, entityId, movement
								.getCounterPartyId(), movement.getBeneficiaryId(),
						movement.getCustodianId(), movement.getMatchingParty(),movement.getOrderingCustomerId(), movement.getOrderingInstitutionId(),
						movement.getSenderCorrespondentId(), movement.getReceiverCorrespondentId(), movement.getIntermediaryInstitutionId(),
						movement.getAccountWithInstitutionId(), movement.getBeneficiaryCustomerId());

				// get the position level collection for corresponding host id
				// and entity id.
				posLevelColl = getExternalPositionLevels(hostId, entityId);
				if ((posLevelColl != null) && (posLevelColl.size() > 0)) {
					// Iterate the postion level
					posLevelItr = posLevelColl.iterator();
					while (posLevelItr.hasNext()) {
						// check the collection of position level match with
						// movement position level then set the show external
						// flag
						if (posLevelItr.next().equalsIgnoreCase(
								movement.getPositionLevelAsString())) {
							request.setAttribute("showExternal", "Y");
							break;
						}
					}
				}

			} else {
				// set the entityId value to 'All' if entered movement is not
				// present.
				entityId = "All";
				if (movement == null) {
					// set the movementAvailable flag into request to display
					// the
					// alert message to user about movement is not in file
					request.setAttribute("movementAvailable", "no");
				} else if (currEntityOrCurrencyAccess.equals(SwtConstants.NO)) {
					// set the movementAccessAvailable flag into request to
					// display the
					// alert message to user about movement is not accessible
					request.setAttribute("movementAccessAvailable", "no");
				}
				// Instantiate the Movement
				movDetail = new Movement();
				// set the invalid movement id
				movDetail.getId().setMovementIdAsString(movementIdAsString);
				// set the movement into dyna Form to set the form values
				setMovement(movDetail);
				// set the enable/disable status for
				// change,open,match,notes,log,message,close button in movement
				// display
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
				// put the position level,entity,sign,pay
				// channel list in request to populate the
				// corresponding select box in movement display screen.
				putPositionLevelListInReq(request, entityId, null);
				putEntityListInReq(request);
				putSignListInReq(request);
			}
			// set the attribute for parentScreen in request which is used to
			// identify the parent of this screen
			if (!SwtUtil.isEmptyOrNull(request.getParameter("parentScreen"))) {
				// set the parentScreen into request
				request.setAttribute("parentScreen", request
						.getParameter("parentScreen"));
			}

			// set the method name for form
			request.setAttribute("methodName", "displayMovement");
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "yes");
			// set the screenFieldsStatus for movement display screen which is
			// used to disable the component .
			request.setAttribute("screenFieldsStatus", "true");
			// set the attribute for openUnopenFlag in request based on this
			// flag display the open or unopen button
			if (movement != null) {
				request.setAttribute(SwtConstants.OPEN_UNOPEN_FLAG, movement
						.getOpenFlag());
			}
			// set the attribute to identify which screen is opened
			request.setAttribute("screenIdentifier", "movementDisplay");
			log.debug(this.getClass().getName()
					  + " - [displayMovement] - Existing");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'displayMovement' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'displayMovement' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayMovement", MovementAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			movementIdAsString = null;
			hostId = null;
			entityId = null;
			movement = null;
			posLevelColl = null;
			posLevelItr = null;
			movDetail = null;
			roleId = null;
			entityUserAccessList = null;
			entityUserAccessItr = null;
			entityUserAccess = null;
			currEntityOrCurrencyAccess = null;
		}
	}

	/**
	 * This method is called from unspecified action method and used to display
	 * the full input screen which is used to create a new movement
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Instance the Entity object
		Entity entity = null;
		// Variable to hold position
		String position = null;
		// boolean to hold position flag
		boolean positionFlag;
		// decleartion of dyForm to get the dyanamic page
		// DynaValidatorForm dynaForm = null;
		// decleartion entityID
		String entityId = null;
		// Variable top hold the current hostId
		String hostId = null;
		// Variable top hold the MovementId
		String movementIdAsString = null;
		// Variable to hold the pojo class
		Movement movInitial = null;
		// Variable to hold the currentcurreny code
		String ccyCodeInForm = null;
		// Variable to hold currency code
		String currencyCode = null;
		// Collection to hold the postion level
		Collection<LabelValueBean> collPositionLvl = null;
		// Label value bean for Position level
		LabelValueBean lblPositionLvl = null;
		// Varaiable to hold the access values
		int accessInd;
		// Pojo class to get the Sytem information
		SystemInfo systemInfo = null;
		// Variable to hold the MovementID
		Long movementId = null;
		// variable decleration for the pojo class
		Movement movement = null;
		// variable to hold the counterPartyId
		String counterPartyId = null;
		// variable to hold the beneficiaryId
		String beneficiaryId = null;
		// variable to hold the custodianId
		String custodianId = null;
		// variable to hold the matchingParty
		String matchingParty = null;
		// Variable is declared to store the saved Movement Id
		String savedMovementId = null;
		// Flag declered to determine the currency access
		boolean currAccessFlag;
		// Collection declared get the currencies in the list
		Collection<LabelValueBean> currencyColl = null;
		// Collection to hold the entity
		Collection<LabelValueBean> collEntity = null;
		// check the availability for entity
		boolean entityAvailable;
		try {
			log.debug(this.getClass().getName() + " - [add] - Entering");
			// get the // DynaValidatorForm
			// initialize default position level flag
			positionFlag = false;
			// set the screenFieldsStatus for movement display screen which is
			// used to disable the component.
			request.setAttribute("screenFieldsStatus", "false");
			// get the current hostId
			hostId = CacheManager.getInstance().getHostId();
			// Get the movementID
			movementIdAsString = request.getParameter("selectedMovementId");
			// Set Entity object values
			entity = new Entity();
			// set the host id
			entity.getId().setHostId(hostId);
			if (movementIdAsString != null) {
				movementIdAsString = movementIdAsString.trim();
			}
			// check the movementIdAsString is null or empty
			if (SwtUtil.isEmptyOrNull(movementIdAsString)) {
				// Instantiate the Movement
				movement = new Movement();
				// set the ValueDate

				// When a user has selected a currency,
				// it should stay the same for the second and subsequent
				// movements that are input, instead of reverting back to
				// a different currency each time the screen refreshes
				movInitial = (Movement) getMovement();
				// set the Entity
				if (request.getAttribute("isSave") != null
					&& request.getAttribute("isSave").equals("yes")) {
					// get the entityId from bean
					entityId = movInitial.getId().getEntityId();
					// set the entityId in movement
					movement.getId().setEntityId(entityId);
				} else {
					// get the user current entityId
					entityId = SwtUtil.getUserCurrentEntity(request
							.getSession());
					// set the entityId in movement
					movement.getId().setEntityId(entityId);
				}
				movement.setValueDateAsString(SwtUtil.formatDate(SwtUtil
						.getSysParamDateWithEntityOffset(entityId), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue()));
				MovementExt ext = new MovementExt();
				movement.setMovementExt(ext);

				// put the entity collection in request
				putEntityListInReq(request);
				// get the collection of entity from request
				collEntity = (Collection<LabelValueBean>) request
						.getAttribute("entities");
				// check the entity access
				if ((SwtUtil.getUserEntityAccess(SwtUtil
								.getUserEntityAccessList(request.getSession()),
						entityId)) != 0) {
					// set default value for availability of entity
					entityAvailable = false;
					if (collEntity != null && collEntity.size() > 0) {
						// iterate the entity
						for (Iterator<LabelValueBean> itrEntity = collEntity
								.iterator(); itrEntity.hasNext();) {
							// get the LabelValueBean for entity
							LabelValueBean lBean = itrEntity.next();
							// check the labelvaluebean entity id with current
							// entity id
							if (lBean.getValue().equals(entityId)) {
								// set the available flag for entity
								entityAvailable = true;
								break;
							}
						}
						// If not available then set the first entity of
						// collection to movement entity
						if (!entityAvailable) {
							entityId = ((LabelValueBean) collEntity.toArray()[0])
									.getValue();
							movement.getId().setEntityId(entityId);
						}
					}
				}
				// get the currency code from the form value
				ccyCodeInForm = movInitial.getCurrencyCode();
				// set the movement into dyna Form to set the form values
				setMovement(movement);
				// put currency full access list in request
				putCurrencyFullAccessListInReq(request, hostId, entityId);
				// if some currency code is present in the form then
				// setting it as the currency code next time.
				// set currency code
				if (request.getAttribute("isSave") != null
					&& request.getAttribute("isSave").equals("yes")) {
					currencyCode = ccyCodeInForm;
					movement.setCurrencyCode(currencyCode);
				} else {

					currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
							hostId, entityId);
					movement.setCurrencyCode(currencyCode);

				}
				// get the access for current entity and currency
				currAccessFlag = SwtUtil.getFullAccesOnCurrencyAndEntity(
						request, hostId, entityId, currencyCode);
				if (!currAccessFlag) {
					// get the currenices collection
					currencyColl = (Collection<LabelValueBean>) request
							.getAttribute("currencies");
					// get the currency code
					if (currencyColl != null && currencyColl.size() != 0) {
						currencyCode = ((LabelValueBean) currencyColl.toArray()[0])
								.getValue();
					} else {
						currencyCode = null;
					}
				}
				// put the position level,sign,pay
				// channel list in request to populate the
				// corresponding select box in full input screen.
				putPositionLevelListInReq(request, entityId, null);
				putSignListInReq(request);
				// get the collection for position level
				collPositionLvl = (Collection<LabelValueBean>) request
						.getAttribute("positionLevelList");
				if (collPositionLvl != null && collPositionLvl.size() > 0) {
					lblPositionLvl = (LabelValueBean) collPositionLvl.toArray()[0];
					position = lblPositionLvl.getValue();
					// set Position level
					entity.getId().setEntityId(entityId);
					entity.setExternalBalance(Integer.parseInt(position));
					// Check whether the given position level is External
					// balance
					positionFlag = movementManager
							.checkExternalPositionLevel(entity);
				}
				if (positionFlag) {
					// If not exist set the value as E
					movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
				} else {
					// If exist set value as I
					movement.setExtBalStatus(SwtConstants.EXT_BAL_INC);
				}
				//Mantis 6250: set Ilm Fcast status as predict status
				if (!SwtUtil.isEmptyOrNull(movement.getPredictStatus())){
					movement.setIlmFcastStatus(movement.getPredictStatus());
				}
				// Code for setting default entity in entityDropDown on the
				// screen ends
			} else {
				// Instantiate the SystemInfo
				systemInfo = new SystemInfo();
				// get movementID from request param
				movementId = new Long(movementIdAsString);
				// get movement details for given movement id
				movement = movementManager.getMovementDetails(hostId,
						movementId, systemInfo, SwtUtil
								.getCurrentSystemFormats(request.getSession()));
				// set the values in movement bean
				movement.setMessageId(null);
				movement.setMessageFormat(null);
				movement.getId().setMovementIdAsString("");
				// get the
				// entityId,currencyCode,counterPartyId,beneficiaryId,custodianId,matchingParty
				// from movement bean which are used to get the position
				// level,pay channel,party collection
				entityId = movement.getId().getEntityId();
				currencyCode = movement.getCurrencyCode();
				counterPartyId = movement.getCounterPartyId();
				beneficiaryId = movement.getBeneficiaryId();
				custodianId = movement.getCustodianId();
				matchingParty = movement.getMatchingParty();
				// set the PostingDate for movement
				if ((movement.getPostingDate() != null)) {
					movement.setPostingDateAsString((SwtUtil.formatDate(
							movement.getPostingDate(), SwtUtil
									.getCurrentSystemFormats(
											request.getSession())
									.getDateFormatValue())));
				}
				// put the position level,entity,sign,pay
				// channel,description,party list in request to populate the
				// corresponding select box in screen.
				putCurrencyFullAccessListInReq(request, hostId, entityId);
				putPositionLevelListInReq(request, entityId, movement
						.getInputSource());
				putEntityListInReq(request);
				putSignListInReq(request);
				putDescriptionsInReq(request, movement);
				movement.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(movement
						.getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue() + " HH:mm:ss")));
				// set the SettlementDateTime for movement
				movement.setSettlementDateTimeAsString((SwtUtil.formatDate(movement
						.getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue() + " HH:mm:ss")));

				putPartyListInReq(request, hostId, entityId, counterPartyId,
						beneficiaryId, custodianId, matchingParty, movement.getOrderingCustomerId(), movement.getOrderingInstitutionId(),
						movement.getSenderCorrespondentId(), movement.getReceiverCorrespondentId(), movement.getIntermediaryInstitutionId(),
						movement.getAccountWithInstitutionId(), movement.getBeneficiaryCustomerId());
				// set the entity id
				entity.getId().setEntityId(entityId);
				// set the ExternalBalance for entity
				entity.setExternalBalance(Integer.parseInt(movement
						.getPositionLevelAsString()));
				// Check whether the given position level is External balance
				positionFlag = movementManager
						.checkExternalPositionLevel(entity);
				if (positionFlag) {
					// If not exist set the value as E
					movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
				} else {
					// If exist set value as I
					movement.setExtBalStatus(SwtConstants.EXT_BAL_INC);
				}

				//Mantis 6250: set Ilm Fcast status as predict status
				if (!SwtUtil.isEmptyOrNull(movement.getPredictStatus())){
					movement.setIlmFcastStatus(movement.getPredictStatus());
				}

				// set the movement in dynavalidator form
				setMovement(movement);
				// set the attribute for openUnopenFlag in request based on this
				// flag display the open or unopen button
				if (movement != null) {
					request.setAttribute(SwtConstants.OPEN_UNOPEN_FLAG,
							movement.getOpenFlag());
				}
			}
			// get the access value for menu,entity,currency group
			accessInd = getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			// based on the access value set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// movement
			// display
			if (accessInd == 0) {
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);

			} else {
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

			}
			// set the attribute for methodName in form
			request.setAttribute("methodName", "save");
			// get the saved MovementId
			savedMovementId = (String) request.getSession().getAttribute(
					"savedMovementId");
			if (savedMovementId != null) {
				// set the attribute for parentFormRefresh which is used to
				// refresh the parent screen and update the details
				request.setAttribute("parentFormRefresh", "yes");
				request.getSession().removeAttribute("savedMovementId");
				request.setAttribute("savedMovementId", savedMovementId);
			}
			// set the attribute for movement which is used to get the movement
			// details in front end.
			request.setAttribute("movement", movement);
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "no");
			// set the attribute to identify which screen is opened
			request.setAttribute("screenIdentifier", "manualInput");
			log.debug(this.getClass().getName() + " - [add] - Exit");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in MovementAction.'add' method : "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MovementAction.'add' method : "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", MovementAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			entity = null;
			position = null;
			entityId = null;
			hostId = null;
			movementIdAsString = null;
			movInitial = null;
			ccyCodeInForm = null;
			currencyCode = null;
			collPositionLvl = null;
			lblPositionLvl = null;
			systemInfo = null;
			movementId = null;
			movement = null;
			counterPartyId = null;
			beneficiaryId = null;
			custodianId = null;
			matchingParty = null;
			savedMovementId = null;
			currencyColl = null;
			collEntity = null;
		}
	}

	/**
	 * This method is called while clicking the save button on movement change
	 * screen and used to update the movement details which are changed
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To host id
		String hostId = null;
		// To maintain the previous movement object
		Movement existingMovement = null;
		// variable to hold the authorizeStatus
		String authorizeStatus = null;
		// variable to hold the roleId
		String roleId = null;
		// variable to hold the userId
		String userId = null;
		// variable for PreAdviceInputManager
		PreAdviceInputManager preAdviceInputManager = null;
		// get the current system formats
		SystemFormats sysFormat = null;
		// To get all form details
		// DynaValidatorForm dynaForm = null;
		// Movement instance
		Movement movement = null;
		// Temporary to hold the movement
		Movement tempMovement = null;
		// To hold movement id as string
		String movementIdAsString = null;
		// To maintain screen name
		String screenIdentifier = null;
		// movementId as long
		Long movementId = null;
		// To hold entiy id from form
		String entityId = null;
		// To get the position for movement
		int positionLevel;
		// To maintain position level
		String positionLvlInterExt = null;
		// Get the old status for predict
		String oldPredictStatus = null;
		// Get the new status for predict
		String newPredictStatus = null;
		// Get the old COUNTERPARTY_ID
		String oldCounterPartyId = null;
		// Get the new COUNTERPARTY_ID
		String newCounterPartyId = null;

		// To maintain collection for position
		Collection<String> positionLvlColl = null;
		// To hold the parent screen
		String parentScreen = null;
		// To set movement details in exception block
		Movement exceptionMov = null;
		// collection of misc params
		Collection<MiscParams> collMiscParams = null;
		// set editable flag
		Hashtable editFlagArr = null;
		// Iterate the postion level
		Iterator<String> itrPositionLvl = null;
		try {
			log.debug(this.getClass().getName() + " - [update] - Entering");
			// Get host id
			hostId = CacheManager.getInstance().getHostId();
			// Instantiate the Movement
			existingMovement = new Movement();
			// get the current system format from session.
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get the form details
			// get the movement details from form
			movement = (Movement) getMovement();
			// assign into temporary movement
			tempMovement = movement;
			/* Getting details of the selected movement from the database */
			movementIdAsString = movement.getId().getMovementIdAsString();
			// get the screenIdentifier from request
			screenIdentifier = request.getParameter("screenIdentifier");
			// assign the movement id
			movementId = new Long(movementIdAsString);
			// get the movement details for given movement id
			movement = movementManager.getMovementDetails(hostId, movementId,
					new SystemInfo(), SwtUtil.getCurrentSystemFormats(request
							.getSession()));
			// assign into existing movement
			existingMovement = movement;
			// create instance of PreAdviceInputManager
			preAdviceInputManager = (PreAdviceInputManager) SwtUtil
					.getBean("preAdviceInputManager");
			// get the role id to get the authorise status
			roleId = SwtUtil.getCurrentUser(request.getSession()).getRoleId();
			// get the current user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the authorizeStatus for given role id
			authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);
			if ((authorizeStatus != null)
				&& authorizeStatus.equalsIgnoreCase("Y")) {
				// update the authorize status in movement
				preAdviceInputManager.manageMovementAlert(movement, movement
						.getMatchStatus(), SwtConstants.AUTHORISE_STATUS);
				// set the match status
				movement.setMatchStatus(SwtConstants.AUTHORISE_STATUS);
				// set the updated user id
				movement.setUpdateUser(userId);
			}
			// get the entity id & position level for movement
			entityId = movement.getId().getEntityId();
			positionLevel = movement.getPositionLevel().intValue();
			// get the position level for internal,external for given host
			// id,entity id
			positionLvlInterExt = movementManager
					.getPositionLevelInternalExternal(hostId, entityId,
							positionLevel);
			// get the old and new predict status for movement
			oldPredictStatus = movement.getPredictStatus();
			newPredictStatus = tempMovement.getPredictStatus();
			// set the movement open flag based on the movement position level
			// internal and external
			if ((positionLvlInterExt
						 .equals(SwtConstants.POSITION_LEVEL_INTERNAL)
				 && newPredictStatus.equals(SwtConstants.PREDICT_EXC) && !newPredictStatus
					.equals(oldPredictStatus))
				|| (positionLvlInterExt
							.equals(SwtConstants.POSITION_LEVEL_EXTERNAL)
					&& newPredictStatus
							.equals(SwtConstants.PREDICT_INC) && !newPredictStatus
					.equals(oldPredictStatus))) {
				movement.setOpenFlag(SwtConstants.NO);
			}
			// set the initial postion level for movement
			if (newPredictStatus.equals(SwtConstants.PREDICT_INC)
				|| newPredictStatus.equals(SwtConstants.PREDICT_EXC)) {
				movement.setInitialPredStatus(newPredictStatus);
			}
			// get the old and new predict status for movement
			oldCounterPartyId = movement.getCounterPartyId();
			newCounterPartyId = tempMovement.getCounterPartyId();

			// check the screenIdentifier for movementChange
			if ((screenIdentifier != null)
				&& screenIdentifier.equals("movementChange")) {
				// set the hostid & MovementId for movement
				tempMovement.getId().setHostId(hostId);
				tempMovement.getId()
						.setMovementId(new Long(movementIdAsString));
				// set the book code "N" if it has no value else it will be "Y"
				if (SwtUtil.isEmptyOrNull(tempMovement.getBookCode())) {
					tempMovement.setBookCodeAvail("N");
				} else {
					movement.setBookCodeAvail("Y");
				}
				// Added by Bouazizi Mefteh for Mantis 1885: Note count not updated
				// set the notesCount for movement
				if(movement.getNotesCount()!=null)
					tempMovement.setNotesCount(movement.getNotesCount());

				/**
				 * set the match id,match status,input date,input user, update
				 * user,message id,message format,initial predict status, input
				 * role,position level,movement type,open flag from movement
				 * which has taken from db to temporary movement
				 */
				if (movement.getMatchId() != null) {
					tempMovement.setMatchId(movement.getMatchId());
				}
				if (movement.getMatchStatus() != null) {
					tempMovement.setMatchStatus(movement.getMatchStatus());
				}
				if (movement.getInputDate() != null) {
					tempMovement.setInputDate(movement.getInputDate());
				}
				if (movement.getInputUser() != null) {
					tempMovement.setInputUser(movement.getInputUser());
				}
				if (movement.getUpdateUser() != null) {
					tempMovement.setUpdateUser(movement.getUpdateUser());
				}
				if (movement.getMessageId() != null) {
					tempMovement.setMessageId(movement.getMessageId());
				}
				if (movement.getMessageFormat() != null) {
					tempMovement.setMessageFormat(movement.getMessageFormat());
				}
				if (movement.getInitialPredStatus() != null) {
					tempMovement.setInitialPredStatus(movement
							.getInitialPredStatus());
				}
				if (movement.getInputRole() != null) {
					tempMovement.setInputRole(movement.getInputRole());
				}
				if (!SwtUtil.isEmptyOrNull(tempMovement
						.getPositionLevelAsString())) {
					tempMovement.setPositionLevel(new Integer(tempMovement
							.getPositionLevelAsString()));
				}
				tempMovement.setMovementType(movement.getMovementType());
				if (!SwtUtil.isEmptyOrNull(movement.getOpenFlag())) {
					tempMovement.setOpenFlag(movement.getOpenFlag());
				} else {
					tempMovement.setOpenFlag(SwtConstants.NO);
				}
				// set the screenIdentifier for update the movement all fields
				screenIdentifier = "updateAllFields";
				movement = tempMovement;
			} else {
				/**
				 * if screenidentifier not equals movement change then set the
				 * movement id,book code,predict status,extract status,external
				 * balance status,matching party,product type,posting date from
				 * temporary movement to original movement
				 *
				 */
				movementIdAsString = movement.getId().getMovementId()
						.toString();
				movement.getId().setMovementIdAsString(movementIdAsString);
				movement.setBookCode(tempMovement.getBookCode());
				movement.setPredictStatus(tempMovement.getPredictStatus());
				movement.setExtractStatus(tempMovement.getExtractStatus());
				positionLvlColl = getExternalPositionLevels(hostId, entityId);
				if ((positionLvlColl != null) && (positionLvlColl.size() > 0)) {
					itrPositionLvl = positionLvlColl.iterator();
					while (itrPositionLvl.hasNext()) {
						// check the collection of position level match with
						// movement position level then set the external balance
						// status fro movement
						if (itrPositionLvl.next().equalsIgnoreCase(
								movement.getPositionLevelAsString())) {
							movement.setExtBalStatus(tempMovement
									.getExtBalStatus());
						}
					}
				}
			}
			movement.setMatchingParty(tempMovement.getMatchingParty());
			movement.setProductType(tempMovement.getProductType());
			if (!SwtUtil.isEmptyOrNull(tempMovement.getPostingDateAsString())) {
				movement.setPostingDate((SwtUtil.parseDate(tempMovement
						.getPostingDateAsString(), sysFormat
						.getDateFormatValue())));
			}
			if (!SwtUtil.isEmptyOrNull(tempMovement.getSettlementDateTimeAsString())) {
				movement.setSettlementDateTime((SwtUtil.parseDate(tempMovement
						.getSettlementDateTimeAsString(), sysFormat
																  .getDateFormatValue() + " HH:mm:ss")));
			}
			if (!SwtUtil.isEmptyOrNull(tempMovement.getExpectedSettlementDateTimeAsString())) {
				movement.setExpectedSettlementDateTime((SwtUtil.parseDate(tempMovement
						.getExpectedSettlementDateTimeAsString(), sysFormat
																		  .getDateFormatValue() + " HH:mm:ss")));
			}
			// set the attribute for openUnopenFlag in request based on this
			// flag display the open or unopen button
			if (movement != null) {
				request.setAttribute(SwtConstants.OPEN_UNOPEN_FLAG, movement
						.getOpenFlag());
			}
			// set the notescount for movement on the form
			movement
					.setNotesCount(((movement.getNotesCount() != null) && (movement
																				   .getNotesCount() > 0)) ? movement.getNotesCount()
							: 0);
			// update the new values for movement
			movementManager.updateMovementDetails(movement, screenIdentifier,
					SwtUtil.getCurrentSystemFormats(request.getSession()));
			request.setAttribute("screenFieldsStatus", "true");

			if (SwtConstants.OUTSTANDING_STATUS.equals(movement.getMatchStatus()) || SwtConstants.AUTHORISE_STATUS.equals(movement.getMatchStatus()) || SwtConstants.REFERRED_STATUS.equals(movement.getMatchStatus())) {
				movementManager.updateOriginalValues(movement, existingMovement);
			}

			// get the parent screen from request and if it is movement summary
			// display then change the response
			parentScreen = request.getParameter("parentScreen");
			if ((parentScreen != null)
				&& parentScreen.equals("movementSummaryDisplay")) {
				request.setAttribute("parentScreen", parentScreen);
				log.debug(this.getClass().getName() + " - [Update] - Existing");
				return showMovementDetails();
			} else {
				log.debug(this.getClass().getName() + " - [Update] - Existing");
				return displayMovement();
			}
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in MovementAction.'update' method : "
					  + swtexp.getMessage());
			// get the movements from form
			exceptionMov = (Movement) getMovement();
			// get the collection of the movement field to get the access.
			collMiscParams = CacheManager.getInstance().getMiscParams(
					"MOVEMENTFIELD", exceptionMov.getId().getEntityId());
			// Initialize the array for movement filed collection
			editFlagArr = new Hashtable<String, Integer>();
			// get the access list for movement field based on the entityid and
			// set it into request
			editFlagArr = movementManager.getEditFlagDetails(hostId,
					exceptionMov.getId().getEntityId(), exceptionMov
							.getInputSource());
			putEditFlagsInRequest(request, editFlagArr, exceptionMov
					.getInputSource());
			// put currency full access list in request
			putCurrencyFullAccessListInReq(request, hostId, entityId);
			// put the position level,entity,sign,pay
			// channel,description,party list in request to populate the
			// corresponding select box in screen.
			putPositionLevelListInReq(request, entityId, movement
					.getInputSource());
			putEntityListInReq(request);
			putSignListInReq(request);
			putDescriptionsInReq(request, exceptionMov);
			putPartyListInReq(request, hostId, exceptionMov.getId()
							.getEntityId(), exceptionMov.getCounterPartyId(),
					exceptionMov.getBeneficiaryId(), exceptionMov
							.getCustodianId(), exceptionMov.getMatchingParty(),exceptionMov.getOrderingCustomerId(), exceptionMov.getOrderingInstitutionId(),
					exceptionMov.getSenderCorrespondentId(), exceptionMov.getReceiverCorrespondentId(), exceptionMov.getIntermediaryInstitutionId(),
					exceptionMov.getAccountWithInstitutionId(), exceptionMov.getBeneficiaryCustomerId());
			request.setAttribute("screenIdentifier", "movementChange");
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("screenFieldsStatus", "false");
			request.setAttribute("movement", exceptionMov);
			// set the enable/disable status for copy
			// from,save,change,open,match,notes,log,message,close button in
			// change movement screen
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			// set the movement into dyna Form to set the form values
//			if (dynaForm != null) {
			setMovement(exceptionMov);
//			}
			// save the exception on screen
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("success");
		} catch (Exception exp) {
			log.error("Exception Catch in MovementAction.'update' method : "
					  + exp.getMessage());
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("screenFieldsStatus", "false");
			// set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// change movement screen.
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			// set the movement into dyna Form to set the form values
//			if (dynaForm != null) {
			setMovement(existingMovement);
//			}
			// save the exception on screen
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(exp, "update",
							MovementAction.class), request, ""));
			return getView("success");
		} finally {
			// nullify objects
			hostId = null;
			existingMovement = null;
			authorizeStatus = null;
			roleId = null;
			userId = null;
			preAdviceInputManager = null;
			sysFormat = null;
			movement = null;
			tempMovement = null;
			movementIdAsString = null;
			screenIdentifier = null;
			movementId = null;
			entityId = null;
			positionLvlInterExt = null;
			oldPredictStatus = null;
			newPredictStatus = null;
			positionLvlColl = null;
			parentScreen = null;
			exceptionMov = null;
			collMiscParams = null;
			editFlagArr = null;
		}
	}

	/**
	 *
	 * This method is called while changing the entity in the select box on the
	 * full input screen and used to populate the position level,currency
	 * list,form value etc., corresponding to the selected entity id
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayListByEntity() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Instance the Entity object
		Entity entity = null;
		// Variable to hold position
		String position = null;
		// boolean to hold position flag
		boolean positionFlag;
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold DynaValidatorForm
		// DynaValidatorForm dynaForm = null;
		// variable to hold the pojo class
		Movement movement = null;
		// variable declared to hold the what type of movement is genetrating
		String movType = null;
		// Variable decleartion ton hold the current entityId
		String entityId = null;
		// Variable to hold the currency code
		String currencyCode = null;
		// variable decleared to store the access valeues
		int accessInd;
		// Variable to hold the Movement pojo class
		Movement movEntity = null;
		// Variable to hold the position level
		Collection<LabelValueBean> collPositionLvl = null;
		// Variable to hold the itreated Psition level
		LabelValueBean lblPositionLvl = null;
		// Flag declered to determine the currency access
		boolean currAccessFlag;
		// Collection declared get the currencies in the list
		Collection<LabelValueBean> currencyColl = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [displayListByEntity] - Entering");
			// initialize default position level
			positionFlag = false;
			// setting screenFieldsStatus
			request.setAttribute("screenFieldsStatus", "false");
			// getting movement form
			movement = (Movement) getMovement();
			// getting movement type
			movType = movement.getMovementType();
			// getting current hostId
			hostId = CacheManager.getInstance().getHostId();
			// getting entityId from selected entity
			entityId = movement.getId().getEntityId();
			// add full access currency list into request to display the
			// currency in select box.
			putCurrencyFullAccessListInReq(request, hostId, entityId);
			// if movement type has no value then set the default value as cash
			// "c"
			movType = (!SwtUtil.isEmptyOrNull(movType)) ? movType
					: SwtConstants.MOVEMENT_TYPE_CASH;
			// Set Entity object values
			entity = new Entity();
			entity.getId().setHostId(hostId);
			// get the domestic currency code for selected entity id
			currencyCode = SwtUtil.getDomesticCurrencyForUser(request, hostId,
					entityId);
			// check access for currency and entity,if both have full access
			// then return true otherwise return false
			currAccessFlag = SwtUtil.getFullAccesOnCurrencyAndEntity(request,
					hostId, entityId, currencyCode);
			if (!currAccessFlag) {
				currencyColl = (Collection<LabelValueBean>) request
						.getAttribute("currencies");
				if (currencyColl != null && currencyColl.size() != 0) {
					currencyCode = ((LabelValueBean) currencyColl.toArray()[0])
							.getValue();
				} else {
					currencyCode = null;
				}
			}
			// put the position level,entity,sign,pay
			// channel list in request to populate the
			// corresponding select box in full input screen.
			putPositionLevelListInReq(request, entityId, movement
					.getInputSource());
			putEntityListInReq(request);
			putSignListInReq(request);
			// get the access value for menu,entity,currency group
			accessInd = getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			// based on the access value set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// full input
			if (accessInd == 0) {
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);

			} else {
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

			}
			// code to get the type of entity access for this entity ends
			movEntity = new Movement();
			// Collection for positionLevelList
			collPositionLvl = (Collection<LabelValueBean>) request
					.getAttribute("positionLevelList");
			if (collPositionLvl != null && collPositionLvl.size() > 0) {
				// get the first label value bean from bean.
				lblPositionLvl = (LabelValueBean) collPositionLvl.toArray()[0];
				// get the position level
				position = lblPositionLvl.getValue();
				// Setting entityId
				entity.getId().setEntityId(entityId);
				// Setting External Balance
				entity.setExternalBalance(Integer.parseInt(position));
				// Check whether the given position level is External balance
				positionFlag = movementManager
						.checkExternalPositionLevel(entity);
			}
			// set the values in the bean
			if (positionFlag) {
				// If not exist set the value as E
				movEntity.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
			} else {
				// If exist set value as I
				movEntity.setExtBalStatus(SwtConstants.EXT_BAL_INC);
			}

			/**
			 * set the entity id,value date,posting date,currency code in the
			 * movement bean to display the form values in the screen.
			 */
			movEntity.getId().setEntityId(entityId);
			movEntity.setValueDateAsString(SwtUtil.formatDate(SwtUtil
					.getSysParamDateWithEntityOffset(entityId), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()));
			movEntity.setPostingDateAsString((SwtUtil.formatDate(movEntity
					.getPostingDate(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue())));
			movEntity.setCurrencyCode(currencyCode);
			// set the movement into dyna Form to set the form values
			setMovement(movEntity);
			request.setAttribute("movement", movEntity);
			// set the attribute to identify which screen is opened
			request.setAttribute("screenIdentifier", "manualInput");
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "no");
			log.debug(this.getClass().getName()
					  + " - [displayListByEntity] - Exiting");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
						   + "- [SwtException Catch in MovementAction.'displayListByEntity' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
						   + "- [Exception Catch in MovementAction.'displayListByEntity' method : "
						   + exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListByEntity", MovementAction.class), request,
					"");
			return getView("fail");
		} finally {
			// Nullify Objects
			entity = null;
			position = null;
			hostId = null;
			movement = null;
			movType = null;
			entityId = null;
			currencyCode = null;
			movEntity = null;
			collPositionLvl = null;
			lblPositionLvl = null;
			currencyColl = null;
		}
	}

	/**
	 *
	 * This method is used to set the button status for
	 * ADD,CHANGE,SAVE,CANCEL,COPY FROM,NOTES,MESSAGE,MATCH,CLOSE,LOG, OPEN
	 * UNOPEN button based on the screen like movement display, manual input
	 * screen,movement change screen.
	 *
	 * @param req
	 * @param form
	 * @param changeStatus
	 * @param notesStatus
	 * @param saveStatus
	 * @param cancelStatus
	 * @param closeStatus
	 * @param matchStatus
	 * @param messageStatus
	 * @param addStatus
	 * @param copyFromStatus
	 * @param logStatus
	 * @param openUnopenSatus
	 * @throws SwtException
	 */
	private void setButtonStatus(HttpServletRequest req,
								 String changeStatus, String notesStatus, String saveStatus,
								 String cancelStatus, String closeStatus, String matchStatus,
								 String messageStatus, String addStatus, String copyFromStatus,
								 String logStatus, String openUnopenSatus) throws SwtException {
		// dyna validator form to get the form values
		// DynaValidatorForm dynaForm = null;
		// get the movement details from form
		Movement movement = null;
		// movement id in long
		Long movementId = null;
		// get value date in string
		String valueDateAsString = null;
		// used to maintain value date calendar
		Calendar valueDtCal = null;
		// hold year for value date
		int valDtYear;
		// hold month for value date
		int valDtMonth;
		// hold date for value date
		int valDtDate;
		try {
			log.debug(this.getClass().getName()
					  + " - [setButtonStatus] - Entering");
			/**
			 * Based on the flag set for ADD,CHANGE,SAVE,CANCEL,COPY
			 * FROM,NOTES,MESSAGE,MATCH, CLOSE,LOG,OPEN/UNOPEN button . It will
			 * be decided on runtime to be disabled or enabled
			 *
			 */
			req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
			req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
			req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
			req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
			req.setAttribute(SwtConstants.COPY_FROM_BUT_STS, copyFromStatus);
			req.setAttribute(SwtConstants.NOTES_BUT_STS, notesStatus);
			req.setAttribute(SwtConstants.MESS_BUT_STS, messageStatus);
			req.setAttribute(SwtConstants.MATCH_BUT_STS, matchStatus);
			req.setAttribute(SwtConstants.CLOSE_BUT_STS, closeStatus);
			req.setAttribute(SwtConstants.LOG_BUT_STS, logStatus);
			req.setAttribute(SwtConstants.OPEN_UNOPEN_BUT_STS, openUnopenSatus);
			// get the dyna validator form
			// get the movement from form
			movement = (Movement) getMovement();
			if (!SwtUtil
					.isEmptyOrNull(movement.getId().getMovementIdAsString())) {
				// get the movement id in long
				movementId = new Long(movement.getId().getMovementIdAsString());
				// get the value date for given movement id
				valueDateAsString = (String) movementManager.getValueDateField(
						CacheManager.getInstance().getHostId(), SwtUtil
								.getUserCurrentEntity(req.getSession()),
						movementId);
				if (!SwtUtil.isEmptyOrNull(valueDateAsString)) {
					// split year,month,date from value date which has taken
					// from previous
					valDtYear = Integer.parseInt(valueDateAsString.substring(0,
							4)) - 1900;
					valDtMonth = Integer.parseInt(valueDateAsString.substring(
							5, 7)) - 1;
					valDtDate = Integer.parseInt(valueDateAsString.substring(8,
							10));
					// Instantiate the calender instance
					valueDtCal = Calendar.getInstance();
					// set the year,month,date for valudate calendar
					valueDtCal.set(valDtYear + 1900, valDtMonth, valDtDate);
					/**
					 * set the empty value for hour,hour of
					 * day,minute,second,millisecond for value date.
					 */
					valueDtCal.set(Calendar.HOUR, 0);
					valueDtCal.set(Calendar.HOUR_OF_DAY, 0);
					valueDtCal.set(Calendar.MINUTE, 0);
					valueDtCal.set(Calendar.SECOND, 0);
					valueDtCal.set(Calendar.MILLISECOND, 0);
					// perform comparison between the value date and system
					// date and based on
					// comparison set the value date index
					if (valueDtCal.getTime().compareTo(
							SwtUtil.getSystemDatewithoutTime()) <= 0) {
						req.setAttribute("valueDateInd", "1");
					} else {
						req.setAttribute("valueDateInd", "0");
					}
				}
			}
			log.debug(this.getClass().getName()
					  + " - [setButtonStatus] - Exiting");
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					  + " - [setButtonStatus] - Exception -" + ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			movement = null;
			movementId = null;
			valueDateAsString = null;
			valueDtCal = null;
			valDtYear = 0;
			valDtMonth = 0;
			valDtDate = 0;
		}
	}

	/**
	 * This method is used to get the collection of entities which are having
	 * access for current user's role
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		// To hold the current role
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putEntityListInReq] - Entering");
			// get the role id for current user
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the collection of entities which having full access for given
			// role id and set the collection of entities in request is used to
			// display in
			// the select box.
			request.setAttribute("entities", SwtUtil.getSwtMaintenanceCache()
					.getFullEntityAccessCollectionLVL(roleId));
			log.debug(this.getClass().getName()
					  + " - [putEntityListInReq] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putEntityListInReq] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			roleId = null;
		}
	}

	/**
	 *
	 * This method is used to get the counter party,beneficiary, matching
	 * party,custodian based on given party details and later it will be
	 * displayed on the movement display/change screen.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param counterPartyId
	 * @param beneficiaryId
	 * @param custodianId
	 * @param matchingParty
	 * @throws SwtException
	 */
	private void putPartyListInReq(HttpServletRequest request, String hostId,
								   String entityId, String counterPartyId, String beneficiaryId,
								   String custodianId, String matchingParty, String orderingCust, String orderingInst,
								   String senderCorres, String receivCorres, String intermInst,
								   String acctWithInst, String benefCust) throws SwtException {
		// To hold counter party name
		String counterPartyDesc = null;
		// To hold the beneficiary name
		String beneficiaryName = null;
		// To hold the custodian name
		String custodianName = null;
		// To hold matching party name
		String matchingPartyDesc = null;
		// collection of counter party list
		Collection<Party> collCounterParty = null;
		// Iterate the counter party
		Iterator<Party> itrCounterParty = null;
		// collection of beneficiary list
		Collection<Party> collBeneficiary = null;
		// Iterate the counter party
		Iterator<Party> itrBeneficiary = null;
		// collection of Matching Party list
		Collection<Party> collMatchingParty = null;
		// Iterate the matching party list
		Iterator<Party> itrMatchingParty = null;
		// collection of custodian list
		Collection<Party> collCustodian = null;
		// Iterate the custodian list
		Iterator<Party> itrCustodian = null;
		Party orderingCustParty;
		Party orderingInstParty;
		Party senderCorresParty;
		Party receivCorresParty;
		Party intermInstParty;
		Party acctWithInstParty;
		Party benefCustParty;

		String tempString = "";

		try {
			log.debug(this.getClass().getName()
					  + " - [putPartyListInReq] - Entering");
			// Initialize the all party details to empty
			counterPartyDesc = "";
			beneficiaryName = "";
			custodianName = "";
			matchingPartyDesc = "";

			/**
			 * set the counterparty name in request for given counterparty id
			 * which is used to display the value adjacent to the counter party
			 * id in movement display/change screen.
			 */
			if (!SwtUtil.isEmptyOrNull(counterPartyId)) {
				collCounterParty = movementManager.getCounterPartyRecord(
						hostId, entityId, counterPartyId);
				if (collCounterParty != null) {
					itrCounterParty = collCounterParty.iterator();
					while (itrCounterParty.hasNext()) {
						counterPartyDesc = itrCounterParty.next()
								.getPartyName();
						break;
					}
				}
			}
			request.setAttribute("counterPartyDesc", counterPartyDesc);

			/**
			 * set the beneficiary name in request for given beneficiary id
			 * which is used to display the value adjacent to the beneficiary id
			 * in movement display/change screen.
			 */
			if (!SwtUtil.isEmptyOrNull(beneficiaryId)) {
				collBeneficiary = movementManager.getBeneficiaryRecord(hostId,
						entityId, beneficiaryId);
				if (collBeneficiary != null) {
					itrBeneficiary = collBeneficiary.iterator();
					while (itrBeneficiary.hasNext()) {
						beneficiaryName = itrBeneficiary.next().getPartyName();
						break;
					}
				}
			}
			request.setAttribute("beneficiaryName", beneficiaryName);

			/**
			 * set the matching party name in request for given matching party
			 * id which is used to display the value adjacent to the matching
			 * party id in movement display/change screen.
			 */
			if (!SwtUtil.isEmptyOrNull(matchingParty)) {
				collMatchingParty = movementManager.getMatchingPartyRecord(
						hostId, entityId, matchingParty);
				if (collMatchingParty != null) {
					itrMatchingParty = collMatchingParty.iterator();
					while (itrMatchingParty.hasNext()) {
						matchingPartyDesc = itrMatchingParty.next()
								.getPartyName();
						break;
					}
				}
			}
			request.setAttribute("matchingPartyDesc", matchingPartyDesc);

			/**
			 * set the custodian name in request for given custodian id which is
			 * used to display the value adjacent to the custodian id in
			 * movement display/change screen.
			 */
			if (!SwtUtil.isEmptyOrNull(custodianId)) {
				collCustodian = movementManager.getCustodianRecord(hostId,
						entityId, custodianId);
				if (collCustodian != null) {
					itrCustodian = collCustodian.iterator();
					while (itrCustodian.hasNext()) {
						custodianName = itrCustodian.next().getPartyName();
						break;
					}
				}
			}
			request.setAttribute("custodianName", custodianName);


			tempString = "";
			if (!SwtUtil.isEmptyOrNull(orderingCust)) {
				orderingCustParty = movementManager.getPartyRecord(hostId,
						entityId, orderingCust);
				if (orderingCustParty != null)
					tempString =  orderingCustParty.getPartyName();
				else
					tempString = "";
			}

			request.setAttribute("orderingCustName", tempString);

			tempString = "";
			if (!SwtUtil.isEmptyOrNull(orderingInst)) {
				orderingInstParty = movementManager.getPartyRecord(hostId,
						entityId, orderingInst);
				if (orderingInstParty != null)
					tempString =  orderingInstParty.getPartyName();
				else
					tempString = "";
			}

			request.setAttribute("orderingInstName", tempString);

			tempString = "";
			if (!SwtUtil.isEmptyOrNull(senderCorres)) {
				senderCorresParty = movementManager.getPartyRecord(hostId,
						entityId, senderCorres);
				if (senderCorresParty != null)
					tempString =  senderCorresParty.getPartyName();
				else
					tempString = "";
			}

			request.setAttribute("senderCorresName", tempString);

			tempString = "";
			if (!SwtUtil.isEmptyOrNull(receivCorres)) {
				receivCorresParty = movementManager.getPartyRecord(hostId,
						entityId, receivCorres);
				if (receivCorresParty != null)
					tempString =  receivCorresParty.getPartyName();
				else
					tempString = "";
			}

			request.setAttribute("receivCorresName", tempString);

			tempString = "";
			if (!SwtUtil.isEmptyOrNull(intermInst)) {
				intermInstParty = movementManager.getPartyRecord(hostId,
						entityId, intermInst);
				if (intermInstParty != null)
					tempString =  intermInstParty.getPartyName();
				else
					tempString = "";
			}

			request.setAttribute("intermInstName", tempString);

			tempString = "";
			if (!SwtUtil.isEmptyOrNull(acctWithInst)) {
				acctWithInstParty = movementManager.getPartyRecord(hostId,
						entityId, acctWithInst);
				if (acctWithInstParty != null)
					tempString =  acctWithInstParty.getPartyName();
				else
					tempString = "";
			}

			request.setAttribute("acctWithInstName", tempString);

			tempString = "";
			if (!SwtUtil.isEmptyOrNull(benefCust)) {
				benefCustParty = movementManager.getPartyRecord(hostId,
						entityId, benefCust);
				if (benefCustParty != null)
					tempString =  benefCustParty.getPartyName();
				else
					tempString = "";
			}

			request.setAttribute("benefCustName", tempString);

			log.debug(this.getClass().getName()
					  + " - [putPartyListInReq] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putPartyListInReq] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			counterPartyDesc = null;
			beneficiaryName = null;
			custodianName = null;
			matchingPartyDesc = null;
			collCounterParty = null;
			itrCounterParty = null;
			collBeneficiary = null;
			itrBeneficiary = null;
			collMatchingParty = null;
			itrMatchingParty = null;
			collCustodian = null;
			itrCustodian = null;
		}
	}

	/**
	 * This method is used to set the collection of sign list in request to
	 * decide credit/debit for movement input/change
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putSignListInReq(HttpServletRequest request)
			throws SwtException {
		// To hold the credit/debit value
		Collection<LabelValueBean> signList = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putSignListInReq] - Entering");
			// Instantiate the collecion for sign
			signList = new ArrayList<LabelValueBean>();
			// add the credit value in sign collection
			signList.add(new LabelValueBean(SwtConstants.CREDIT,
					SwtConstants.CREDIT));
			// add the debit value in sign collection
			signList.add(new LabelValueBean(SwtConstants.DEBIT,
					SwtConstants.DEBIT));
			// set the signlist collection in request.
			request.setAttribute("signList", signList);
			log.debug(this.getClass().getName()
					  + " - [putSignListInReq] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putSignListInReq] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// nullify objects
			signList = null;

		}
	}

	/**
	 *
	 * This method is used to put the collection of position level list for
	 * given entity in request and it will be populated in the select box
	 *
	 * @param request
	 * @param entityId
	 * @param inputSource
	 * @throws SwtException
	 */
	private void putPositionLevelListInReq(HttpServletRequest request,
										   String entityId, String inputSource) throws SwtException {
		// To hold the collection of position level
		Collection<LabelValueBean> collPositionLvl = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putPositionLevelListInReq] - Entering");
			collPositionLvl = SwtUtil.getSwtMaintenanceCache()
					.getEntityPositionLevelObjectLVL(entityId);
			// set positionLevel in request
			request.setAttribute("positionLevelList", collPositionLvl);
			log.debug(this.getClass().getName()
					  + " - [putPositionLevelListInReq] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putPositionLevelListInReq] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			collPositionLvl = null;

		}
	}


	/**
	 *
	 * This method is used to display the copy from screen and used to copy the
	 * movement details for manual input screen
	 *
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copyFrom()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + " - [copyFrom] - Entering");
			// set the button status for copy from screen
			setButtonStatus(request,  SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			// set the flag for copyFromFlag in the request session
			request.getSession().setAttribute("copyFromFlag", "y");
			// set the attribute for selectedEntityId in request which is used
			// by the copy from screen
			request.setAttribute("selectedEntityId", request
					.getParameter("selectedEntityId"));
			log.debug(this.getClass().getName() + " - [copyFrom] - Existing");
			return getView("copyFrom");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'copyFrom' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MovementAction.'copyFrom' method : "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copyFrom", MovementAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method is called while clicking the save button on full input screen
	 * and used to create new movement for given details entered in the full
	 * input screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable to get the host id
		String hostId = null;
		// Pojo class to get the system formats details
		SystemFormats sysFormat = null;
		// variable to set the dynaform
		// DynaValidatorForm dynaForm = null;
		// variable decleared for Match driver operation
		String matchDriverOperation = null;
		// variable declare pojo class for system information
		SystemInfo systemInfo = null;
		// Variable Declare the Movement Pojo class
		Movement movement = null;
		// Variable declared to get the Entity Id
		String entityId = null;
		// Variable declared to get the userId
		String userId = null;
		// Variable declared to get the roleId
		String roleId = null;
		// Variable declared to get the authorizeFlag
		String authorizeFlag = null;
		// Variable declared to get the savedMovementId
		String savedMovementId = null;
		// This flag checks if an entry is to be
		boolean isPresent;
		// Collection get the Notes details in session
		Collection<MovementNote> sessionNotesDetails = null;
		// Variable declared to get the currency code
		String currencyCode = null;
		// Collection to get the Match Driver Details
		Collection<MatchDriver> matchDriverDetails = null;
		// Declearation for Pojo class MatchDriver
		MatchDriver matchDriverSave = null;
		// Declearation for Pojo class MatchDriver
		MatchDriver matchDriverUpdate = null;
		// MovementNote instance
		MovementNote movNote = null;
		// used to iterate the notes collection
		Iterator<MovementNote> itrNotes = null;
		// used to iterate the match driver
		Iterator<MatchDriver> itrMatchDriver = null;
		// Variable declared to get the currencies
		Collection<LabelValueBean> currencies = null;
		// Variable declared to get the token
		String token = null;
		try {
			log.debug(this.getClass().getName() + " - [save] - Entering");
			/*
			 * check whether token is valid, when refresh the full input screen.
			 * if token is valid then only create movement, otherwise retain the
			 * full input screen.
			 */
		/*	if(!TokenHelper.validToken()) {
				// Instantiate the currency list label value bean
				currencies = new ArrayList<LabelValueBean>();
				// Setting required attributes
				request.setAttribute("methodNameAddOrChange", "no");
				request.setAttribute("currencies", currencies);
				return add();
			}*/
			isPresent = false;
			// Get the current host id
			hostId = CacheManager.getInstance().getHostId();
			// Get the current system formats
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			matchDriverOperation = new String(""); // This flag decides
			// whether a
			// new entry is to be saved in Match Driver table or existing entry
			// is to be updated
			systemInfo = new SystemInfo();
			movement = (Movement) (getMovement());
			movement.setAmountAsString(movement.getAmountAsString().trim());
			// Get the entity Id
			entityId = movement.getId().getEntityId();
			// get current User Id
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get Role Id for given user id
			roleId = movementManager.getUserDetails(hostId, userId);
			// get authorise flag for given role id
			authorizeFlag = movementManager.getRoleDetails(roleId);
			// System set MATCH STATUS in P_MOVEMENT table as "A" if
			// AUTHORIZE_FALG in S_ROLE is " Y".
			if (authorizeFlag != null && authorizeFlag.equalsIgnoreCase("Y")) {
				movement.setMatchStatus(SwtConstants.AUTHORISE_STATUS);
				movement.setToMatch(SwtConstants.NO);

			} else {
				movement.setMatchStatus(SwtConstants.OUTSTANDING_STATUS);
				movement.setToMatch(SwtConstants.YES);
			}
			// while creating new movement set the default value for role,user
			// host on the movement bean
			movement.setMatchId(null);
			movement.setInputRole(roleId);
			movement.setInputUser(userId);
			movement.setUpdateUser(userId);
			movement.getId().setHostId(hostId);
			movement.setInputSource(SwtConstants.MOVEMENT_SOURCE_DEFAULT);
			movement.setInputDate(SwtUtil.getSystemDatewithTime());
			// check the book code if it null then set the "N" otherwise "Y"
			movement.setBookCodeAvail(!SwtUtil.isEmptyOrNull(movement
					.getBookCode()) ? "Y" : "N");
			// set the initial predict status for movement
			if (movement.getPredictStatus() != null) {
				movement.setInitialPredStatus(movement.getPredictStatus());
			}
			// if postion level is empty then set the default value as one
			// otherwise set the form value
			if (SwtUtil.isEmptyOrNull(movement.getPositionLevelAsString())) {
				movement.setPositionLevel(Integer.valueOf(1));
			} else {
				movement.setPositionLevel(Integer.valueOf(movement
						.getPositionLevelAsString()));
			}
			// get the note details from session and set the host id and entity
			// id which is to be saved along with movement
			sessionNotesDetails = (Collection<MovementNote>) request
					.getSession().getAttribute("sessionNotesDetails");
			if (sessionNotesDetails != null) {
				itrNotes = sessionNotesDetails.iterator();
				while (itrNotes.hasNext()) {
					movNote = itrNotes.next();
					movNote.getId().setHostId(hostId);
					movNote.getId().setEntityId(entityId);
				}
			}
			// get currency code
			currencyCode = movement.getCurrencyCode();
			// get the match driver details for host id
			matchDriverDetails = movementManager.getMatchDriverDetails(hostId);
			// Instantiate the match driver to save the match driver details
			matchDriverSave = new MatchDriver();
			// Instantiate the match driver to update the match driver details
			matchDriverUpdate = new MatchDriver();
			// check match driver is already exists
			if (matchDriverDetails != null) {
				itrMatchDriver = matchDriverDetails.iterator();
				while (itrMatchDriver.hasNext()) {
					matchDriverUpdate = itrMatchDriver.next();
					if (matchDriverUpdate.getId().getHostId().equals(hostId)
						&& (matchDriverUpdate.getId().getEntityId()
							.equals(entityId))
						&& (matchDriverUpdate.getId().getCurrencyCode()
							.equals(currencyCode))) {
						isPresent = true;
						break;
					}
				}
			}
			// set the notes count and posting date for movement
			movement.setNotesCount(new Integer(0));
			if (!SwtUtil.isEmptyOrNull(movement.getPostingDateAsString())) {
				movement.setPostingDate((SwtUtil.parseDate(movement
						.getPostingDateAsString(), sysFormat
						.getDateFormatValue())));
			}
			// System will not update the P_MATCH_DRIVER table for which
			// AUTHORIZE_FLAG is "Y" in S_ROLE table.
			// some part of the code commented as no iteration with matchDriver
			// to take place now (Rabo bank customization)
			if (isPresent) {
				// set the move flag to match driver to update the movement
				// details .
				matchDriverUpdate.setNewMoveFlag("Y");
				savedMovementId = movementManager.saveMovementDetails(movement,
						matchDriverUpdate, matchDriverOperation,
						sessionNotesDetails, systemInfo, SwtUtil
								.getCurrentSystemFormats(request.getSession()));
			} else {
				// set the host id,entity id, currency code, move flag,process
				// flag to match driver to save the movement details .
				matchDriverSave.getId().setHostId(hostId);
				matchDriverSave.getId().setEntityId(entityId);
				matchDriverSave.getId().setCurrencyCode(currencyCode);
				matchDriverSave.setNewMoveFlag("Y");
				matchDriverSave.setProcessingFlag("N");
				savedMovementId = movementManager.saveMovementDetails(movement,
						matchDriverSave, matchDriverOperation,
						sessionNotesDetails, systemInfo, SwtUtil
								.getCurrentSystemFormats(request.getSession()));
			}
			// remove the notes details from session once movement was created
			request.getSession().removeAttribute("sessionNotesDetails");
			// store the created movement id in session which will be displayed
			// through alert message to user
			request.getSession().setAttribute("savedMovementId",
					savedMovementId);
			// set match status in request to display the status of match in
			// screen
			request.setAttribute("matchStatus", movement.getMatchStatus());
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "yes");
			// isSave flag denotes save operation have been completed
			request.setAttribute("isSave", "yes");
			// nullify the notes details in request
			request.getSession().setAttribute("sessionNotesDetails", null);
			// set the attribute to identify which screen is opened
			request.setAttribute("screenIdentifier", "manualInput");
			/*
			 * if token is valid re assign token value in session attribute and
			 * create movement.
			 */
			token  = TokenHelper.setToken();
			request.getSession().setAttribute(TokenHelper.TOKEN_NAME_FIELD,
					token);
			// Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement
			// is not locked while changing
			request.setAttribute("initialinputscreen", request
					.getParameter("initialInputScreen"));
			log.debug(this.getClass().getName() + " - [save] - exit");
			return add();
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in MovementAction.'save' method : "
					  + swtexp.getMessage());
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "no");
			// set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// full input screen
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			// used to display the exception in screen
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return add();
		} catch (Exception exp) {
			log.error("Exception Catch in MovementAction.'save' method : "
					  + exp.getMessage());
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "no");
			// set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// full input screen
			setButtonStatus(request,  SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			// used to display the exception in screen
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(exp, "save",
							MovementAction.class), request, ""));
			return add();
		} finally {
			// nullify objects
			hostId = null;
			sysFormat = null;
			matchDriverOperation = null;
			systemInfo = null;
			movement = null;
			entityId = null;
			userId = null;
			roleId = null;
			authorizeFlag = null;
			savedMovementId = null;
			sessionNotesDetails = null;
			currencyCode = null;
			matchDriverDetails = null;
			matchDriverSave = null;
			matchDriverUpdate = null;
			movNote = null;
			itrNotes = null;
			itrMatchDriver = null;
			currencies = null;
			token = null;
		}
	}

	/**
	 *
	 * This method is invoked while clicking the ok button on copy from screen
	 * after entered the valid movement id and method used to display the
	 * movement details in full input screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copy()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To get the form details
		// DynaValidatorForm dynaForm = null;
		// To hold the values from form movement
		Movement formMovement = null;
		// To hold movement in string
		String movementIdAsString = null;
		// Movement Id
		Long movementId = null;
		// get the movement from database
		Movement movement = null;
		// based on flag details will be showed or not
		boolean showDetails;
		// To hold entity id
		String entityId = null;
		// To hold the currency code
		String currencyCode = null;
		// To hold value from request
		String clickDisplay = null;
		// To hold access for entity and currency group
		int accessInd;
		// To get the movement id
		String showMovementId = null;
		// To hold selected entity id
		String selectedEntityId = null;
		try {
			log.debug(this.getClass().getName() + " - [copy] - Entering");
			// get the form assign into the dynavalidator form
			// get the movement from dyna validator form
			formMovement = (Movement) getMovement();
			// get the movement from form
			movementIdAsString = formMovement.getId().getMovementIdAsString();
			movementIdAsString = movementIdAsString.trim();
			// movement in long format
			movementId = new Long(movementIdAsString);
			// get the movement details for given movement id
			movement = movementManager.getMovementDetails(CacheManager
							.getInstance().getHostId(), movementId, new SystemInfo(),
					SwtUtil.getCurrentSystemFormats(request.getSession()));
			if (movement != null) {
				// get the entity id , currency code from movement
				entityId = movement.getId().getEntityId();
				currencyCode = movement.getCurrencyCode();
				// get the clickDisplay from request and set default value into
				// request
				clickDisplay = request.getParameter("clickDisplay");
				request.setAttribute("clickDisplay", "yes");
				// get the access for given entity id and currency code
				accessInd = getMenuEntityCurrGrpAccess(request, entityId,
						currencyCode);
				if (accessInd != 0) {
					showDetails = false;
				} else {
					showDetails = true;
				}
				// set the show details flag
				if ((clickDisplay != null) && clickDisplay.equals("yes")) {
					showDetails = true;
				}
				if (showDetails) {
					// get the movement id from request
					showMovementId = request.getParameter("showMovementId");
					// set the attribute for parent refresh which is used to
					// update the parent screen.
					if (!((clickDisplay != null) && clickDisplay.equals("yes"))) {
						if ((showMovementId != null)
							&& showMovementId.equals("yes")) {
							request.setAttribute("parentFormRefresh", "yes");
						} else {
							// get the selected entity from request
							selectedEntityId = request
									.getParameter("selectedEntityId");
							if (!SwtUtil.isEmptyOrNull(selectedEntityId)) {
								request.setAttribute("selectedEntityId",
										selectedEntityId);
							}
							request.setAttribute("parentFormRefreshOk", "yes");
						}
					} else {
						request.setAttribute("movementAvailable", "yes");
					}
					// set the selectedMovementId in request
					request.setAttribute("selectedMovementId",
							movementIdAsString);
				} else {
					// set the request attribute for entity access and currency
					// group access
					if (getEntityAccessType(request, entityId) != SwtConstants.ENTITY_FULL_ACCESS) {
						request.setAttribute("entityAccessNotAvailable", "yes");
					}

					if (getCcyGrpAccessType(request, entityId, currencyCode) != SwtConstants.CURRENCYGRP_FULL_ACCESS) {
						request.setAttribute("ccyGrpAccessNotAvailable", "yes");
					}
					// set the form values for movement
					setMovement(formMovement);
				}
				// set the enable/disable status for
				// save,copyfrom,change,open,match,notes,log,message,close
				// button in movementdisplay
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			} else {
				// if entered movement not present then display alert message to
				// user
				request.setAttribute("movementAvailable", "no");
			}
			if (movement != null) {
				// set the attribute for openUnopenFlag in request based on this
				// flag display the open or unopen button
				request.setAttribute(SwtConstants.OPEN_UNOPEN_FLAG, movement
						.getOpenFlag());
			}
			log.debug(this.getClass().getName() + " - [copy] - Existing");
			return getView("copyFrom");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in MovementAction.'copy' method : "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MovementAction.'copy' method : "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copy", MovementAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			formMovement = null;
			movementIdAsString = null;
			movementId = null;
			movement = null;
			entityId = null;
			currencyCode = null;
			clickDisplay = null;
			showMovementId = null;
			selectedEntityId = null;
		}
	}

	/**
	 * This method is invoked while clicking the movement button on movement
	 * summary display screen and open the movement display screen with movement
	 * details for selected movement
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String showMovementDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To hold the Archive Id
		String archiveId = null;
		// SwtDataSource instance
		SwtDataSource dataSource = null;
		// To get the form values
		// DynaValidatorForm dynaForm = null;
		// To hold the host id
		String hostId = null;
		// Movement instance
		Movement movement = null;
		// movement in long
		Long movementId = null;
		// To hold the entity id
		String entityId = null;
		// To maintain the movement id in session.
		String movementIdInSession = null;
		// variable to hold role id
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [showMovementDetails] - Entering");
			// set the screenFieldsStatus for movement display screen which is
			// used to disable the component .
			request.setAttribute("screenFieldsStatus", "true");
			// Added for Archive Movement Search
			archiveId = request.getParameter("archiveId");
			entityId = request.getParameter("entityCode");
			// Instantiate the SwtDataSource
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			// Instantiate the // DynaValidatorForm
			// get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// movement id in long
			movementId = new Long(request.getParameter("movementId").trim());
			// set the archiveId in data source
			dataSource.useArchive(archiveId);
			// get the movement details for given movement id
			if (SwtUtil.isEmptyOrNull(archiveId)) {
				movement = movementManager.getMovementDetails(hostId,
						movementId, new SystemInfo(), SwtUtil
								.getCurrentSystemFormats(request.getSession()));
			} else {
				movement = movementManager.getArchiveMovementDetails(entityId,
						movementId, archiveId, new SystemInfo(), SwtUtil
								.getCurrentSystemFormats(request.getSession()));

			}
			// get the entity id for movement
			entityId = movement.getId().getEntityId();
			// set the posting date for movement
			movement.setPostingDateAsString((SwtUtil.formatDate(movement
					.getPostingDate(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue())));
			// set the ExpectedSettlementDateTime for movement
			movement.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(movement
					.getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue() + " HH:mm:ss")));
			// set the SettlementDateTime for movement
			movement.setSettlementDateTimeAsString((SwtUtil.formatDate(movement
					.getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue() + " HH:mm:ss")));
			// put the position level,entity,sign,pay
			// channel,description,party list in request to populate the
			// corresponding select box in movement display screen.
			putPositionLevelListInReq(request, entityId, movement
					.getInputSource());
			putEntityListInReq(request);
			putSignListInReq(request);
			putDescriptionsInReq(request, movement);
			putPartyListInReq(request, hostId, entityId, movement
							.getCounterPartyId(), movement.getBeneficiaryId(), movement
							.getCustodianId(), movement.getMatchingParty(), movement.getOrderingCustomerId(), movement.getOrderingInstitutionId(),
					movement.getSenderCorrespondentId(), movement.getReceiverCorrespondentId(), movement.getIntermediaryInstitutionId(),
					movement.getAccountWithInstitutionId(), movement.getBeneficiaryCustomerId());
			// maintain the current entity in the request session
			request.getSession().setAttribute("entityIdInSession",
					movement.getId().getEntityId());
			// set the movement id in the movement bean and set the movement
			// bean into form/request.
			movement.getId().setMovementIdAsString(
					movement.getId().getMovementId().toString());

			//Mantis 6250
			if (SwtUtil.isEmptyOrNull(movement.getIlmFcastStatus())) {
				movement.setIlmFcastStatus("E");
			}

			// set the movement into dyna Form to set the form values
			setMovement(movement);
			request.setAttribute("movement", movement);
			// get the movement from request session
			movementIdInSession = (String) request.getSession().getAttribute(
					"movementIdInSession");
			// call to set the Match, Message and Change buttons based on
			// Entity, CurrencyGroup and MessageId Access
			setButtonInfo(request, movement);
			if (movementIdInSession != null) {
				// remove the movement id from request session
				request.getSession().removeAttribute("movementIdInSession");
			}
			// set the screenFieldsStatus for movement display screen which is
			// used to disable the component .
			request.setAttribute("screenFieldsStatus", "true");
			// used to denote the parent for opened movement display screen
			request.setAttribute("parentScreen", "movementSummaryDisplay");
			// used to denote if notes present for movement
			request.setAttribute("isNotesPresent", request
					.getParameter("isNotesPresent"));
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// set the attribute to currGrpAccess
			request.setAttribute("currGrpAccess", SwtUtil
					.getSwtMaintenanceCache().getCurrencyAccess(roleId,
							movement.getId().getEntityId(),
							movement.getCurrencyCode()));
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "yes");
			// archive id for database in which movement would be available
			request.setAttribute("archiveId", archiveId);
			// set the attribute to identify which screen is opened
			request.setAttribute("screenIdentifier", "movementDisplay");
			// Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement
			// is not locked while changing
			request.setAttribute("initialinputscreen", request
					.getParameter("initialInputScreen"));
			if (movement != null) {
				// set the attribute for openUnopenFlag in request based on this
				// flag display the open or unopen button
				request.setAttribute(SwtConstants.OPEN_UNOPEN_FLAG, movement
						.getOpenFlag());
			}
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'showMovementDetails' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'showMovementDetails' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "showMovementDetails", MovementAction.class), request,
					"");
			return getView("fail");
		} finally {
			// clear the datasource instance
			((SwtDataSource) SwtUtil.getBean("dataSourceDb")).clearArchive();
			// nullify objects
			archiveId = null;
			dataSource = null;
			hostId = null;
			movement = null;
			movementId = null;
			entityId = null;
			movementIdInSession = null;
			roleId = null;
			log.debug(this.getClass().getName()
					  + " - [showMovementDetails] - Existing");
		}
		return getView("success");
	}

	/**
	 *
	 * This method is called while clicking on cancel button in manual input
	 * screen and used to clear session notes details in request.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String clearSession()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName()
					  + " - [clearSession] - Entering");
			// clear the session notes details in request session
			request.getSession().setAttribute("sessionNotesDetails", null);
			log.debug(this.getClass().getName()
					  + " - [clearSession] - Existing");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'clearSession' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "clearSession", MovementAction.class), request, "");
			return getView("fail");
		}
		return add();
	}

	/**
	 *
	 * This method is used to get access for entity for given entity id
	 *
	 * @param request
	 * @param entityId
	 * @return entityAccess
	 */
	private int getEntityAccessType(HttpServletRequest request, String entityId)
			throws SwtException {
		// hold the colllection of entity
		Collection<EntityUserAccess> collEntity = null;
		// default entity access
		int entityAccess;
		try {
			log.debug(this.getClass().getName()
					  + " - [getEntityAccessType] - Entering");
			// get the collection of entity based on user access
			collEntity = SwtUtil.getUserEntityAccessList(request.getSession());
			// get the access for entity
			entityAccess = SwtUtil.getUserEntityAccess(collEntity, entityId);
			log.debug(this.getClass().getName()
					  + " - [getEntityAccessType] - Existing");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
						   + " - [getEntityAccessType] - Exception -"
						   + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			collEntity = null;

		}
		return entityAccess;
	}

	/**
	 *
	 * This method is used to get access for currency for given currency id
	 *
	 * @param request
	 * @param entityId
	 * @param currencyId
	 * @return ccyGrpAccess
	 */
	private int getCcyGrpAccessType(HttpServletRequest request,
									String entityId, String currencyId) throws SwtException {
		// currency group access
		int ccyGrpAccess;
		// To hold user id
		String userId = null;
		// To hold role id
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getCcyGrpAccessType] - Entering");
			// get the current user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the role id for given user
			roleId = movementManager.getUserDetails(CacheManager.getInstance()
					.getHostId(), userId);
			// get the access for currency id
			ccyGrpAccess = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
					roleId, entityId, currencyId);
			log.debug(this.getClass().getName()
					  + " - [getCcyGrpAccessType] - Existing");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
						   + " - [getCcyGrpAccessType] - Exception -"
						   + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			userId = null;
			roleId = null;
		}
		return ccyGrpAccess;
	}

	/**
	 * This method sets the status of Match, Change and Message buttons in
	 * Movement Display screen based on Entity, CurrencyGroup, Message Access
	 *
	 * @param request
	 * @param form
	 * @param movement
	 * @throws SwtException
	 */
	private void setButtonInfo(HttpServletRequest request, 	Movement movement) throws SwtException {
		// To hold match id
		Long matchId = null;
		// To hold message id
		String messageId = null;
		// To hold access id
		int accessInd;
		// variable to hold role id
		String roleId = null;
		// to hold currGrpAccess
		int currGrpAccess;
		try {
			log.debug(this.getClass().getName()
					  + " - [setButtonInfo] - Entering");
			// set the movement id as string format in movement bean
			movement.getId().setMovementIdAsString(
					movement.getId().getMovementId().toString());
			// set the movement bean in request attribute
			request.setAttribute("movement", movement);
			// enabling or disabling log button depending on the match
			// Status of the movement displayed
			matchId = movement.getMatchId();
			messageId = movement.getMessageId();
			// get the access value for menu,entity,currency group
			accessInd = getMenuEntityCurrGrpAccess(request, movement.getId()
					.getEntityId(), movement.getCurrencyCode());
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			currGrpAccess = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
					roleId, movement.getId().getEntityId(),
					movement.getCurrencyCode());
			// based on the access value set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// screen
			if (accessInd == 0) {
				if (matchId != null) {
					if (!SwtUtil.isEmptyOrNull(messageId)) {
						// if there are match id,message id available then set
						// the button status for screen
						setButtonStatus(request,  SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_TRUE);
					} else {
						// match id is available,message id not available
						setButtonStatus(request,  SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_TRUE);
					}
				} else {
					if (!SwtUtil.isEmptyOrNull(messageId)) {
						// match id not available,message id available then set
						// the button status for screen
						setButtonStatus(request, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_TRUE);
					} else {
						// match id not available,message id not available
						setButtonStatus(request,  SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_TRUE);
					}

					request.setAttribute("EntityAccess",
							SwtConstants.ENTITY_FULL_ACCESS + "");
				}
				request.setAttribute("reconButtonStatus", SwtConstants.YES);
			} else {
				if (matchId != null) {
					if (!SwtUtil.isEmptyOrNull(messageId)) {
						// if there are match id,message id available then set
						// the button status for screen
						setButtonStatus(request,  SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
					} else {
						// match id is available,message id not available
						setButtonStatus(request,  SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
					}
				} else {
					if (!SwtUtil.isEmptyOrNull(messageId)) {
						// match id not available,message id available then set
						// the button status for screen
						setButtonStatus(request,  SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
					} else {
						// match id not available,message id not available
						setButtonStatus(request,  SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
								SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
								SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
					}
					// set the read access for entity in request
					request.setAttribute("EntityAccess",
							SwtConstants.ENTITY_READ_ACCESS + "");
				}
			}
			if (currGrpAccess == 1) {
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			} else if (currGrpAccess == 2) {
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}
			log.debug(this.getClass().getName()
					  + " - [setButtonInfo] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [setButtonInfo] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			matchId = null;
			messageId = null;
			roleId = null;
		}
	}

	/**
	 * This method is called while clicking the message button on movement
	 * display screen and used to open the movement message window with message
	 * details for movement
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String mvmntMessageDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To get the movement from request
		String movementId = null;
		// To hold currenct system formats
		SystemFormats sysFormat = null;
		// To hold movement message collection
		Collection<MovementMessage> messageColl = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [mvmntMessageDisplay] - Entering");
			// get the movement id from parameter
			movementId = request.getParameter("movmentId");
			// get the current system format
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get the collection of movement message for given moevement id
			messageColl = movementManager.getMovMessageColl(movementId,
					sysFormat);
			// set the movement message collection in request and used to
			// display the message details on screen
			request.setAttribute("movMessageList", messageColl);
			log.debug(this.getClass().getName()
					  + " - [mvmntMessageDisplay] - Existing");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'mvmntMessageDisplay' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'mvmntMessageDisplay' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "mvmntMessageDisplay", MovementAction.class), request,
					"");
			return getView("fail");
		} finally {
			// nullify objects
			movementId = null;
			sysFormat = null;
			messageColl = null;
		}
		return getView("movMessage");
	}

	/**
	 * This method is used to update the movement status to reconcile while
	 * clicking on recon button in movement display
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String updateMatchStatusToReconciled() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To get the form values
		// DynaValidatorForm dynaForm = null;
		// To get the movement from db
		Movement formMovement = null;
		// Movement Id in long
		Long movementId = null;
		// To hold the movement bean
		Movement movement = null;
		// To hold the parent screen name
		String parentScreen = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [updateMatchStatusToReconciled] - Entering");
			// Instantiate the // DynaValidatorForm
			// get the movement from form
			formMovement = (Movement) getMovement();
			// Instantiate movement id in long
			movementId = new Long(formMovement.getId().getMovementIdAsString());
			// get the movement for given movement id
			movement = movementManager.getMovementDetails(CacheManager
							.getInstance().getHostId(), movementId, new SystemInfo(),
					SwtUtil.getCurrentSystemFormats(request.getSession()));
			// Setting matchStatus to Reconciled 'E'
			movement.setMatchStatus(SwtConstants.RECONCILE_STATUS);
			if (movement != null) {
				request.setAttribute(SwtConstants.OPEN_UNOPEN_FLAG, movement
						.getOpenFlag());
			}
			// update the changed movement details
			movementManager.updateMovementDetails(movement, "", null);
			// Code to find out if save is done from via the path
			// movementSummarydisplay-->mvmnt-->change-->save.
			// based on parent screen forward the mappping
			parentScreen = request.getParameter("parentScreen");
			if ((parentScreen != null)
				&& parentScreen.equals("movementSummaryDisplay")) {
				// denote the parent of the movement display screen
				request.setAttribute("parentScreen", parentScreen);
				log.debug(this.getClass().getName()
						  + " - [updateMatchStatusToReconciled] - Existing");
				return showMovementDetails();
			} else {
				log.debug(this.getClass().getName()
						  + " - [updateMatchStatusToReconciled] - Existing");
				return displayMovement();
			}
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'updateMatchStatusToReconciled' method : "
						   + swtexp.getMessage());
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "yes");
			// set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// movementdisplay
			setButtonStatus(request,  SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'updateMatchStatusToReconciled' method : "
						   + exp.getMessage());
			// based on showMovementId flag the components will be decided
			request.setAttribute("showMovementId", "yes");
			// set the enable/disable status for
			// save,copyfrom,change,open,match,notes,log,message,close button in
			// movementdisplay
			setButtonStatus(request,  SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(exp,
							"updateMatchStatusToReconciled",
							MovementAction.class), request, ""));
			return getView("fail");
		} finally {
			// nullify objects
			formMovement = null;
			movementId = null;
			movement = null;
			parentScreen = null;
		}
	}

	/**
	 * This method is used to update the movement status to open/unopen while
	 * clicking on open/unopen button in movement display
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String updateOpenUnopenFlag() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To get the form values
		// DynaValidatorForm dynaForm = null;
		// To hold movement
		Movement movement = null;
		// movement id in long
		Long movementId = null;
		// To hold the open status flag
		String openFlag = null;
		// To hold update user
		String updateUser = null;
		// To hold parent screen name
		String parentScreen = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [updateOpenUnopenFlag] - Entering");
			// Instantiate the // DynaValidatorForm
			// get the movement beamn from form .
			movement = (Movement) getMovement();
			// Instantiate the movement id
			movementId = new Long(0);
			// assign value for movement id
			if (movement.getId().getMovementIdAsString() != null) {
				movementId = new Long(movement.getId().getMovementIdAsString());
			}
			// get the open/unopen flag from request
			openFlag = request.getParameter(SwtConstants.OPEN_UNOPEN_FLAG);
			// get the user from request session
			updateUser = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId()
					.getUserId();
			// update the open/unopen flag for movement
			movementManager.updateOpenUnopenFlag(CacheManager.getInstance()
							.getHostId(), movement.getId().getEntityId(), movementId,
					openFlag, updateUser);
			// get the parent screen from request
			parentScreen = request.getParameter("parentScreen");
			// based on parentScreen return the mapping forward
			if ((parentScreen != null)
				&& parentScreen.equals("movementSummaryDisplay")) {
				// denote the parent of the movement display screen
				request.setAttribute("parentScreen", parentScreen);
				log.debug(this.getClass().getName()
						  + " - [updateOpenUnopenFlag] - Existing");
				return showMovementDetails();
			} else {
				log.debug(this.getClass().getName()
						  + " - [updateOpenUnopenFlag] - Existing");
				return displayMovement();
			}
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'updateOpenUnopenFlag' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'updateOpenUnopenFlag' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "updateOpenUnopenFlag", MovementAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			movement = null;
			movementId = null;
			openFlag = null;
			updateUser = null;
			parentScreen = null;
		}
	}

	/**
	 * This method is used to get the collection of the external postion level
	 * list for given host id and entity id
	 *
	 * @param hostId
	 * @param entityId
	 * @return extPositionLevelList
	 * @throws SwtException
	 */

	private Collection<String> getExternalPositionLevels(String hostId,
														 String entityId) throws SwtException {
		// EntityManager instance
		EntityManager entityManager = null;
		// whole collection of postion level
		Collection<EntityPositionLevel> collPostionLvl = null;
		// hold collection of external postion level
		Collection<String> extPositionLevelList = null;
		// iterate the external postion level list
		Iterator<EntityPositionLevel> itrPositionLvl = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getExternalPositionLevels] - Entering");
			// Instantiate the EntityManager
			entityManager = (EntityManager) SwtUtil.getBean("entityManager");
			// get the collection of postion level for entity id
			collPostionLvl = entityManager.getPosLvlNameDetails(hostId,
					entityId);
			// Instantiate the external postion level list
			extPositionLevelList = new ArrayList<String>();
			if ((collPostionLvl != null) && (collPostionLvl.size() > 0)) {
				// Iterate the collection of postion level
				itrPositionLvl = collPostionLvl.iterator();
				while (itrPositionLvl.hasNext()) {
					// get the EntityPositionLevel
					EntityPositionLevel entityPositionLevel = itrPositionLvl
							.next();
					// if indicator equals "E" then add into the
					// extPositionLevelList
					if (entityPositionLevel.getIndicator()
							.equalsIgnoreCase("E")) {
						extPositionLevelList.add(entityPositionLevel.getId()
								.getPositionLevel().toString());
					}
				}
			}
			log.debug(this.getClass().getName()
					  + " - [getExternalPositionLevels] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [getExternalPositionLevels] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			entityManager = null;
			collPostionLvl = null;
			itrPositionLvl = null;
		}
		return extPositionLevelList;
	}

	/**
	 * checkMovementId() This method is used to check the movement id which is
	 * existing or not. if given movement id is available then set true flag in
	 * response otherwise set false flag
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws IOException
	 * @throws Exception
	 */
	public String checkMovementId() throws SwtException {
		// Declare the movement object
		Movement movement = null;
		// Declare the SystemInfo object
		SystemInfo systemInfo = null;
		// To hold the movement from request
		Long movementId = null;
		String archiveId = null;
		String entityId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					  + "- [checkMovementId] - Entering ");
			// create instance for SystemInfo
			systemInfo = new SystemInfo();
			// convert the movementId as Long
			movementId = new Long(request.getParameter("movId"));
			archiveId = request.getParameter("archiveId");
			entityId = request.getParameter("entityId");
			if (SwtUtil.isEmptyOrNull(archiveId)) {
				// Get the movement details for given movement id
				movement = movementManager.getMovementDetails(CacheManager
								.getInstance().getHostId(), movementId, systemInfo,
						SwtUtil.getCurrentSystemFormats(request.getSession()));
			} else {
				movement = movementManager.getArchiveMovementDetails(entityId,
						movementId, archiveId, new SystemInfo(), SwtUtil
								.getCurrentSystemFormats(request.getSession()));

			}
			// if movement is available then set true flag otherwise set false
			// flag
			if (movement == null) {
				response.getWriter().print(false);
			} else {
				response.getWriter().print(true);
			}
			log.debug(this.getClass().getName()
					  + "- [checkMovementId] - Existing ");
		} catch (SwtException swtexp) {
            try {
                response.getWriter().print(swtexp.getMessage());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            SwtUtil.logException(swtexp, request, "");
			log.error(this.getClass().getName()
					  + "- [checkMovementId] - SwtException "
					  + swtexp.getMessage());
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + "- [checkMovementId] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkMovementId", MovementAction.class), request, "");
            try {
                response.getWriter().print(exp.getMessage());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } finally {
			// nullify objects
			movement = null;
			systemInfo = null;
		}
		return null;
	}

	/**
	 * This method is used to change the movement details while clicking on the
	 * change button in movement display screen.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the role id
		String roleId = null;
		// Variable to hold the authorize status
		String authorizeStatus = null;
		// To hold Entity Id
		String entityId = null;
		// To hold the form values
		// DynaValidatorForm dynaForm = null;
		// To hold the movement form
		Movement formMovement = null;
		// To hold the movement id in string
		String movementIdAsString = null;
		// Movement instance
		Movement movement = null;
		// movement id in long
		Long movementId = null;
		// To hold the host id
		String hostId = null;
		// To hold the parent screen
		String parentScreen = null;
		// Movement bean hold only movement id
		Movement movIdBean = null;
		// collecion of miusc params
		Collection<MiscParams> collMisc = null;
		// editable flag array
		Hashtable editFlagArr = null;
		// System date in string format
		String systemDateAsString = null;
		// collection of external psotion level
		Collection<String> collExtPostionLvl = null;
		// Iterate the collection of external psotion level
		Iterator<String> itrExtPostionLvl = null;
		// PreAdviceInputManager instance
		PreAdviceInputManager preAdviceInputManager = null;
		try {
			log.debug(this.getClass().getName() + " - [change] - Entering");
			// To get the form values
			// assignment for form values
			formMovement = (Movement) getMovement();
			// get the movement id from form
			movementIdAsString = formMovement.getId().getMovementIdAsString();
			// get the host id from cahce manager
			hostId = CacheManager.getInstance().getHostId();
			// get the parentScreen from request
			parentScreen = request.getParameter("parentScreen");
			// movement id in long format
			movementId = new Long(movementIdAsString);
			// get the movement for given movement id from database
			movement = movementManager.getMovementDetails(hostId, movementId,
					new SystemInfo(), SwtUtil.getCurrentSystemFormats(request
							.getSession()));
			if (movement == null) {
				// set attribute for Movement Available flag used to open alert
				// message about unavailable for movement
				request.setAttribute("movementAvailable", "no");
				// Instantiate the movement id bean
				movIdBean = new Movement();
				// set the movemeny id and set into form
				movIdBean.getId().setMovementIdAsString(movementIdAsString);
				// set the movement into dyna Form to set the form values
				setMovement(movIdBean);
				// set the enable/disable status for
				// save,copyfrom,change,open,match,notes,log,message,close
				// button in movementdisplay
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
				if ((parentScreen != null)
					&& parentScreen.equals("movementSummaryDisplay")) {
					// denote parent of movement display screen
					request.setAttribute("parentScreen", parentScreen);
				}
				// based on showMovementId flag the components will be decided
				// for
				// movement display
				request.setAttribute("showMovementId", "yes");
				// set the screenFieldsStatus for movement display screen which
				// is
				// used to disable the component .
				request.setAttribute("screenFieldsStatus", "true");
				// set the attribute to identify which screen is opened
				request.setAttribute("screenIdentifier", "movementDisplay");
			} else {
				// set the status for movement whether it is in open/unopen
				request.setAttribute(SwtConstants.OPEN_UNOPEN_FLAG, movement
						.getOpenFlag());
				// get the movement id and set into movement
				movementIdAsString = movement.getId().getMovementId()
						.toString();
				movement.getId().setMovementIdAsString(movementIdAsString);
				/**
				 * get the entity Id,currency code from movement which has taken
				 * from the DB.
				 */
				entityId = movement.getId().getEntityId();
				// set the posting date for movement
				movement.setPostingDateAsString((SwtUtil.formatDate(movement
						.getPostingDate(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue())));
				// set the ExpectedSettlementDateTime for movement
				movement.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(movement
						.getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue() + " HH:mm:ss")));
				// set the SettlementDateTime for movement
				movement.setSettlementDateTimeAsString((SwtUtil.formatDate(movement
						.getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue() + " HH:mm:ss")));
				// set the button status
				setButtonStatus(request,  SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
						SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
						SwtConstants.STR_TRUE, SwtConstants.STR_FALSE);
				// get the collection of movement field which has assigned to
				// corresponding entity id
				collMisc = CacheManager.getInstance().getMiscParams(
						"MOVEMENTFIELD", entityId);
				// Instantiate the editable flag array
				editFlagArr = new Hashtable<String, String>();
				// get the all ediatable flage for movement field and set it
				// into request
				editFlagArr = movementManager.getEditFlagDetails(hostId,
						entityId, movement.getInputSource());
				putEditFlagsInRequest(request, editFlagArr, movement
						.getInputSource());
				// put currency full access list in request
				putCurrencyFullAccessListInReq(request, hostId, entityId);
				// put the position level,entity,sign,pay
				// channel,description,party list in request to populate the
				// corresponding select box in movement display screen.
				putPositionLevelListInReq(request, entityId, movement
						.getInputSource());
				putEntityListInReq(request);
				putSignListInReq(request);
				putDescriptionsInReq(request, movement);
				putPartyListInReq(request, hostId, entityId, movement
								.getCounterPartyId(), movement.getBeneficiaryId(),
						movement.getCustodianId(), movement.getMatchingParty(), movement.getOrderingCustomerId(), movement.getOrderingInstitutionId(),
						movement.getSenderCorrespondentId(), movement.getReceiverCorrespondentId(), movement.getIntermediaryInstitutionId(),
						movement.getAccountWithInstitutionId(), movement.getBeneficiaryCustomerId());
				// get the current system date from database and set into
				// request
				systemDateAsString = SwtUtil.formatDate(SwtUtil
						.getSystemDatewithoutTime(), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
				request.setAttribute("systemDateAsString", systemDateAsString);
				// based on showMovementId flag the components will be decided
				// for
				// movement display
				request.setAttribute("showMovementId", "yes");
				// set the screenFieldsStatus for movement display screen which
				// is
				// used to disable the component .
				request.setAttribute("screenFieldsStatus", "true");

				if ((parentScreen != null)
					&& parentScreen.equals("movementSummaryDisplay")) {
					// denotes parent of movement display screen.
					request.setAttribute("parentScreen", parentScreen);
				}

				// get the collection of external postionl level
				collExtPostionLvl = getExternalPositionLevels(hostId, entityId);
				if ((collExtPostionLvl != null)
					&& (collExtPostionLvl.size() > 0)) {
					// iterate the collection of external postion level
					itrExtPostionLvl = collExtPostionLvl.iterator();
					while (itrExtPostionLvl.hasNext()) {
						// set the flag for show external flag
						if (itrExtPostionLvl.next().equalsIgnoreCase(
								movement.getPositionLevelAsString())) {
							request.setAttribute("showExternal", "Y");
						}
					}
				}

				// set the movement into dyna form
				setMovement(movement);
				request.setAttribute("showMovementId", "yes");
				if ((parentScreen != null)
					&& parentScreen.equals("movementSummaryDisplay")) {
					request.setAttribute("parentScreen", parentScreen);
				}
				// set the parentscreen attibute
				if (!SwtUtil.isEmptyOrNull(parentScreen)
					&& parentScreen.equals("authorise")) {
					request.setAttribute("parentScreen", "authorise");
				}
				// Create instance of PreAdviceInputManager
				preAdviceInputManager = (PreAdviceInputManager) SwtUtil
						.getBean("preAdviceInputManager");
				// get the role id
				roleId = SwtUtil.getCurrentUser(request.getSession())
						.getRoleId();
				// get the authorize Status
				authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);
				// set the authorizeStatus attribute
				if (!movement.getMatchStatus().equals("A")) {
					request.setAttribute("authorizeStatus", authorizeStatus);
				}

				if(SwtUtil.isEmptyOrNull(movement.getIlmFcastStatus())){
					movement.setIlmFcastStatus("E");
				}
				// set the movement in request which will be used to take the
				// specific value for movement in front end
				request.setAttribute("movement", movement);
				// set the attribute to identify which screen is opened
				request.setAttribute("screenIdentifier", "movementChange");
				// set the screenFieldsStatus for movement display screen which
				// is
				// used to disable the component .
				request.setAttribute("screenFieldsStatus", "false");
			}
			// Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement
			// is not locked while changing
			request.setAttribute("initialinputscreen", request
					.getParameter("initialInputScreen"));
			log.debug(this.getClass().getName() + " - [change] - Existing");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in MovementAction.'change' method : "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MovementAction.'change' method : "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", MovementAction.class), request, "");

			return getView("fail");
		} finally {
			// nullify objects
			roleId = null;
			authorizeStatus = null;
			entityId = null;
			formMovement = null;
			movementIdAsString = null;
			movement = null;
			movementId = null;
			hostId = null;
			parentScreen = null;
			movIdBean = null;
			collMisc = null;
			editFlagArr = null;
			systemDateAsString = null;
			collExtPostionLvl = null;
			itrExtPostionLvl = null;
			preAdviceInputManager = null;
		}
	}

	/**
	 * putEditFlagsInRequest() This method is used to set the movement fields
	 * for the movement display screen based on these value component will be
	 * decided at runtime
	 *
	 * @param request
	 * @param editFlagArr
	 * @param inputSource
	 * @throws SwtException
	 */
	private void putEditFlagsInRequest(HttpServletRequest request,
									   Hashtable<String, Integer> editFlagArr, String inputSource) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					  + " - [putEditFlagsInRequest] - Entering ");
			request.setAttribute("valueDateEditStatus", editFlagArr.get("Value Date")!=null && editFlagArr.get("Value Date") == 1?"false":"true");
			request.setAttribute("amountEditStatus", editFlagArr.get("Amount")!=null && editFlagArr.get("Amount") == 1?"false":"true");
			request.setAttribute("posLevelEditStatus", editFlagArr.get("Position Level")!=null && editFlagArr.get("Position Level") == 1?"false":"true");
			request.setAttribute("accountEditStatus", editFlagArr.get("Account")!=null && editFlagArr.get("Account") == 1?"false":"true");
			request.setAttribute("bookCodeEditStatus", editFlagArr.get("Book Code")!=null && editFlagArr.get("Book Code") == 1?"false":"true");
			request.setAttribute("preStatusEditStatus", editFlagArr.get("Predict Status") !=null && editFlagArr.get("Predict Status") == 1?"false":"true");
			request.setAttribute("refEditStatus", editFlagArr.get("References") !=null && editFlagArr.get("References") == 1?"false":"true");
			request.setAttribute("cpartyEditStatus", editFlagArr.get("Counterparty")!=null && editFlagArr.get("Counterparty") == 1?"false":"true");
			request.setAttribute("benEditStatus", editFlagArr.get("Beneficiary") != null && editFlagArr.get("Beneficiary") == 1?"false":"true");
			request.setAttribute("custEditStatus", editFlagArr.get("Custodian")!= null && editFlagArr.get("Custodian") == 1?"false":"true");
			request.setAttribute("extBalStatus", editFlagArr.get("External Status") != null && editFlagArr.get("External Status") == 1?"false":"true");
			request.setAttribute("ilmFcastStatusEditStatus", editFlagArr.get("Ilm Forcast Status") !=null && editFlagArr.get("Ilm Forcast Status") == 1?"false":"true");
			request.setAttribute("UETREditStatus", editFlagArr.get("UETR") !=null && editFlagArr.get("UETR") == 1?"false":"true");
			request.setAttribute("MatchPartyEditStatus", editFlagArr.get("Matching Party")!=null && editFlagArr.get("Matching Party") == 1?"false":"true");
			request.setAttribute("PrdTypeEditStatus", editFlagArr.get("Product Type")!=null && editFlagArr.get("Product Type") == 1?"false":"true");
			request.setAttribute("PostDateEditStatus", editFlagArr.get("Posting Date")!=null && editFlagArr.get("Posting Date") == 1?"false":"true");
			request.setAttribute("ExpSettEditStatus", editFlagArr.get("Exp Sett Date")!=null && editFlagArr.get("Exp Sett Date") == 1?"false":"true");
			request.setAttribute("ActualSettEditStatus", editFlagArr.get("Act Sett Date")!=null&& editFlagArr.get("Act Sett Date") == 1?"false":"true");
			request.setAttribute("CritPayTypeEditStatus", editFlagArr.get("Crit Pay Type")!=null && editFlagArr.get("Crit Pay Type") == 1?"false":"true");

			request.setAttribute("OrederCustEditStatus", editFlagArr.get("Ordering Customer")!=null && editFlagArr.get("Ordering Customer") == 1?"false":"true");
			request.setAttribute("OrederInstEditStatus", editFlagArr.get("Ordering Institution")!=null && editFlagArr.get("Ordering Institution") == 1?"false":"true");
			request.setAttribute("SendCorresEditStatus", editFlagArr.get("Sender's Corresp")!=null && editFlagArr.get("Sender's Corresp") == 1?"false":"true");
			request.setAttribute("ReceivCorrespEditStatus", editFlagArr.get("Receiver's Corresp")!=null && editFlagArr.get("Receiver's Corresp") == 1?"false":"true");
			request.setAttribute("IntermInstitEditStatus", editFlagArr.get("Intermed. Institution")!=null && editFlagArr.get("Intermed. Institution") == 1?"false":"true");
			request.setAttribute("ActWithInstitEditStatus", editFlagArr.get("Act with Institution")!=null && editFlagArr.get("Act with Institution") == 1?"false":"true");
			request.setAttribute("BenfCustomEditStatus", editFlagArr.get("Beneficiary Customer")!=null && editFlagArr.get("Beneficiary Customer") == 1?"false":"true");
			request.setAttribute("SendToReceivInfEditStatus", editFlagArr.get("Send To Receiv")!=null && editFlagArr.get("Send To Receiv") == 1?"false":"true");



			log.debug(this.getClass().getName()
					  + " - [putEditFlagsInRequest] - Existing ");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'putEditFlagsInRequest' method : "
						   + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putEditFlagsInRequest", MovementAction.class);
		}
	}

	/**
	 * This method is used to get the message count for given movement id.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getMvmntMessageCount() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// get the movement id from request
		String movementId = null;
		// get the system format from request
		SystemFormats sysFormat = null;
		// get the collection of message
		Collection<MovementMessage> messageColl = null;
		// hold the message id
		String msgId = null;
		// iterate the collection of message
		Iterator<MovementMessage> itrMessageColl = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getMvmntMessageCount] - Entering");
			// get the movement id from request
			movementId = request.getParameter("movmentId");
			// get the currenct system formats from request
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get the collection of message for given movement and set it into
			// request
			messageColl = movementManager.getMovMessageColl(movementId,
					sysFormat);
			request.setAttribute("movMessageList", messageColl);
			msgId = "";
			if ((messageColl != null) && (messageColl.size() == 1)) {
				// iterate the message collection
				itrMessageColl = messageColl.iterator();
				while (itrMessageColl.hasNext()) {
					// get the MovementMessage from collection
					MovementMessage movMessage = itrMessageColl.next();
					if (movMessage.getMessageId() != null) {
						// set the message id
						msgId = movMessage.getMessageId().toString();
					}
				}
			}
			// write the message id into response
			response.getWriter().print(messageColl.size() + "|" + msgId);
			log.debug(this.getClass().getName()
					  + " - [getMvmntMessageCount] - Existing");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + "- [getMvmntMessageCount] - SwtException "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + "- [getMvmntMessageCount] - Exception "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getMvmntMessageCount", MovementAction.class),
					request, "");
		} finally {
			// nullify objects
			movementId = null;
			sysFormat = null;
			messageColl = null;
			msgId = null;
			itrMessageColl = null;
		}
		return null;
	}

	/**
	 *
	 * This method is used to get the movement status for given movement id
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkMovementStatus() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// get the movement id to check the status
		String movementIds = null;
		// hold the entity id
		String entityId = null;
		try {
			log.debug(this.getClass().getName()
					  + "- [checkMovementStatus] - Entering ");
			// get the movementids
			movementIds = request.getParameter("movementIds");
			// get the entity id
			entityId = request.getParameter("entityId");
			// check the movement status
			// and set the status
			response.getWriter().print(
					movementManager.checkMovementStatus(movementIds, entityId,
							CacheManager.getInstance().getHostId()));
			log.debug(this.getClass().getName()
					  + "- [checkMovementStatus] - Existing ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + "- [checkMovementStatus] - SwtException "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
						   + "- [checkMovementStatus] - Exception "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "checkMovementStatus", MovementAction.class), request,
					"");
		} finally {
			// nullify objects
			movementIds = null;
			entityId = null;
		}
		return null;
	}

	private CrossReference crossReference;

	/**
	 * This method is used to display the Cross Reference for corresponding
	 * Movement
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String crossReference()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Variable Declaration for DynaValidatorForm */
		// DynaValidatorForm dynaForm = null;
		/* For storing Movement ID as String type */
		String movementIdAsString = null;
		/* For storing Movement ID as Long type */
		Long movementId = null;
		/* For storing Entity ID from request */
		String entityId = null;
		/* Variable Declaration for CrossReference */
		CrossReference crossReference = null;
		/* Variable Declaration for Iterator */
		Iterator<CrossReference> itrCrossRef = null;
		/* Variable Declaration for Cross Reference Array List */
		ArrayList<CrossReference> crossReferenceCollTemp = null;
		ArrayList<CrossReference> crossReferenceColl = null;
		/* Variable to hold date as Sting */
		String dateFormat = null;
		/* Variable to hold date format */
		SimpleDateFormat simpleDateFmt = null;
		/* Variable to hold Cross Reference */
		CrossReference crossReferenceData = null;
		try {
			log.debug(this.getClass().getName()
					  + "- [crossReference] - Entering ");
			crossReference = (CrossReference) (getCrossReference());
			crossReferenceCollTemp = new ArrayList<CrossReference>();
			/* get Movement Id from request */
			movementIdAsString = request.getParameter("movId");
			/* check Whether Movement Id is null. */
			if (SwtUtil.isEmptyOrNull(movementIdAsString)) {
				movementIdAsString = request.getParameter("selectedMovId");
			}
			/* check Whether Movement Id is not null. */
			if (movementIdAsString != null) {
				movementIdAsString = movementIdAsString.trim();
			}
			/* convert Movement Id from String to Long. */
			movementId = new Long(movementIdAsString);
			/* get Entity Id from request. */
			entityId = request.getParameter("entityCode");
			/* check whether Entity Id is empty. */
			if (entityId.equalsIgnoreCase("")) {
				entityId = request.getParameter("selectedEntityId");
			}
			/* set Movement Id in Cross Reference Object. */
			crossReference.setMovementId(movementId);
			/* get Cross Reference Object from Database. */
			crossReferenceCollTemp = movementManager.getCrossReference(
					CacheManager.getInstance().getHostId(), entityId,
					movementId);
			/* get iterator over ArrayList<CrossReference> */
			itrCrossRef = crossReferenceCollTemp.iterator();
			crossReferenceColl = new ArrayList<CrossReference>();
			/* get Current System Date format from session. */
			dateFormat = SwtUtil.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue();
			/* set date format with time format. */
			simpleDateFmt = new SimpleDateFormat(dateFormat + " " + "HH:mm:ss");
			/*
			 * Iterate the Collection and set formated UpdateDate in
			 * CrossReference Object.
			 */
			while (itrCrossRef.hasNext()) {
				crossReferenceData = itrCrossRef.next();
				crossReferenceData.setUpdateDateTime(simpleDateFmt
						.format(crossReferenceData.getUpdateDate()));
				crossReferenceColl.add(crossReferenceData);
			}
			/* Setting required attributes. */
			request.setAttribute("listViewData", crossReferenceColl);
			request.setAttribute("movId", movementIdAsString);
			request.setAttribute("entityId", entityId);
			request.setAttribute("methodName", "crossReference");
			/* set CrossReference value in form bean. */
			setCrossReference(crossReference);
			log.debug(this.getClass().getName() + "- [crossReference] - Exit ");
			return getView("crossReference");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + "- [crossReference] - SwtException "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + "- [crossReference] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "crossReference", MovementAction.class), request, "");
			return getView("fail");
		} finally {
			movementIdAsString = null;
			movementId = null;
			entityId = null;
			crossReference = null;
			itrCrossRef = null;
			crossReferenceCollTemp = null;
			crossReferenceColl = null;
			dateFormat = null;
			simpleDateFmt = null;
			crossReferenceData = null;
		}
	}

	/**
	 * This method is used to to check the External Position Levels for the
	 * external balance and get default entity for the position level
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkExternalPositionLevels() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable declaration for entityId
		String entityId = null;
		// Instance the Entity object
		Entity entity = null;
		// variable declaration for externalValue
		String externalValue = null;
		try {
			log.debug(this.getClass().getName()
					  + "- [checkExternalPositionLevels] - Entering ");
			// Getting entityId
			entityId = request.getParameter("entityId");
			// Innstatiate the entity
			entity = new Entity();
			// Setting hostId
			entity.getId().setHostId(CacheManager.getInstance().getHostId());
			// Setting entityId
			entity.getId().setEntityId(entityId);
			// Getting EntityDefaultPositionlevel
			externalValue = movementManager
					.getEntityDefaultPositionlevel(entity);
			// Servlet Response - Returns response to the screen
			response.getWriter().print(externalValue);
			log.debug(this.getClass().getName()
					  + "- [checkExternalPositionLevels] - Exiting ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + "- [checkExternalPositionLevels] - SwtException "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + "- [checkExternalPositionLevels] - Exception "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "checkExternalPositionLevels", MovementAction.class),
					request, "");
		} finally {
			// nullify objects
			entityId = null;
			entity = null;
			externalValue = null;
		}
		return null;
	}

	/**
	 * This method is used to get the account list for given entity id,currency
	 * code and it will be invoked while clicking account select box button.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getAccountsList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// Movement Type
		String movType = null;
		// Input Source
		String inputSource = null;
		// text for sending response
		StringBuffer responseText = null;
		// Main Account list
		Collection<LabelValueBean> collAcctList = null;
		// Iterator for account list
		Iterator<LabelValueBean> itrAcctList = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getAccountsList] - Entering");
			// get the entityId,currencyCode,movType,inputSource from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			movType = request.getParameter("movType");
			inputSource = request.getParameter("inputSource");
			// Initialize the string buffer
			responseText = new StringBuffer("");
			// get the AccountList
			collAcctList = movementManager.getAccountIdDropDown(CacheManager
							.getInstance().getHostId(), entityId, currencyCode,
					movType, inputSource);
			// Iterate the collection
			itrAcctList = collAcctList.iterator();
			while (itrAcctList.hasNext()) {
				// get the LabelValueBean
				LabelValueBean lBean = itrAcctList.next();
				responseText.append(lBean.getLabel()).append("~~~").append(
						lBean.getValue()).append("\n");
			}
			// write the account list into response
			response.getWriter().print(responseText.toString());
			log.debug(this.getClass().getName()
					  + " - [getAccountsList] - Existing");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'getAccountsList' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'getAccountsList' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getAccountsList", MovementAction.class), request, "");
		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
			movType = null;
			inputSource = null;
			responseText = null;
			collAcctList = null;
			itrAcctList = null;
		}
		return null;
	}

	/**
	 * This method is used to get the book code list for given entity
	 * id,currency code and it will be invoked while clicking book code select
	 * box button.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getBookCodeList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Entity Id
		String entityId = null;
		// text for sending response
		StringBuffer responseText = null;
		// Main Account list
		Collection<BookCode> collBookCode = null;
		// Iterator for book code
		Iterator<BookCode> itrBookCode = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getBookCodeList] - Entering");
			// get the entityId,currencyCode from request
			entityId = request.getParameter("entityId");
			// get the collection of book code for host id,entity id
			collBookCode = movementManager.getBookCodeList(CacheManager
					.getInstance().getHostId(), entityId);
			// Initialize the string buffer
			responseText = new StringBuffer("");
			// add empty book name and code
			responseText.append("~~~").append("").append("\n");
			if (collBookCode != null) {
				// iterate the book code collection
				itrBookCode = collBookCode.iterator();
				while (itrBookCode.hasNext()) {
					// get the bookcode
					BookCode bookCode = itrBookCode.next();
					// add the book name and code
					responseText.append(bookCode.getBookName()).append("~~~")
							.append(bookCode.getId().getBookCode())
							.append("\n");
				}
			}
			// write the book code list into response
			response.getWriter().print(responseText.toString());
			log.debug(this.getClass().getName()
					  + " - [getBookCodeList] - Existing");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MovementAction.'getBookCodeList' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementAction.'getBookCodeList' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getBookCodeList", MovementAction.class), request, "");
		} finally {
			// nullify objects
			entityId = null;
			responseText = null;
			collBookCode = null;
			itrBookCode = null;
		}
		return null;
	}

	/**
	 * This method is used to get the collection of currency full access list
	 * for given host id and entity id and set it into request which is used to
	 * populate the currency list in select box
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @throws SwtException
	 */
	private void putCurrencyFullAccessListInReq(HttpServletRequest request,
												String hostId, String entityId) throws SwtException {
		// collection of currency drop down
		Collection<LabelValueBean> currencyDropDown = null;
		// Map for currency id , currency name
		Map<String, String> currencyMap = null;
		// To iterate currency key
		Iterator<String> itrCurrencyKey = null;
		// hold the role id
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putCurrencyFullAccessListInReq] - Entering");
			// Instantiate the currency drop down
			currencyDropDown = new ArrayList<LabelValueBean>();
			// get the role id for current user
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the map for currency with full access
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);
			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					String currencyId = itrCurrencyKey.next();
					// add labelvaluebean for currency id
					currencyDropDown.add(new LabelValueBean(currencyId,
							currencyId));
				}
			}
			// set the currency drop down collection in request
			request.setAttribute("currencies", currencyDropDown);
			log.debug(this.getClass().getName()
					  + " - [putCurrencyFullAccessListInReq] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putCurrencyFullAccessListInReq] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			currencyDropDown = null;
			currencyMap = null;
			itrCurrencyKey = null;
			roleId = null;
		}
	}

	/**
	 * This function puts the description corresponding to accountId,
	 *  bookcode, entityId ,position level in the request and
	 * these values will be displayed adjacent to that corresponding id.
	 *
	 * @param request
	 * @param movement
	 * @throws SwtException
	 */
	private void putDescriptionsInReq(HttpServletRequest request,
									  Movement movement) throws SwtException {
		// hold collection of entity
		Collection<LabelValueBean> collEntity = null;
		// hold collection of position level
		Collection<LabelValueBean> collPosLevel = null;
		// hold account id for movement
		String accountId = null;
		// hold entity id for movement
		String entityId = null;
		// hold postion level for movement
		String posLevel = null;
		// iterate the entity
		Iterator<LabelValueBean> itrEntity = null;
		// Iterate the postion level
		Iterator<LabelValueBean> itrPositionLvl = null;
		// AccountMaintenanceManager instance
		AcctMaintenanceManager acctManager = null;
		// AcctMaintenance instance
		AcctMaintenance acctMaintenance = null;
		// BookCodeManager instance
		BookCodeManager bookCodeManager = null;
		// BookCode instance
		BookCode bookCodeBean = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putDescriptionsInReq] - Entering");
			// get the collection of entity from request to get the entity
			// description
			collEntity = (Collection<LabelValueBean>) request
					.getAttribute("entities");
			// get the collection of postion level list to get the postion level
			// description
			collPosLevel = (Collection<LabelValueBean>) request
					.getAttribute("positionLevelList");
			if (movement != null) {
				/**
				 * get the account id ,paychannel id,entity id, postion level
				 * for movement to take the corresponding name
				 */
				accountId = movement.getAccountId();

				entityId = movement.getId().getEntityId();
				posLevel = movement.getPositionLevelAsString();
				// set the notes are available for movement
				if ((movement.getHasNotes() != null)
					&& movement.getHasNotes().equals("Y")) {
					request.setAttribute("hasNotes", "Y");
				}
				/**
				 * Initialize the account id,book
				 * code,entity id ,pos level with empty string if the value has
				 * null value.
				 */
				if (accountId == null) {
					accountId = "";
				}

				if (entityId == null) {
					entityId = "";
				}
				if (posLevel == null) {
					posLevel = "";
				}
				// putting the entityDescription in the request to display
				// adjacent to the entity id in screen
				if (collEntity != null) {
					itrEntity = collEntity.iterator();
					while (itrEntity.hasNext()) {
						// get the label value bean for entity
						LabelValueBean lblValueBean = itrEntity.next();
						if (lblValueBean.getValue().equals(entityId)) {
							request.setAttribute("entityDesc", lblValueBean
									.getLabel());
							break;
						}
					}
				}

				// putting the position Level Description in the request to
				// display adjacent to the position level id in screen
				if (collPosLevel != null) {
					itrPositionLvl = collPosLevel.iterator();
					while (itrPositionLvl.hasNext()) {
						// get the label value bean for position level
						LabelValueBean lblValueBean = itrPositionLvl.next();
						if (lblValueBean.getValue().equals(posLevel)) {
							request.setAttribute("posLevelDesc", lblValueBean
									.getLabel());
							break;
						}
					}
				}
				// Instantiate the AcctMaintenanceManager
				acctManager = (AcctMaintenanceManager) SwtUtil
						.getBean("acctMaintenanceManager");
				// get the account details for given account id
				acctMaintenance = acctManager.getMainOrLinkAccount(entityId,
						CacheManager.getInstance().getHostId(), accountId);
				// set the account name in request to dispaly in screen
				if (acctMaintenance != null)
					request.setAttribute("accountDesc", acctMaintenance
							.getAcctname());
				// Instantiate the bookCodeManager
				bookCodeManager = (BookCodeManager) SwtUtil
						.getBean("bookCodeManager");
				// get the book code details for given book code
				bookCodeBean = bookCodeManager.getEditableData(CacheManager
						.getInstance().getHostId(), entityId, movement
						.getBookCode());
				// set the book code name in request to display in screen
				if (bookCodeBean != null)
					request.setAttribute("bookCodeDesc", bookCodeBean
							.getBookName());

			}
			log.debug(this.getClass().getName()
					  + " - [putDescriptionsInReq] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putDescriptionsInReq] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			collEntity = null;
			collPosLevel = null;
			accountId = null;
			entityId = null;
			posLevel = null;
			itrEntity = null;
			itrPositionLvl = null;
			acctManager = null;
			acctMaintenance = null;
			bookCodeManager = null;
			bookCodeBean = null;
		}
	}

	/**
	 * This method return the access index zero if menu,entity,currency group
	 * having full access otherwise return one
	 *
	 * @param request
	 * @param entityId
	 * @param currencyId
	 * @return accessInd
	 * @throws SwtException
	 */
	private int getMenuEntityCurrGrpAccess(HttpServletRequest request,
										   String entityId, String currencyId) throws SwtException {
		// access index to hold access for menu,entity,currency group
		int accessInd;
		// To hold menu access
		int menuAccess;
		// To hold entity access
		int entityAccess;
		// To hold currency group access
		int currGrpAccess;
		// To hold the role id get from session
		String roleId = null;
		// To hold the menu access get from request
		String menuAccessAsStr = null;
		// To hold the collection for user entity access list
		Collection<EntityUserAccess> collUserEntity = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getMenuEntityCurrGrpAccess] - Entering");
			// get the role id for current user from session.
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the access id for menu
			menuAccessAsStr = request.getParameter("menuAccessId");
			if (!SwtUtil.isEmptyOrNull(menuAccessAsStr)
				&& !menuAccessAsStr.equals("null")) {
				// set the integer value for menu access
				menuAccess = Integer.parseInt(menuAccessAsStr);
			} else {
				menuAccess = 0;
			}
			// if entity id is not null get the access for entity id
			if (!SwtUtil.isEmptyOrNull(entityId)) {
				// get the user accessible entity collection
				collUserEntity = SwtUtil.getUserEntityAccessList(request
						.getSession());
				// get the access for given entity
				entityAccess = SwtUtil.getUserEntityAccess(collUserEntity,
						entityId);
			} else {
				entityAccess = 0;
			}
			// if currency Id is not null get the access for currency Id
			if (!SwtUtil.isEmptyOrNull(currencyId)) {
				// get the access index for given currency id
				currGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyAccess(roleId, entityId, currencyId);
			} else {
				currGrpAccess = 0;
			}
			// IF menu,entity & currency group having full access then set the
			// access index as 0 oherwise set as 1
			if ((menuAccess == 0) && (entityAccess == 0)
				&& (currGrpAccess == 0)) {
				accessInd = 0;
			} else {
				accessInd = 1;
			}
			// set the menu access Id,menuEntityCurrGrpAccess in the request
			// attribute to get actual access for menu, entity currency group
			// based on this value button will be enabled/disabled in front end
			request.setAttribute("menuAccessId", "" + menuAccessAsStr);
			request.setAttribute("menuEntityCurrGrpAccess", "" + accessInd);
			log.debug(this.getClass().getName()
					  + " - [getMenuEntityCurrGrpAccess] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [getMenuEntityCurrGrpAccess] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			roleId = null;
			menuAccessAsStr = null;
			collUserEntity = null;
		}
		return accessInd;
	}

	public String openAlerting() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("tooltipFacilityId", request.getParameter("tooltipFacilityId"));
		request.setAttribute("tooltipMvtId", request.getParameter("tooltipMvtId"));
		return getView("alert");
	}



	public CrossReference getCrossReference() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		crossReference = RequestObjectMapper.getObjectFromRequest(CrossReference.class, request, "crossReference");
		return this.crossReference;
	}
	public void setCrossReference(CrossReference crossReference) {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("crossReference", crossReference);
		this.crossReference = crossReference;

	}




}
