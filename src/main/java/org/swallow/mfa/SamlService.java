package org.swallow.mfa;

import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.InvocationCallback;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.codehaus.jackson.map.ObjectMapper;
import org.swallow.exception.SwtException;
import org.swallow.model.SamlUserDTO;
import org.swallow.util.SwtUtil;
import org.swallow.util.XTrustProvider;
import org.swallow.util.XTrustProvider.TrustManagerFactoryImpl;

public class SamlService {
	private final static String NAME = "XTrustJSSE";

	/**
	 * Perform a POST request to a remote server
	 * 
	 * @param url
	 * @return
	 */
	public SamlUserDTO verifySamlToken(String token) {
		SamlUserDTO user = null;
		if (RegisterMFA.getInstance().useSmartAuthenticator) {
			String url = RegisterMFA.getInstance().verifyUrl;

			if (!SwtUtil.isEmptyOrNull(url)) {
				url = url.replaceAll("\\{.*?}", "" + token);
			}

			if (Security.getProvider(NAME) == null) {
				Security.insertProviderAt(new XTrustProvider(), 2);
				Security.setProperty("ssl.TrustManagerFactory.algorithm", TrustManagerFactoryImpl.getAlgorithm());
			}
			

			WebTarget managementResource = null;  
			SSLContext context = null;
			try {
				context = SSLContext.getInstance("TLSv1.2");
				final TrustManager[] trustManagerArray = {new NullX509TrustManager()};
				context.init(null, trustManagerArray, null);
			
			} catch (Exception e2) {
				e2.printStackTrace();
			}
			
			//Client client =ClientBuilder.newBuilder().trustStore(trustStore).hostnameVerifier(hnv).build();
			Client client =ClientBuilder.newBuilder().hostnameVerifier(new NullHostnameVerifier()).sslContext(context).build();
			
			managementResource = client.target(url);
			Map<String, Object> parameters = new HashMap<String, Object>();
			// Perform the remote call
			Entity<Map> entity = Entity.entity(parameters, MediaType.APPLICATION_JSON_TYPE);
			try {
				Map<String, Object> response = (Map<String, Object>) managementResource
						.request(MediaType.APPLICATION_JSON_TYPE).header("Content-type", MediaType.APPLICATION_JSON)
						.header("Authorization", token).post(entity, Map.class);
				ObjectMapper mapper = new ObjectMapper();
				user = mapper.convertValue(response, SamlUserDTO.class);
			} catch (Exception e) {
				String logString = "Adfs token verfification failed, please check the config or contact your administrator";
				System.err.println("ERROR 1: " + logString + ", cause : " + e);
				// TODO add logs
			}
		} else {
			String logString = "MFA authentification is disabled due to useSmartAuthenticator properties or to bad config ";
			// TODO add logs
			System.err.println("ERROR 2: " + logString);
		}
		return user;
	}

	/**
	 * Perform a POST request to a remote server
	 * 
	 * @param url
	 * @return
	 */
	public SamlUserDTO logoutSaml(String token) {
		SamlUserDTO user = null;
		if (RegisterMFA.getInstance().useSmartAuthenticator) {
			String url = RegisterMFA.getInstance().getLogoutUrl();

			if (!SwtUtil.isEmptyOrNull(url)) {
				url = url.replaceAll("\\{.*?}", "" + token);
			}
			WebTarget managementResource = null;  
			SSLContext context = null;
			try {
				context = SSLContext.getInstance("TLSv1.2");
				final TrustManager[] trustManagerArray = {new NullX509TrustManager()};
				context.init(null, trustManagerArray, null);
			
			} catch (Exception e2) {
				e2.printStackTrace();
			}
			
			//Client client =ClientBuilder.newBuilder().trustStore(trustStore).hostnameVerifier(hnv).build();
			Client client =ClientBuilder.newBuilder().hostnameVerifier(new NullHostnameVerifier()).sslContext(context).build();
	        WebTarget target = client.target(url);
		        		
		        		
			Map<String, Object> parameters = new HashMap<String, Object>();
			// Perform the remote call
			try {
				target.request().async().get(new InvocationCallback<Response>() {
					          @Override
					          public void completed(Response response) {
					              String responseString = response.readEntity(String.class);
					          }

					          @Override
					          public void failed(Throwable throwable) {
					              throwable.printStackTrace();
					          }
					      });
//					      System.out.println("request returns");
			} catch (Exception e) {
				e.printStackTrace();
				String logString = "Adfs logout failed, please check the config or contact your administrator";
				System.err.println(logString);
			}
		} else {
			String logString = "MFA authentification is disabled due to useSmartAuthenticator properties or to bad config ";
			System.err.println(logString);
		}
		return user;
	}

	public static void main(String[] args) throws IOException {
		String verifyUrl = "https://**************:8443/smartauth/saml/verify";
		String hostnameAdd = getHostName(verifyUrl);
		if (Security.getProvider(NAME) == null) {
			Security.insertProviderAt(new XTrustProvider(), 2);
			Security.setProperty("ssl.TrustManagerFactory.algorithm", TrustManagerFactoryImpl.getAlgorithm());
		}
		javax.net.ssl.HttpsURLConnection.setDefaultHostnameVerifier(new javax.net.ssl.HostnameVerifier() {

			public boolean verify(String hostname, javax.net.ssl.SSLSession sslSession) {
				return hostname.equals(hostnameAdd); // or return true
			}
		});
		WebTarget managementResource = null;
		Client client = ClientBuilder.newClient();
		managementResource = client.target("https://**************:8443/smartauth/saml/verify");
		String token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzY2hlYmthIiwiYXV0aCI6IlJPTEVfU0FNTCIsImV4cCI6MTYyODg0NTM1MSwiUEhPTkVfTlVNQkVSIzAiOiIwMDMzMDYzNDI0Mjg5MSIsIlJPTEUjMSI6IkTDqXZlbG9wcGV1ciIsIkVNQUlMX0FERFJFU1MjMiI6InNhYmVyLmNoZWJrYUBzd2FsbG93dGVjaC5jb20iLCJGSVJTVF9OQU1FIzQiOiJTYWJlckNoZWJrYSIsIkZJUlNUX05BTUUjNSI6IlNBQkVSLUNIRUJLQSIsIlVTRVJfSUQjNiI6InNhYmVyLmNoZWJrYUBzd2FsbG93dGVjaC5jb20iLCJVU0VSX0lEIzciOiJzY2hlYmthIn0.c0PBKXNvjIvo-xN9i8ja6euadxH3NeL-QED-oKOPmBNnt4xkZ32TdqaLL6nDVA9YQROp9fiG9eWs-ZzFDNEJ9w";
		Map<String, Object> parameters = new HashMap<String, Object>();
		// Perform the remote call
		Entity<Map> entity = Entity.entity(parameters, MediaType.APPLICATION_JSON_TYPE);

		Map<String, Object> response = (Map<String, Object>) managementResource.request(MediaType.APPLICATION_JSON_TYPE)
				.header("Content-type", MediaType.APPLICATION_JSON).header("Authorization", token)
				.post(entity, Map.class);

		System.err.println(response);
	}

	public static String getHostName(String url) {
		URI uri;
		String hostname = null;
		try {
			uri = new URI(url);

			hostname = uri.getHost();
			// to provide faultproof result, check if not null then return only hostname,
			// without www.
			if (hostname != null) {
				return hostname.startsWith("www.") ? hostname.substring(4) : hostname;
			}
		} catch (URISyntaxException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return hostname;
	}
	
	/**
	 * Host name verifier that does not perform nay checks.
	 */
	private static class NullHostnameVerifier implements HostnameVerifier {
	 public boolean verify(String hostname, SSLSession session) {
	  return true;
	 }
	}
	
	
	private static class NullX509TrustManager implements X509TrustManager {
		 public void checkClientTrusted(X509Certificate[] chain, String authType)
		   throws CertificateException {
		 }

		 public void checkServerTrusted(X509Certificate[] chain, String authType)
		   throws CertificateException {
		 }

		 public X509Certificate[] getAcceptedIssuers() {
		  return new X509Certificate[0];
		 }
		}

}

