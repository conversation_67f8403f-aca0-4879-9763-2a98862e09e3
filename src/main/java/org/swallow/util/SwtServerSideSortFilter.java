/*
 * @(#)SwtServerSideSortFilter.java
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.util;

import org.swallow.exception.SwtException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


public class SwtServerSideSortFilter {
/**
 * @desc For Logging
 */
private static final Log log = LogFactory.getLog(SwtServerSideSortFilter.class);


/**
 * @desc This method constructs the query based on filter n sort criteria for Movement Summary screen
 * @param entityId - EntityID
 * @param query -StringBuffer  Query
 * @param filterSortStatus -String
 * @param dateFormat - String
 * @return query - StringBuffer containing the modified query
 * @throws Exception - Exception
 */
public static StringBuffer constructQuery(String entityId, StringBuffer query, String filterSortStatus, String dateFormat)
 throws SwtException {
    log.debug("Entering constructQuery Method");

    String[] filterSort = filterSortStatus.split(",");
    String filterStatus = filterSort[0].toString();
    String sortStatus = filterSort[1].toString();
    log.debug("Filter status===>" + filterStatus);
    log.debug("Sort status===>" + sortStatus);

    String[] sortValues = new String[3];
    sortValues = sortStatus.split("|");

    java.util.StringTokenizer st1 = new java.util.StringTokenizer(sortStatus,
            "|");

    int i = 0;

    while (st1.hasMoreTokens()) {
        sortValues[i] = st1.nextToken();

        i++;
    }

    String sortColumn = sortValues[0];
    String sortDesc = sortValues[1];
    log.debug("sortColumn==>" + sortColumn);
    log.debug("sortDesc==>" + sortDesc);

    String[] filterValues = new String[22];
    int[] nonString = new int[22];

    if (!(filterStatus.equals("all")) &&
            !(filterStatus.equals("undefined"))) {
        java.util.StringTokenizer st = new java.util.StringTokenizer(filterStatus,
                "|");

        i = 0;

        while (st.hasMoreTokens()) {
            String nextVal = st.nextToken();
            log.debug("nextVal--->" + nextVal);

            if (nextVal.equals("(Empty)")) {
                filterValues[i] = "is null";
                nonString[i] = 1;
            } else if (nextVal.equals("(Not empty)")) {
                filterValues[i] = "is not null";
                nonString[i] = 1;
            } else {
                filterValues[i] = nextVal;
            }

            log.debug("sortValues[i]===>" + filterValues[i]);

            i++;
        }

        //		log.debug("filterValues[0]===>"  + filterValues[0]);
        //		log.debug("filterValues[1]===>"  + filterValues[1]);
        //		log.debug("filterValues[2]===>"  + filterValues[2]);
        //		log.debug("filterValues[3]===>"  + filterValues[3]);
        if (!filterValues[0].toString().equals("All")) {
        	log.debug("---->" + filterValues[0].toString().trim() + "<---");
        	Integer posLvlId = SwtUtil.getSwtMaintenanceCache().getEntityPositionLevelId(entityId,filterValues[0].toString().trim());
            query.append(" and p.positionLevel=" + posLvlId.intValue());
        }

        if (!filterValues[1].toString().equals("All")) {
            if (dateFormat.equals("D")) {
                query.append(
                    " and to_date(to_char(p.valueDate,'DD-MM-YYYY'),'DD-MM-YYYY') = to_date('" +
                    filterValues[1] + "','DD-MM-YYYY')");
            } else {
                query.append(
                    " and to_date(to_char(p.valueDate,'MM-DD-YYYY'),'MM-DD-YYYY') = to_date('" +
                    filterValues[1] + "','MM-DD-YYYY')");
            }
        }

        if (!filterValues[3].toString().equals("All")) {
            query.append(" and p.sign='" + filterValues[3].trim() + "'");
        }

        if (!filterValues[4].toString().equals("All")) {
            query.append(" and p.currencyCode='" + filterValues[4].trim() +
                "'");
        }

        if (!filterValues[6].toString().equals("All")) {
            if (nonString[6] == 1) {
                query.append(" and p.accountId " + filterValues[6]);
            } else {
                query.append(" and p.accountId='" + filterValues[6].trim() +
                    "'");
            }
        }

        if (!filterValues[7].toString().equals("All")) {
            if (nonString[7] == 1) {
                query.append(" and p.inputDate " + filterValues[7]);
            } else {
                if (dateFormat.equals("D")) {
                    query.append(
                        " and to_date(to_char(p.inputDate,'DD-MM-YYYY HH24:MI:SS'),'DD-MM-YYYY HH24:MI:SS') = to_date('" +
                        filterValues[7] + "','DD-MM-YYYY HH24:MI:SS')");
                } else {
                    query.append(
                        " and to_date(to_char(pmov.inputDate,'MM-DD-YYYY HH24:MI:SS'),'MM-DD-YYYY HH24:MI:SS') = to_date('" +
                        filterValues[7] + "','MM-DD-YYYY HH24:MI:SS')");
                }
            }
        }

        if (!filterValues[8].toString().equals("All")) {
            if (nonString[8] == 1) {
                query.append(" and p.counterPartyId " + filterValues[8]);
            } else {
                query.append(" and p.counterPartyId='" +
                    filterValues[8].trim() + "'");
            }
        }

        if (!filterValues[9].toString().equals("All")) {
            if (nonString[9] == 1) {
                query.append(" and p.predictStatus " + filterValues[9]);
            } else {
                query.append(" and p.predictStatus='" +
                    filterValues[9].trim() + "'");
            }
        }
/* Start : UAT Phase2Defects20061208_STL2 Defect No:117 */
        if (!filterValues[11].toString().equals("All")) {
            if (nonString[11] == 1) {
                query.append(" and p.matchId " + filterValues[11]);
            } else {
                query.append(" and p.matchId="
                		+ Integer.parseInt(filterValues[11].trim()));
            }
        }

        if (!filterValues[12].toString().equals("All")) {
            if (nonString[12] == 1) {
                query.append(" and p.inputSource " + filterValues[12]);
            } else {
                query.append(" and p.inputSource='" +
                    filterValues[12].trim() + "'");
            }
        }
        if (!filterValues[13].toString().equals("All")) {
            if (nonString[13] == 1) {
                query.append(" and p.messageFormat " + filterValues[13]);
            } else {
                query.append(" and p.messageFormat='" +
                    filterValues[13].trim() + "'");
            }
        }


        if (!filterValues[15].toString().equals("All")) {
            if (nonString[15] == 1) {
                query.append(" and p.beneficiaryId " + filterValues[15]);
            } else {
                query.append(" and p.beneficiaryId='" + filterValues[15] +
                    "'");
            }
        }

        if (!filterValues[19].toString().equals("All")) {
            if (nonString[19] == 1) {
                query.append(" and p.bookCode " + filterValues[19]);
            } else {
                query.append(" and p.bookCode='" + filterValues[19].trim() +
                    "'");
            }
        }

        if (!filterValues[20].toString().equals("All")) {
            if (nonString[20] == 1) {
                query.append(" and p.custodianId " + filterValues[20]);
            } else {
                query.append(" and p.custodianId='" +
                    filterValues[20].trim() + "'");
            }
        }

    }

    if (!sortStatus.equals("none")) {
        query.append(" order by");
        log.debug("sortColumn--->" + sortColumn.trim());

      switch (Integer.parseInt(sortColumn.trim())) {
       case 0:  query.append(" p.positionLevel");
       			break;
       case 1:  query.append(" p.valueDate");
       			break;
       case 2:  query.append(" p.amount");
       			break;
       case 3:  query.append(" p.sign");
				break;
	   case 4:  query.append(" p.currencyCode");
	   			break;
	   case 5:  query.append(" p.reference1");
	   			break;
	   case 6:  query.append(" p.accountId");
	   			break;
	   case 7: query.append(" p.inputDate");
			    break;
	   case 8:  query.append(" p.counterPartyId");
	   			break;
	   case 9:  query.append(" p.predictStatus");
	   			break;
	   case 11: query.append(" p.matchId");
				break;
	   case 12: query.append(" p.inputSource");
	   			break;
	   case 13: query.append(" p.messageFormat");
				break;
	   case 15: query.append(" p.beneficiaryId");
	   			break;
	   case 16: query.append(" p.reference2");
	   			break;
	   case 17: query.append(" p.reference3");
	   			break;
	   case 18: query.append(" p.id.movementId");
	   			break;
	   case 19:	query.append(" p.bookCode");
       			break;
       case 20: query.append(" p.custodianId");
       			break;
      
      }


        if (sortDesc.equalsIgnoreCase("false")) {
            query.append(" desc");
        }
    }
//    else {
//    	if (sortStatus.equals("none")) {
//            query.append(" order by p.inputDate desc");
//        }
//    }

    return query;
} // End of constructQuery method

/* End: UAT Phase2Defects PositionLevel filter Defect */
}
