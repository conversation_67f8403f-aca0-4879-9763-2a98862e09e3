package org.swallow.util;

import java.io.Serializable;
import java.sql.CallableStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;

import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;
import jakarta.servlet.http.HttpSessionBindingEvent;
import jakarta.servlet.http.HttpSessionBindingListener;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.stereotype.Component;
import org.swallow.model.User;
import org.swallow.work.model.ILMConfig;
@Component
public class CommonDataManager implements HttpSessionBindingListener, ServletContextListener, Serializable{
	private static final long serialVersionUID = 1L;

    private User userObject;
    /**
     * variable to hold challenged Users
     */
    private User challengedUser;
    /**
     * variable for used radius checker
     */
    private SwtRadiusChecker swtRadiusChecker;
    private Collection shortcutList;
    private Collection profileList;
    private Collection screenList;
    private SystemFormats systemFormats;
    private String dateFormat;      //used for saving the data into db as per the format
    private String currencyFormatValue;  //used for saving the data into db as per the format
    private Date systemDate;   // used to store the current system date
    private String cancelExport=""; // Added for mantis 1443, to handle when the export is cancelled
    private String cancelMSDExport=""; // Added for mantis 2450: Export problem with movement summary display screen
    private ILMConfig liquidityMonitorConfig;
    private LinkedHashMap<String, Statement> ilmScreenConnectionDetails; 
    private LinkedHashMap<String, ArrayList<CallableStatement> > ilmCalculCcyProcessStatement; 
    private ArrayList<String> validTokenList; 
    private HashMap<String, XSSFCellStyle> excelStyle = null;
	private final Log log = LogFactory.getLog(CommonDataManager.class);    
	private String referer ;
    private HashMap<String, AccessItem > roleAccessControlCashList; 
    private String mfatoken = null ;
    private String bearerToken;
    private Date lastLoginDateBeforeThisAttempt = null;
    private Integer lastLoginAttemptsBeforeThis = 0;
	public User getChallengedUser() {
		return challengedUser;
	}
	public void setChallengedUser(User challenged) {
		this.challengedUser = challenged;
	}
	public SwtRadiusChecker getSwtRadiusChecker() {
		return swtRadiusChecker;
	}
	public void setSwtRadiusChecker(SwtRadiusChecker swtRadiusChecker) {
		this.swtRadiusChecker = swtRadiusChecker;
	}
	
	/**
	 * @return Returns the currencyFormat.
	 */
	public String getCurrencyFormat() {
		return systemFormats != null?systemFormats.getCurrencyFormat():null;
	}
	
	/**
	 * @param currencyFormat The currencyFormat to set.
	 */
	public void setCurrencyFormat(String currencyFormat) {
		systemFormats.setCurrencyFormat(currencyFormat);
	}
	
	/**
	 * @return Returns the dateFormat.
	 */
	public String getDateFormatValue() {
		return systemFormats != null?systemFormats.getDateFormatValue():null;
	}
	
	/**
	 * @param dateFormat The dateFormat to set.
	 */
	public void setDateFormatValue(String dateFormat) {
		systemFormats.setDateFormatValue(dateFormat);
	}
	public User getUser() {
		return userObject;
	}
	public void setUser(User userObject) {
		this.userObject = userObject;
	}
	
	/**
	 * @return Returns the profileList.
	 */
	public Collection getProfileList() {
		return profileList;
	}
	
	/**
	 * @param profileList The profileList to set.
	 */
	public void setProfileList(Collection profileList) {
		this.profileList = profileList;
	}
	
	/**
	 * @return Returns the shortcutList.
	 */
	public Collection getShortcutList() {
		return shortcutList;
	}
	
	/**
	 * @param shortcutList The shortcutList to set.
	 */
	public void setShortcutList(Collection shortcutList) {
		this.shortcutList = shortcutList;
	}
	
	/**
	 * @return Returns the screenList.
	 */
	public Collection getScreenList() {
		return screenList;
	}
	
	/**
	 * @param screenList The screenList to set.
	 */
	public void setScreenList(Collection screenList) {
		this.screenList = screenList;
	}
	
	/**
	 * @return Returns the currencyFormatValue.
	 */
	public String getCurrencyFormatValue() {
		return currencyFormatValue;
	}
	
	/**
	 * @param currencyFormatValue The currencyFormatValue to set.
	 */
	public void setCurrencyFormatValue(String currencyFormatValue) {
		this.currencyFormatValue = currencyFormatValue;
	}
	
	/**
	 * @return Returns the dateFormatValue.
	 */
	public String getDateFormat() {
		return dateFormat;
	}
	
	/**
	 * @param dateFormatValue The dateFormatValue to set.
	 */
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}
	
	/**
	 * @return Returns the systemFormats.
	 */
	public SystemFormats getSystemFormats() {
		return systemFormats;
	}
	
	/**
	 * @param systemFormats The systemFormats to set.
	 */
	public void setSystemFormats(SystemFormats systemFormats) {
		this.systemFormats = systemFormats;
	}
	
	public void valueBound(HttpSessionBindingEvent event){
		
		log.debug("entering 'valueBound' function");
		SessionManager.getInstance().registerSession(event);
		log.debug("exiting 'valueBound' function");
		
	}

	public void valueUnbound(HttpSessionBindingEvent event){
		// do nothing
		
		log.debug("entering 'valueUnbound' function");
		log.debug("exiting 'valueUnbound' function");
		
	}

	/**
	 * @return Returns the systemDate.
	 */
	public Date getSystemDate() {
		return systemDate;
	}
	
	/**
	 * @param systemDate The systemDate to set.
	 */
	public void setSystemDate(Date systemDate) {
		this.systemDate = systemDate;
	}

	// Added for Mantis 1443
	/**
	 * @return the cancelExport
	 */
	public String getCancelExport() {
		return cancelExport;
	}
	
	/**
	 * @param cancelExport the cancelExport to set
	 */
	public void setCancelExport(String cancelExport) {
		this.cancelExport = cancelExport;
	}
	//Added by Med Amine for M2352
	/**
	 * 
	 * @return liquidityMonitorConfig
	 */
	public ILMConfig getLiquidityMonitorConfig() {
		return liquidityMonitorConfig;
	}
	/**
	 * 
	 * @param liquidityMonitorConfig
	 */
	public void setLiquidityMonitorConfig(ILMConfig liquidityMonitorConfig) {
		this.liquidityMonitorConfig = liquidityMonitorConfig;
	}
	public LinkedHashMap<String, Statement> getIlmScreenConnectionDetails() {
		return ilmScreenConnectionDetails;
	}
	public void setIlmScreenConnectionDetails(
			LinkedHashMap<String, Statement> ilmScreenConnectionDetails) {
		this.ilmScreenConnectionDetails = ilmScreenConnectionDetails;
	}
	public LinkedHashMap<String, ArrayList<CallableStatement>> getIlmCalculCcyProcessStatement() {
		return ilmCalculCcyProcessStatement;
	}
	public void setIlmCalculCcyProcessStatement(
			LinkedHashMap<String, ArrayList<CallableStatement>> ilmCalculCcyProcessStatement) {
		this.ilmCalculCcyProcessStatement = ilmCalculCcyProcessStatement;
	}
	
	public void contextDestroyed(ServletContextEvent arg0) {
		// TODO Auto-generated method stub
		
	}
	
	public void contextInitialized(ServletContextEvent arg0) {
		// TODO Auto-generated method stub
		
	}
	public HashMap<String, XSSFCellStyle> getExcelStyle() {
		return excelStyle;
	}
	public void setExcelStyle(HashMap<String, XSSFCellStyle> excelStyle) {
		this.excelStyle = excelStyle;
	}
	
	public String getReferer() {
		return referer;
	}
	public void setReferer(String referer) {
		this.referer = referer;
	}
	public ArrayList<String> getValidTokenList() {
		return validTokenList;
	}
	public void setValidTokenList(ArrayList<String> validTokenList) {
		this.validTokenList = validTokenList;
	}
	
	public void addRoleAccessCache(String method, AccessItem result) {
		if(roleAccessControlCashList == null)
			roleAccessControlCashList = new HashMap<String, AccessItem>();
		
		roleAccessControlCashList.put(method, result);
	}
	
	public AccessItem getRoleAccessFromCache(String method) {
		if(roleAccessControlCashList != null) {
			if(roleAccessControlCashList.containsKey(method)){
				return roleAccessControlCashList.get(method);
			}else {
				return null;
			}
				
		}
		return null;
	}
	
	public String getCancelMSDExport() {
		return cancelMSDExport;
	}
	public void setCancelMSDExport(String cancelMSDExport) {
		this.cancelMSDExport = cancelMSDExport;
	}


	public String getMfatoken() {
		return mfatoken;
	}
	public void setMfatoken(String mfatoken) {
		this.mfatoken = mfatoken;
	}


	/**
	 * @return the bearerToken
	 */
	public String getBearerToken() {
		return bearerToken;
	}
	/**
	 * @param bearerToken the bearerToken to set
	 */
	public void setBearerToken(String bearerToken) {
		this.bearerToken = bearerToken;
	}


	public Date getLastLoginDateBeforeThisAttempt() {
		return lastLoginDateBeforeThisAttempt;
	}
	public void setLastLoginDateBeforeThisAttempt(Date lastLoginDateBeforeThisAttempt) {
		this.lastLoginDateBeforeThisAttempt = lastLoginDateBeforeThisAttempt;
	}


	public Integer getLastLoginAttemptsBeforeThis() {
		return lastLoginAttemptsBeforeThis;
	}
	public void setLastLoginAttemptsBeforeThis(Integer lastLoginAttemptsBeforeThis) {
		this.lastLoginAttemptsBeforeThis = lastLoginAttemptsBeforeThis;
	}


	public class AccessItem implements Serializable {
		private static final long serialVersionUID = 1L;
		private boolean allowAccess = false;
		private Date time = null;
		
		public AccessItem() {
		}
		public boolean isAllowAccess() {
			return allowAccess;
		}
		public void setAllowAccess(boolean allowAccess) {
			this.allowAccess = allowAccess;
		}
		public Date getTime() {
			return time;
		}
		public void setTime(Date time) {
			this.time = time;
		}
		
		
		
		
		
	}
}
