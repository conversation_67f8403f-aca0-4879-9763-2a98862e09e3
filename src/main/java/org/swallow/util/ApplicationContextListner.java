/*
 * @(#)ApplicationContextListner.java 1.0 06/07/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK  
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of 
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.util;

import java.io.File;
import java.time.Duration;
import java.util.*;
import java.util.logging.Filter;
import java.util.logging.Handler;
import java.util.logging.LogRecord;
import java.util.logging.Logger;

import jakarta.mail.Authenticator;
import jakarta.mail.PasswordAuthentication;
import javax.sql.DataSource;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimplePBEConfig;
import org.quartz.*;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.triggers.SimpleTriggerImpl;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.support.RequestHandledEvent;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.batchScheduler.ConnectionPoolMonitorJob;
import org.swallow.batchScheduler.ExecuteJavaMethodJob;
import org.swallow.batchScheduler.SwtJobScheduler;
import org.swallow.cluster.RegisterZookeeper;
import org.swallow.cluster.ZkUtils;
import org.swallow.control.service.ConnectionPoolControlManager;
import org.swallow.control.service.UserStatusManager;
import org.swallow.exception.ClusterException;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.mfa.RegisterMFA;
import org.swallow.smtp.pool.SmtpConnectionPool;
import org.swallow.smtp.transport.factory.SmtpConnectionFactoryBuilder;
import org.swallow.work.service.ILMAnalysisMonitorManager;

/**
 * The class ApplicationContextListner is used to catch the Application Context
 * Event.
 * 
 * @version 1.0 06 July 2006
 * <AUTHOR> Systems
 */
@Component
@DependsOn("dataSource")
public class ApplicationContextListner implements ApplicationListener {

	/** The log variable is used for store the log object. */
	private static final Log log = LogFactory
			.getLog(ApplicationContextListner.class);


	/**
	 * The onApplicationEvent function is called by Spring Framework to listen
	 * the application events.It does two things 1. Get the ApplicationContext
	 * from ApplicationEvent object passes as parameter in function 2. Create
	 * the SwtJobScheduler Singleton instance that start the Quartz Batch
	 * Schedular
	 * 
	 * @param arg0
	 *            the ApplicationEvent instance.
	 */
	public void onApplicationEvent(ApplicationEvent arg0) {
		String pcmEnabled = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [onApplicationEvent] - Entering - Event Type : "+arg0);
			String classPathReport = "";
			/**
			 * Called when the ApplicationContext gets initialized or refreshed
			 * Assigns the ApplicationContext Instance into SwtUtil Create the
			 * SwtJobScheduler instance
			 */
			if (arg0 instanceof ContextRefreshedEvent || arg0 instanceof ServletWebServerInitializedEvent) {
				log.info("ContextRefreshedEvent Catched");
				SwtUtil.ctx = arg0 instanceof ContextRefreshedEvent ? ((ContextRefreshedEvent) arg0).getApplicationContext() : ((ServletWebServerInitializedEvent) arg0).getApplicationContext();
				SwtUtil.appMessages_en = ResourceBundle.getBundle(SwtConstants.DICTIONARY_ENGLISH);	
				SwtUtil.appMessages_fr = ResourceBundle.getBundle(SwtConstants.DICTIONARY_FRENCH);	
				pcmEnabled = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PCM_ENABLED);
				if ("true".equals(pcmEnabled)) {
					SwtUtil.pcSessionFactory = (SessionFactory) SwtUtil.ctx.getBean("sessionFactoryPC");
					SwtUtil.updatePCMViews();
				}
				SwtUtil.sessionFactory = (SessionFactory) SwtUtil.ctx.getBean("sessionFactory");
				
				/*for( int j = 0 ; j < beanFactory.getBeanDefinitionNames().length ; j++) {
					System.err.println(beanFactory.getBeanDefinitionNames()[j]);
				}
							
				PaymentControlManager paymentControlManager = (PaymentControlManager) SwtUtil.ctx.getBean("paymentControlManager");
				paymentControlManager.saveNewDetailInRemoteDB();*/
				
				
				RegisterMFA.getInstance().register();
				
				
				
				// Register instance on zookeeper
				RegisterZookeeper rzk = new RegisterZookeeper();
				rzk.register();
				
				ZkUtils.setProperty(ZkUtils.PROPERTY_LOAD_BALACING_ENABLED,""+rzk.isLoadBalancingEnabled());
				
				if(!SwtUtil.isDeveloperMode()) {
				// Get the ilm analysis manager 
				ILMAnalysisMonitorManager ilmAnalysisMonitorManager = (ILMAnalysisMonitorManager)(SwtUtil.getBean("ilmAnalysisMonitorManager"));
				// Clean up the process driver
				ilmAnalysisMonitorManager.cleanUpProcessDriver(null, SwtConstants.USER_SYSTEM);
				// Get the ILMGeneralMaintenanceManager
				ILMGeneralMaintenanceManager ilmGeneralMaintenanceManager = (ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));
				// update the process status that has current processing status 'R' or 'TR' to 'N'
				ilmGeneralMaintenanceManager.updateProcessStatus(null, null, null, null, new Date(0), new Date(0), null, null);
	//				
				}
				// Clean S_USER_STATUS from any records 
				UserStatusManager userStatusManager = (UserStatusManager)(SwtUtil.getBean("userStatusManager"));
				boolean result = userStatusManager.truncateUserStatus();
				
				if(!result)
					log.error(this.getClass().getName()
							+ "- [onApplicationEvent] - Exception Unable to clean S_USER_STATUS table");
			
				
	            
	            // Mantis 2269: [Internal]: Enhancing predict's logging, Added by Saber Chebka on 30-05-2013: START
	            //Add the a filter by content for the console handler, so that it will not log stack trace
	            Logger rootLogger = Logger.getLogger("");
	    		Handler[] handlers = rootLogger.getHandlers();
	    		ArrayList<String> list;
	    		list = new ArrayList<String>();
	    		list.add("Error");
	    		
	    		// add classpath to jasperreport classpath
	    		 String reportsPackage = "org/swallow/reports";
	    		if (Thread.currentThread().getContextClassLoader().getResource(reportsPackage) != null) {
	    			 classPathReport = Thread.currentThread().getContextClassLoader().getResource(reportsPackage).toString();
	    		}
	    		if (!SwtUtil.isEmptyOrNull(classPathReport) && classPathReport.lastIndexOf("/Predict.jar") != -1) {
	    	     classPathReport = classPathReport.substring(classPathReport.indexOf(":/") + 1, classPathReport.lastIndexOf("/Predict.jar"));
	    	     File folder = new File(classPathReport);
			    	    if(folder.exists()) {
	    	     File[] listOfFiles = folder.listFiles();
				    	    if(listOfFiles != null) {
	    	     for (File file : listOfFiles) {
	    	              if (file.isFile()) {
	    	                  if (System.getProperty("jasper.reports.compile.class.path") == null){
	    	                	  System.setProperty("jasper.reports.compile.class.path",
	    	                	  file+System.getProperty("path.separator"));
	    	                  }else{
	    	                	  System.setProperty("jasper.reports.compile.class.path",
	    	                	  file+System.getProperty("path.separator")+
	    	                	  System.getProperty("jasper.reports.compile.class.path"));
	    	                  }
	    	              }
	    	          }	    	     
				    	    }
			    	    }
	    		}
	    		for(int i=0;i<handlers.length;i++){
	    			// Add a filter by content if handler is of type ConsoleHandler, 
	    			// Purpose: A stack trace needs not to be written in the console, however it should be on files
	    			if((""+handlers[i].getClass()).contains(SwtErrorHandler.CONSOLE_HANDLER_CLASSNAME)){
	    				handlers[i].setFilter(new Filter() {
	    			    	@Override
	    			    	public boolean isLoggable(LogRecord record) {
	    			    		boolean rtn = true;
	    			    		if((record != null && !SwtUtil.isEmptyOrNull(record.getMessage())) && (record.getMessage().startsWith(SwtErrorHandler.STACK_TRACE) || 
	    			    				record.getMessage().startsWith(SwtErrorHandler.SEVERE) || record.getMessage().indexOf(SwtErrorHandler.DATA_VALIDATION_ERROR) != -1 || record.getMessage().indexOf(SwtErrorHandler.DATA_VALIDATION_DELETE_ERROR) != -1 ||
	    			    				record.getMessage().indexOf(SwtErrorHandler.CHILD_RECORD_FOUND_ERROR) != -1 || record.getMessage().indexOf(SwtErrorHandler.COULD_NOT_SYNCHRONIZE_ERROR) != -1 || record.getMessage().indexOf(SwtErrorHandler.VALIDATORRESOURCES_ERROR) != -1
	    			    				|| record.getMessage().indexOf(SwtErrorHandler.FACES_CONFIG_CONFIGURELISTENER_ERROR) != -1 ))
	    			    			rtn = false;
									
	    			    		return rtn;	
	    			    	}
						});
	    			}
	    			else if ((""+handlers[i].getClass()).contains(SwtErrorHandler.FILE_HANDLER_CLASSNAME)){
	    				handlers[i].setFilter(new Filter() {
	    			    	@Override
	    			    	public boolean isLoggable(LogRecord record) {
	    			    		boolean rtn = true;
	    			    		if((record != null && !SwtUtil.isEmptyOrNull(record.getMessage())) && (record.getMessage().indexOf(SwtErrorHandler.DATA_VALIDATION_ERROR) != -1 || 
	    			    				record.getMessage().indexOf(SwtErrorHandler.CHILD_RECORD_FOUND_ERROR) != -1 || record.getMessage().indexOf(SwtErrorHandler.DATA_VALIDATION_DELETE_ERROR) != -1 ||
	    			    				record.getMessage().indexOf(SwtErrorHandler.COULD_NOT_SYNCHRONIZE_ERROR) != -1  || record.getMessage().indexOf(SwtErrorHandler.VALIDATORRESOURCES_ERROR) != -1
	    			    				|| record.getMessage().indexOf(SwtErrorHandler.FACES_CONFIG_CONFIGURELISTENER_ERROR) != -1 )) 
	    			    			rtn = false;
	    			    		 		
	    			    		return rtn;	
	    			    	}
						});
	    			}
	    		}
	    		// Mantis 2269: [Internal]: Enhancing predict's logging, Added by Saber Chebka on 30-05-2013: END  
	    		// The path for queries.xml file
	    		String prototypePath = this.getClass().getResource("/org/swallow/util/queries.xml").getPath().replace("%20", " ");
	    		String templatesDirectory = null;
	    		// Case jetty in Eclipse (RJR plugin): 
	    		if(prototypePath.indexOf("/WebRoot")!=-1){
	    			templatesDirectory = prototypePath.substring(prototypePath.indexOf(":/")+2, prototypePath.indexOf("/build/classes")) + "/WebRoot/templates";
	    		}
	    		else if (prototypePath.indexOf("/bin/org/swallow")!=-1){
	    			templatesDirectory = prototypePath.substring(1, prototypePath.indexOf("/bin/org/swallow")) + "/WebRoot/templates";
	    		}
	    		else if (prototypePath.indexOf("/build/classes")!=-1){
	    			templatesDirectory = prototypePath.substring(1, prototypePath.indexOf("/build/classes")) + "/WebRoot/templates";
	    		}
	    		else if (prototypePath.indexOf("/target/classes")!=-1){ // spring boot on eclipse
	    			templatesDirectory = prototypePath.substring(1, prototypePath.indexOf("/target/classes")) + "/src/main/webapp/templates";
	    		}
	    		else {
	    			// Case of jboss 7 under windows
	    			templatesDirectory = prototypePath.substring(0, prototypePath.indexOf("/WEB-INF")) + "/templates";
	    		}

	    		if(!new File(templatesDirectory).exists()) {
	    			throw new SwtException("templatesDirectory '"+ templatesDirectory +"' does not exist !");
	    		}
	    		
	    		//Fix issue related to disable client side SSL Renegotiation
	    		System.setProperty("jdk.tls.rejectClientInitiatedRenegotiation","true");
	    		System.setProperty( "sun.security.ssl.allowUnsafeRenegotiation", "false" );
	    		System.setProperty( "org.apache.jasper.compiler.Parser.STRICT_WHITESPACE", "false" );
	    		
	    		// Update the context real path:
	    		String OS = System.getProperty("os.name").toLowerCase();
	    		
	    		if ((OS.indexOf("win") >= 0)) {
	    			SwtUtil.contextRealPath = templatesDirectory.substring(0, templatesDirectory.indexOf("/templates"));
	    		} else {
	    			SwtUtil.contextRealPath = "/"
							+ templatesDirectory.substring(0, templatesDirectory.indexOf("/templates"));
	    		}

	    		
	    		if(arg0 instanceof ContextRefreshedEvent) {
		    		configurateEmailPool();
		    		SwtJobScheduler.getInstance();
					
					
					
					JobDetailImpl predictMonitorJob = null;
					SimpleTriggerImpl triggerPredict = null;
					Scheduler scheduler = null;
					predictMonitorJob = new JobDetailImpl();
					predictMonitorJob.setName("Connection Pool Predict 2 ");
					predictMonitorJob.setJobClass(ConnectionPoolMonitorJob.class);
					
					// get job data map
		            JobDataMap jdm = predictMonitorJob.getJobDataMap();
		            jdm.put(ConnectionPoolMonitorJob.MODULE_ID, "PREDICT");
		            predictMonitorJob.setJobDataMap(jdm);
					
					//configure the scheduler time
			            triggerPredict = new SimpleTriggerImpl();
		            triggerPredict.setStartTime(new Date(System.currentTimeMillis() + 1000));
		            triggerPredict.setRepeatCount(SimpleTrigger.REPEAT_INDEFINITELY);
		            triggerPredict.setRepeatInterval(10000); 
		            triggerPredict.setName("Connection Pool Predict Trigger");
					
		            
		          //schedule it
					scheduler = new StdSchedulerFactory().getScheduler();
					scheduler.start();
					scheduler.scheduleJob(predictMonitorJob, triggerPredict);
					
		            if ("true".equalsIgnoreCase(pcmEnabled)) {
			            	JobDetailImpl PCMMonitorJob = null;
			            	SimpleTriggerImpl triggerPCM = null;
							PCMMonitorJob = new JobDetailImpl();
						PCMMonitorJob.setName("Connection Pool PCM");
						PCMMonitorJob.setJobClass(ConnectionPoolMonitorJob.class);
						
						// get job data map
						jdm = PCMMonitorJob.getJobDataMap();
						jdm.put(ConnectionPoolMonitorJob.MODULE_ID, "PCM");
						PCMMonitorJob.setJobDataMap(jdm);
						
						//configure the scheduler time
							triggerPCM = new SimpleTriggerImpl();
						triggerPCM.setStartTime(new Date(System.currentTimeMillis() + 5000));
						triggerPCM.setRepeatCount(SimpleTrigger.REPEAT_INDEFINITELY);
						triggerPCM.setRepeatInterval(10000); 
						triggerPCM.setName("Connection Pool PCM");
						scheduler.scheduleJob(PCMMonitorJob, triggerPCM);
		            }
					//Create java execution job handler called from Database

				/*	JobDetail job = JobBuilder.newJob(ExecuteJavaMethodJob.class)
							.withIdentity("executeJavaMethod", "group1")
							.build();

					// Trigger the job to run now, and then every 40 seconds
					Trigger trigger = TriggerBuilder.newTrigger()
							.withIdentity("myTrigger", "group1")
							.startNow()
							.withSchedule(SimpleScheduleBuilder.simpleSchedule()
									.withIntervalInSeconds(40)
									.repeatForever())
							.build();

					// Tell quartz to schedule the job using our trigger
					scheduler.scheduleJob(job, trigger);*/

				}
			}

			// Called when the ApplicationContext gets closed
			else if (arg0 instanceof ContextClosedEvent) {
				// Free zookeeper client resoucres
				RegisterZookeeper.getInstance().teardown();
				
				log.info("ContextClosedEvent Catched");
			}
			// Called when the request is handled within a WebApplicationContext
			else if (arg0 instanceof RequestHandledEvent) {
				log.info("ContextClosedEvent Catched");
			}
			// Default Case
			else {
				log.debug("Unknown Event Cathced - " + arg0);
			}
			
		} catch (ClusterException e) {
			log.error(this.getClass().getName()
					+ "- [onApplicationEvent] - ClusterException " + e.getMessage(), e);
			throw e;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [onApplicationEvent] - Exception " + e.getMessage(), e);

		} finally {
			log.debug(this.getClass().getName()
					+ "- [onApplicationEvent] - Exit ");
		}
	}
	
	
	
	
	private void updatePoolStatTable(HashMap<String, String> stats, String moduleId) {
		String temStr = null;
		int numActive = 0;
		int numIdle = 0;
		int maxIdle = 0;
		int maxActive = 0;
		try {
			ConnectionPoolControlManager connectionPoolControlManager = (ConnectionPoolControlManager) (SwtUtil
					.getBean("connectionPoolControlManager"));
			if (stats != null) {
				temStr = stats.get(SwtConstants.CONNECTION_POOL_ACTIVE_ID);
				if (temStr != null) {
					numActive = Integer.parseInt(temStr.split("/")[0]);
					maxActive = Integer.parseInt(temStr.split("/")[1]);
				}
				temStr = stats.get(SwtConstants.CONNECTION_POOL_IDLE_ID);
				if (temStr != null) {
					numIdle = Integer.parseInt(temStr.split("/")[0]);
					maxIdle = Integer.parseInt(temStr.split("/")[1]);
				}

				connectionPoolControlManager.updatePoolStatsTable(moduleId, numActive, numIdle, maxActive, maxIdle);

			}
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ "- [updatePoolStatTable] - Exception " + e.getMessage());
		}
	}

	private static transient String decryptedPassword_ = null;
	private static void configurateEmailPool() throws Exception {

		try {
			if(SwtUtil.isMailEnabled()){
				// Email's properties
				Properties mailProps = new Properties();
				StandardPBEStringEncryptor encryptor ;
				// Email data
				String protocol = PropertiesFileLoader.getInstance()
						.getMailPropertyValue(SwtConstants.MAIL_TRANSPORT_PROTOCOL);
				String hostName = PropertiesFileLoader.getInstance()
						.getMailPropertyValue(SwtConstants.MAIL_PROTOCOL_HOST);
				String port = PropertiesFileLoader.getInstance().getMailPropertyValue(
						SwtConstants.MAIL_PROTOCOL_PORT);
				String isAuthRequired =  PropertiesFileLoader.getInstance().getMailPropertyValue(SwtConstants.MAIL_PROTOCOL_AUTH);
				final String emailSender = PropertiesFileLoader.getInstance()
						.getMailPropertyValue(SwtConstants.MAIL_USER_EMAIL);

				String sslProtocls = PropertiesFileLoader.getInstance().getMailPropertyValue(
						SwtConstants.MAIL_SSL_PROTOCOL_AUTH);

				String encPassword = PropertiesFileLoader.getInstance()
						.getMailPropertyValue(SwtConstants.MAIL_USER_PASSWORD);


				// Specifies the host name of the mail server
				mailProps.put("mail."+protocol+".host", hostName);
				// The port number of the mail server for the specified protocol,
				// the default is 25, set it from a variable
				mailProps.put("mail."+protocol+".port", port != null ? port : "25");
				mailProps.put("mail."+protocol+".starttls.enable", "true");
				mailProps.put("mail."+protocol+".ssl.trust", "*");
				if(SwtUtil.isEmptyOrNull(sslProtocls)) {
					mailProps.put("mail."+protocol+".ssl.protocols", "TLSv1 TLSv1.1 TLSv1.2");
				}else {
					mailProps.put("mail."+protocol+".ssl.protocols", sslProtocls.replaceAll("(,|\\s)+", " "));
				}



				if("false".equalsIgnoreCase(isAuthRequired)){
//						mailSession = Session.getInstance(mailProps);
					SwtUtil.transportFactory = SmtpConnectionFactoryBuilder.newSmtpBuilder().protocol(protocol)
							.session(mailProps).build();

				}
				else{
					if (decryptedPassword_==null) {
						encryptor = new StandardPBEStringEncryptor();
						SimplePBEConfig pbeConfig = new SimplePBEConfig();
						pbeConfig.setPassword(SwtConstants.PSWD_SEPARATOR);
						encryptor.setConfig(pbeConfig);
						if (encPassword.indexOf("ENC(")!=-1) {
							encryptor.setAlgorithm("PBEWithMD5AndDES");
							decryptedPassword_ = encryptor.decrypt(encPassword.substring(4,encPassword.lastIndexOf(')')));
						}
						else if (encPassword.indexOf("ENC2(")!=-1) {
							encryptor.setAlgorithm("PBEWITHSHA256AND128BITAES-CBC-BC");
							encryptor.setProvider(new BouncyCastleProvider());
							decryptedPassword_ = encryptor.decrypt(encPassword.substring(5,encPassword.lastIndexOf(')')));
						}
						else{
							decryptedPassword_ = encPassword;
						}
					}

					final String decryptedPassword = decryptedPassword_;

					mailProps.put("mail."+protocol+".auth", "true");
					// Get the default Session object.
					// Authentication
					Authenticator auth = new Authenticator()  {
						protected PasswordAuthentication getPasswordAuthentication() {
							return new PasswordAuthentication(emailSender,
									decryptedPassword);
						}
					};


					SwtUtil.transportFactory = SmtpConnectionFactoryBuilder.newSmtpBuilder().protocol(protocol)
							.session(mailProps, auth).build();

				}

				int maxActiveMailConnection = 10;
				int maxActiveIdleConnection = 5;
				final String mailMaxActiveAsString = PropertiesFileLoader.getInstance()
						.getMailPropertyValue(SwtConstants.MAIL_MAXACTIVE_CONNECTION);
				final String mailMaxIdleAsString = PropertiesFileLoader.getInstance()
						.getMailPropertyValue(SwtConstants.MAIL_MAXIDLE_CONNECTION);

				try {
					if(!SwtUtil.isEmptyOrNull(mailMaxActiveAsString)) {
						maxActiveMailConnection = Integer.parseInt(mailMaxActiveAsString);
					}
				}catch(Exception e) {

				}

				try {
					if(!SwtUtil.isEmptyOrNull(mailMaxIdleAsString)) {
						maxActiveIdleConnection = Integer.parseInt(mailMaxIdleAsString);
					}
				}catch(Exception e) {

				}

				GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
				genericObjectPoolConfig.setMaxTotal(maxActiveMailConnection);
				genericObjectPoolConfig.setTestOnBorrow(true);

				genericObjectPoolConfig.setMinIdle(maxActiveIdleConnection);
				Duration evictionInterval = Duration.ofSeconds(5);
				genericObjectPoolConfig.setTimeBetweenEvictionRuns(evictionInterval);

				SwtUtil.smtpConnectionPool = new SmtpConnectionPool(SwtUtil.transportFactory, genericObjectPoolConfig);
				SwtUtil.smtpConnectionPool.initialize();
			}
		} catch (Exception e) {
			log.error("ApplicationContextListner - [configurateEmailPool] - Exception: Could not configure mail, mail configuration properties are Invalid, please check error log for more details");
			SwtException ex = new SwtException(e.getMessage());
			ex.setErrorLogFlag("Y");
			ex.setErrorCode("MAIL_CONFIG");
			ex.setErrorId("MAIL_CONFIG");
			ex.setErrorDesc("Could not configure mail, mail configuration properties are Invalid, please check error log for more details");
			ex.setSrcCodeLocation(ExceptionUtils.getStackTrace(e));
			SwtUtil.logErrorInDatabase(ex);
			SwtErrorHandler.getInstance().handleException(e, "configurateEmailPool", ApplicationContextListner.class);
		}
	}
}
