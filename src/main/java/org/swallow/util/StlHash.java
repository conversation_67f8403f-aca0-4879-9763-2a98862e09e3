package org.swallow.util;

import java.security.MessageDigest;
/**
 * This class contains hashing functionality for Swallow's one-way passwords
 */
public class StlHash {
   // Java-supported Hash algorithms: MD2, MD5, SHA-1, SHA-256, SHA-384, SHA-512
   // Hash length: MD2=32, MD5=32, SHA-1=40, SHA-256=64, SHA-384=96, SHA-512=128
   private static final String DEFAULT_HASH_ALGORITHM = "SHA-256";

   // For increased security, repeat the hashing process many times. In each
   // round, a different salt is added to defend against rainbow table attacks.
   // Increasing this number uses more processor resources, and creates more
   // string objects resulting in increased garbage collection.
   private static final int HASH_ROUNDS = 256;

   // Hexadecimal = Base 16
   private static final int RADIX_HEX = 16;

   /**
    * Get the official hash digest. This will match the value generated from
    * any other standard hashing tool or application.
    * To verify the official hash use Google, e.g. "SHA-256 Hash Generator".
    * There are plenty of sites that generate the hash.
    */
   public static String getOfficialHash(String strToHash, String hashAlgorithm) throws Exception {
      MessageDigest md = MessageDigest.getInstance(hashAlgorithm);
      md.update(strToHash.getBytes());

      byte byteData[] = md.digest();

      // Convert the byte to hex
      StringBuilder hexString = new StringBuilder();
      for (int i = 0; i < byteData.length; i++) {
         hexString.append(
            Integer.toString((byteData[i] & 0xFF) + 0x100, RADIX_HEX).substring(1).toUpperCase()
         );
      }

      return hexString.toString();
   }

   /**
    * Same as (String, String) method, but overridden to use default hash algorithm
    */
   public static String getOfficialHash(String strToHash) throws Exception {
      return getOfficialHash(strToHash, DEFAULT_HASH_ALGORITHM);
   }

   /**
    * Get the STL hash digest. This is a custom hash using salts and multiple rounds
    * to further enhance the level of security.
    * To improve security, ensure the number of salt prefixes and the salt characters
    * are relatively prime (so that they repeat less often).
    * In this case 7 and 94 are, and has a period of 658.
    *
    * Parameter: strHost = host/site/location (in most cases, use S_HOST_ID.HOST_ID).
    * All parameters are case sensitive.
    * Note: the host and user id are to ensure that the hash of the same password
    * cannot be reused for a different user and/or host.
    */
   public static String getStlHash(String strHost, String strUser, String strPass, String hashAlgorithm) throws Exception {
      final String[] saltPrefix = {"<S>", "[L]", "{T}", "(^)", "<s>", "[l]", "{t}"};
      final String separator = "/";
      final int saltPrefixCount = saltPrefix.length;

      // Not worth using StringBuilder here to try to reduce creation of new objects
      // as you would need to create new object when converting String to StringBuilder.
      String str = separator + strHost + separator + strUser + separator + strPass + separator;
      for (int j = 0; j < HASH_ROUNDS; j++) {
         str = getOfficialHash(saltPrefix[j % saltPrefixCount] + str + getSaltChar(j), hashAlgorithm);
      }
      return str;
   }

   /**
    * Same as (String, String, String, String) method, but overridden to use default hash algorithm
    */
   public static String getStlHash(String strHost, String strUser, String strPass) throws Exception {
      return getStlHash(strHost, strUser, strPass, DEFAULT_HASH_ALGORITHM);
   }
   
   /**
    * Generate a salt character from a given number.
    * Basically the given number is mapped into the visible printable ASCII character set
    * from 33 to 126, which is a total of 94 chars.
    */
   private static char getSaltChar(long aNumber) {
      // The first printable ASCII character
      final int firstPrintableChar = 33;
      // Total number of printable ASCII characters
      final int totalPrintableChars = 94;

      return (char) ((aNumber % totalPrintableChars) + firstPrintableChar);
   }

   /**
    * Test if the given character is a hex character, i.e. 0-9 or A-F
    * Is not case sensitive.
    */
   private static boolean isHexCharacter(char chrToCheck) {
      if (Character.digit(chrToCheck, RADIX_HEX) == -1) {
         return false;
      }
      else {
         return true;
      }
   }

   /**
    * Test if the given string complies with SHA-256 format,
    * i.e. 64 characters in length and only contains hex characters
    * Is not case sensitive.
    */
   public static boolean isStringInSha256Format(String strToCheck) {
      final int sha256Length = 64;
      boolean isSha256Format = true;
      String tempString = strToCheck.toUpperCase();

      if (tempString.length() == sha256Length) {
         for (int j = 0; j < tempString.length(); j++) {
            if (!isHexCharacter(tempString.charAt(j))) {
               isSha256Format = false;
               break;
            }
         }
      }
      else {
         isSha256Format = false;
      }
      return isSha256Format;
   }
}