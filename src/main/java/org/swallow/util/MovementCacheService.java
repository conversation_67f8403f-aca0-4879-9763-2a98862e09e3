package org.swallow.util;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.swallow.work.service.MovementManager;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

@Configuration
@EnableScheduling
@Service
@DependsOn("movementManager")
public class MovementCacheService {

    private final Log logger = LogFactory.getLog(MovementCacheService.class);
    @Autowired
    private MovementManager movementManager;

    private volatile CopyOnWriteArrayList<String> cachedInputSources = new CopyOnWriteArrayList<>();
    private volatile CopyOnWriteArrayList<String> cachedMessageFormats = new CopyOnWriteArrayList<>();
    private LocalDateTime lastUpdateTime;

    private final ConcurrentHashMap<String, Integer> cacheUpdateStats = new ConcurrentHashMap<>();

    @Value("${cache.update.cooldown.minutes:5}")
    private int cacheUpdateCooldownMinutes;

    @Value("${cache.monitoring.enabled:true}")
    private boolean monitoringEnabled;

    public MovementCacheService() {
        this.cacheUpdateStats.put("successCount", 0);
        this.cacheUpdateStats.put("failureCount", 0);
    }

    @Scheduled(fixedRate = 30000) // 5 minutes
    public void updateCache() {
        this.movementManager = (MovementManager) SwtUtil.getBean("movementManager");
        if (shouldSkipUpdate()) {
            return;
        }

        logger.debug("Starting cache update for movement data");

        try {
            ArrayList<String> newInputSources = movementManager.getSourceList(null,null);
            ArrayList<String> newMessageFormats = movementManager.getFormatList(null,null);

            // Update cache with new values
            updateCacheValues(newInputSources, newMessageFormats);

            // Update statistics
            cacheUpdateStats.computeIfPresent("successCount", (k, v) -> v + 1);
            lastUpdateTime = LocalDateTime.now();

            logCacheMetrics(newInputSources.size(), newMessageFormats.size());

        } catch (Exception e) {
            cacheUpdateStats.computeIfPresent("failureCount", (k, v) -> v + 1);
            logger.error("Error updating movement cache", e);
        }
    }

    private boolean shouldSkipUpdate() {
        return lastUpdateTime != null &&
               ChronoUnit.MINUTES.between(lastUpdateTime, LocalDateTime.now()) < cacheUpdateCooldownMinutes;
    }

    private void updateCacheValues(List<String> newInputSources, List<String> newMessageFormats) {
        // Thread-safe update of cached values
        this.cachedInputSources = new CopyOnWriteArrayList<>(newInputSources);
        this.cachedMessageFormats = new CopyOnWriteArrayList<>(newMessageFormats);
    }

    private void logCacheMetrics(int inputSourcesCount, int messageFormatsCount) {
        if (!monitoringEnabled) {
            return;
        }

        logger.info(String.format("Cache Update Metrics - Input Sources: %d, Message Formats: %d, " +
                                  "Success Count: %d, Failure Count: %d, Last Update: %s",
                inputSourcesCount,
                messageFormatsCount,
                cacheUpdateStats.get("successCount"),
                cacheUpdateStats.get("failureCount"),
                lastUpdateTime));
    }

    public List<String> getCachedInputSources() {
        return new CopyOnWriteArrayList<>(cachedInputSources);
    }

    public List<String> getCachedMessageFormats() {
        return new CopyOnWriteArrayList<>(cachedMessageFormats);
    }

    public ConcurrentHashMap<String, Integer> getCacheStats() {
        return new ConcurrentHashMap<>(cacheUpdateStats);
    }

    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
}