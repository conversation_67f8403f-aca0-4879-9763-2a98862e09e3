package org.swallow.util;

import java.util.ArrayList;
import java.util.HashMap;

import javax.naming.NamingException;

import net.jradius.exception.RadiusException;

import org.swallow.radius.SwtRadiusException;
import org.swallow.radius.client.SwtRadiusClient;

/**
 * This class allows a verification of a given code/username on a radius server
 * 
 * <AUTHOR> SwallowTech Tunisia
 * @version 1.0
 */
public class SwtRadiusChecker { 
	SwtRadiusClient radiusClient = null;
	String lastReplyMessage = "";
	
	/**
	 * RadiusChecker constructor
	 * 
	 * @param fromLdap
	 */
	public SwtRadiusChecker() throws Exception{
		// Initialize
		init();
	}

	/**
	 * Initialize configs of the SwtRadiusChecker instance
	 */
	private void init() throws Exception {
		String attributeExpression = "";
		String attributeValue = "";
		HashMap<String, Object> props = new HashMap<String, Object>();
		ArrayList<String> rsaServers = new ArrayList<String>();
		boolean fromLdap = false;
		try {
			String fromLdapStr = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.PROPERTY_DFA_FROMLDAP);
			if(fromLdapStr!=null)
				fromLdap = fromLdapStr.equalsIgnoreCase(SwtConstants.STR_TRUE);
			else
				fromLdap = false;
			
			if (!fromLdap) {
				// rsaServer1
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_RSASERVER1);
				rsaServers.add(attributeValue);
	
				// rsaServer2
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_RSASERVER2);
				rsaServers.add(attributeValue);
	
				// rsaServer3
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_RSASERVER3);
				rsaServers.add(attributeValue);
				
				props.put(SwtConstants.KEY_DFA_RSASERVERS, rsaServers);
				
				// sharedKey
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_SHAREDKEY);
				props.put(SwtConstants.KEY_DFA_SHAREDKEY,attributeValue);
				
				// portAuth
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_PORTAUTH);
				props.put(SwtConstants.KEY_DFA_PORTAUTH,Integer.parseInt(attributeValue));
				 
				// portAcct
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_PORTACCT);
				props.put(SwtConstants.KEY_DFA_PORTACCT,Integer.parseInt(attributeValue));
			
				// timeout
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_TIMEOUT);
				props.put(SwtConstants.KEY_DFA_TIMEOUT,Integer.parseInt(attributeValue));
	
				
				// authProtocol
				attributeValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.PROPERTY_DFA_AUTHPROTOCOL);
				props.put(SwtConstants.KEY_DFA_AUTHPROTOCOL,attributeValue);
			} else {
				// Fetch for the ldap url <host>:<port number>
				String ldapUrl = PropertiesFileLoader.getInstance().getLdapValue(
						SwtConstants.PROPERTY_DFA_URL);
				// Fetch for the user DN
				String ldapUserDn = PropertiesFileLoader.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_USERDN); 
				// Fetch for password
				String ldapPassword = PropertiesFileLoader.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_PASSWORD);
				// Fecth for the ldap password
				String ldapBase = PropertiesFileLoader.getInstance().getLdapValue(
						SwtConstants.PROPERTY_DFA_BASE);
	
				String ldapFilter = null;
				
				// Create an ldap client instance
				SwtLdapClient ldapClient = new SwtLdapClient(ldapUrl,
						ldapUserDn, ldapPassword);

				// Get rsaServer1
				attributeExpression = PropertiesFileLoader
						.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_RSASERVER1_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				rsaServers.add(attributeValue);

				// Get rsaServer2
				attributeExpression = PropertiesFileLoader
						.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_RSASERVER2_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				if (attributeValue != null)
					rsaServers.add(attributeValue);

				// Get rsaServer3
				attributeExpression = PropertiesFileLoader
						.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_RSASERVER3_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				if (attributeValue != null)
					rsaServers.add(attributeValue);

				props.put(SwtConstants.KEY_DFA_RSASERVERS, rsaServers);
				
				// Get sharedKey
				attributeExpression = PropertiesFileLoader.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_SHAREDKEY_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				props.put(SwtConstants.KEY_DFA_SHAREDKEY,attributeValue);
				
				// Get portAuth
				attributeExpression = PropertiesFileLoader.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_PORTAUTH_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				props.put(SwtConstants.KEY_DFA_PORTAUTH,Integer.parseInt(attributeValue));
				
				// Get portAcc
				attributeExpression = PropertiesFileLoader.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_PORTACCT_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				props.put(SwtConstants.KEY_DFA_PORTACCT,Integer.parseInt(attributeValue));

				// Get timeout
				attributeExpression = PropertiesFileLoader.getInstance()
						.getLdapValue(SwtConstants.PROPERTY_DFA_TIMEOUT_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				props.put(SwtConstants.KEY_DFA_TIMEOUT,Integer.parseInt(attributeValue));

				// Get authProtocol
				attributeExpression = PropertiesFileLoader.getInstance()
						.getLdapValue(
								SwtConstants.PROPERTY_DFA_AUTHPROTOCOL_EXPR);
				attributeValue = ldapClient.searchAttributeValue(
						attributeExpression, ldapBase, ldapFilter);
				props.put(SwtConstants.KEY_DFA_AUTHPROTOCOL,attributeValue);	
			}

			// Create the radius client
			radiusClient = new SwtRadiusClient(props);
		} catch (NamingException e) {
			throw new Exception("Ldap Error, attributeValue="+attributeValue+", exception:"+e,e);
		} catch (Exception e){
			Exception ex = new Exception("RSA Configuration Error, attributeValue="+attributeValue+", exception:"+e,e);
			ex.setStackTrace(e.getStackTrace());
			throw ex;
		}
	}


	/**
	 * Check method
	 * 
	 * @param code
	 * @param username
	 * @return
	 * @throws Exception
	 */
	public int check(String userName, String code) throws SwtRadiusException {
		Object[] result = radiusClient.check(userName, code);
		lastReplyMessage = (String)result[1];
		return (Integer)result[0];
	}

	/**
	 * Process challenge response
	 * @param challengeCode
	 * @return
	 * @throws RadiusException
	 */
	public int processChallenge(String challengeCode)
     throws SwtRadiusException{
		Object[] result = radiusClient.processChallenge(challengeCode);
		lastReplyMessage = (String)result[1];
		return (Integer)result[0];
	}
	
	/**
	 * Call radius client to recycle requests
	 */
	public void recycle(){
		radiusClient.recycleRequest();	
	}
	
	/**
	 * Get last radius server reply
	 * @return
	 */
	public String getLastReplyMessage() {
		return lastReplyMessage;
	}


}