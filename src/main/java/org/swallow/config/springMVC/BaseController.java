package org.swallow.config.springMVC;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.swallow.util.SwtUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;

import java.util.ArrayList;
import java.util.List;

/**
 * Base Controller class that provides similar functionality to CustomActionSupport
 * Maintains compatibility with legacy error handling and attribute keys
 *
 * <AUTHOR> 2024
 */
public abstract class BaseController {

    // Legacy attribute keys
    private static final String ERROR_KEY = "org.apache.struts.action.ERROR";
    private static final String MESSAGE_KEY = "errorMessage";
    private static final String ERROR_DESC = "errordesc";
    private static final String ERROR_CAUSE = "errorCause";

    // Common request parameters
    protected String response;
    protected String nocache;
    protected String isAjax;
    protected String csrf;
    protected String isForm;
    protected String mfaToken;
    protected String method;
    protected String user_lang1234;

    // Lists to store errors and messages
    private final List<String> actionErrors = new ArrayList<>();
    private final List<String> actionMessages = new ArrayList<>();

    /**
     * Get current request from RequestContextHolder
     */
    protected HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }

    public String getActionError() {
        return actionErrors.isEmpty() ? "" : actionErrors.get(0);
    }

    public String getHtmlErrors() {
        return getActionError();
    }

    public String getActionMessage() {
        return actionMessages.isEmpty() ? "" : actionMessages.get(0);
    }

    /**
     * Clear all error-related attributes from the request
     */
    protected void clearErrors() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            request.removeAttribute(ERROR_KEY);
            request.removeAttribute(MESSAGE_KEY);
            request.removeAttribute(ERROR_DESC);
            request.removeAttribute(ERROR_CAUSE);
        }
        actionErrors.clear();
    }

    /**
     * Save errors in a way similar to Struts
     * @param request The servlet request
     * @param errors Legacy ActionMessages object
     */
    protected void saveErrors(HttpServletRequest request, ActionMessages errors) {
        // First clear existing errors
        clearErrors();

        if (errors == null || errors.isEmpty()) {
            return;
        }
        StringBuilder errorMessages = new StringBuilder();

        errors.get().forEachRemaining(message -> {
            ActionMessage error = (ActionMessage) message;
            String translated = SwtUtil.getMessage(error.getKey(), request, error.getValues());
            addActionError(translated);
            // Set the error attributes in the request
            request.setAttribute(ERROR_KEY, errors);
            request.setAttribute(MESSAGE_KEY, translated);
            request.setAttribute(ERROR_DESC, translated); // You might want to customize this based on your needs

            errorMessages.append(translated).append("\n");
        });
        String allErrors = errorMessages.toString().trim();
        request.setAttribute("actionError", allErrors);
    }

    /**
     * Save messages in a way similar to Struts
     * @param request The servlet request
     * @param messages Legacy ActionMessages object
     */
    protected void saveMessages(HttpServletRequest request, ActionMessages messages) {
        if (messages == null || messages.isEmpty()) {
            clearMessages();
            return;
        }

        messages.get().forEachRemaining(message -> {
            ActionMessage msg = (ActionMessage) message;
            String translated = SwtUtil.getMessage(msg.getKey(), request, msg.getValues());
            addActionMessage(translated);

            // Set the message attribute in the request
            request.setAttribute(MESSAGE_KEY, translated);
        });
    }

    /**
     * Add a single error message and set corresponding attributes
     */
    protected void addActionError(String error) {
        actionErrors.add(error);
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            request.setAttribute(ERROR_KEY, error);
            request.setAttribute(MESSAGE_KEY, error);
            request.setAttribute(ERROR_DESC, error);
        }
    }

    /**
     * Add a single message and set corresponding attribute
     */
    protected void addActionMessage(String message) {
        actionMessages.add(message);
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            request.setAttribute(MESSAGE_KEY, message);
        }
    }

    protected void clearMessages() {
        actionMessages.clear();
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            request.removeAttribute(MESSAGE_KEY);
        }
    }

    // You can add this method to set a specific error cause
    protected void setErrorCause(String cause) {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            request.setAttribute(ERROR_CAUSE, cause);
        }
    }

    // Getters and setters remain the same for compatibility
    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getNocache() {
        return nocache;
    }

    public void setNocache(String nocache) {
        this.nocache = nocache;
    }

    public String getIsAjax() {
        return isAjax;
    }

    public void setIsAjax(String isAjax) {
        this.isAjax = isAjax;
    }

    public String getCsrf() {
        return csrf;
    }

    public void setCsrf(String csrf) {
        this.csrf = csrf;
    }

    public String getIsForm() {
        return isForm;
    }

    public void setIsForm(String isForm) {
        this.isForm = isForm;
    }

    public String getMfaToken() {
        return mfaToken;
    }

    public void setMfaToken(String mfaToken) {
        this.mfaToken = mfaToken;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getUser_lang1234() {
        return user_lang1234;
    }

    public void setUser_lang1234(String user_lang1234) {
        this.user_lang1234 = user_lang1234;
    }
}