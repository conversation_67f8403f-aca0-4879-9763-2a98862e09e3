<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.reports.model.Reports" table="P_STAT_REPORT">
		<composite-id name="id" class="org.swallow.reports.model.Reports$Id" unsaved-value="any">
	        <key-property name="hostId" access="field" column="HOST_ID"/>
	        <key-property name="userId" access="field" column="USER_ID"/>
	        <key-property name="currencyCode" access="field" column="CURRENCY_CODE"/>
	        <key-property name="inputDate" access="field" column="INPUT_DATE"/>
	     </composite-id>
        	
		<property name="totalMovements" column="TOTAL_MOVEMENTS" not-null="false"/>	
		<property name="matchQualityA" column="MATCH_QUALITY_A" not-null="false"/>	
		<property name="matchQualityB" column="MATCH_QUALITY_B" not-null="false"/>	
		<property name="matchQualityC" column="MATCH_QUALITY_C" not-null="false"/>	
		<property name="matchQualityD" column="MATCH_QUALITY_D" not-null="false"/>	
		<property name="matchQualityE" column="MATCH_QUALITY_E" not-null="false"/>	
		<property name="matchQualityZ" column="MATCH_QUALITY_Z" not-null="false"/>	
		<property name="totalAmount" column="TOTAL_AMOUNT" not-null="false"/>	
		
    
 	</class>
</hibernate-mapping>
