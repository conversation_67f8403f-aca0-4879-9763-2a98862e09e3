/*
 * @(#)IntradayBalanceDAOHibernate.java 1.0 09/09/2008
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import javax.sql.DataSource;

import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.fill.JRSwapFileVirtualizer;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.dao.IntradayBalanceDAO;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * 
 * <AUTHOR> G Class that implements the IntradayBalanceDAO and acts as
 *         DAO layer for all database operations
 * 
 */
@Repository ("intradayBalanceDAO")
@Transactional
public class IntradayBalanceDAOHibernate extends CustomHibernateDaoSupport implements
		IntradayBalanceDAO {
	public IntradayBalanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	/*
	 * Initializing Log variable for logging comments
	 */
	private static final Log log = LogFactory
			.getLog(IntradayBalanceDAOHibernate.class);

	/**
	 * Get the distinct AccountId from p_intraday_stats table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @return Collection object
	 */
	public Collection getMainAccountDetails(String hostId, String entityId,
			String currencyCode) {

		log.debug(this.getClass().getName()
				+ "- [getMainAccountDetails] - Entry ");
		/* Local variable declaration */
		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet res = null;
		List currentStatus = null;
		StringBuffer acctDetailQuery = null;
		try {

			/* Establish the connection using connection manager */
			conn = ConnectionManager.getInstance().databaseCon();
			/* Object initialization */
			currentStatus = new ArrayList();
			/* Query for getting accountId from p_intraday_stats */
			/* Start: Modified the query for Mantis 948 by Kalidass on 21-Apr-09 */
			acctDetailQuery = new StringBuffer(
					"select account_id from p_account where host_id=? and entity_id=? "
							+ "and ACCOUNT_CLASS='N'");
			if (!currencyCode.equals("All")) {
				acctDetailQuery.append(" and currency_code=? ");
			}
			acctDetailQuery.append(" order by account_level,account_id ");
			/* End: Modified the query for Mantis 948 by Kalidass on 21-Apr-09 */
			stmt = conn.prepareStatement(acctDetailQuery.toString());
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			if (!currencyCode.equals("All")) {
				stmt.setString(3, currencyCode);
			}
			res = stmt.executeQuery();

			/* Getting the accountId and put it in List */

			while (res.next()) {
				currentStatus.add(res.getString(1));

			}
		} catch (SQLException e) {

			log
					.debug(this.getClass().getName()
							+ "- [getMainAccountDetails] - Exception "
							+ e.getMessage());
			log
					.error(this.getClass().getName()
							+ "- [getMainAccountDetails] - Exception "
							+ e.getMessage());
			e.printStackTrace();
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(res, stmt, conn, null);
		    
			try{
				if (conn != null)
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				log.error("org.swallow.reports.dao.hibernate.IntradayBalanceDAOHibernate - "
						+ "[getMainAccountDetails] - Exception - "
						+ e.getMessage());
			}
		}
		log.debug(this.getClass().getName()
				+ "- [getMainAccountDetails] - Exit ");

		return currentStatus;
	}

	/**
	 * This method is used to compile and return the reports
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * @param hostId -
	 *            String
	 * @param entityId -
	 *            String
	 * @param entityName -
	 *            String
	 * @param reportDate -
	 *            String
	 * @param accountId -
	 *            String
	 * @param currencyCode -
	 *            String
	 * @param currencyName -
	 *            String
	 * @param rawData -
	 *            String
	 * @param virtualizer -
	 *            JRSwapFileVirtualizer
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	public JasperPrint getIntradayBalancesReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String reportDate, String accountId, String currencyCode,
			String currencyName, String rawData,
			JRSwapFileVirtualizer virtualizer) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getIntradayBalancesReport] - Entering ");

		// Declare the connection
		Connection connection = null;
		// Declare the data source
		DataSource ds = null;
		// Declare the jasper report
		JasperReport jasperReport = null;
		// Declare the jasper print
		JasperPrint jasperPrint = null;
		// Declare Map to send the parameters to the Japser Engine
		java.util.Map parms = new HashMap();
		// Declaration for List
		List pagesize = null;
		// Declare the date variable.
		Date date;
		// Declare the currency format
		String currencyFormat = null;

		try {

			// Design file will be compiled
			if (rawData.equals("Y"))
				jasperReport = JasperCompileManager.compileReport(request
						.getServletContext().getRealPath("/")
						+ SwtConstants.INTRADAY_BALANCES_REPORT_FILE);
			else {
				/* Start: Code modified by Kalidass G for Mantis 827 on 09-08-2010 */
		    	if(!accountId.equals("All")){
		    		jasperReport = JasperCompileManager.compileReport(request.getServletContext().getRealPath("/")+SwtConstants.INTRADAY_BALANCES_REPORT_FILE_GRAPH);
		    	}else{
		    		jasperReport = JasperCompileManager.compileReport(request.getServletContext().getRealPath("/")+SwtConstants.INTRADAY_BALANCES_REPORT_FILE_GRAPH_ALL);
		    	}
		    	/* End: Code modified by Kalidass G for Mantis 827 on 09-08-2010 */
			}
			// Get the datasource and get the session
			ds = (DataSource) SwtUtil.getBean("dataSource");
			connection = ds.getConnection();
			// parse the report date as current system data format
			date = SwtUtil.parseDateGeneral(reportDate);
			// Getting the user session
			HttpSession session = UserThreadLocalHolder.getUserSession();
			// Initialize the CommonDataManager for getting currencyFormat
			CommonDataManager cdm = (CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN);
            //edited by Med amine in Mantis_2068
			 String dateFormat="";
			if (cdm != null) {
				currencyFormat = cdm.getCurrencyFormat();

	             if (cdm.getDateFormat().equals("datePat1"))
	            	 dateFormat="dd/MM/yyyy HH24:MI:SS";
	             else
	            	 dateFormat="MM/dd/yyyy HH24:MI:SS";
			}

			// Preparing the parameters to be passed to Jasper Engine.
			parms.put("pHost_Id", SwtUtil.getCurrentHostId());
			parms.put("pEntity_Id", entityId);
			parms.put("pEntity_Name", entityName);
			parms.put("pCurrency_Code", currencyCode);
			parms.put("pCurrency_Name", currencyName);
			parms.put("pReport_Date", date);
			parms.put("pAccount_Id", accountId);
			parms.put("pCurrencyPattern", currencyFormat);
			parms.put("pdateFormat", dateFormat);
			/* Start code: Code added for Mantis 942 by Betcy  on 12-Jun-10 */
			//add the virtualizer in parameter
			parms.put(JRParameter.REPORT_VIRTUALIZER, virtualizer);
			/* End code: Code added for Mantis 942 by Betcy  on 12-Jun-10 */
			// Compiled Opportunity cost Report will be filled here.
			jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
					connection);
			// To get the page size
			pagesize = jasperPrint.getPages();
			// If the page size is Zero Empty datasource will be passed to avoid
			// the blank report.
			if (pagesize.size() == 0) {
				jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
						new JREmptyDataSource(1));

			}

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ "- [getIntradayBalancesReport] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [getIntradayBalancesReport] - Exception "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getIntradayBalancesReport",
					IntradayBalanceDAOHibernate.class), request, "");
			throw new SwtException(exp.getMessage());

		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(connection);

		}

		log.debug(this.getClass().getName()
				+ "- [getIntradayBalancesReport] - Exiting ");
		return jasperPrint;
	}

}