package org.swallow.reports.dao.hibernate.ilmexcel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.swallow.exception.SwtException;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;

public class MultipleCurrenciesAvailableLiquidity extends MultipleCurrencies{
	

	public static Map<String, Object> getBeansMap(HashMap<String, Object> allData) throws Exception{
		Map<String, Object> beans = new HashMap<String, Object>();
		
		
		ArrayList<ReportItem> availbleLiquidity = new ArrayList<MultipleCurrenciesAvailableLiquidity.ReportItem>();
		ArrayList<Date> thrtimes = new ArrayList<Date>();
		ArrayList<HashMap<String, Object>> availbleLiquidityAvg = new ArrayList<HashMap<String, Object>>();
		LinkedList<HashMap<String, Object>> allccy = (LinkedList<HashMap<String, Object>>) allData.get("allccy");
		LinkedList<HashMap<String, Object>> mainDataDaily = null;
		LinkedList<HashMap<String, Object>> mainDataAvg = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> mainDataForSourceOfLiq = new LinkedList<HashMap<String,Object>>();
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> availableLiqAvgList = null;
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> mainDataDailyList = null;
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> availableLiqDailyList = null;
		availableLiqAvgList = (ConcurrentHashMap) allData.get("availableLiqRankedList");
		mainDataDailyList = (ConcurrentHashMap) allData.get("mainDataDailyList");
		availableLiqDailyList = (ConcurrentHashMap) allData.get("availableLiqDailyList");
		ArrayList<String> ccyList = new ArrayList<String>();
		HashMap<String, Object> data = null;
		boolean fillTimes = true;
		try{
		
		HashMap<String, String> StyleMap = new HashMap<String, String>();
		StyleMap = SwtUtil.getStyleMap();
		// Dictionary
		HashMap<String, String> dictionary = new HashMap<String, String>(){
			{
				put("report_title", SwtUtil.getMessageFromSession("ilmExcelReport.titleIntradayLiquidityManagementReport", UserThreadLocalHolder.getUserSession()));
				put("entity_id", SwtUtil.getMessageFromSession("ilmExcelReport.entity", UserThreadLocalHolder.getUserSession()));
				put("date_from", SwtUtil.getMessageFromSession("ilmExcelReport.fromDate", UserThreadLocalHolder.getUserSession()));
				put("date_end", SwtUtil.getMessageFromSession("ilmExcelReport.toDate", UserThreadLocalHolder.getUserSession()));
				put("ccy_multiplier", SwtUtil.getMessageFromSession("ilmExcelReport.multiplier", UserThreadLocalHolder.getUserSession()));
				put("scenario", SwtUtil.getMessageFromSession("ilmExcelReport.scenario", UserThreadLocalHolder.getUserSession()));
				put("group", SwtUtil.getMessageFromSession("ilmExcelReport.group", UserThreadLocalHolder.getUserSession()));
				put("src_int_liq_ex_inc", SwtUtil.getMessageFromSession("ilmExcelReport.sourcesOfIntradayLiquidityExclIncomingPayments", UserThreadLocalHolder.getUserSession()));
				put("cent_bank_bal", SwtUtil.getMessageFromSession("ilmExcelReport.centralBankBalance", UserThreadLocalHolder.getUserSession()));
				put("cent_bank_coll", SwtUtil.getMessageFromSession("ilmExcelReport.centralBankCollateral", UserThreadLocalHolder.getUserSession()));
				put("bal_oth_banks", SwtUtil.getMessageFromSession("ilmExcelReport.balancesOtherBanks", UserThreadLocalHolder.getUserSession()));
				put("bal", SwtUtil.getMessageFromSession("ilmReport.balances", UserThreadLocalHolder.getUserSession()));
				put("oth_sys_coll", SwtUtil.getMessageFromSession("ilmExcelReport.otherSystemsCollateral", UserThreadLocalHolder.getUserSession()));
				put("coll", SwtUtil.getMessageFromSession("ilmReport.collateral", UserThreadLocalHolder.getUserSession()));
				put("tot_cre_lin", SwtUtil.getMessageFromSession("ilmExcelReport.totalCreditLines", UserThreadLocalHolder.getUserSession()));
				put("unc_liq_ass", SwtUtil.getMessageFromSession("ilmExcelReport.unencumberedLiquidAssets", UserThreadLocalHolder.getUserSession()));
				put("oth", SwtUtil.getMessageFromSession("ilmExcelReport.other", UserThreadLocalHolder.getUserSession()));
				put("tot_avai_int_liq_inc_incom", SwtUtil.getMessageFromSession("ilmExcelReport.totalAvailableIntradayLiquidityInclIncomingPayments", UserThreadLocalHolder.getUserSession()));
				put("avg", SwtUtil.getMessageFromSession("ilmExcelReport.average", UserThreadLocalHolder.getUserSession()));
				put("hi", SwtUtil.getMessageFromSession("ilmExcelReport.high", UserThreadLocalHolder.getUserSession()));
				put("low", SwtUtil.getMessageFromSession("ilmExcelReport.low", UserThreadLocalHolder.getUserSession()));
				put("sc_hi", SwtUtil.getMessageFromSession("ilmExcelReport.secondHigh", UserThreadLocalHolder.getUserSession()));
				put("th_hi", SwtUtil.getMessageFromSession("ilmExcelReport.thirdHigh", UserThreadLocalHolder.getUserSession()));
				put("sc_lw", SwtUtil.getMessageFromSession("ilmExcelReport.secondLow", UserThreadLocalHolder.getUserSession()));
				put("th_lw", SwtUtil.getMessageFromSession("ilmExcelReport.thirdLow", UserThreadLocalHolder.getUserSession()));
				put("standard", SwtUtil.getMessageFromSession("ilmExcelReport.standard", UserThreadLocalHolder.getUserSession()));
				put("currencyGlobalGroup", SwtUtil.getMessageFromSession("ilmExcelReport.currencyGlobalGroup", UserThreadLocalHolder.getUserSession()));
				put("subtitle", SwtUtil.getMessageFromSession("ilmExcelReport.tabNameAvailableLiquidity", UserThreadLocalHolder.getUserSession()));

			}
		};
		beans.put("dic", dictionary);
		beans.put("styles", StyleMap);
		// Main data
		SimpleDateFormat sdf = new SimpleDateFormat(pDateFormat);
		final Date   pValue_Date = sdf.parse(dateFrom);
		final Date   pValue_Date_End = sdf.parse(dateTo);
		
		// Header
		HashMap<String, Object> header = new HashMap<String, Object>(){
			{
				put("entity_id", pEntity_Id);
				put("entity_name", pEntityName);
				put("date_from",pValue_Date);
				put("date_to",pValue_Date_End);
				put("scenario",pSeriesIdentifier);
				put("scenarioDesc",pSeriesIdentifierDesc);
				put("isGlobalGroup",isGlobalGroup);
				put("group",pGroup);
				put("groupName",pGroupName);
				put("ccy_multiplier","Y".equals(pUseCcyMultiplier)?SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierEnabled", null):SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierDisabled", UserThreadLocalHolder.getUserSession()));
				put("comment","Poc for mantis 2787: ILM Report: Option to produce a report in Excel displaying multiple currencies");
			}
		};
		beans.put("header", header);
		
		// Throughput


		int i = 0;
		for(HashMap<String, Object> rec:allccy){
			ccyList.add((String)rec.get("ccy"));
			ReportItem th = new ReportItem((String)rec.get("ccy"));
			
			if("Y".equals(pUseCcyMultiplier)) {
				th.setCurrencyLabel((String)rec.get("ccy_multiplier_label"));
			}
			else { 
				th.setCurrencyLabel((String)rec.get("ccy"));
			}
			mainDataDaily = availableLiqDailyList.get(rec.get("ccy"));
			mainDataAvg = availableLiqAvgList.get(rec.get("ccy"));
			mainDataForSourceOfLiq = mainDataDailyList.get(rec.get("ccy"));
			Double avg_cb_avg_external_sod  = null;
			Double avg_cb_avg_collateral    = null;
			Double avgbob                   = null;
			Double avgosc                   = null;
			Double avg_gc_avg_total         = null;
			Double avg_gc_avg_assets        = null;
			Double avg_gc_avg_other_total   = null;
			Double availabalLiqudity   = null;
			Double totSrcLiq   = null;
			int h = 0;
			for(HashMap<String, Object> rec2:mainDataDaily){
				
				avg_cb_avg_external_sod = getValueOf(rec2.get("avg_cb_external_sod"));
				avg_cb_avg_collateral = getValueOf(rec2.get("avg_cb_collateral"));
				
				if(isGlobalGroup) {
					avgbob = getValueOf(rec2.get("avgbob"));
					avgosc = getValueOf(rec2.get("avgosc"));
				}else {
					avgbob = getValueOf(rec2.get("avg_gc_external_sod"));
					avgosc = getValueOf(rec2.get("avg_gc_collateral"));
				}
				avg_gc_avg_total = getValueOf(rec2.get("avg_gc_cl_total"));
				avg_gc_avg_assets = getValueOf(rec2.get("avg_gc_unc_assets"));
				avg_gc_avg_other_total = getValueOf(rec2.get("avg_gc_other_total"));
				
				if(!"N".equals(rec2.get("is_holiday"))){
					
					th.pushCentBankBal(avg_cb_avg_external_sod, "#D1D1D1");
					th.pushCentBankColl(avg_cb_avg_collateral, "#D1D1D1");
					th.pushBallOtherBanks(avgbob, "#D1D1D1");
					th.pushOthSysCollateral(avgosc, "#D1D1D1");
					th.pushTotCreLines(avg_gc_avg_total, "#D1D1D1");
					th.pushUnencumLiqAssets(avg_gc_avg_assets, "#D1D1D1");
					th.pushOtherTotal(avg_gc_avg_other_total, "#D1D1D1");
				}else {
					th.pushCentBankBal(avg_cb_avg_external_sod, null);
					th.pushCentBankColl(avg_cb_avg_collateral, null);
					th.pushBallOtherBanks(avgbob, null);
					th.pushOthSysCollateral(avgosc, null);
					th.pushTotCreLines(avg_gc_avg_total, null);
					th.pushUnencumLiqAssets(avg_gc_avg_assets, null);
					th.pushOtherTotal(avg_gc_avg_other_total, null);
				}
				
				// If first currency, fill in times
				if(fillTimes){
					thrtimes.add((Date)rec2.get("value_date"));
				}
				try{
					availabalLiqudity = getValueOf(mainDataForSourceOfLiq.get(h).get("min1_avlbl_assets_v"));
				}catch(Exception e) {
				}
				
				if(!"N".equals(rec2.get("is_holiday"))){
					th.pushSourceOfLiq(availabalLiqudity, "#D1D1D1");
					totSrcLiq = getValueOf(mainDataForSourceOfLiq.get(h).get("tot_src_liq"));
					th.pushTotalSourIncIncom(totSrcLiq, "#D1D1D1");
				}else {
					th.pushSourceOfLiq(availabalLiqudity, null);
					totSrcLiq = getValueOf(mainDataForSourceOfLiq.get(h).get("tot_src_liq"));
					th.pushTotalSourIncIncom(totSrcLiq, null);
				}
				
				h++;
			}
			availbleLiquidity.add(th);
			fillTimes = false;
			data = new HashMap<String, Object>();
			for(HashMap<String, Object> rec3:mainDataAvg){
				String realKey = null;
				for (Map.Entry<String, Object> entry : rec3.entrySet()) {
					
					String key = entry.getKey();
					if(key.indexOf("asc") != -1 || key.indexOf("desc") != -1){
					    Double value = getValueOf(entry.getValue());
					    if(key.indexOf("asc") != -1 && (value == 1 ||  value == 2 || value == 3)) {
					    	realKey = key.substring(0, key.length() - 4);
					    	Double valueOf = getValueOf(rec3.get(realKey));
					    	data.put(realKey+"_min"+value.intValue(), valueOf);
					    }
					    if(key.indexOf("desc") != -1 && (value == 1 ||  value == 2 || value == 3)) {
					    	realKey = key.substring(0, key.length() - 5);
					    	Double valueOf = getValueOf(rec3.get(realKey));
					    	data.put(realKey+"_max"+value.intValue(), valueOf);
					    }
					}else if(key.indexOf("avg") != -1 ){
						Double valueOf = getValueOf(rec3.get(key));
						data.put(key, valueOf);
					}
				}
				
			}
			
			if("Y".equals(pUseCcyMultiplier)) {
				data.put("currencyLabel",(String)rec.get("ccy_multiplier_label"));
			}
			else { 
				data.put("currencyLabel",(String)rec.get("ccy"));
			}
			
			availbleLiquidityAvg.add(data);
			i++;
		}
		
		
		beans.put("availbleLiquidity", availbleLiquidity);
		beans.put("ccyList", ccyList);
		beans.put("availbleLiquidityAvg", availbleLiquidityAvg);
		beans.put("dates", thrtimes);
		}catch(SwtException exp){
			
		}
		return beans;
	}
	
	
	protected static Double getValueOf(Object tmp) throws Exception{
		if(tmp != null)
			return (Double)tmp;
		else 
			return null;
	}
	
	public static class ReportItem{
		private String currency;
		private String currencyLabel;
		private ArrayList<String> dates;
		private ArrayList<ValueTime> sourceOfLiq= new ArrayList<ValueTime>();

		private ArrayList<ValueTime> centBankBal= new ArrayList<ValueTime>();
		private ArrayList<ValueTime> centBankColl = new ArrayList<ValueTime>();
		private ArrayList<ValueTime> ballOtherBanks = new ArrayList<ValueTime>();
		private ArrayList<ValueTime> othSysCollateral = new ArrayList<ValueTime>();
		private ArrayList<ValueTime> totCreLines = new ArrayList<ValueTime>();
		private ArrayList<ValueTime> unencumLiqAssets = new ArrayList<ValueTime>();
		private ArrayList<ValueTime> otherTotal = new ArrayList<ValueTime>();
		private ArrayList<ValueTime> totalSourExcIncom = new ArrayList<ValueTime>();
		private ArrayList<ValueTime> totalSourIncIncom = new ArrayList<ValueTime>();
		
		public ArrayList<ValueTime> getSourceOfLiq() {
			return sourceOfLiq;
		}
		public void setSourceOfLiq(ArrayList<ValueTime> sourceOfLiq) {
			this.sourceOfLiq = sourceOfLiq;
		}
		public void pushSourceOfLiq(Double value, String backGroundColor ){
			if(sourceOfLiq==null)
				sourceOfLiq = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				sourceOfLiq.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				sourceOfLiq.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		public void pushCentBankBal(Double value, String backGroundColor ){
			if(centBankBal==null)
				centBankBal = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				centBankBal.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				centBankBal.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		
		public void pushOtherTotal(Double value, String backGroundColor ){
			if(otherTotal==null)
				otherTotal = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				otherTotal.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				otherTotal.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		
		public void pushCentBankColl(Double value, String backGroundColor ){
			if(centBankColl==null)
				centBankColl = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				centBankColl.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				centBankColl.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		
		public void pushOthSysCollateral(Double value, String backGroundColor ){
			if(othSysCollateral==null)
				othSysCollateral = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				othSysCollateral.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				othSysCollateral.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		
		public void pushTotCreLines(Double value, String backGroundColor ){
			if(totCreLines==null)
				totCreLines = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				totCreLines.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				totCreLines.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		
		public void pushUnencumLiqAssets(Double value, String backGroundColor ){
			if(unencumLiqAssets==null)
				unencumLiqAssets = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				unencumLiqAssets.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				unencumLiqAssets.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		public void pushBallOtherBanks(Double value, String backGroundColor ){
			if(ballOtherBanks==null)
				ballOtherBanks = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				ballOtherBanks.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				ballOtherBanks.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		
		public ArrayList<ValueTime> getCentBankBal() {
			return centBankBal;
		}
		
		public void setCentBankBal(ArrayList<ValueTime> centBankBal) {
			this.centBankBal = centBankBal;
		}
		
		public ArrayList<ValueTime> getCentBankColl() {
			return centBankColl;
		}
		
		public void setCentBankColl(ArrayList<ValueTime> centBankColl) {
			this.centBankColl = centBankColl;
		}
		
		public ArrayList<ValueTime> getBallOtherBanks() {
			return ballOtherBanks;
		}
		
		public void setBallOtherBanks(ArrayList<ValueTime> ballOtherBanks) {
			this.ballOtherBanks = ballOtherBanks;
		}
		
		public ArrayList<ValueTime> getOthSysCollateral() {
			return othSysCollateral;
		}
		
		public void setOthSysCollateral(ArrayList<ValueTime> othSysCollateral) {
			this.othSysCollateral = othSysCollateral;
		}
		
		public ArrayList<ValueTime> getTotCreLines() {
			return totCreLines;
		}
		
		public void setTotCreLines(ArrayList<ValueTime> totCreLines) {
			this.totCreLines = totCreLines;
		}
		
		public ArrayList<ValueTime> getUnencumLiqAssets() {
			return unencumLiqAssets;
		}
		
		public void setUnencumLiqAssets(ArrayList<ValueTime> unencumLiqAssets) {
			this.unencumLiqAssets = unencumLiqAssets;
		}
		
		
		public ReportItem(String currency) {
			this.currency = currency;
		}
		
		public void pushDate(String value){
			if(dates==null)
				dates = new ArrayList<String>();
			dates.add(value);
		}
		
		public void pushTotalSourExcIncom(Double value, String backGroundColor ){
			if(totalSourExcIncom==null)
				totalSourExcIncom = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				totalSourExcIncom.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				totalSourExcIncom.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		public void pushTotalSourIncIncom(Double value, String backGroundColor ){
			if(totalSourIncIncom==null)
				totalSourIncIncom = new ArrayList<ValueTime>();
			if(backGroundColor != null) {
				totalSourIncIncom.add(new ValueTime(value, backGroundColor, "white"));
			}else {
				totalSourIncIncom.add(new ValueTime(value, backGroundColor, "black"));
			}
		}
		
		public ArrayList<String> getDates() {
			return dates;
		}

		public void setDates(ArrayList<String> dates) {
			this.dates = dates;
		}


		public ArrayList<ValueTime> getTotalSourExcIncom() {
			return totalSourExcIncom;
		}

		public void setTotalSourExcIncom(ArrayList<ValueTime> totalSourExcIncom) {
			this.totalSourExcIncom = totalSourExcIncom;
		}

		public ArrayList<ValueTime> getTotalSourIncIncom() {
			return totalSourIncIncom;
		}

		public void setTotalSourIncIncom(ArrayList<ValueTime> totalSourIncIncom) {
			this.totalSourIncIncom = totalSourIncIncom;
		}

		public String getCurrency() {
			return currency;
		}
		public void setCurrency(String currency) {
			this.currency = currency;
		}

		public ArrayList<ValueTime> getOtherTotal() {
			return otherTotal;
		}

		public void setOtherTotal(ArrayList<ValueTime> otherTotal) {
			this.otherTotal = otherTotal;
		}
		public String getCurrencyLabel() {
			return currencyLabel;
		}
		public void setCurrencyLabel(String currencyLabel) {
			this.currencyLabel = currencyLabel;
		}
		
		
	}
	
	public static class ValueTime{
		private Double value = null;
		private String backGroundColor = null;
		private String fontColor = null;
		public Double getValue() {
			return value;
		}
		public void setValue(Double value) {
			this.value = value;
		}
		public ValueTime(Double value, String backGroundColor, String fontColor) {
			super();
			this.value = value;
			this.backGroundColor = backGroundColor;
			this.fontColor = fontColor;
		}
		public String getBackGroundColor() {
			return backGroundColor;
		}
		public void setBackGroundColor(String backGroundColor) {
			this.backGroundColor = backGroundColor;
		}
		public String getFontColor() {
			return fontColor;
		}
		public void setFontGroundColor(String fontColor) {
			this.fontColor = fontColor;
		}
		
	}
}
