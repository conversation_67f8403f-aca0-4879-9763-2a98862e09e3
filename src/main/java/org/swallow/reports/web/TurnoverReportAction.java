/*
 * @(#)TurnoverReportAction.java 1.0 30/07/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.reports.web;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jett.transform.ExcelTransformer;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;





import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.reports.model.Reports;
import org.swallow.reports.model.TurnoverReport;
import org.swallow.reports.service.TurnoverReportManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

import com.thoughtworks.xstream.XStream;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;

import com.thoughtworks.xstream.io.xml.StaxDriver;
/**
 *
 * <AUTHOR>
 *
 * Class that extends the CustomActionSupport .
 *
 */




import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/turnoverReport", "/turnoverReport.do"})
public class TurnoverReportAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/reports/turnoverreport");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();

	private TurnoverReport turnoverreport;
	public TurnoverReport getTurnoverreport() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		if (turnoverreport == null) {
			turnoverreport = new TurnoverReport();
		}
		return turnoverreport;
	}
	public void setTurnoverreport(TurnoverReport turnoverreport) {
		this.turnoverreport = turnoverreport;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("turnoverreport", turnoverreport);
	}


	/* Get the log for logging */
	private final Log log = LogFactory.getLog(TurnoverReportAction.class);

	/* Manager class instance set as null */
	@Autowired
	private TurnoverReportManager turnoverReportManager = null;

	/**
	 * set the TurnoverReportmanager
	 * @param turnoverReportManager
	 * @return none
	 */
	public void setTurnoverReportManager(
			TurnoverReportManager turnoverReportManager) {
		this.turnoverReportManager = turnoverReportManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "displayList":
				return displayList();
			case "report":
				return report();
		}


		return unspecified();
	}
	/**
	 * While on loaded screen this method is called.
	 * @return ActionForward
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + "- [unspecified] - "
				+"returns to displayList");
		/* Returns displayList method */
		return displayList();
	}

	/**
	 * This method is use full to display the default entity and date in UI.
	 * @return ActionForward
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String displayList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + "- [displayList] - Entering ");
		/* Method's local variable and class instance declaration */
		String entityId = null;
		String reportDate = null;
		String entityText = null;
		Calendar cal=null;
		Date date;
// To remove: 		DynaValidatorForm dyForm;
		TurnoverReport turnoverreport;
		boolean isEntityChanged = false;
		String firstThresholdDay = null;
		String lastThresholdDay = null;
		Integer movementRetainValue = 30;
		Date entityTimeFrame = null;
		Entity entity = null;
		EntityManager entityManger = null;
		int largeMovementRetentionPerriod = 30;
		int smallMovementRetentionPerriod = 30;
		String configScheduler = null;
		String schedulerConfigXML = null;
		String newTurnoverReportSchedConfig = null;
		String reportType = null;
		String dateFormat = null;
		try {
			configScheduler = request.getParameter("configScheduler");
			newTurnoverReportSchedConfig = request.getParameter("newTurnoverReportSchedConfig");
			reportType = request.getParameter("reportType");
			/* Struts Framework built-in Class used to set and get the Form(JSP) values */
// To remove: 			dyForm = (DynaValidatorForm) form;
			/* Gets the bean from the form and validate the fields */
			turnoverreport = (TurnoverReport) getTurnoverreport();
			if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))) {
				if (!SwtUtil.isEmptyOrNull(newTurnoverReportSchedConfig)
						&& "false".equals(newTurnoverReportSchedConfig)) {
					HashMap<String, String> schedulerConfigMap;
					schedulerConfigXML = SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
					schedulerConfigMap = convertschedulerConfigXMLtoHashmap(schedulerConfigXML);
					turnoverreport = new TurnoverReport();
					dateFormat = schedulerConfigMap.get("dateformatasstring");
					entityId = schedulerConfigMap.get("entityid");
				}
			}

			/*
			 * Getting the entityId from request.If it is null,getting the User
			 * current entityId from session
			 */
			entityId = ((request.getParameter("entityId") != null) && (request
					.getParameter("entityId").trim().length() > 0)) ? request
					.getParameter("entityId") : SwtUtil.isEmptyOrNull(entityId)?SwtUtil
					.getUserCurrentEntity(request.getSession()):entityId;

			isEntityChanged = SwtUtil.isEmptyOrNull(request.getParameter("changeEntity")) ? false : request.getParameter("changeEntity").equalsIgnoreCase("Y");
			/* get entity name from UI */
			entityText = request.getParameter("entityText");
			/* Sets the entityId in turnoverreport bean object */
			turnoverreport.getId().setEntityId(entityId);
			/* Puts the EntityAccess rights into request object */
			putEntityAccessInReq(request, entityId);

			/* Sets the entity in request object */
			request.setAttribute("entityName", entityId);
			request.setAttribute("entityText", entityText);
			/* Puts the list of entities into request object */
			putEntityListInReq(request);
			entityTimeFrame = SwtUtil.getSysParamDateWithEntityOffset(entityId);

			/* Gets the today date */
			cal = Calendar.getInstance();
			cal.setTime(entityTimeFrame);
			cal.add(Calendar.DATE, -1);
			date = cal.getTime();
			/* Format the date for Current System Formats */
			reportDate = SwtUtil.formatDate(date, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());


			if (SwtUtil.isEmptyOrNull(turnoverreport.getDateAsString() )  || isEntityChanged) {
				turnoverreport.setDateAsString(reportDate);
			}

			if (SwtUtil.isEmptyOrNull(turnoverreport.getToDateAsString() ) || isEntityChanged) {
				turnoverreport.setToDateAsString(reportDate);
			}


			request.setAttribute("yesterday", reportDate);
			request.setAttribute("changeEntity", request.getParameter("changeEntity"));


			// Set the date value, single or date range
			if (SwtUtil.isEmptyOrNull(turnoverreport.getSingleOrRange())) {
				turnoverreport.setSingleOrRange("S");
			}

			if (SwtUtil.isEmptyOrNull(turnoverreport.getForecastedOrActuals())) {
				turnoverreport.setForecastedOrActuals("F");
			}

			if (SwtUtil.isEmptyOrNull(turnoverreport.getPdfOrExcel())) {
				turnoverreport.setPdfOrExcel("P");
			}

			// Get the entity manager from which to get the max retain value
			entityManger = (EntityManager) (SwtUtil
					.getBean("entityManager"));
			entity = new Entity();
			entity.getId().setEntityId(entityId);
			entity.getId().setHostId(SwtUtil.getCurrentHostId());
			entity = entityManger.getEntityDetail(entity);

			if(entity != null) {
				if(entity.getMovementRetention() != null) {
					try {
						largeMovementRetentionPerriod = Integer.parseInt(entity.getMovementRetention());
					} catch (NumberFormatException e) {
					}
				}else {
					largeMovementRetentionPerriod = -1;
				}

				if(entity.getSmallMovementRetain() != null) {
					try {
						smallMovementRetentionPerriod = Integer.parseInt(entity.getSmallMovementRetain());
					} catch (NumberFormatException e) {
					}
				}else {
					smallMovementRetentionPerriod = -1;
				}


			}

			if(smallMovementRetentionPerriod == -1  || largeMovementRetentionPerriod == -1)
				movementRetainValue = -1;
			else
				movementRetainValue = Math.max(largeMovementRetentionPerriod, smallMovementRetentionPerriod);


			// Calendar last threshold date value
			lastThresholdDay = SwtUtil.formatDate(SwtUtil.dateAdd(entityTimeFrame, 0), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			// Calendar first threshold value
			if(movementRetainValue != -1){
				firstThresholdDay = SwtUtil.formatDate(SwtUtil.dateAdd(entityTimeFrame, -movementRetainValue - 1), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}else{
				firstThresholdDay = lastThresholdDay;
			}

			if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))) {
				if (!SwtUtil.isEmptyOrNull(newTurnoverReportSchedConfig) && "false".equals(newTurnoverReportSchedConfig)) {
					HashMap<String, String> schedulerConfigMap;
					schedulerConfigXML = SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
					schedulerConfigMap = convertschedulerConfigXMLtoHashmap(schedulerConfigXML);
					turnoverreport = new TurnoverReport();

					turnoverreport.getId().setEntityId(schedulerConfigMap.get("entityid"));
					turnoverreport.setForecastedOrActuals(schedulerConfigMap.get("forecastedoractuals"));
					turnoverreport.setPdfOrExcel(schedulerConfigMap.get("pdforexcel"));
					turnoverreport.setSingleOrRange(schedulerConfigMap.get("singleorrange"));
					turnoverreport.setDateAsString(schedulerConfigMap.get("dateasstring"));
					turnoverreport.setToDateAsString(schedulerConfigMap.get("todateasstring"));
					turnoverreport.setshowMain(schedulerConfigMap.get("selectedshowmain"));
					turnoverreport.setshowSub(schedulerConfigMap.get("selectedshowsub"));
					dateFormat = schedulerConfigMap.get("dateformatasstring");

					if(!SwtUtil.isEmptyOrNull(dateFormat) && !SwtUtil.isEmptyOrNull(schedulerConfigMap.get("dateasstring"))) {
						String dateToSet = null;
						if(SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("dateasstring"))) {
							dateToSet = schedulerConfigMap.get("dateasstring");
						}else {
							Date fromdate = SwtUtil.parseDate(schedulerConfigMap.get("dateasstring"), dateFormat);
							dateToSet = SwtUtil.formatDate(fromdate, SwtUtil.getCurrentDateFormat(request.getSession()));
						}
						turnoverreport.setDateAsString(dateToSet);
					}else {
						turnoverreport.setDateAsString(schedulerConfigMap.get("dateasstring"));
					}

					if(!SwtUtil.isEmptyOrNull(dateFormat) && !SwtUtil.isEmptyOrNull(schedulerConfigMap.get("todateasstring"))) {

						String toDateToSet = null;
						if(SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("dateasstring"))) {
							toDateToSet = schedulerConfigMap.get("todateasstring");
						}else {
							Date toDate = SwtUtil.parseDate(schedulerConfigMap.get("todateasstring"), dateFormat);
							toDateToSet = SwtUtil.formatDate(toDate, SwtUtil.getCurrentDateFormat(request.getSession()));
						}
						turnoverreport.setToDateAsString(toDateToSet);
					}else {
						turnoverreport.setToDateAsString(schedulerConfigMap.get("todateasstring"));
					}


				}

			}else {
				/* Sets the date in turnoverreport bean object */
				turnoverreport.setDateAsString(reportDate);
				turnoverreport.setToDateAsString(reportDate);
			}

			// adding into label bean
			request.setAttribute("keywords", SwtUtil.getKeywords(request));
			request.setAttribute("firstThresholdDay", firstThresholdDay);
			request.setAttribute("lastThresholdDay", lastThresholdDay);
			request.setAttribute("configScheduler", configScheduler);
			request.setAttribute("newTurnoverReportSchedConfig", newTurnoverReportSchedConfig);
			request.setAttribute("reportType",reportType);
			request.setAttribute("schedulerConfigXML", request.getParameter("schedulerConfigXML"));
			request.setAttribute("jobId", request.getParameter("jobId"));
			/* set bean in dynaValidator */
			setTurnoverreport(turnoverreport);
			log.debug(this.getClass().getName() + "- [displayList] - Exiting ");

			return getView("success");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [displayList] method - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayList", TurnoverReportAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * This method will accept the input from the user and send the generated
	 * report to the user
	 * @return ActionForward - Result of type
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String report()
			throws SwtException {

		log.debug(this.getClass().getName() + "- [report] - Entering ");

		/* Method's local variable declaration. */
		String hostId=null;
		String entityId=null;
		String reportFromDate=null;
		String reportToDate=null;
		String entityName=null;
		String showMain=null;
		String showSub=null;
		String outputFormat=null;
		String dataSource=null;
		String singleOrRange=null;
		String dataSourceName = null;
		JasperPrint jasperPrint = null;
		JRPdfExporter pdfexporter;
		ServletOutputStream out;
		SystemFormats sysformat = null;
		InputStream fileIn = null;


		List<String>	sheetNamesList = null;
		List<String>	templateSheetNamesList = null;
		ArrayList<Map<String, Object>> beansList = null;


		String sqlQueryAsString = null;
		try {

			/* Getting the hostId from the cacheManager */
			hostId = CacheManager.getInstance().getHostId();

			/*
			 * Getting the entityId ,entityName,reportDate,showMain,showSub from
			 * request
			 */
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityText");

			reportFromDate = request.getParameter("selectedFromDate");
			reportToDate = request.getParameter("selectedToDate");

			showMain = request.getParameter("selectedShowMain");
			showSub = request.getParameter("selectedShowSub");


			outputFormat = request.getParameter("selectedOutputFormat");
			dataSource = request.getParameter("selectedDatasource");
			singleOrRange = request.getParameter("singleOrDateRange");

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());


			out = response.getOutputStream();
			/*
			 * In UI, if the user selected showMain option, store the Main value
			 * as M, same like Sub is S
			 */
			if (showMain.equals("Y")) {
				showMain = "M";
			} else if (showMain.equals("N")) {
				showMain = "";
			}

			if (showSub.equals("Y")) {
				showSub = "S";
			} else if (showSub.equals("N")) {
				showSub = "";
			}

			//If single day is selected then to date = from date
			if (singleOrRange.equals(SwtConstants.TURNOVER_SINGLE_DAY)) {
				reportToDate = reportFromDate;
			}

			if (dataSource.equals(SwtConstants.TURNOVER_DATASOURCE_FORECASTED)) {
				dataSource = "I";
				dataSourceName = SwtUtil.getMessage("turnoverReport.forecasted", request);
			} else {
				dataSource = "E";
				dataSourceName = SwtUtil.getMessage("turnoverReport.actuals", request);
			}


			if(SwtConstants.TURNOVER_OUTPUT_PDF.equals(outputFormat)) {
				sqlQueryAsString = SwtUtil.getNamedQuery("turnover_report_query");
				/* To get the filled report form reportsManager */
				jasperPrint = turnoverReportManager
						.getTurnoverReport(request, hostId, entityId, entityName,
								reportFromDate, reportToDate, showMain, showSub, dataSource, dataSourceName, sysformat.getDateFormatValue());

				/* Initializing the JRDFExporter */
				pdfexporter = new JRPdfExporter();
				/* To set the output type as PDF file */
				response.setContentType("application/pdf");
				/* To set the content as attachment */
				response.setHeader("Content-disposition", "attachment; filename="
						+ jasperPrint.getName() + "-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
				/* To pass the filled report */
				pdfexporter.setParameter(JRExporterParameter.JASPER_PRINT,
						jasperPrint);
				/* Providing the output stream */
				pdfexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				/* Exporting as UTF-8 */
				pdfexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
						"UTF-8");
				/* Export Report */
				pdfexporter.exportReport();
			}else {

				ExcelTransformer transformer = new ExcelTransformer();

				String excelTabName = SwtUtil.getMessage("turnoverReport.templateSheetName", request);


				fileIn = new FileInputStream(request.getSession().getServletContext().getRealPath("/")+ SwtConstants.TURNOVER__REPORTS_TEMPLATE);


				sheetNamesList = new ArrayList<String>();
				templateSheetNamesList = new ArrayList<String>();


				templateSheetNamesList.add(excelTabName);
				sheetNamesList.add(excelTabName);


				beansList = turnoverReportManager
						.getExcelTurnoverReportData(request, hostId, entityId, entityName,
								reportFromDate, reportToDate, showMain, showSub, dataSource, dataSourceName,  sysformat.getDateFormatValue());


				// Silently ignore null values
				transformer.setSilent(true);
				transformer.setDebug(false);
				Workbook workbook = transformer.transform(fileIn, templateSheetNamesList, sheetNamesList, beansList);


				response.setContentType("application/vnd.ms-excel");

				// To set the content as attachment
				response.setHeader("Content-disposition", "attachment; filename="
						+ "TurnoverReport" + "-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xlsx");
				out = response.getOutputStream();

				workbook.write(out);
				//out.close();

			}

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [report] method - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [report] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "report", TurnoverReportAction.class), request, "");
			return getView("fail");

		}

		log.debug(this.getClass().getName() + "- [report] - Exiting ");
		return null;
	}

	/**
	 * This method is used to put the Entity Access rights in request object
	 *
	 * @param request - HttpServletRequest
	 * @param entityId - String
	 * @return int
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private int putEntityAccessInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Entering ");
		/* Used to get the Menu Entity Access */
		int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
				null);
		/* sets the EntityAccess in request object */
		if (accessInd == 0) {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Exiting ");
		return accessInd;
	}

	/**
	 * This method is used to put the list of entities in request object
	 *
	 * @param request
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering ");
		/* Method's local variable declaration. */
		HttpSession session=null;
		Collection entities=null;

		/* Gets the current session */
		session = request.getSession();
		/* Create an instance for Collection */
		entities = new ArrayList();
		/* Gets the list of entities */
		entities = SwtUtil.getUserEntityAccessList(session);
		/* Convert to Label Value bean */
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		/* Sets the entities in request object */
		request.setAttribute("entities", entities);
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting ");
	}

	public HashMap<String, String> convertschedulerConfigXMLtoHashmap(String schedulerConfigXML) {
		XStream xStream = new XStream(new StaxDriver());
		xStream.registerConverter(new SwtUtil.MapEntryConverter());
		xStream.alias("schedconfig", Map.class);
		xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
		HashMap<String, String> extractedMap = (HashMap<String, String>) xStream.fromXML(schedulerConfigXML);
		return extractedMap;
	}

}