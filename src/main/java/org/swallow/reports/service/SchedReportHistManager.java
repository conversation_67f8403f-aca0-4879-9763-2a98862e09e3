package org.swallow.reports.service;

import java.lang.*;
import java.util.*;
import org.swallow.exception.SwtException;
import org.swallow.reports.model.SchedReportHist;

public interface SchedReportHistManager {

	public void saveSchedReportHist(SchedReportHist schedReportHist)
			throws SwtException;

	public void updateSchedReportHist(SchedReportHist schedReportHist)
			throws SwtException;

	public void deleteSchedReportHist(SchedReportHist schedReportHist)
			throws SwtException;

	public SchedReportHist getSchedReportHist(String fileId)
			throws SwtException;

	public Collection<SchedReportHist> getSchedReportHistList(String hostId, String reportJob, String reportType ,boolean isDateRange , Date fromDate , Date toDate, String userId)
			throws SwtException;
	
	public Collection<SchedReportHist> getSchedReportHistListJob(String scheduleId)
			throws SwtException;
	
	public ArrayList<Object[]> getSchedReportHistListRetentionTime(String scheduleId)
			throws SwtException;
	
}