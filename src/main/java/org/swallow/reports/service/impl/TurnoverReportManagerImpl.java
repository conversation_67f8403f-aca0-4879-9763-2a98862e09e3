/*
 * @(#)TurnoverReportManagerImpl.java 1.0 31/07/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.reports.service.impl;

import java.util.ArrayList;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;

import net.sf.jasperreports.engine.JasperPrint;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.dao.TurnoverReportDAO;
import org.swallow.reports.service.TurnoverReportManager;


/**
 * <AUTHOR> 
 * 
 * Class that implements the TurnoverReportManager.
 * 
 */
@Component("turnoverReportManager")
public class TurnoverReportManagerImpl implements TurnoverReportManager {

	/* Log instance for logging */
	private static final Log log = LogFactory
			.getLog(TurnoverReportManagerImpl.class);

	/* DAO's local variable */
	@Autowired
	private TurnoverReportDAO turnoverReportDAO;

	/**
	 * Setter method for the DAO
	 * @param turnoverReportDAO
	 */
	public void setTurnoverReportDAO(TurnoverReportDAO turnoverReportDAO) {
		this.turnoverReportDAO = turnoverReportDAO;
	}

	/**
	 * This method is used to compile and return the reports
	 * 
	 * @param request - HttpServletRequest
	 * @param hostId - String
	 * @param entityId - String
	 * @param entityName - String
	 * @param reportDate - String
	 * @param showMain - String
	 * @param showSub - String
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	public JasperPrint getTurnoverReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String reportFromDate,String reportToDate, String showMain, String showSub, String dataSource, String dataSourceName , String dateFormatValue)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Entering ");
		try {
		/* To call the getTurnoverReport in TurnoverReportDAOHibernate.*/
		JasperPrint jasperPrint = turnoverReportDAO.getTurnoverReport(request,
				hostId, entityId, entityName, reportFromDate, reportToDate, showMain, showSub, dataSource, dataSourceName, dateFormatValue);
		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Exiting ");
		
		return jasperPrint;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getTurnoverReport] method : - "
							+ exp.getMessage());
			log	.error(this.getClass().getName()
							+ " - Exception Catched in [getTurnoverReport] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getTurnoverReport", TurnoverReportManagerImpl.class);
		}
	}

	/**
	 * This method is used to generate data for the turnover excel report
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param entityName
	 * @param reportFromDate
	 * @param reportToDate
	 * @param showMain
	 * @param showSub
	 * @param dataSource
	 * @param dataSourceName
	 * @param dateFormatValue
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<Map<String, Object>> getExcelTurnoverReportData(HttpServletRequest request, String hostId,
			String entityId, String entityName, String reportFromDate, String reportToDate, String showMain,
			String showSub, String dataSource, String dataSourceName, String dateFormatValue) throws SwtException {
		
		
		log.debug(this.getClass().getName()
				+ "- [getUnsettledMovements] - Entering ");
		ArrayList<Map<String, Object>> excelData = turnoverReportDAO.getExcelTurnoverReportData(request,
				hostId, entityId, entityName, reportFromDate, reportToDate, showMain, showSub, dataSource, dataSourceName, dateFormatValue);
		log.debug(this.getClass().getName()
				+ "- [getUnsettledMovements] - Exiting ");
		return excelData;
	}

}