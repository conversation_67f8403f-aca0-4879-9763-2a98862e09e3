/*
 * @(#)HolidayDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.HolidayDAO;
import org.swallow.maintenance.model.Holiday;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
/**
 * DAO implementation layer for Mainataing Holiday list 
 * 
 * @ Modified Vivekanandan.A
 * 
 */

@Repository("holidayDAO")
@Transactional
public class HolidayDAOHibernate extends CustomHibernateDaoSupport implements HolidayDAO {
    private final Log log = LogFactory.getLog(HolidayDAOHibernate.class);

    public HolidayDAOHibernate(@Lazy SessionFactory sessionfactory, 
            @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    @Transactional
    public void deleteHoliday(Holiday holiday) throws SwtException {
        log.debug(this.getClass().getName() + " - [deleteHoliday] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory()
                .withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
                
            Transaction tx = session.beginTransaction();
            try {
                String deleteHQL = "delete from Holiday m where m.id.hostId = :hostId " +
                    "and m.id.entityId = :entityId and m.id.holidayDate = :holidayDate " +
                    "and m.id.countryCode = :countryCode";
                
                Query query = session.createQuery(deleteHQL);
                query.setParameter("hostId", holiday.getId().getHostId());
                query.setParameter("entityId", holiday.getId().getEntityId());
                query.setParameter("holidayDate", holiday.getId().getHolidayDate());
                query.setParameter("countryCode", holiday.getId().getCountryCode());
                query.executeUpdate();
                
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
        } catch (Exception e) {
            log.error("Exception in deleteHoliday: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "deleteHoliday", 
                    HolidayDAOHibernate.class);
        }
        
        log.debug(this.getClass().getName() + " - [deleteHoliday] - Exit");
    }

    @Transactional
    public void saveHoliday(Holiday holiday) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveHoliday] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Long> checkQuery = session.createQuery(
                "select count(m) from Holiday m where m.id.hostId = :hostId " +
                "and m.id.entityId = :entityId and m.id.holidayDate = :holidayDate " +
                "and m.id.countryCode = :countryCode",
                Long.class);
            
            checkQuery.setParameter("hostId", holiday.getId().getHostId());
            checkQuery.setParameter("entityId", holiday.getId().getEntityId());
            checkQuery.setParameter("holidayDate", holiday.getId().getHolidayDate());
            checkQuery.setParameter("countryCode", holiday.getId().getCountryCode());
            
            if (checkQuery.getSingleResult() == 0) {
                try (Session saveSession = getHibernateTemplate().getSessionFactory()
                        .withOptions()
                        .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                        .openSession()) {
                    
                    Transaction tx = saveSession.beginTransaction();
                    try {
                        saveSession.save(holiday);
                        tx.commit();
                    } catch (Exception e) {
                        if (tx != null) tx.rollback();
                        throw e;
                    }
                }
            } else {
                throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
            }
        } catch (Exception e) {
            log.error("Exception in saveHoliday: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "saveHoliday", 
                    HolidayDAOHibernate.class);
        }
        
        log.debug(this.getClass().getName() + " - [saveHoliday] - Exit");
    }

    @Transactional(readOnly = true)
    public Collection<Holiday> getHolidayList(String entity, String hostId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getHolidayList] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Holiday> query = session.createQuery(
                "from Holiday h where h.id.entityId = :entityId and h.id.hostId = :hostId " +
                "order by h.id.countryCode, h.id.holidayDate",
                Holiday.class);
            
            query.setParameter("entityId", entity);
            query.setParameter("hostId", hostId);
            
            List<Holiday> result = query.getResultList();
            log.debug(this.getClass().getName() + " - [getHolidayList] - Exit");
            return result;
        } catch (Exception e) {
            log.error("Exception in getHolidayList: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getHolidayList", 
                    HolidayDAOHibernate.class);
        }
    }

    @Transactional(readOnly = true)
    public synchronized Calendar getBusinessDate(String facility, Calendar cal,
            String entityId, String hostId, String currencyCode,
            int tabPosition, String accountId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getBusinessDate] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             PreparedStatement ps = conn.prepareCall(
                 "select PKG_NON_WORKDAY.GetNextBusinessDate(?,?,?,?,?,?) from dual")) {
            
            ps.setString(1, facility);
            ps.setDate(2, SwtUtil.truncateDateTime(cal.getTime()));
            ps.setString(3, entityId);
            ps.setString(4, accountId);
            ps.setString(5, currencyCode);
            ps.setInt(6, tabPosition);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    cal.setTime(rs.getDate(1));
                }
            }
            
            log.debug(this.getClass().getName() + " - [getBusinessDate] - Exit");
            return cal;
        } catch (Exception e) {
            log.error("Exception in getBusinessDate: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getBusinessDate", 
                    HolidayDAOHibernate.class);
        }
    }

    @Transactional(readOnly = true)
    public synchronized Calendar getBusinessDateMinusOne(Calendar cal,
            String entityId, String hostId, String currencyCode, String facility)
            throws SwtException {
        log.debug(this.getClass().getName() + " - [getBusinessDateMinusOne] - Entry");
        
        if (currencyCode == null || currencyCode.trim().isEmpty()) {
            return cal;
        }
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             PreparedStatement ps = conn.prepareCall(
                 "select PKG_NON_WORKDAY.GETPREVBUSINESSDATE(?,?,?,?,?) from dual")) {
            
            ps.setString(1, facility);
            ps.setDate(2, SwtUtil.truncateDateTime(cal.getTime()));
            ps.setString(3, entityId);
            ps.setString(4, null);
            ps.setString(5, currencyCode);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    cal.setTime(rs.getDate(1));
                }
            }
            
            log.debug(this.getClass().getName() + " - [getBusinessDateMinusOne] - Exit");
            return cal;
        } catch (Exception e) {
            log.error("Exception in getBusinessDateMinusOne: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getBusinessDateMinusOne", 
                    HolidayDAOHibernate.class);
        }
    }

    @Transactional(readOnly = true)
    public Date getNextBusinessDate(String hostId, String entityId,
            Date inputDate, String currencyCode, String selectedTabIndex,
            String facility) throws SwtException {
        log.debug(this.getClass().getName() + " - [getNextBusinessDate] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             PreparedStatement ps = conn.prepareCall(
                 "select PKG_NON_WORKDAY.GetNextBusinessDate(?,?,?,?,?,?) from dual")) {
            
            if ("All".equals(currencyCode)) {
                currencyCode = null;
            }
            
            ps.setString(1, facility);
            ps.setDate(2, SwtUtil.truncateDateTime(inputDate));
            ps.setString(3, entityId);
            ps.setString(4, null);
            ps.setString(5, currencyCode);
            ps.setInt(6, selectedTabIndex != null ? Integer.parseInt(selectedTabIndex) : 0);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getDate(1);
                }
            }
            
            log.debug(this.getClass().getName() + " - [getNextBusinessDate] - Exit");
            return inputDate;
        } catch (Exception e) {
            log.error("Exception in getNextBusinessDate: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getNextBusinessDate", 
                    HolidayDAOHibernate.class);
        }
    }

    @Transactional(readOnly = true)
    public boolean isWeekend(String facility, Calendar cal, String entityId,
            String hostId, String currencyCode) throws SwtException {
        log.debug(this.getClass().getName() + " - [isWeekend] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             PreparedStatement ps = conn.prepareCall(
                 "select PKG_NON_WORKDAY.GetDateStatus(?,?,?,?,?) from dual")) {
            
            if ("All".equals(currencyCode)) {
                currencyCode = SwtUtil.getDomesticCurrencyForEntity(hostId, entityId);
            }
            
            ps.setString(1, facility);
            ps.setDate(2, SwtUtil.truncateDateTime(cal.getTime()));
            ps.setString(3, entityId);
            ps.setString(4, null);
            ps.setString(5, currencyCode);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    String status = rs.getString(1);
                    return status != null && !status.equals("W");
                }
            }
            
            log.debug(this.getClass().getName() + " - [isWeekend] - Exit");
            return false;
        } catch (Exception e) {
            log.error("Exception in isWeekend: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "isWeekend", 
                    HolidayDAOHibernate.class);
        }
    }
}
