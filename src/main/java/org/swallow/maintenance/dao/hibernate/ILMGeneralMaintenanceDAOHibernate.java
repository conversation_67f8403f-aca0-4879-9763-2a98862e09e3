/*
 * @(#)ILMGeneralMaintenanceDAOHibernate.java 1.0 29/11/13
 *
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import jakarta.persistence.TypedQuery;
import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.swallow.control.model.EntityAccessGui;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.ILMGeneralMaintenanceDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.CcyProcessStatus;
import org.swallow.maintenance.model.CurrencyTO;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMCcyParameters;
import org.swallow.maintenance.model.ILMParams;
import org.swallow.model.AccountQueryResult;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.hibernate.ILMAnalysisMonitorDAOHibernate;
import org.swallow.work.service.ILMAnalysisMonitorManager;

@Repository ("ilmGeneralMaintenanceDAO")
public class ILMGeneralMaintenanceDAOHibernate extends CustomHibernateDaoSupport
		implements ILMGeneralMaintenanceDAO {

	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory
			.getLog(ILMGeneralMaintenanceDAOHibernate.class);

	private final String PUBLIC_ILMGROUP = "PUBLIC";

	public ILMGeneralMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
		super(sessionfactory, entityManager);
	}

	public void updateAccountGroupDetails(ILMAccountGroup group) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		// Old style logging and method comments preserved
		try {
			log.debug(this.getClass().getName() + " - [updateAccountGroupDetails] - " + "Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				session.merge(group);
				tx.commit();
				log.debug(this.getClass().getName() + " - [updateAccountGroupDetails] - " + "Exit");
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [updateAccountGroupDetails] method : - "
					  + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateAccountGroupDetails",
					ILMGeneralMaintenanceDAOHibernate.class);
		}
	}


	public void saveAccountGroupDetails(ILMAccountGroup group) throws SwtException {

		List<?> records = null;
		Transaction tx = null;

		try {
			log.debug(this.getClass().getName() + " - [saveAccountGroupDetails] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMAccountGroup> query = session.createQuery(
						"from ILMAccountGroup g where g.id.ilmGroupId = :groupId",
						ILMAccountGroup.class
				);
				query.setParameter("groupId", group.getId().getIlmGroupId());
				records = query.getResultList();

				// Condition to check list size
				if (records.size() == 0) {
					// Save the account group in the database
					tx = session.beginTransaction();
					session.save(group);
					tx.commit();
				} else {
					throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
				}
			}

			log.debug(this.getClass().getName() + " - [saveAccountGroupDetails] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [saveAccountGroupDetails] method : - "
					  + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveAccountGroupDetails",
					ILMGeneralMaintenanceDAOHibernate.class);
		}
	}



	public boolean saveAccountGroupSubAccounts(String hostId, String entityId, String accountGroupId, String accountToAdd, String accountToDelete)
			throws SwtException {

		Session session = null;
		Connection connection = null;
		CallableStatement callStatement = null;
		ResultSet resultSetILMCcyParametersDetails = null;
		String resulString = null;
		boolean result;

		try {
			log.debug(this.getClass().getName() + "- [saveAccountGroupSubAccounts] - Entering ");

			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			// Calls the Stored Procedure
			callStatement = connection
					.prepareCall("{call pkg_ilm.sp_save_acct_group_details(?,?,?,?,?,?) }");
			callStatement.setString(1, hostId);
			callStatement.setString(2, entityId);
			callStatement.setString(3, accountGroupId);
			callStatement.setString(4, accountToAdd);
			callStatement.setString(5, accountToDelete);
			// Registers the CURSOR as OUT parameter
			callStatement.registerOutParameter(6, oracle.jdbc.OracleTypes.VARCHAR);
			// Executes the CallableStatement
			callStatement.execute();
			// Gets the CURSOR value in the ResultSet
			resulString = callStatement.getString(6);
			result  = SwtUtil.isEmptyOrNull(resulString) && resulString.equals("true") ? true : false;
			log.debug(this.getClass().getName() + "- [saveAccountGroupSubAccounts] - Exit");
		} catch (Exception exp) {
			log.error("Exception Catch in [saveAccountGroupSubAccounts] method : "
					+ exp.getMessage());
			try {
				if (connection != null) {
					connection.rollback();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			result = false;
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveAccountGroupSubAccounts",
					ILMGeneralMaintenanceDAOHibernate.class);

		} finally {
			SwtException thrownException = null;
			try {
				if (connection != null) {
					connection.commit();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
			Object[] exceptions = JDBCCloser.close(
					resultSetILMCcyParametersDetails, callStatement, connection,
					session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"saveAccountGroupSubAccounts",
								ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"saveAccountGroupSubAccounts",
								ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
			// nullify objects
			resulString = null;
		}
		return result;
	}

	public boolean isCentralCcyGroup(String groupId) throws SwtException {
		List<?> acctGrpList;

		try {
			log.debug(this.getClass().getName() + " - [isCentralCcyGroup] - " + "Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMCcyParameters> query = session.createQuery(
						"from ILMCcyParameters cc where cc.centralBankGroupId = :groupId",
						ILMCcyParameters.class
				);
				query.setParameter("groupId", groupId);
				acctGrpList = query.getResultList();
			}

			log.debug(this.getClass().getName() + " - [isCentralCcyGroup] - " + "Exit");

			if (acctGrpList.size() == 0)
				return false;
			else
				return true;

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					  + " - Exception Catched in [isCentralCcyGroup] method : - "
					  + exp.getMessage());

			log.error(this.getClass().getName()
					  + " - Exception Catched in [isCentralCcyGroup] method : - "
					  + exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					exp, "isCentralCcyGroup", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}



	public void deleteAccountDetails(ILMAccountGroup accountGroup) throws SwtException {

		String deleteAccountGroupHQL = null;
		String deleteAccountInGroupHQL = null;
		List<?> records = null;
		ILMCcyParameters ilmCcyParams = null;
		Iterator<?> itr;
		int deleteCounterAccountInGroup;
		int deleteCounterAccountGroup;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		Session sesion = null;

		try {
			log.debug(this.getClass().getName() + "- [deleteAccountDetails] - Entry");

			// If there is a currency parameter that has a global group with the same account
			// group that we have to delete, then update it by nullifying it
			try (Session sessionRead = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMCcyParameters> query = sessionRead.createQuery(
						"from ILMCcyParameters ccyParams where ccyParams.globalGroupId = :groupId",
						ILMCcyParameters.class
				);
				query.setParameter("groupId", accountGroup.getId().getIlmGroupId());
				records = query.getResultList();
			}

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			if (records != null && records.size() > 0) {
				itr = records.iterator();
				ilmCcyParams = new ILMCcyParameters();

				while (itr.hasNext()) {
					ilmCcyParams = (ILMCcyParameters) itr.next();
					ilmCcyParams.setGlobalGroupId(null);

					try (Session sessionUpdate = getHibernateTemplate().getSessionFactory().openSession()) {
						Transaction txUpdate = sessionUpdate.beginTransaction();
						sessionUpdate.update(ilmCcyParams);
						txUpdate.commit();
					}
				}
			}

			// HQL queries with named parameters
			deleteAccountGroupHQL = "delete from ILMAccountGroup s where s.id.ilmGroupId = :groupId";
			deleteAccountInGroupHQL = "delete from ILMAccountInGroups accInGroup where accInGroup.id.ilmGroupId = :groupId";

			// Open main session with SwtInterceptor
			sesion = sessionFactory
					.withOptions()
					.interceptor(interceptor)
					.openSession();
			tx = sesion.beginTransaction();

			deleteCounterAccountInGroup = sesion.createQuery(deleteAccountInGroupHQL)
					.setParameter("groupId", accountGroup.getId().getIlmGroupId())
					.executeUpdate();

			deleteCounterAccountGroup = sesion.createQuery(deleteAccountGroupHQL)
					.setParameter("groupId", accountGroup.getId().getIlmGroupId())
					.executeUpdate();

			tx.commit();

			// Throw an exception if no records were deleted
			if (deleteCounterAccountInGroup == 0 && deleteCounterAccountGroup == 0) {
				log.debug(this.getClass().getName()
						  + "- [deleteAccountDetails] - Throw SwtRecordNotExist");
				throw new SwtRecordNotExist();
			}

			log.debug(this.getClass().getName() + "- [deleteAccountDetails] - Exit");

		} catch (Exception exp) {
			log.error("Exception occured in saving/updating scenerio list. Cause : " + exp.getMessage());
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (Exception ignoreExp) {
				log.error("HibernateException occured in rolling back transaction. Cause : "
						  + ignoreExp.getMessage());
			}
			throw SwtErrorHandler.getInstance().handleException(
					exp, "deleteAccountDetails", ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(sesion);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());

			// Nullify the objects
			deleteAccountGroupHQL = null;
			deleteAccountInGroupHQL = null;
			records = null;
			ilmCcyParams = null;
			itr = null;
		}
	}



	public ILMAccountGroup getEditableData(String accountGroupId) throws SwtException {

		ILMAccountGroup accountGroup = null;
		List<?> acctGrpList;
		Iterator<?> itr;

		try {
			log.debug(this.getClass().getName() + " - [getEditableData] - " + "Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMAccountGroup> query = session.createQuery(
						"from ILMAccountGroup grp where grp.id.ilmGroupId = :groupId",
						ILMAccountGroup.class
				);
				query.setParameter("groupId", accountGroupId);
				acctGrpList = query.getResultList();
			}

			itr = acctGrpList.iterator();
			while (itr.hasNext())
				accountGroup = (ILMAccountGroup) itr.next();

			log.debug(this.getClass().getName() + " - [getEditableData] - " + "Exit");
			return accountGroup;

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					  + " - Exception Catched in [getEditableData] method : - "
					  + exp.getMessage());

			log.error(this.getClass().getName()
					  + " - Exception Catched in [getEditableData] method : - "
					  + exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					exp, "getEditableData", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}



	public Collection getAccountGroupsList(String hostId, String entityId, String currencyCode) throws SwtException {

		List<?> list;

		try {
			log.debug(this.getClass().getName() + " - [getAccountGroupsList] - " + "Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				if (!SwtUtil.isEmptyOrNull(currencyCode) && currencyCode.equals("All")) {
					TypedQuery<ILMAccountGroup> query = session.createQuery(
							"from ILMAccountGroup ilm where ilm.hostId = :hostId and ilm.entityId = :entityId order by ilm.ilmGroupName",
							ILMAccountGroup.class
					);
					query.setParameter("hostId", hostId);
					query.setParameter("entityId", entityId);
					list = query.getResultList();
				} else {
					if (SwtUtil.isEmptyOrNull(entityId)) {
						TypedQuery<ILMAccountGroup> query = session.createQuery(
								"from ILMAccountGroup ilm where ilm.hostId = :hostId order by ilm.ilmGroupName",
								ILMAccountGroup.class
						);
						query.setParameter("hostId", hostId);
						list = query.getResultList();
					} else {
						TypedQuery<ILMAccountGroup> query = session.createQuery(
								"from ILMAccountGroup ilm where ilm.hostId = :hostId and ilm.entityId = :entityId and ilm.currencyCode = :currencyCode order by ilm.ilmGroupName",
								ILMAccountGroup.class
						);
						query.setParameter("hostId", hostId);
						query.setParameter("entityId", entityId);
						query.setParameter("currencyCode", currencyCode);
						list = query.getResultList();
					}
				}
			}

			log.debug(this.getClass().getName() + " - [getAccountGroupsList] - " + "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getAccountGroupsList] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					e, "getAccountGroupsList", ILMGeneralMaintenanceDAOHibernate.class);
		}

		return list;
	}

	public List<LabelValueBean> getAccountGroupsListSQL(String hostId, String entityId,
														String currencyCode, boolean throuputOnly) throws SwtException {

		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		ArrayList<LabelValueBean> accountGroupList = null;
		int paramenterIndex = 1;
		try {
			log.debug(this.getClass().getName() + " - [getAccountGroupsListSQL] - " + "Entry");


			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("SELECT  ILM_GROUP_ID, ILM_GROUP_NAME "
					+"FROM P_ILM_ACC_GROUP "
					+"WHERE HOST_ID = ? "+(!SwtConstants.ALL_VALUE.equals(entityId)?"AND  ENTITY_ID = ?":" ")+ (!SwtConstants.ALL_VALUE.equals(currencyCode)?"AND  CURRENCY_CODE = ? ":" ")
					+ "ORDER BY ILM_GROUP_NAME");

			cstmt.setString(1, hostId);
			if(!SwtConstants.ALL_VALUE.equals(entityId)) {
				cstmt.setString(++paramenterIndex, entityId);
			}
			if(!SwtConstants.ALL_VALUE.equals(currencyCode)) {
				cstmt.setString(++paramenterIndex, currencyCode);
			}
			rs= cstmt.executeQuery();
			accountGroupList = new ArrayList<LabelValueBean>();

			while(rs.next()){
				LabelValueBean bean = new LabelValueBean();
				bean.setLabel(rs.getString("ILM_GROUP_NAME"));
				bean.setValue(rs.getString("ILM_GROUP_ID"));
				accountGroupList.add(bean);
			}
			log.debug(this.getClass().getName() + " - [getAccountGroupsListSQL] - " + "Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountGroupsListSQL] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountGroupsListSQL",
					ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getAccountGroupsListSQL",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getAccountGroupsListSQL",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return accountGroupList;
	}
	public List<LabelValueBean> getAccountInGroupListSQL(String hostId, String entityId, String currencyCode,
														 String ilmAccountGroup) throws SwtException {

		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		ResultSet rs2 = null;
		CallableStatement cstmt = null;
		CallableStatement cstmt2 = null;
		ArrayList<LabelValueBean> accountGroupList = null;
		int paramenterIndex = 1;
		String groupType = null;
		String query = null;
		try {
			log.debug(this.getClass().getName() + " - [getAccountInGroupListSQL] - " + "Entry");


			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);


			cstmt2 = conn.prepareCall(
					"SELECT  GROUP_TYPE,FILTER_CONDITION  FROM P_ILM_ACC_GROUP WHERE HOST_ID = ? AND  ENTITY_ID = ? AND  ILM_GROUP_ID = ?");

			cstmt2.setString(1, hostId);
			cstmt2.setString(2, entityId);
			cstmt2.setString(3, ilmAccountGroup);
			rs2= cstmt2.executeQuery();
			accountGroupList = new ArrayList<LabelValueBean>();

			rs2.next();
			groupType = rs2.getString("GROUP_TYPE");
			query = rs2.getString("FILTER_CONDITION");
			if("D".equalsIgnoreCase(groupType)) {
				AccountQueryResult queryResylt = getAccountsQueryResult(hostId, entityId, currencyCode, query);
				for (AcctMaintenance account : queryResylt.getAccountList()) {
					LabelValueBean bean = new LabelValueBean();
					bean.setLabel(account.getAcctname());
					bean.setValue(account.getId().getAccountId());
					accountGroupList.add(bean);
				}
			}else {
				cstmt = conn.prepareCall(
						"SELECT  ag.ACCOUNT_ID, a.ACCOUNT_NAME  FROM P_ILM_ACC_IN_GROUP ag, p_account a WHERE A.ACCOUNT_ID = AG.ACCOUNT_ID AND A.ENTITY_ID ="
								+ " AG.ENTITY_ID AND AG.HOST_ID = ? AND  AG.ENTITY_ID = ? AND  AG.ILM_GROUP_ID = ?");

				cstmt.setString(1, hostId);
				cstmt.setString(++paramenterIndex, entityId);
				cstmt.setString(++paramenterIndex, ilmAccountGroup);
				rs= cstmt.executeQuery();
				accountGroupList = new ArrayList<LabelValueBean>();

				while(rs.next()){
					LabelValueBean bean = new LabelValueBean();
					bean.setLabel(rs.getString("ACCOUNT_NAME"));
					bean.setValue(rs.getString("ACCOUNT_ID"));
					accountGroupList.add(bean);
				}
			}
			log.debug(this.getClass().getName() + " - [getAccountInGroupListSQL] - " + "Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountGroupsListSQL] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountInGroupListSQL",
					ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			JDBCCloser.close(rs2, cstmt2, null, null);
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getAccountInGroupListSQL",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getAccountGroupsListSQL",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return accountGroupList;
	}

	private final String ILMPARAMETERSQUERY = "from ILMParams c where c.hostId = :hostId";

	public ILMParams getIlmParamsDetails(String hostId) {

		log.debug(this.getClass().getName() + " - [getIlmParamsDetails] - " + "Entry");
		List<?> ilmParamsColl = null;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<ILMParams> query = session.createQuery(ILMPARAMETERSQUERY, ILMParams.class);
			query.setParameter("hostId", hostId);
			ilmParamsColl = query.getResultList();
		}

		log.debug(this.getClass().getName() + " - [getIlmParamsDetails] - " + "Exit");

		if (ilmParamsColl.size() == 1)
			return (ILMParams) ilmParamsColl.get(0);
		else
			return null;
	}


	public void updateIlmParams(ILMParams ilmParams) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [updateIlmParams] - " + "Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				session.update(ilmParams);
				tx.commit();
			}

			log.debug(this.getClass().getName() + " - [updateIlmParams] - " + "Exit");

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					  + " - Exception Catched in [updateIlmParams] method : - "
					  + exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					exp, "updateIlmParams", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}



	public Collection<ILMCcyParameters> getILMCcyParametersDetailList(
			String hostId, String roleId, String entityId) throws SwtException {

		Collection<ILMCcyParameters> ILMCcyParametersList = null;
		ILMCcyParameters ilmCcyParameters = null;
		Session session = null;
		Connection connection = null;
		CallableStatement callStatement = null;
		ResultSet resultSetILMCcyParametersDetails = null;

		try {
			log.debug(this.getClass().getName() + "- [getILMCcyParametersDetailList] - Entering");

			session = getHibernateTemplate().getSessionFactory().openSession();
			connection = SwtUtil.connection(session);

			callStatement = connection.prepareCall("{call pkg_ilm.sp_get_currency_param(?,?,?,?)}");
			callStatement.setString(1, hostId);
			callStatement.setString(2, roleId);
			callStatement.setString(3, entityId);
			callStatement.registerOutParameter(4, oracle.jdbc.OracleTypes.CURSOR);
			callStatement.execute();

			resultSetILMCcyParametersDetails = (ResultSet) callStatement.getObject(4);

			if (resultSetILMCcyParametersDetails != null) {
				ILMCcyParametersList = new ArrayList<>();

				while (resultSetILMCcyParametersDetails.next()) {
					ilmCcyParameters = new ILMCcyParameters();

					ilmCcyParameters.getId().setHostId(resultSetILMCcyParametersDetails.getString("HOST_ID"));
					ilmCcyParameters.getId().setEntityId(resultSetILMCcyParametersDetails.getString("ENTITY_ID"));
					ilmCcyParameters.getId().setCurrencyCode(resultSetILMCcyParametersDetails.getString("CURRENCY_CODE"));

					ilmCcyParameters.setGlobalGroupId(resultSetILMCcyParametersDetails.getString("GLOBAL_GROUP_ID"));
					ilmCcyParameters.setDefaultMapTime(resultSetILMCcyParametersDetails.getString("DEFAULT_MAP_TIME"));
					ilmCcyParameters.setLvpsName(resultSetILMCcyParametersDetails.getString("LVPS_NAME"));
					ilmCcyParameters.setCentralBankGroupId(resultSetILMCcyParametersDetails.getString("CENTRAL_BANK_GROUP_ID"));
					ilmCcyParameters.setPrimaryAccountId(resultSetILMCcyParametersDetails.getString("PRIMARY_ACCOUNT_ID"));
					ilmCcyParameters.setClearingStartTime(resultSetILMCcyParametersDetails.getString("CLEARING_START_TIME"));
					ilmCcyParameters.setClearingEndTime(resultSetILMCcyParametersDetails.getString("CLEARING_END_TIME"));

					ILMCcyParametersList.add(ilmCcyParameters);
				}
			}

			log.debug(this.getClass().getName() + "- [getILMCcyParametersDetailList] - Exit");

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					  + " - Exception Catched in [getILMCcyParametersDetailList] method : - "
					  + exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					exp, "getILMCcyParametersDetailList", ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(
					resultSetILMCcyParametersDetails, callStatement, connection, session);

			if (exceptions[0] != null) {
				thrownException = SwtErrorHandler.getInstance().handleException(
						(SQLException) exceptions[0], "getILMCcyParametersDetailList", ILMGeneralMaintenanceDAOHibernate.class);
			}

			if (thrownException == null && exceptions[1] != null) {
				thrownException = SwtErrorHandler.getInstance().handleException(
						(HibernateException) exceptions[1], "getILMCcyParametersDetailList", ILMGeneralMaintenanceDAOHibernate.class);
			}

			if (thrownException != null)
				throw thrownException;

			ilmCcyParameters = null;
		}

		return ILMCcyParametersList;
	}


	public void saveILMCcyParametersDetails(ILMCcyParameters ilmCcyParameters) throws SwtException {

		List<?> records = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			log.debug(this.getClass().getName() + " - [saveILMCcyParametersDetails] - Entry");

			try (Session sessionRead = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMCcyParameters> query = sessionRead.createQuery(
						"from ILMCcyParameters ilmCcy " +
						"where ilmCcy.id.hostId = :hostId " +
						"and ilmCcy.id.entityId = :entityId " +
						"and ilmCcy.id.currencyCode = :currencyCode",
						ILMCcyParameters.class
				);
				query.setParameter("hostId", ilmCcyParameters.getId().getHostId());
				query.setParameter("entityId", ilmCcyParameters.getId().getEntityId());
				query.setParameter("currencyCode", ilmCcyParameters.getId().getCurrencyCode());
				records = query.getResultList();
			}

			// Condition to check list size
			if (records.size() == 0) {
				try (Session session = sessionFactory
						.withOptions()
						.interceptor(interceptor)
						.openSession()) {

					tx = session.beginTransaction();
					session.save(ilmCcyParameters);
					tx.commit();
				}

				createStandardScenarioForCcy(
						ilmCcyParameters.getId().getHostId(),
						ilmCcyParameters.getId().getEntityId(),
						ilmCcyParameters.getId().getCurrencyCode());

			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}

			log.debug(this.getClass().getName() + " - [saveILMCcyParametersDetails] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [saveILMCcyParametersDetails] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					e, "saveILMCcyParametersDetails", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}



	public void createStandardScenarioForCcy(String hostId, String entityId, String currencyCode) throws SwtException {
		log.debug(this.getClass().getName() + " - [createStandardScenarioForCcy] - Entry");

		try (Session session = getHibernateTemplate().getSessionFactory().openSession();
			 Connection connection = SwtUtil.connection(session);
			 CallableStatement callStatement = connection.prepareCall("{call pkg_ilm_utils.createStandardScenario(?,?,?)}")) {

			// Set parameters
			callStatement.setString(1, hostId);
			callStatement.setString(2, entityId);
			callStatement.setString(3, currencyCode);

			// Execute stored procedure
			callStatement.executeQuery();
			connection.commit();;
			log.debug(this.getClass().getName() + " - [createStandardScenarioForCcy] - Stored procedure executed successfully");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [createStandardScenarioForCcy] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "createStandardScenarioForCcy", ILMGeneralMaintenanceDAOHibernate.class);
		}

		log.debug(this.getClass().getName() + " - [createStandardScenarioForCcy] - Exit");
	}



	public void updateILMCcyParametersDetails(ILMCcyParameters ilmCcyParameters) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [updateILMCcyParametersDetails] - Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				session.update(ilmCcyParameters);
				tx.commit();
			}

			log.debug(this.getClass().getName() + " - [updateILMCcyParametersDetails] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [updateILMCcyParametersDetails] method : - "
					  + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateILMCcyParametersDetails", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}

	public void deleteILMCcyParametersDetails(ILMCcyParameters ilmCcyParameters) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [deleteILMCcyParametersDetails] - Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				session.delete(ilmCcyParameters);
				tx.commit();
			}

			log.debug(this.getClass().getName() + " - [deleteILMCcyParametersDetails] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [deleteILMCcyParametersDetails] method : - "
					  + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteILMCcyParametersDetails", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}

	public ILMCcyParameters getILMCcyParameterEditableData(String hostId, String entityId, String currencyCode) throws SwtException {
		ILMCcyParameters ilmCcyParameters = null;
		List<?> record;

		try {
			log.debug(this.getClass().getName() + " - [getILMCcyParameterEditableData] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMCcyParameters> query = session.createQuery(
						"from ILMCcyParameters ilmCcy where ilmCcy.id.hostId = :hostId and ilmCcy.id.entityId = :entityId and ilmCcy.id.currencyCode = :currencyCode",
						ILMCcyParameters.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
				record = query.getResultList();
			}

			for (Object obj : record) {
				ilmCcyParameters = (ILMCcyParameters) obj;
			}

			log.debug(this.getClass().getName() + " - [getILMCcyParameterEditableData] - Exit");
			return ilmCcyParameters;

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					  + " - Exception Catched in [getILMCcyParameterEditableData] method : - "
					  + exp.getMessage());
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getILMCcyParameterEditableData] method : - "
					  + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getILMCcyParameterEditableData", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}

	public ILMCcyParameters getGlobalAlternativeAccount(String hostId, String entityId, String currencyCode, String groupId) throws SwtException {
		ILMCcyParameters ilmCcyParameters = null;
		List<?> record;

		try {
			log.debug(this.getClass().getName() + " - [getGlobalAlternativeAccount] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMCcyParameters> query = session.createQuery(
						"from ILMCcyParameters ilmCcy where ilmCcy.id.hostId = :hostId and ilmCcy.id.entityId = :entityId and ilmCcy.id.currencyCode = :currencyCode and ilmCcy.globalGroupId = :groupId",
						ILMCcyParameters.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
				query.setParameter("groupId", groupId);
				record = query.getResultList();
			}

			if (record.isEmpty()) {
				return null;
			}

			for (Object obj : record) {
				ilmCcyParameters = (ILMCcyParameters) obj;
			}

			log.debug(this.getClass().getName() + " - [getGlobalAlternativeAccount] - Exit");
			return ilmCcyParameters;

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					  + " - Exception Catched in [getGlobalAlternativeAccount] method : - "
					  + exp.getMessage());
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getGlobalAlternativeAccount] method : - "
					  + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getGlobalAlternativeAccount", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}

	public Collection<AcctMaintenance> getAccountListDetails(String entityId, String currencyCode) throws SwtException {
		Collection<AcctMaintenance> records;

		try {
			log.debug(this.getClass().getName() + " - [getAccountListDetails] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<AcctMaintenance> query = session.createQuery(
						"from AcctMaintenance acct where acct.id.entityId = :entityId and acct.currcode = :currencyCode order by acct.id.accountId",
						AcctMaintenance.class);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
				records = query.getResultList();
			}

			log.debug(this.getClass().getName() + " - [getAccountListDetails] - Exit");
			return records;

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getAccountListDetails] method : - "
					  + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountListDetails", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}



	public AccountQueryResult getAccountsQueryResult(String hostId,
													 String entityId, String currencyCode, String query)
			throws SwtException {

		Session session = null;
		Connection connection = null;
		CallableStatement callStatement = null;
		ResultSet rs = null;
		AccountQueryResult accountQueryResult;
		ArrayList<AcctMaintenance> accountList = null;

		try {
			log.debug(this.getClass().getName() + "- [getAccountsQueryResult] - Entering ");

			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			accountQueryResult = new AccountQueryResult();
			// Calls the Stored Procedure
			callStatement = connection
					.prepareCall("{call pkg_ilm.PROC_GET_ACCGRP_CLAUSE(?,?,?,?,?) }");
			callStatement.setString(1, query);
			callStatement.setString(2, hostId);
			callStatement.setString(3, entityId);
			callStatement.setString(4, currencyCode );
			// Registers the CURSOR as OUT parameter
			callStatement.registerOutParameter(5,
					oracle.jdbc.OracleTypes.CURSOR);
			// Executes the CallableStatement
			callStatement.execute();
			// Gets the CURSOR value in the ResultSet
			rs = (ResultSet) callStatement.getObject(5);
			accountList = new ArrayList();
			if(rs!=null){
				while(rs.next()){
					AcctMaintenance account = new AcctMaintenance();
					account.getId().setAccountId(rs.getString("ACCOUNT_ID"));
					account.setAcctname(rs.getString("ACCOUNT_NAME"));
					account.setAcctlevel(rs.getString("ACCOUNT_LEVEL"));
					account.setAcctClass(rs.getString("ACCOUNT_CLASS"));
					account.setAccttype(rs.getString("ACCOUNT_TYPE"));
					accountList.add(account);
				}
			}
			accountQueryResult.setAccountList(accountList);
			accountQueryResult.setCorrect(true);

			log.debug(this.getClass().getName() + "- [getAccountsQueryResult] - Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountsQueryResult] method : - "
					+ exp.getMessage());

			accountQueryResult = new AccountQueryResult();
			accountQueryResult.setException(exp.getMessage());
			accountQueryResult.setCorrect(false);

		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, callStatement, connection, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getAccountsQueryResult",
								ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getAccountsQueryResult",
								ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return accountQueryResult;
	}


	public String getFilterConditionResult(String filterCondition)
			throws SwtException {

		Session session = null;
		Connection connection = null;
		CallableStatement callStatement = null;
		String errorText = null;

		try {
			log.debug(this.getClass().getName() + " - [getFilterConditionResult] - Entry");

			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			// Calls the Stored Procedure
			callStatement = connection
					.prepareCall("{call pkg_ilm.PROC_TEST_ACCGRP_CLAUSE(?,?,?) }");
			callStatement.setString(1,filterCondition);

			// Registers the ERROR_CODE as OUT parameter
			callStatement.registerOutParameter(2,
					oracle.jdbc.OracleTypes.NUMBER);
			// Registers the ERROR_TEXT as OUT parameter
			callStatement.registerOutParameter(3,
					oracle.jdbc.OracleTypes.VARCHAR);
			// Executes the CallableStatement
			callStatement.execute();
			// Gets the errorText value in the ResultSet
			errorText = (String) callStatement
					.getObject(3);

			log.debug(this.getClass().getName() + " - [getFilterConditionResult] - Exit");
			return errorText;

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFilterConditionResult] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getFilterConditionResult",
					ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			try {
				if (connection != null) {
					connection.commit();
				}
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			Object[] exceptions = JDBCCloser.close(
					null, callStatement, connection,
					session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getFilterConditionResult",
								ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getFilterConditionResult",
								ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
	}



	public Collection<ILMAccountGroup> getPublicAccountGroupsList(String hostId, String entityId, String currencyCode) throws SwtException {
		List<ILMAccountGroup> list = null;

		try {
			log.debug(this.getClass().getName() + " - [getPublicAccountGroupsList] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMAccountGroup> query = session.createQuery(
						"from ILMAccountGroup ilm where ilm.hostId = :hostId and ilm.entityId = :entityId and ilm.currencyCode = :currencyCode and ilm.publicPrivate = :publicPrivate",
						ILMAccountGroup.class
				);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
				query.setParameter("publicPrivate", PUBLIC_ILMGROUP);
				list = query.getResultList();
			}

			log.debug(this.getClass().getName() + " - [getPublicAccountGroupsList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getPublicAccountGroupsList] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getPublicAccountGroupsList",
					ILMGeneralMaintenanceDAOHibernate.class);
		}

		return list;
	}

	public Integer accountListCount(String groupId) throws SwtException {
		Integer accs = 0;
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		String query = "select count(*) vCount from  TABLE (pkg_ILM.FN_GET_ILM_ACC_IN_GROUP ('" + groupId +"'))";

		try {
			log.debug(this.getClass().getName() + "- [accountListCount] - Entering ");

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			rs = stmt.executeQuery(query);

			if (rs != null && rs.next()) {
				accs = rs.getInt("vCount");
			}

			log.debug(this.getClass().getName() + "- [accountListCount] - Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [accountListCount] method : - "
					  + exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "accountListCount", ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "accountListCount", ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}

		return accs;
	}

	@Override
	public String getGlobalGroup(String entityId, String currencyId) throws SwtException {
		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		String globalGrp = null;

		try {
			log.debug(this.getClass().getName() + " - [getGlobalGroup] - Entry");

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("SELECT GLOBAL_GROUP_ID FROM P_ILM_CCY_PARAMETERS WHERE ENTITY_ID = ? AND CURRENCY_CODE = ?");
			cstmt.setString(1, entityId);
			cstmt.setString(2, currencyId);

			rs = cstmt.executeQuery();
			if (rs.next()) {
				globalGrp = rs.getString(1);
			}

			log.debug(this.getClass().getName() + " - [getGlobalGroup] - Exit");
		} catch (SQLException sqlException) {
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getGlobalGroup", ILMGeneralMaintenanceDAOHibernate.class);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getGlobalGroup] method : - "
					  + exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getGlobalGroup", ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getGlobalGroup", ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}

		return globalGrp;
	}

	public Collection<ILMAccountGroup> getAllowedAccountGroupsListForUser(String hostId, String userId, String entityId, String currencyCode, String reportType) throws SwtException {
		List<ILMAccountGroup> list;

		try {
			log.debug(this.getClass().getName() + " - [getAllowedAccountGroupsListForUser] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				StringBuilder queryString = new StringBuilder(
						"FROM ILMAccountGroup ilm WHERE ilm.hostId = :hostId AND ilm.entityId = :entityId AND ilm.currencyCode = :currencyCode AND ilm.allowReporting = 'Y'"
				);

				if ((SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(reportType)) {
					queryString.append(" AND ilm.correspondentBank = 'Y'");
				}
				queryString.append(" ORDER BY ilm.id.ilmGroupId ASC");

				TypedQuery<ILMAccountGroup> query = session.createQuery(queryString.toString(), ILMAccountGroup.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
				list = query.getResultList();
			}

			log.debug(this.getClass().getName() + " - [getAllowedAccountGroupsListForUser] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getAllowedAccountGroupsListForUser] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAllowedAccountGroupsListForUser",
					ILMGeneralMaintenanceDAOHibernate.class);
		}

		return list;
	}

	@Override
	public boolean getCurrencyGMTOffset(String hostId, String entityId, String currencyId) throws SwtException {
		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		String currencyOffset = null;
		boolean haveGMTOffset = false;

		try {
			log.debug(this.getClass().getName() + " - [getCurrencyGMTOffset] - Entry");

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall(
					"select CURRENCY_TZNAME from s_currency_master m, s_currency c where m.CURRENCY_CODE = c.CURRENCY_CODE and c.HOST_ID = ? and c.entity_id = ? and c.CURRENCY_CODE = ?"
			);
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, currencyId);

			rs = cstmt.executeQuery();
			if (rs.next()) {
				currencyOffset = rs.getString(1);
				haveGMTOffset = !SwtUtil.isEmptyOrNull(currencyOffset);
			}

			log.debug(this.getClass().getName() + " - [getCurrencyGMTOffset] - Exit");
		} catch (SQLException sqlException) {
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getCurrencyGMTOffset", ILMGeneralMaintenanceDAOHibernate.class);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getCurrencyGMTOffset] method : - "
					  + exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getCurrencyGMTOffset", ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getCurrencyGMTOffset", ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}

		return haveGMTOffset;
	}



	public Collection<ILMAccountGroup> getAllowedReportAccountGroupList(String hostId,
																		String entityId,
																		String currencyCode) throws SwtException {
		List<ILMAccountGroup> list = null;

		try {
			log.debug(this.getClass().getName() + " - [getAllowedReportAccountGroupList] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMAccountGroup> query = session.createQuery(
						"from ILMAccountGroup ilm " +
						"where ilm.hostId = :hostId and ilm.entityId = :entityId and ilm.currencyCode = :currencyCode " +
						"and ilm.publicPrivate = :publicPrivate and ilm.allowReporting = 'Y' " +
						"order by ilm.ilmGroupName asc",
						ILMAccountGroup.class
				);

				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
				query.setParameter("publicPrivate", PUBLIC_ILMGROUP);

				list = query.getResultList();
			}

			log.debug(this.getClass().getName() + " - [getAllowedReportAccountGroupList] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getAllowedReportAccountGroupList] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					e, "getAllowedReportAccountGroupList", ILMGeneralMaintenanceDAOHibernate.class);
		}

		return list;
	}



	@Override
	public Collection getAllowedEntityList(String hostId, String roleId)
			throws SwtException {

		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		List listEntities = null;

		try {
			log.debug(this.getClass().getName() + " - [getAllowedEntityList] - " + "Entry");


			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("SELECT DISTINCT P.ENTITY_ID, ENTITY_NAME "
					+ "FROM S_ENTITY E "
					+ "INNER JOIN P_ILM_CCY_PARAMETERS P ON (E.HOST_ID = P.HOST_ID AND E.ENTITY_ID = P.ENTITY_ID) "
					+ "WHERE PK_APPLICATION.FNGETENTITYACCESS(?, P.ENTITY_ID, ?) != 2 "
					+ "ORDER BY P.ENTITY_ID");

			cstmt.setString(1, hostId);
			cstmt.setString(2, roleId);
			rs= cstmt.executeQuery();
			listEntities = new ArrayList<EntityAccessGui>();

			while(rs.next()){
				EntityAccessGui entityAccess = new EntityAccessGui();
				entityAccess.setEntityId(rs.getString("ENTITY_ID"));
				entityAccess.setEntityName(rs.getString("ENTITY_NAME"));
				listEntities.add(entityAccess);
			}
			log.debug(this.getClass().getName() + " - [getAllowedEntityList] - " + "Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAllowedEntityList] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAllowedEntityList",
					ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getAllowedEntityList",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getAllowedEntityList",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return listEntities;
	}


	public Collection getAllowedCurrencyList(String hostId, String roleId,
											 String entityId) throws SwtException {

		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		List listCurrencies = null;

		try {
			log.debug(this.getClass().getName() + " - [getAllowedCurrencyList] - " + "Entry");


			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("SELECT P.CURRENCY_CODE, CURRENCY_NAME, E.ENTITY_ID, E.ENTITY_NAME, "
					+ "PK_APPLICATION.FNGETCURRENCYACCESS (?,?,P.ENTITY_ID,P.CURRENCY_CODE) CURRENCY_ACCESS "
					+ "FROM P_ILM_CCY_PARAMETERS P "
					+ "INNER JOIN S_ENTITY E "
					+ "ON (P.ENTITY_ID = E.ENTITY_ID AND P.HOST_ID = E.HOST_ID) "
					+ "INNER JOIN S_CURRENCY_MASTER CM ON P.CURRENCY_CODE = CM.CURRENCY_CODE "
					+ "WHERE PK_APPLICATION.FNGETCURRENCYACCESS (?,?,P.ENTITY_ID,P.CURRENCY_CODE) != 2 "
					+ "AND P.ENTITY_ID=? "
					+ "ORDER BY P.CURRENCY_CODE");

			cstmt.setString(1, hostId);
			cstmt.setString(2, roleId);
			cstmt.setString(3, hostId);
			cstmt.setString(4, roleId);
			cstmt.setString(5, entityId);
			rs= cstmt.executeQuery();
			listCurrencies = new ArrayList<CurrencyTO>();

			while(rs.next()){
				String currencyId =	rs.getString("CURRENCY_CODE");
				String currencyName	= rs.getString("CURRENCY_NAME");
				int currencyAccess = rs.getInt("CURRENCY_ACCESS");
				CurrencyTO currency = new CurrencyTO(entityId, currencyId, currencyName, currencyAccess);
				listCurrencies.add(currency);
			}

			log.debug(this.getClass().getName() + " - [getAllowedCurrencyList] - " + "Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAllowedCurrencyList] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAllowedCurrencyList",
					ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getAllowedCurrencyList",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getAllowedCurrencyList",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return listCurrencies;
	}


	public Object[] getCcyProcessStatusDetails(String hostId, String entityId,
											   String currencyCode, String userId, Date startDate,
											   Date endDate, String overwrite, String processOption)
			throws SwtException {

		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		ArrayList<CcyProcessStatus> ccyProcessStatusList = null;
		CcyProcessStatus ccyProcessStatus = null;
		Object[] result = new Object[]{"", null};

		String statusSummary = null;
		try {
			log.debug(this.getClass().getName() + " - [getCcyProcessStatusDetails] - " + "Entry");


			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("{call pkg_job_ilm.GetCcyProcessStatus(?,?,?,?,?,?,?,?,?) }");

			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, currencyCode);
			cstmt.setString(4, userId);
			cstmt.setDate(5, SwtUtil.truncateDateTime(startDate));
			cstmt.setDate(6, SwtUtil.truncateDateTime(endDate));
			cstmt.setString(7, processOption);
			cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.VARCHAR);
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);

			cstmt.executeQuery();

			// Gets the status summary value in the ResultSet
			statusSummary = (String)cstmt.getObject(8);

			//Gets the CURSOR value in the ResultSet
			rs = (ResultSet) cstmt.getObject(9);

			ccyProcessStatusList = new ArrayList<CcyProcessStatus>();
			while(rs != null && rs.next()){

				ccyProcessStatus = new CcyProcessStatus();
				ccyProcessStatus.getId().setHostId(rs.getString("HOST_ID"));
				ccyProcessStatus.getId().setEntityId(rs.getString("ENTITY_ID"));
				ccyProcessStatus.getId().setProcessId(rs.getString("PROCESS_ID"));
				ccyProcessStatus.getId().setCcyCode(rs.getString("CURRENCY_CODE"));
				ccyProcessStatus.getId().setValueDate(rs.getDate("VALUE_DATE"));
				ccyProcessStatus.setLastStarted(rs.getTimestamp("LAST_STARTED"));
				ccyProcessStatus.setLastEnded(rs.getTimestamp("LAST_ENDED"));
				ccyProcessStatus.setCurrentStatus(rs.getString("CURRENT_STATUS"));
				ccyProcessStatus.setLastExecuteStatus(rs.getString("LAST_EXECUTE_STATUS"));

				String access = rs.getString("CCY_ACCESS_ID");
				if(!SwtUtil.isEmptyOrNull(access) && "1".equals(access)) {
					ccyProcessStatus.setHasViewAccessOnly(true);
				}else {
					ccyProcessStatus.setHasViewAccessOnly(false);
				}

				ccyProcessStatusList.add(ccyProcessStatus);
			}

			// Prepare the result
			result = new Object[]{statusSummary, ccyProcessStatusList};

			log.debug(this.getClass().getName() + " - [getCcyProcessStatusDetails] - " + "Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCcyProcessStatusDetails] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getCcyProcessStatusDetails",
					ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getCcyProcessStatusDetails",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getCcyProcessStatusDetails",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return result;
	}



	public String runManualCurrencyProcess(String hostId, String entityId,
										   String currencyCode, String userId, Date startDate,
										   Date endDate, String overwrite, String processOption,
										   String sequenceNumber, CommonDataManager CDM)
			throws SwtException {
		Connection connection = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement callStatement = null;
		String resulString = "N";
		boolean erredProcess = false;
		try {
			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			// Calls the Stored Procedure
			/*
			   PROCEDURE launchManualCCYProcess(
			      pHostID                   s_host.host_id%type,
			      pEntityID                 s_entity.entity_id%type,
			      pCurrencyCode             s_currency.currency_code%type,
			      pUserID                   s_users.user_id%type,
			      pValueDateStart           p_movement.value_date%type,
			      pValueDatEnd              p_movement.value_date%type,
			      pProcessId                VARCHAR2,
			      pOverwrite                IN VARCHAR2 DEFAULT 'N',
			      pOption                   IN VARCHAR2 DEFAULT 'A',
			      pResult                   OUT VARCHAR2
			  )
			 */
			callStatement = connection
					.prepareCall("{call PKG_JOB_ILM.launchManualCCYProcess(?,?,?,?,?,?,?,?,?,?)}");

			callStatement.setString(1, hostId);
			callStatement.setString(2, entityId);
			callStatement.setString(3, currencyCode);
			callStatement.setString(4, userId);
			callStatement.setDate(5, SwtUtil.truncateDateTime(startDate));
			if(endDate!= null)
				callStatement.setDate(6, SwtUtil.truncateDateTime(endDate));
			else
				callStatement.setDate(6, SwtUtil.truncateDateTime(startDate));
			callStatement.setString(7, sequenceNumber);
			callStatement.setString(8, overwrite);
			callStatement.setString(9, processOption);
			// Registers the result as OUT parameter
			callStatement.registerOutParameter(10, oracle.jdbc.OracleTypes.VARCHAR);


			CDM.getIlmScreenConnectionDetails().put(sequenceNumber, callStatement);
			rs= callStatement.executeQuery();

			// Gets the result value in the ResultSet
			resulString = callStatement.getString(10);
		} catch (Exception e) {
			// Roll back data
			try {
				if (connection != null) {
					connection.rollback();
				}
			} catch (SQLException e1) {
			}

			// log error when isn't a cancellation request 
			if(CDM.getIlmScreenConnectionDetails().get(sequenceNumber) != null ){
				erredProcess = true;
				log.error(this.getClass().getName()
						+ " - Exception Catched in [ runManualCurrencyProcess ] method : - "
						+ e.getMessage());
				throw  SwtErrorHandler.getInstance().handleException(e,"runManualCurrencyProcess",ILMAnalysisMonitorDAOHibernate.class);
			}
		} finally {
			// remove sequence from 
			try {CDM.getIlmScreenConnectionDetails().remove(sequenceNumber);} catch (Exception e) {}

			// Close resources
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, callStatement, connection, session);

			// Clean up after error
			ILMAnalysisMonitorManager ilmAnalysisMonitorManager = (ILMAnalysisMonitorManager) (SwtUtil
					.getBean("ilmAnalysisMonitorManager"));

			// Clean driver table P_PROCESS_DRIVER: to avoid any deadlock, perform this after resources closure
			try {
				ilmAnalysisMonitorManager.cleanUpProcessDriver(sequenceNumber, CDM.getUser().getId().getUserId());
			} catch (SwtException e) {
			}

			// An exception is raised back, status "F" will fail only running statuses, and set not running for TR records: to avoid any deadlock, perform this after resources closure
			if(erredProcess)
			{
				try {
					updateProcessStatus(hostId, entityId, currencyCode, userId, startDate, endDate, processOption, "F");
				} catch (Exception e2) {
				}
			}

			// If resources closing errors are raised, and at the same time erredProcess=true ==> then do not raise closing error
			if(!erredProcess)
			{
				if (exceptions[0] != null)
					thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "runManualCurrencyProcess",ILMGeneralMaintenanceDAOHibernate.class);

				if (thrownException == null && exceptions[1] !=null)
					thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "runManualCurrencyProcess",ILMGeneralMaintenanceDAOHibernate.class);

				if (thrownException != null)
					throw thrownException;
			}
		}
		return resulString;
	}


	/**
	 * Updates the process status , mainly table P_CCY_PROCESS_STATUS
	 */
	public void updateProcessStatus(String hostId, String entityId,
									String currencyCode, String userId, Date startDate, Date endDate,
									String processOption, String proccessStatus) throws SwtException {
		Connection connection = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement callStatement = null;
		try {
			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			// Calls the Stored Procedure*
			/*
			 PROCEDURE updateProcessStatus (
		      pHostID                   s_host.host_id%type,
		      pEntityID                 s_entity.entity_id%type,
		      pCurrencyCode             s_currency.currency_code%type,
		      pUserID                   s_users.user_id%type,
		      pValueDateStart           p_movement.value_date%type,
		      pValueDatEnd              p_movement.value_date%type,
		      pOption                   IN VARCHAR2,
		      pProcessStatus            VARCHAR2)
			 */
			callStatement = connection
					.prepareCall("{call PKG_JOB_ILM.updateProcessStatus(?,?,?,?,?,?,?,?)}");

			callStatement.setString(1, hostId);
			callStatement.setString(2, entityId);
			callStatement.setString(3, currencyCode);
			callStatement.setString(4, userId);
			callStatement.setDate(5, SwtUtil.truncateDateTime(startDate));
			if(endDate!= null)
				callStatement.setDate(6, SwtUtil.truncateDateTime(endDate));
			else
				callStatement.setDate(6, SwtUtil.truncateDateTime(startDate));
			callStatement.setString(7, processOption);
			callStatement.setString(8, proccessStatus);
			rs= callStatement.executeQuery();

		} catch (Exception e) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateProcessStatus] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"updateProcessStatus",
					ILMGeneralMaintenanceDAOHibernate.class);

		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, callStatement, connection, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "updateProcessStatus",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "updateProcessStatus",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}

	}

	public String getAccountName(String hostId, String entityId, String accountId) throws SwtException {
		String accountName = null;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<String> query = session.createQuery(
					"select acct.acctname from AcctMaintenance acct " +
					"where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.id.accountId = :accountId",
					String.class
			);

			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);

			List<String> result = query.getResultList();
			if (!result.isEmpty()) {
				accountName = result.get(0);
			}

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getAccountName] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					e, "getAccountName", ILMGeneralMaintenanceDAOHibernate.class);
		}

		return accountName != null ? accountName : "";
	}



	/**
	 * Checks for actually running processes
	 */
	public String checkRunningProcesses(String hostId, String entityId,
										String currencyCode, String userId, Date startDate, Date endDate,
										String processOption, HttpServletRequest request) throws SwtException {
		Connection connection = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement callStatement = null;
		String resulString = "S";
		String dateInterference = null;
		String entityCcyInterference = null;
		try {
			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			// Calls the Stored Procedure
			/*
			 PROCEDURE checkRunningCCYProcess(
		          pHostID                   s_host.host_id%type,
		          pEntityID                 s_entity.entity_id%type,
		          pCurrencyCode             s_currency.currency_code%type,
		          pUserID                   s_users.user_id%type,
		          pValueDateStart           p_movement.value_date%type,
		          pValueDatEnd              p_movement.value_date%type,
		          pOption                   IN VARCHAR2 DEFAULT 'A',
		          pRunningEntityCcy         IN OUT VARCHAR2,
		          pRunningValueDate         IN OUT DATE
		    )
			*/
			callStatement = connection
					.prepareCall("{call PKG_JOB_ILM.checkRunningCCYProcess(?,?,?,?,?,?,?,?,?)}");

			callStatement.setString(1, hostId);
			callStatement.setString(2, entityId);
			callStatement.setString(3, currencyCode);
			callStatement.setString(4, userId);
			callStatement.setDate(5, SwtUtil.truncateDateTime(startDate));
			if(endDate!= null)
				callStatement.setDate(6, SwtUtil.truncateDateTime(endDate));
			else
				callStatement.setDate(6, SwtUtil.truncateDateTime(startDate));

			callStatement.setString(7, processOption);
			// Registers the result as OUT parameter
			callStatement.registerOutParameter(8, oracle.jdbc.OracleTypes.VARCHAR);
			callStatement.registerOutParameter(9, oracle.jdbc.OracleTypes.DATE);

			rs= callStatement.executeQuery();

			// Gets the result value in the ResultSet
			if(callStatement.getString(8) != null){
				CommonDataManager CDM = (CommonDataManager) request.getSession()
						.getAttribute("CDM");
				entityCcyInterference = callStatement.getString(8);
				dateInterference = SwtUtil.formatDate(callStatement.getDate(9), CDM.getDateFormatValue());
				resulString= "C#"+entityCcyInterference+"#"+dateInterference;
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [ checkRunningProcesses ] method : - "
					+ e.getMessage());
			throw  SwtErrorHandler.getInstance().handleException(e,"checkRunningProcesses",ILMAnalysisMonitorDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, callStatement, connection, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "checkRunningProcesses",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "checkRunningProcesses",ILMGeneralMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return resulString;
	}

	public boolean checkCentralBankGroupExists(String hostId, String userId, String entityId, String currencyCode) throws SwtException {
		List<?> records = null;

		try {
			log.debug(this.getClass().getName() + " - [checkCentralBankGroupExists] - Entry");

			String hql = "from ILMCcyParameters ilmCcy " +
						 "where ilmCcy.id.hostId = :hostId " +
						 "and ilmCcy.id.entityId = :entityId " +
						 "and ilmCcy.id.currencyCode in (" + currencyCode + ") " +
						 "and ilmCcy.centralBankGroupId is null";

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				Query<?> query = session.createQuery(hql);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				records = query.getResultList();
			}

			return records.size() == 0;

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					  + " - Exception Catched in [checkCentralBankGroupExists] method : - "
					  + exp.getMessage());
			log.error(this.getClass().getName()
					  + " - Exception Catched in [checkCentralBankGroupExists] method : - "
					  + exp.getMessage());
			exp.printStackTrace();

			throw SwtErrorHandler.getInstance().handleException(
					exp, "checkCentralBankGroupExists", ILMGeneralMaintenanceDAOHibernate.class);
		}
	}

	public Collection<ILMAccountGroup> getAllAccountGroupsList(String hostId) throws SwtException {
		List<ILMAccountGroup> listAcc = new ArrayList<>();

		try {
			log.debug(this.getClass().getName() + " - [getAllAccountGroupsList] - Entry");

			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ILMAccountGroup> query = session.createQuery(
						"from ILMAccountGroup ilm where ilm.hostId = :hostId order by ilm.currencyCode",
						ILMAccountGroup.class
				);
				query.setParameter("hostId", hostId);
				listAcc = query.getResultList();
			}

		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getAllAccountGroupsList] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					e, "getAllAccountGroupsList", ILMGeneralMaintenanceDAOHibernate.class);
		}

		return listAcc;
	}

}