/*
 * @(#)CountryOverrideDAOHibernate.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CountryOverrideDAO;
import org.swallow.maintenance.model.CountryOverride;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR> A
 * 
 * Data Access Object Implementations layer for S_COUNTRY_OVERRIDE
 */
@Repository("countryOverrideDAO")
@Transactional
public class CountryOverrideDAOHibernate extends CustomHibernateDaoSupport implements
		CountryOverrideDAO {
	
	/* Final instance for log */
	private final Log log = LogFactory.getLog(this.getClass());
	
	/**
	 * Constructor with dependency injection
	 * @param sessionfactory The session factory
	 * @param entityManager The entity manager
	 */
	public CountryOverrideDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}

	/**
	 * Method to get list of non working days
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getDayList() throws SwtException {
		log.debug(this.getClass().getName() + " - [getDayList] - Entry");
		
		// Label Value Bean Collection to hold list of non working day
		Collection<LabelValueBean> dayListColl = new ArrayList<>();
		// Add empty value for label and value
		dayListColl.add(new LabelValueBean("", ""));

		try (Session session = getHibernateTemplate().getSessionFactory().openSession();
			 Connection conn = SwtUtil.connection(session);
			 CallableStatement cstmt = conn.prepareCall("{call pkg_non_workday.DaysList(?)}")) {
			
			// Set cursor for output
			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			// execute statement
			cstmt.execute();
			
			// Get result set
			try (ResultSet rs = (ResultSet) cstmt.getObject(1)) {
				// condition to check result set not null
				if (rs != null) {
					while (rs.next()) {
						// Set label and value in collection
						dayListColl.add(new LabelValueBean(rs.getString(2), rs.getString(1)));
					}
				}
			}
			
			log.debug(this.getClass().getName() + " - [getDayList] - Exit");
			// return day list collection
			return dayListColl;
		} catch (SQLException sqlException) {
			log.error("sqlException in CountryOverrideDAOHibernate.getDayList", sqlException);
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getDayList", this.getClass());
		} catch (HibernateException hibernateException) {
			log.error("hibernateException in CountryOverrideDAOHibernate.getDayList", hibernateException);
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getDayList", this.getClass());
		} catch (Exception exception) {
			log.error("exception in CountryOverrideDAOHibernate.getDayList", exception);
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getDayList", this.getClass());
		}
	}

	/**
	 * Method to get country list
	 * 
	 * @param entity
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CountryOverride> getCountryList(String entity,
			String hostId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getCountryList] - Entry");
		
		// Collection to hold country over ride list
		Collection<CountryOverride> countryOverrideColl = new ArrayList<>();

		try (Session session = getHibernateTemplate().getSessionFactory().openSession();
			 Connection conn = SwtUtil.connection(session);
			 CallableStatement cstmt = conn.prepareCall("{call pkg_non_workday.CountryMaintenance(?,?,?)}")) {
			
			// pass host Id
			cstmt.setString(1, hostId);
			// pass Entity Id
			cstmt.setString(2, entity);
			// Set cursor to hold values
			cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.CURSOR);
			// execute statement
			cstmt.execute();
			
			// get result set
			try (ResultSet rs = (ResultSet) cstmt.getObject(3)) {
				// Condition to check result set not null
				if (rs != null) {
					// set values in country override bean
					while (rs.next()) {
						CountryOverride countryOverride = new CountryOverride();
						countryOverride.getId().setCountryCode(rs.getString(1));
						countryOverride.setCountryName(rs.getString(2));
						countryOverride.setWeekend1(rs.getString(3));
						countryOverride.setWeekend2(rs.getString(4));
						countryOverride.setOverrideWeekend1(rs.getString(5));
						countryOverride.setOverrideWeekend2(rs.getString(6));
						countryOverrideColl.add(countryOverride);
					}
				}
			}
			
			log.debug(this.getClass().getName() + " - [getCountryList] - Exit");
			return countryOverrideColl;
		} catch (SQLException sqlException) {
			log.error("sqlException in CountryOverrideDAOHibernate.getCountryList", sqlException);
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getCountryList", this.getClass());
		} catch (HibernateException hibernateException) {
			log.error("hibernateException in CountryOverrideDAOHibernate.getCountryList", hibernateException);
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getCountryList", this.getClass());
		} catch (Exception exception) {
			log.error("exception in CountryOverrideDAOHibernate.getCountryList", exception);
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getCountryList", this.getClass());
		}
	}

	/**
	 * Method to save or delete or update country override values in
	 * S_COUNTRY_OVERRIDE
	 * 
	 * @param countryOverride
	 * @param saveStatus
	 * @return
	 * @throws SwtException
	 */
	public void save(CountryOverride countryOverride, String saveStatus)
			throws SwtException {
		log.debug(this.getClass().getName() + "-[save]-Entry");
		
		try {
			// Fetch the records for the country
			List<CountryOverride> records;
			
			try (Session querySession = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<CountryOverride> query = querySession.createQuery(
					"from CountryOverride c where c.id.hostId = :hostId and c.id.entityId = :entityId and c.id.countryCode = :countryCode",
					CountryOverride.class);
				query.setParameter("hostId", countryOverride.getId().getHostId());
				query.setParameter("entityId", countryOverride.getId().getEntityId());
				query.setParameter("countryCode", countryOverride.getId().getCountryCode());
				
				records = query.getResultList();
			}
			
			// Update the existing record if not deleting
			if (!records.isEmpty() && !saveStatus.equals("delete")) {
				CountryOverride existingRecord = records.get(0);
				existingRecord.setOverrideWeekend1(countryOverride.getOverrideWeekend1());
				existingRecord.setOverrideWeekend2(countryOverride.getOverrideWeekend2());
				countryOverride = existingRecord;
			}
			
			// Perform the requested operation
			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			
			try (Session session = getHibernateTemplate().getSessionFactory()
					.withOptions().interceptor(interceptor).openSession()) {
				Transaction tx = session.beginTransaction();
				
				try {
					if (saveStatus.equals("delete")) {
						log.debug(this.getClass().getName() + "-[save]-delete country override");
						session.delete(countryOverride);
					} else if (records.isEmpty()) {
						log.debug(this.getClass().getName() + "-[save]-save country override");
						session.save(countryOverride);
					} else {
						log.debug(this.getClass().getName() + "-[save]-update country override");
						session.update(countryOverride);
					}
					
					tx.commit();
				} catch (Exception e) {
					if (tx != null) tx.rollback();
					throw e;
				}
			}
			
			log.debug(this.getClass().getName() + "-[save]-Exit");
		} catch (Exception exception) {
			log.error("exception in CountryOverrideDAOHibernate.save", exception);
			throw SwtErrorHandler.getInstance().handleException(exception,
					"save", this.getClass());
		}
	}
}
