/**
 * @(#)ForecastMonitorTemplateDAOHibernate.java 1.0 24/05/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.sql.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;


import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.ForecastMonitorTemplateDAO;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.maintenance.model.ForecastMonitorTemplateCol;
import org.swallow.maintenance.model.ForecastMonitorTemplateColSrc;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.util.*;
import org.swallow.work.model.UserTemplate;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.persistence.EntityManager;

/**
 * ForecastMonitorTemplateDAOHibernate.java
 * 
 * ForecastMonitorTemplateDAOHibernate class is used for ForecastMonitorTemplate
 * screen that will display ForecastMonitor Templates<br>
 * 
 * <AUTHOR> A
 * @date May 24, 2011
 */
@Repository ("forecastMonitorTemplateDAO")
public class ForecastMonitorTemplateDAOHibernate extends CustomHibernateDaoSupport
		implements ForecastMonitorTemplateDAO {
	public ForecastMonitorTemplateDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(this.getClass());

	/**
	 * 
	 * Method to get public and user's private Forecast monitor Templates
	 * 
	 * @param hostId
	 * @param currentUserId
	 * @return List of templates
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplate> getForecastMonitorTemplate(
			String hostId, String currentUserId) throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplate> fcastTemplateList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getForecastMonitorTemplate] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "from ForecastMonitorTemplate fcast where "
					+ "(fcast.userId = :currentUserId or fcast.publicTemplate = 'Y') "
					+ "and fcast.id.hostId = :hostId ORDER BY fcast.id.templateId";

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplate> query = session.createQuery(hql, ForecastMonitorTemplate.class);

			// Set parameters
			query.setParameter("currentUserId", currentUserId);
			query.setParameter("hostId", hostId);

			// Execute the query and return results
			fcastTemplateList = query.getResultList();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getForecastMonitorTemplate -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplate", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplate] -Exit");
		}

		return fcastTemplateList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @return List of template columns
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateCol()
			throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplateCol> fcastTemplateColList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getForecastMonitorTemplateCol] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "from ForecastMonitorTemplateCol fcast where "
					+ "fcast.userId = :userId "
					+ "and fcast.id.templateId = :templateId "
					+ "and fcast.columnType = :columnType "
					+ "ORDER BY fcast.ordinalPos";

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplateCol> query = session.createQuery(hql, ForecastMonitorTemplateCol.class);

			// Set parameters
			query.setParameter("userId", SwtConstants.DEFAULT);
			query.setParameter("templateId", SwtConstants.DEFAULT);
			query.setParameter("columnType", SwtConstants.FIXED);

			// Execute the query and return results
			fcastTemplateColList = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getForecastMonitorTemplateCol -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplateCol] -Exit");
		}

		return fcastTemplateColList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateCol
	 * 
	 * @return List of template columns
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplateCol> fcastTemplateColList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getForecastMonitorTemplateCol] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "from ForecastMonitorTemplateCol forecast where "
					+ "forecast.id.hostId = :hostId "
					+ "and forecast.userId = :userId "
					+ "and forecast.id.templateId = :templateId "
					+ "ORDER BY forecast.ordinalPos";

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplateCol> query = session.createQuery(hql, ForecastMonitorTemplateCol.class);

			// Set parameters
			query.setParameter("hostId", forecastMonitorTemplateCol.getId().getHostId());
			query.setParameter("userId", forecastMonitorTemplateCol.getUserId());
			query.setParameter("templateId", forecastMonitorTemplateCol.getId().getTemplateId());

			// Execute the query and return results
			fcastTemplateColList = query.getResultList();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getForecastMonitorTemplateCol -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplateCol] -Exit");
		}

		return fcastTemplateColList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns sources
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * 
	 * @return List of template column sources
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateColSrc> getForecastMonitorTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplateColSrc> fcastTemplateColList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getForecastMonitorTemplateColSrc] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "select forecast from ForecastMonitorTemplateColSrc forecast where "
					+ "forecast.id.hostId = :hostId "
					+ "and forecast.userId = :userId "
					+ "and forecast.id.templateId = :templateId";

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplateColSrc> query = session.createQuery(hql, ForecastMonitorTemplateColSrc.class);

			// Set parameters
			query.setParameter("hostId", forecastMonitorTemplateColSrc.getId().getHostId());
			query.setParameter("userId", forecastMonitorTemplateColSrc.getUserId());
			query.setParameter("templateId", forecastMonitorTemplateColSrc.getId().getTemplateId());

			// Execute the query and return results
			fcastTemplateColList = query.getResultList();
		} catch (Exception exp) {
			log
					.error("An exception occured in "
							+ this.getClass().getName()
							+ "- getForecastMonitorTemplateColSrc -"
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateColSrc", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplateColSrc] -Exit");
		}

		return fcastTemplateColList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns source for given column
	 * id
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @param columnType
	 * @return List of template column sources
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateColSrc> getForecastMonitorTemplateColSrcForColumnId(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc,
			String columnType) throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplateColSrc> fcastTemplateColList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " - [getForecastMonitorTemplateColSrcForColumnId] -Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

					// Base query
					StringBuilder queryBuilder = new StringBuilder("select forecast from ForecastMonitorTemplateColSrc forecast where "
							+ "forecast.id.hostId = :hostId and "
							+ "forecast.userId = :userId and "
							+ "forecast.id.templateId = :templateId");

					// Condition to check if column type is not normal
					if (!columnType.equals(SwtConstants.NORMAL)) {
						queryBuilder.append(" and (forecast.id.columnId = :columnId or forecast.id.sourceId = :columnId)");
					} else {
						// Condition to get column id is not null
						if (!SwtUtil.isEmptyOrNull(forecastMonitorTemplateColSrc.getId().getColumnId())) {
							queryBuilder.append(" and ((forecast.id.columnId = :totalId and forecast.id.sourceId = :columnId) ");
							queryBuilder.append(" or forecast.id.columnId = :columnId)");
						}
					}
			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplateColSrc> query = session.createQuery(queryBuilder.toString(), ForecastMonitorTemplateColSrc.class);

			// Set parameters
			query.setParameter("hostId", forecastMonitorTemplateColSrc.getId().getHostId());
			query.setParameter("userId", forecastMonitorTemplateColSrc.getUserId());
			query.setParameter("templateId", forecastMonitorTemplateColSrc.getId().getTemplateId());

			if (!columnType.equals(SwtConstants.NORMAL)) {
				query.setParameter("columnId", forecastMonitorTemplateColSrc.getId().getColumnId());
			} else {
				query.setParameter("totalId", SwtConstants.TOTAL.toUpperCase());
				query.setParameter("columnId", forecastMonitorTemplateColSrc.getId().getColumnId());
			}

			// Execute the query and return results
			fcastTemplateColList = query.getResultList();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getForecastMonitorTemplateColSrcForColumnId -"
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateColSrcForColumnId",
					this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplateColSrcForColumnId] -Exit");
		}

		return fcastTemplateColList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns sources for change screen
	 * for subtotal
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of templates
	 * @throws SwtException 
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateCol> getChangeForecastMonitorTemplateColSrcForSubTotal(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {

		/* Method's local variable declaration */
		// List to hold collection of column list
		List<ForecastMonitorTemplateCol> fcastTemplateColList = null;
		// session instance
		Session session= null;
		// Connection instance
		Connection conn = null;
		// Callable statement instance
		CallableStatement cstmt = null;
		// ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		// Result set instance
		ResultSet rs = null;
		//StringBuffer instance
		String queryStringBuffer = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " - [getChangeForecastMonitorTemplateColSrcForSubTotal] -Entry");

			// Obtain session from hibernate
			session = getHibernateTemplate().getSessionFactory().openSession();
			// OPen data base connection
			conn = SwtUtil.connection(session);
			// Query to get subtotal column source with corresponding multiplier
			queryStringBuffer = "select forecastcol.HOST_ID,forecastcol.USER_ID,forecastcol.TEMPLATE_ID,forecastcol.COLUMN_ID,"
							+ " forecastcol.ORDINAL_POS ,forecastcol.COLUMN_DISPLAY_NAME,forecastcol.COLUMN_DESCRIPTION,"
							+ "forecastcol.COLUMN_TYPE,forecastcol.USER_EDITABLE, forecast.MULTIPLIER "
							+ "from p_fcast_Template_Col forecastcol, p_fcast_Template_Col_Src forecast "
							+ "where forecastcol.COLUMN_ID = forecast.SOURCE_ID and forecastcol.template_Id = forecast.template_Id "
							+ "and forecast.host_Id =  ? and forecast.user_Id=? "
							+ "and forecast.template_Id = ?  and (forecast.COLUMN_ID = ? or forecast.SOURCE_ID = ?)";
			// call procedure
			cstmt = conn.prepareCall(queryStringBuffer);
			cstmt.setString(1, forecastMonitorTemplateColSrc.getId().getHostId());
			cstmt.setString(2, forecastMonitorTemplateColSrc.getUserId());
			cstmt.setString(3, forecastMonitorTemplateColSrc.getId().getTemplateId());
			cstmt.setString(4, forecastMonitorTemplateColSrc.getId().getColumnId());
			cstmt.setString(5, forecastMonitorTemplateColSrc.getId().getColumnId());
			// execute statement
			// get result set
			rs = (ResultSet) cstmt.executeQuery();
			// result is not null
			if (rs != null) {
				fcastTemplateColList = new ArrayList<ForecastMonitorTemplateCol>();
				while (rs.next()) {
					forecastMonitorTemplateCol = new ForecastMonitorTemplateCol();

					// set hostId
					forecastMonitorTemplateCol.getId().setHostId(
							rs.getString(1));
					// set userId
					forecastMonitorTemplateCol.setUserId(rs.getString(2));

					// set template Id
					forecastMonitorTemplateCol.getId().setTemplateId(
							rs.getString(3));
					// set column id
					forecastMonitorTemplateCol.getId().setColumnId(
							rs.getString(4));
					// set ordinal position
					forecastMonitorTemplateCol.setOrdinalPos(rs.getInt(5));
					// set
					forecastMonitorTemplateCol.setColumnDisplayName(rs
							.getString(6));

					// set
					forecastMonitorTemplateCol.setColumnDescription(rs
							.getString(7));
					// set source ID
					forecastMonitorTemplateCol.setColumnType(rs.getString(8));

					// set source ID
					forecastMonitorTemplateCol.setUserEditable(rs.getString(9));

					// set multiplier
					forecastMonitorTemplateCol.setMultiplier(rs
							.getBigDecimal(10));

					// Set bean in collection
					fcastTemplateColList.add(forecastMonitorTemplateCol);
				}
			}

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getChangeForecastMonitorTemplateColSrcForSubTotal -"
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getChangeForecastMonitorTemplateColSrcForSubTotal",
					this.getClass());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getChangeForecastMonitorTemplateColSrcForSubTotal", this.getClass());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getChangeForecastMonitorTemplateColSrcForSubTotal", this.getClass());

			if(thrownException!=null)
				throw thrownException;
			
			forecastMonitorTemplateCol = null;
			queryStringBuffer = null;
			log
					.debug(this.getClass().getName()
							+ " - [getChangeForecastMonitorTemplateColSrcForSubTotal] -Exit");
		}

		return fcastTemplateColList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns sources for change screen
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of templates
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateColSrc> getChangeForecastMonitorTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		/* Method's local variable declaration */
		// list to hold source collection
		List<ForecastMonitorTemplateColSrc> fcastTemplateColList = null;
		// session instance
		Session session = null;
		//Connection instance
		Connection conn = null;
		// callable statement instance
		CallableStatement cstmt = null;
		// result set instance
		ResultSet rs = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getChangeForecastMonitorTemplateColSrc] -Entry");

			// Obtain session from hibernate
			session = getHibernateTemplate().getSessionFactory().openSession();
			// OPen data base connection
			conn = SwtUtil.connection(session);

			// call procedure
			cstmt = conn
					.prepareCall("{call PKG_FORECAST_MONITOR.SP_GETTEMPLATECOLSRCNAME (?,?,?,?)}");
			// set user id for parameter
			cstmt.setString(1, forecastMonitorTemplateColSrc.getUserId());
			// set template id for parameter
			cstmt.setString(2, forecastMonitorTemplateColSrc.getId()
					.getTemplateId());
			// set column id for parameter
			cstmt.setString(3, forecastMonitorTemplateColSrc.getId()
					.getColumnId());
			// set cursor
			cstmt.registerOutParameter(4, oracle.jdbc.OracleTypes.CURSOR);
			// execute statement
			cstmt.execute();
			// get result set
			rs = (ResultSet) cstmt.getObject(4);

			// result is not null
			if (rs != null) {
				fcastTemplateColList = new ArrayList<ForecastMonitorTemplateColSrc>();
				while (rs.next()) {
					forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
					// set name
					forecastMonitorTemplateColSrc.setName(rs.getString(1));
					// set hostId
					forecastMonitorTemplateColSrc.getId().setHostId(
							rs.getString(2));
					// set userId
					forecastMonitorTemplateColSrc.setUserId(rs.getString(3));
					// set template Id
					forecastMonitorTemplateColSrc.getId().setTemplateId(
							rs.getString(4));
					// set column id
					forecastMonitorTemplateColSrc.getId().setColumnId(
							rs.getString(5));
					// set sourceType
					forecastMonitorTemplateColSrc.getId().setSourceType(
							rs.getString(6));
					// set entityId
					forecastMonitorTemplateColSrc.setEntityId(rs.getString(7));
					// set source ID
					forecastMonitorTemplateColSrc.getId().setSourceId(
							rs.getString(8));
					// set multiplier
					forecastMonitorTemplateColSrc.setMultiplier(rs
							.getBigDecimal(9));

					// Set bean in collection
					fcastTemplateColList.add(forecastMonitorTemplateColSrc);
				}
			}

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getChangeForecastMonitorTemplateColSrc -"
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getChangeForecastMonitorTemplateColSrc", this.getClass());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
				
			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getChangeForecastMonitorTemplateColSrc", this.getClass());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getChangeForecastMonitorTemplateColSrc",	this.getClass());
			
			if(thrownException!=null)
			throw thrownException;
			

			log.debug(this.getClass().getName()
					+ " - [getChangeForecastMonitorTemplateColSrc] -Exit");
		}

		return fcastTemplateColList;
	}

	/**
	 * Method to delete Forecast monitor Template
	 * 
	 * @param forecastMonitorTemplate
	 * @return
	 */
	public void deleteForecastMonitorTemplate(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {

			log.debug(this.getClass().getName()
					+ " - [deleteForecastMonitorTemplate] -Entry");
			// delete template
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(forecastMonitorTemplate);
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- deleteForecastMonitorTemplate -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteForecastMonitorTemplate", this.getClass());
		} finally {
                        JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [deleteForecastMonitorTemplate] -Exit");
		}
	}

	/**
	 * Method to delete Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateColList
	 * @return
	 */
	public void deleteAllForecastMonitorTemplateCol(
			List<ForecastMonitorTemplateCol> forecastMonitorTemplateColList)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		try {

			log.debug(this.getClass().getName()
					+ " - [deleteAllForecastMonitorTemplateCol] -Entry");
			// delete column list
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			Iterator it = forecastMonitorTemplateColList.iterator();
			while (it.hasNext()) {
				forecastMonitorTemplateCol =(ForecastMonitorTemplateCol) it.next();
				session.delete(forecastMonitorTemplateCol);
			}
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- deleteAllForecastMonitorTemplateCol -"
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAllForecastMonitorTemplateCol", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [deleteAllForecastMonitorTemplateCol] -Exit");
		}
	}

	/**
	 * Method to delete Forecast monitor Template Column Sources
	 * 
	 * @param forecastMonitorTemplateColSrcList
	 * @return
	 */
	public void deleteAllForecastMonitorTemplateColSrc(
			List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcList)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc;
		try {

			log.debug(this.getClass().getName()
					+ " - [deleteAllForecastMonitorTemplateColSrc] -Entry");
			//delete source list

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			Iterator it = forecastMonitorTemplateColSrcList.iterator();
			while (it.hasNext()) {
				forecastMonitorTemplateColSrc =(ForecastMonitorTemplateColSrc) it.next();
				session.delete(forecastMonitorTemplateColSrc);
			}
			tx.commit();

			

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- deleteAllForecastMonitorTemplateColSrc -"
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAllForecastMonitorTemplateColSrc", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [deleteAllForecastMonitorTemplateColSrc] -Exit");
		}
	}

	/**
	 * 
	 * Method to get Book Code details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return List of book codes
	 */
	@SuppressWarnings("unchecked")
	public List<BookCode> getBookCollection(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException {
		List<BookCode> bookCodeList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getBookCollection] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Base query
			StringBuilder queryBuilder = new StringBuilder("from BookCode p where p.id.hostId = :hostId and p.id.entityId = :entityId");

			// Condition to check fieldId not null
			if (!SwtUtil.isEmptyOrNull(fieldId)) {
				queryBuilder.append(" and p.id.bookCode like :fieldId");
			}

			// Condition to check fieldName not null
			if (!SwtUtil.isEmptyOrNull(fieldName)) {
				queryBuilder.append(" and p.bookName like :fieldName");
			}

			// Create a TypedQuery
			TypedQuery<BookCode> query = session.createQuery(queryBuilder.toString(), BookCode.class);

			// Set parameters
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			if (!SwtUtil.isEmptyOrNull(fieldId)) {
				query.setParameter("fieldId", fieldId + "%");
			}

			if (!SwtUtil.isEmptyOrNull(fieldName)) {
				query.setParameter("fieldName", fieldName + "%");
			}

			// Execute the query and return results
			bookCodeList = query.getResultList();
				

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getBookCollection -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBookCollection", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getBookCollection] -Exit");
		}
		return bookCodeList;
	}

	/**
	 * This is used to get the group details from P_GROUP table.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return list of groups
	 */
	@SuppressWarnings("unchecked")
	public List<Group> getGroupDetails(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException {
		// local variables
		List<Group> groupCollection = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getGroupDetails] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Base query
			StringBuilder queryBuilder = new StringBuilder("from Group c where c.id.hostId = :hostId and c.id.entityId = :entityId");

			// Condition to check fieldId not null
			if (!SwtUtil.isEmptyOrNull(fieldId)) {
				queryBuilder.append(" and c.id.groupId like :fieldId");
			}

			// Condition to check fieldName not null
			if (!SwtUtil.isEmptyOrNull(fieldName)) {
				queryBuilder.append(" and c.groupName like :fieldName");
			}

			// Create a TypedQuery
			TypedQuery<Group> query = session.createQuery(queryBuilder.toString(), Group.class);

			// Set parameters
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			if (!SwtUtil.isEmptyOrNull(fieldId)) {
				query.setParameter("fieldId", fieldId + "%");
			}

			if (!SwtUtil.isEmptyOrNull(fieldName)) {
				query.setParameter("fieldName", fieldName + "%");
			}

			// Execute the query and return results
			groupCollection = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getGroupDetails -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getGroupDetails", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "- [getGroupDetails] - Exit");
		}

		return groupCollection;
	}

	/**
	 * This is used to get meta group details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return list of MetaGroup
	 */
	@SuppressWarnings("unchecked")
	public List<MetaGroup> getMetaGroupDetails(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException {

		/* Method's local variable declaration */
		List<MetaGroup> metaGroupcoll = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getMetaGroupDetails] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Base query
			StringBuilder queryBuilder = new StringBuilder("from MetaGroup c where c.id.hostId = :hostId and c.id.entityId = :entityId");

			// Condition to check fieldId not null
			if (!SwtUtil.isEmptyOrNull(fieldId)) {
				queryBuilder.append(" and c.id.mgroupId like :fieldId");
			}

			// Condition to check fieldName not null
			if (!SwtUtil.isEmptyOrNull(fieldName)) {
				queryBuilder.append(" and c.mgroupName like :fieldName");
			}

			// Create a TypedQuery
			TypedQuery<MetaGroup> query = session.createQuery(queryBuilder.toString(), MetaGroup.class);

			// Set parameters
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			if (!SwtUtil.isEmptyOrNull(fieldId)) {
				query.setParameter("fieldId", fieldId + "%");
			}

			if (!SwtUtil.isEmptyOrNull(fieldName)) {
				query.setParameter("fieldName", fieldName + "%");
			}

			// Execute the query and return results
			metaGroupcoll = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getMetaGroupDetails -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMetaGroupDetails", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "- [getMetaGroupDetails] - Exit");
		}

		return metaGroupcoll;

	}

	/**
	 * 
	 * Method to lock template for user
	 * 
	 * @param forecastMonitorTemplate
	 * @return
	 */
	public void lockTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "- [lockTemplate] - Entry");
			// update template
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(forecastMonitorTemplate);
			tx.commit();
			
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- lockTemplate -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"lockTemplate", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName() + "- [lockTemplate] - Exit");
		}
	}

	/**
	 * 
	 * Method to check whether the template is locked
	 * 
	 * @param forecastMonitorTemplate
	 * @return List of template bean
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplate> checkTemplateLocked(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		// Method's loacal variable declaration
		// List to get template values
		List<ForecastMonitorTemplate> foretemplateList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [checkTemplateLocked] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "select fore from ForecastMonitorTemplate fore where "
					+ "fore.id.hostId = :hostId "
					+ "and fore.id.templateId = :templateId";

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplate> query = session.createQuery(hql, ForecastMonitorTemplate.class);

			// Set parameters
			query.setParameter("hostId", forecastMonitorTemplate.getId().getHostId());
			query.setParameter("templateId", forecastMonitorTemplate.getId().getTemplateId());

			// Execute the query and return results
			foretemplateList = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- checkTemplateLocked -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkTemplateLocked", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "- [checkTemplateLocked] - Exit");
		}
		// return foretemplateList
		return foretemplateList;
	}

	/**
	 * 
	 * Method to get All locked templates for selected user id
	 * 
	 * @param lockedUserId
	 * @return List
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplate> getAllLock(String lockedUserId)
			throws SwtException {
		// Method's loacal variable declaration
		// List to get template values
		List<ForecastMonitorTemplate> foretemplateList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getAllLock] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "select fore from ForecastMonitorTemplate fore where fore.lockedBy = :lockedUserId";

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplate> query = session.createQuery(hql, ForecastMonitorTemplate.class);

			// Set parameters
			query.setParameter("lockedUserId", lockedUserId);

			// Execute the query and return results
			foretemplateList = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getAllLock -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAllLock", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "- [getAllLock] - Exit");
		}
		// return foretemplateList
		return foretemplateList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Templates for copy from
	 * 
	 * 
	 * @return List of templates
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplate> getTemplatesCopyFrom(String hostId,
			String currentUserId) throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplate> fcastTemplateList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getTemplatesCopyFrom] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "from ForecastMonitorTemplate fcast where "
					+ "(fcast.userId = :currentUserId "
					+ "or fcast.publicTemplate = 'Y') and fcast.id.hostId = :hostId "
					+ "ORDER BY fcast.id.templateId";

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplate> query = session.createQuery(hql, ForecastMonitorTemplate.class);

			// Set parameters
			query.setParameter("currentUserId", currentUserId);
			query.setParameter("hostId", hostId);

			// Execute the query and return results
			fcastTemplateList = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getTemplatesCopyFrom -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getTemplatesCopyFrom", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getTemplatesCopyFrom] -Exit");
		}

		return fcastTemplateList;
	}

	/**
	 * 
	 * Method to save forecast template
	 * 
	 * @param forecastMonitorTemplate
	 * @throws SwtException
	 */
	public void saveTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [saveTemplate] -Entry");
			// save template
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(forecastMonitorTemplate);
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- saveTemplate -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveTemplate", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName() + " - [saveTemplate] -Exit");
		}
	}

	/**
	 * 
	 * Method to save forecast template col
	 * 
	 * @param forecastMonitorTemplateCol
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		Connection conn = null;
		SwtInterceptor interceptor = null;
		PreparedStatement stmt = null;
		ResultSet resultSet = null;
		String sequenceValue = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " - [saveTemplateCol] -Entry");
			// save columns
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();

			// Generate the sequence id from the package given for column id field
			String _sequenceQuery = "SELECT LPAD(pk_utility.fn_get_sequence_number('FCASTTEMPLATE'),12,0) FROM dual";

			conn = SwtUtil.connection(session);

			stmt = conn.prepareStatement(_sequenceQuery);
			stmt.execute();
			/* Fetching the Result */
			resultSet = (ResultSet) stmt.getResultSet();
			// Condition to result set has value
			if (resultSet.next())
				// get sequlence value
				sequenceValue = resultSet.getString(1);
			if (sequenceValue == null)
				throw new HibernateException(
						"Could not fecth the sequence number");
			// Condition to check column name is total and is fixed
			if (forecastMonitorTemplateCol.getColumnDisplayName()
					.equals("Total")
					&& forecastMonitorTemplateCol.getColumnType()
					.equals(SwtConstants.FIXED))
				forecastMonitorTemplateCol
						.getId().setColumnId("TOTAL");
				// Condition to check column name is Bucket and is fixed
			else if (forecastMonitorTemplateCol.getColumnDisplayName()
					.equals("Bucket")
					&& forecastMonitorTemplateCol.getColumnType()
					.equals(SwtConstants.FIXED))
				forecastMonitorTemplateCol
						.getId().setColumnId("BUCKET");
				// Condition to check column name is Date and is fixed
			else if (forecastMonitorTemplateCol.getColumnDisplayName()
					.equals("Date")
					&& forecastMonitorTemplateCol.getColumnType()
					.equals(SwtConstants.FIXED))
				forecastMonitorTemplateCol
						.getId().setColumnId("VALDATE");
				// Condition to check column name is Assumption and is fixed
			else if (forecastMonitorTemplateCol.getColumnDisplayName()
					.equals("Assumption")
					&& forecastMonitorTemplateCol.getColumnType()
					.equals(SwtConstants.FIXED))
				forecastMonitorTemplateCol
						.getId().setColumnId("ASSUMPTION");
				// Condition to check column name is Scenario and is fixed
			else if (forecastMonitorTemplateCol.getColumnDisplayName()
					.equals("Scenario")
					&& forecastMonitorTemplateCol.getColumnType()
					.equals(SwtConstants.FIXED))
				forecastMonitorTemplateCol
						.getId().setColumnId("SCENARIO");
				// Condition to check column name is Grand Total and is fixed
			else if (forecastMonitorTemplateCol.getColumnDisplayName()
					.equals("Grand Total")
					&& forecastMonitorTemplateCol.getColumnType()
					.equals(SwtConstants.FIXED))
				forecastMonitorTemplateCol
						.getId().setColumnId("GTOTAL");
			else
				//Set columnid for other user defined columns
				forecastMonitorTemplateCol
						.getId().setColumnId(sequenceValue);

		//	forecastMonitorTemplateCol.setId(forecastMonitorTemplateColIdObj);
		tx = session.beginTransaction();
			session.save(forecastMonitorTemplateCol);
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- saveTemplateCol -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveTemplateCol", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName() + " - [saveTemplateCol] -Exit");
		}
	}

	/**
	 * 
	 * Method to save forecast template col
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @throws SwtException
	 */
	public void saveTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [saveTemplateColSrc] -Entry");

			// Condition to check selected type is book
			if (forecastMonitorTemplateColSrc.getId().getSourceType().equals(
					SwtConstants.BOOK_LABEL))
				forecastMonitorTemplateColSrc.getId().setSourceType(
						SwtConstants.BOOK);
			// Condition to check selected type is group
			else if (forecastMonitorTemplateColSrc.getId().getSourceType()
					.equals(SwtConstants.GROUP_LABEL))
				forecastMonitorTemplateColSrc.getId().setSourceType(
						SwtConstants.GROUP);

			// Condition to check selected type is meta group
			else if (forecastMonitorTemplateColSrc.getId().getSourceType()
					.equals(SwtConstants.META_GROUP_LABEL))
				forecastMonitorTemplateColSrc.getId().setSourceType(
						SwtConstants.META_GROUP);
			// Condition to check selected type is entity
			else if (forecastMonitorTemplateColSrc.getId().getSourceType()
					.equals(SwtConstants.ENTITY_LABEL))
				forecastMonitorTemplateColSrc.getId().setSourceType(
						SwtConstants.ENTITY);
			//save column sources
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(forecastMonitorTemplateColSrc);
			tx.commit();
			

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- saveTemplateColSrc -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveTemplateColSrc", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [saveTemplateColSrc] -Exit");
		}
	}

	/**
	 * 
	 * Method to update forecast template
	 * 
	 * @param forecastMonitorTemplate
	 * @throws SwtException
	 */
	public void updateTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [updateTemplate] -Entry");
			// update template
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(forecastMonitorTemplate);
			tx.commit();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- updateTemplate -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateTemplate", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName() + " - [updateTemplate] -Exit");
		}
	}

	/**
	 * 
	 * Method to update forecast template col
	 * 
	 * @param forecastMonitorTemplateCol
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void updateTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		// Method's local variable declaration
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateTemplateCol] -Entry");
			// update template columns
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(forecastMonitorTemplateCol);
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- updateTemplateCol -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateTemplateCol", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [updateTemplateCol] -Exit");
		}
	}

	/**
	 * 
	 * Method to update forecast template column sources
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @throws SwtException
	 * @throws HibernateException 
	 */
	public void updateTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateTemplateColSrc] -Entry");
			// update template columns sources
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			ForecastMonitorTemplateColSrc forecastColUpdate = (ForecastMonitorTemplateColSrc) session.get(ForecastMonitorTemplateColSrc.class, forecastMonitorTemplateColSrc.getId());
			if(forecastColUpdate != null) {
				session.update(forecastColUpdate);
			} else
				session.save(forecastColUpdate);
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- updateTemplateColSrc -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateTemplateColSrc", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [updateTemplateColSrc] -Exit");
		}
	}

	/**
	 * 
	 * Method to delete forecast template col
	 * 
	 * @param forecastMonitorTemplateCol
	 * @throws SwtException
	 */
	public void deleteTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		// Method's local variable declaration
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteTemplateCol] -Entry");
			// delete template column
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(forecastMonitorTemplateCol);
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- deleteTemplateCol -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteTemplateCol", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [deleteTemplateCol] -Exit");
		}
	}

	/**
	 * 
	 * Method to delete forecast template column source
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @throws SwtException
	 */
	public void deleteTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteTemplateColSrc] -Entry");
			// delete template column source
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			ForecastMonitorTemplateColSrc colSrcToDelete = (ForecastMonitorTemplateColSrc) session.get(ForecastMonitorTemplateColSrc.class, forecastMonitorTemplateColSrc.getId());
			if (colSrcToDelete != null)
				session.delete(colSrcToDelete);

			tx.commit();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- deleteTemplateColSrc -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteTemplateColSrc", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [deleteTemplateColSrc] -Exit");
		}
	}

	/**
	 * 
	 * Method to get User Templates
	 * 
	 * @return List of user templates
	 */
	@SuppressWarnings("unchecked")
	public List<UserTemplate> getUserTemplates(String templateId, String hostId)
			throws SwtException {
		/* Method's local variable declaration */
		List<UserTemplate> userTemplateList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getUser Templates] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "select forecast from UserTemplate forecast where "
					+ "forecast.id.hostId = :hostId and "
					+ "forecast.templateId = :templateId";

			// Create a TypedQuery
			TypedQuery<UserTemplate> query = session.createQuery(hql, UserTemplate.class);

			// Set parameters
			query.setParameter("hostId", hostId);
			query.setParameter("templateId", templateId);

			// Execute the query and return results
			userTemplateList = query.getResultList();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getUserTemplates -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getUserTemplates", this.getClass());
		} finally {
			log
					.debug(this.getClass().getName()
							+ " - [getUserTemplates] -Exit");
		}
		return userTemplateList;
	}

	/**
	 * Method to delete User Template list
	 * 
	 * @param userTemplateList
	 * @return
	 */
	public void deleteAllUserTemplate(List<UserTemplate> userTemplateList)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		UserTemplate userTemplate = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteAllUserTemplate] -Entry");

			// delete user template list
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			Iterator it = userTemplateList.iterator();
			while (it.hasNext()) {
				userTemplate = (UserTemplate) it.next();
				session.delete(userTemplate);
			}
			tx.commit();

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- deleteAllUserTemplate -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAllUserTemplate", this.getClass());
		} finally {
			JDBCCloser.close(session);
			log.debug(this.getClass().getName()
					+ " - [deleteAllUserTemplate] -Exit");
		}

	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns to get column id
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return List of template columns
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateColForColumnId(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplateCol> fcastTemplateColList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getForecastMonitorTemplateColForColumnId] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Base query
			StringBuilder queryBuilder = new StringBuilder("select forecast from ForecastMonitorTemplateCol forecast where "
					+ "forecast.id.hostId = :hostId and "
					+ "forecast.userId = :userId and "
					+ "forecast.id.templateId = :templateId");

			// Condition to check columnDisplayName not null
			if (!SwtUtil.isEmptyOrNull(forecastMonitorTemplateCol.getColumnDisplayName())) {
				queryBuilder.append(" and forecast.columnDisplayName = :columnDisplayName");
			}

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplateCol> query = session.createQuery(queryBuilder.toString(), ForecastMonitorTemplateCol.class);

			// Set parameters
			query.setParameter("hostId", forecastMonitorTemplateCol.getId().getHostId());
			query.setParameter("userId", forecastMonitorTemplateCol.getUserId());
			query.setParameter("templateId", forecastMonitorTemplateCol.getId().getTemplateId());

			if (!SwtUtil.isEmptyOrNull(forecastMonitorTemplateCol.getColumnDisplayName())) {
				query.setParameter("columnDisplayName", forecastMonitorTemplateCol.getColumnDisplayName());
			}

			// Execute the query and return results
			fcastTemplateColList = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getForecastMonitorTemplateColForColumnId -"
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(exp,
							"getForecastMonitorTemplateColForColumnId",
							this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplateColForColumnId] -Exit");
		}
        
		return fcastTemplateColList;
	}

	/**
	 * 
	 * Method to check Forecast monitor Template Columns sources exists
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of templates
	 */
	@SuppressWarnings("unchecked")
	public List<ForecastMonitorTemplateColSrc> checkForecastExist(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		/* Method's local variable declaration */
		List<ForecastMonitorTemplateColSrc> fcastTemplateColList = null;
		Interceptor interceptor = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [checkForecastExist] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();

			// Base query
			StringBuilder queryBuilder = new StringBuilder("select forecast from ForecastMonitorTemplateColSrc forecast where "
					+ "forecast.id.hostId = :hostId and "
					+ "forecast.userId = :userId and "
					+ "forecast.id.templateId = :templateId and "
					+ "forecast.id.sourceId = :sourceId and "
					+ "forecast.id.sourceType = :sourceType");

			// Condition to check columnId not null
			if (!SwtUtil.isEmptyOrNull(forecastMonitorTemplateColSrc.getId().getColumnId())) {
				queryBuilder.append(" and forecast.id.columnId = :columnId");
			}

			// Create a TypedQuery
			TypedQuery<ForecastMonitorTemplateColSrc> query = session.createQuery(queryBuilder.toString(), ForecastMonitorTemplateColSrc.class);

			// Set parameters
			query.setParameter("hostId", forecastMonitorTemplateColSrc.getId().getHostId());
			query.setParameter("userId", forecastMonitorTemplateColSrc.getUserId());
			query.setParameter("templateId", forecastMonitorTemplateColSrc.getId().getTemplateId());
			query.setParameter("sourceId", forecastMonitorTemplateColSrc.getId().getSourceId());
			query.setParameter("sourceType", forecastMonitorTemplateColSrc.getId().getSourceType());

			if (!SwtUtil.isEmptyOrNull(forecastMonitorTemplateColSrc.getId().getColumnId())) {
				query.setParameter("columnId", forecastMonitorTemplateColSrc.getId().getColumnId());
			}

			// Execute the query and return results
			fcastTemplateColList = query.getResultList();
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- checkForecastExist -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkForecastExist", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [checkForecastExist] -Exit");
		}

		return fcastTemplateColList;
	}
}
