/*
 * @(#)ReasonMaintenance.java 1.0 25/08/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class ReasonMaintenance extends BaseObject implements org.swallow.model.AuditComponent{
	
	/*variable declaration*/
	private String description;
	public static Hashtable  logTable = new Hashtable();
	private Id id = new Id();
	/*inner class defines primary key fields */
	public static class Id extends BaseObject{
		
		/* variable declaration */
		private String hostId;
		private String entityId;
		private String reasonCode;
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the entityId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the entityId.
		 */
		public String getReasonCode() {
			return reasonCode;
		}
		/**
		 * @param reasonCode The reasonCode to set.
		 */
		public void setReasonCode(String reasonCode) {
			this.reasonCode = reasonCode;
		}

	}
	
	/**
	 * @return Returns the entityId.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getDescription() {
		return description;
	}
	/**
	 * @param description The description to set.
	 */
	public void setDescription(String description) {
		this.description = description;
	}
}
