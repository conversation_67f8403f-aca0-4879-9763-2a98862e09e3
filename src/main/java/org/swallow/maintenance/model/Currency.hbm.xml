<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.Currency" table="S_CURRENCY">
  <composite-id class="org.swallow.maintenance.model.Currency$Id" name="id" unsaved-value="any">
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="entityId" access="field" column="ENTITY_ID"/>
   <key-property name="currencyCode" access="field" column="CURRENCY_CODE"/>
  </composite-id>
  <!-- Start code commented by betcy for mantis 453:Column unused and it dropped -->
  <!--<property name="exchangeRate" column="EXCHANGE_RATE"/>-->
  <!-- End code commented by betcy for mantis 453:Column unused and it dropped -->
  <property name="interestBasis" column="INTEREST_BASIS"/>
  <property name="tolerance" column="TOLERANCE"/>
  <property name="cutOffTime" column="CUT_OFF"/>
  <property name="preFlag" column="PREDICT_ENABLED"/>
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
  <property name="currencyGroupId" column="CURRENCY_GROUP_ID"/>
  <property name="priorityOrder" column="PRIORITY_ORDER"/>
  
  <!-- START: Code changed as par SRS - Currency Monitor for ING, 05-JUL-2007 -->
  <property name="multiplier" column="MONITOR_DISPLAY_MULTIPLIER" not-null="false"/>
  <!-- END: Code changed as par SRS - Currency Monitor for ING, 05-JUL-2007 -->
  
  <!-- START: Code changed as par SRS - Currency Threshold for ING, 05-JUL-2007 -->
  <property name="threshold" column="THRESHOLD" not-null="false"/>
  <property name="thresholdProduct" column="THRESHOLD_PRODUCT" not-null="false"/>
<many-to-one name="currencyMaster"
    class="org.swallow.maintenance.model.CurrencyMaster" lazy="false"
    column="CURRENCY_CODE" not-null="true" outer-join="true"
    update="false" insert="false"  foreign-key="FK_S_CURRENCY_S_CURRENCY_MST"/>
 </class>
</hibernate-mapping>
