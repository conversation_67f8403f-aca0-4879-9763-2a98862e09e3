/*
 * @(#)CurrencyAlias .java  01/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * CurrencyAlias.java
 * 
 * This java bean has getters and setters for CurrencyAlias details
 * 
 */
public class CurrencyAlias extends BaseObject implements
		org.swallow.model.AuditComponent {

	/**
	 * Default version
	 */
	private static final long serialVersionUID = 1L;
	// Currency Code
	private String currencyCode;
	// Update Date
	private Date updateDate;
	// Update User
	private String updateUser;
	// ID class
	private Id id = new Id();
	// Currency Name
	private String currencyName;

	// Log Table to monitor in the interceptor
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	static {
		logTable.put("currencyCode", "Currency Code");
		logTable.put("currencyName", "Currency Name");
		logTable.put("alias", "Alias");
	}

	public static class Id extends BaseObject {
		/**
		 * Default version of inner class
		 */
		private static final long serialVersionUID = 1L;
		// Host Id
		private String hostId;
		// Entity Id
		private String entityId;
		// Alias
		private String alias;

		/**
		 * @return the hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            the hostId to set
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return the alias
		 */
		public String getAlias() {
			return alias;
		}

		/**
		 * @param alias
		 *            the alias to set
		 */
		public void setAlias(String alias) {
			this.alias = alias;
		}

	}

	/**
	 * @return the currencyCode
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param currencyCode
	 *            the currencyCode to set
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return the updateUser
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            the updateUser to set
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the currencyName
	 */
	public String getCurrencyName() {
		return currencyName;
	}

	/**
	 * @param currencyName
	 *            the currencyName to set
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}

}
