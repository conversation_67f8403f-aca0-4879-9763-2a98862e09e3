<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.DefaultAcct" table="P_DEFAULT_ACCOUNT">
        
		<composite-id name="id" class="org.swallow.maintenance.model.DefaultAcct$Id" unsaved-value="any">
        	<key-property name="hostId" access="field" column="HOST_ID"/>
        	<key-property name="entityId" access="field" column="ENTITY_ID" />
        	<key-property name="currencyCode" access="field" column="CURRENCY_CODE" />
        	<key-property name="xrefCode" access="field" column="XREF_CODE" />
		</composite-id>
			
		<many-to-one name="accountMaster" class="org.swallow.maintenance.model.AccountMaster" lazy="false" not-null="true" outer-join="true" update="false" insert="false">
		  <column name="ENTITY_ID" />
		  <column name="HOST_ID" />
		  <column name="ACCOUNT_ID" />
		</many-to-one>
		
		<property name="acctId" column="ACCOUNT_ID" not-null="true"/>	

    </class>
</hibernate-mapping>
