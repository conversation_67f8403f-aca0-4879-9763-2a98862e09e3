package org.swallow.maintenance.model;

import org.swallow.maintenance.model.CountryOverride.Id;
import org.swallow.model.BaseObject;

import java.util.Hashtable;

public class CriticalPaymentExp extends BaseObject implements
org.swallow.model.AuditComponent {

	private static final long serialVersionUID = 1L;

	// To hold short name (key) and its description (value)
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();

	/**
	 * Static block puts short name and its description in a map
	 */
	static {
		logTable.put("hostId", "Host Id");
		logTable.put("entityId", "Entity Id");
		logTable.put("cpTypeId", "Cp Type Id");
		logTable.put("currencyCode", "Currency Code");
		logTable.put("defaultExpectedTime", "Default Expected Time");;

	}



	// variable to hold override defaultExpectedTime
	private String defaultExpectedTime = null;

	private Id id = new Id();


	/**
	 * Id Class to hold primary key values
	 * 
	 * <AUTHOR> A
	 * 
	 */
	public static class Id extends BaseObject {

		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// variable to hold hostId
		private String hostId = null;
		// variable to hold entityId
		private String entityId = null;
		// variable to hold currencyCode
		private String currencyCode = null;
		// variable to hold cpTypeId
		private String cpTypeId = null;

		// Default constructor
		public Id() {

		}

		/**
		 * Constructor to set primary key values
		 * 
		 * @param hostId
		 * @param entityId
		 */
		public Id(String hostId, String entityId, String cpTypeId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.cpTypeId = cpTypeId;
			this.currencyCode= currencyCode;
		}

		/**
		 * Getter method for EntityId
		 * 
		 * @return String - EntityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method for entityId
		 * 
		 * @param entityId
		 * @return
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * Getter method for hostId
		 * 
		 * @return String - hostId
		 */
		public String getHostId() {

			return hostId;
		}

		/**
		 * Setter method for hostId
		 * 
		 * @param hostId
		 * @return
		 */
		public void setHostId(String hostId) {

			this.hostId = hostId;

		}

		public String getCpTypeId() {
			return cpTypeId;
		}

		public void setCpTypeId(String cpTypeId) {
			this.cpTypeId = cpTypeId;
		}


		public String getCurrencyCode() {
			return currencyCode;
		}

		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
	}

	/**
	 * Getter method for Id class
	 * 
	 * @return Id - id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 * @return
	 */
	public void setId(Id id) {
		this.id = id;
	}

	public String getDefaultExpectedTime() {
		return defaultExpectedTime;
	}

	public void setDefaultExpectedTime(String defaultExpectedTime) {
		this.defaultExpectedTime = defaultExpectedTime;
	}
// Hash table instance to hold maintenance log table values
//	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
//	static {
//		logTable.put("countryOverride", "CountryOverride");
//
//	}

}
