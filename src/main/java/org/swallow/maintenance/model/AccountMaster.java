package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;

public class AccountMaster extends BaseObject {

	private String accountName;
	private String currencyCode;
	private String accountType;
	private String accountClass;

	private Id id = new Id();
	

	public static class Id extends BaseObject{
		private String hostId;
		private String entityId;
		private String accountId;

		public Id() {}

		public Id(String hostId, String entityId,String accountId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.accountId=accountId;
		}
		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the accountId.
		 */
		public String getAccountId() {
			return accountId;
		}
		/**
		 * @param accountId The accountId to set.
		 */
		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}
	}

	public void setId(Id id){
		this.id = id; 
		}
	

	
	public Id getId(){
		return id; 
		}
	
		
	/**
	 * @return Returns the accountName.
	 */
	public String getAccountName() {
		return accountName;
	}
	/**
	 * @param accountName The accountName to set.
	 */
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return Returns the accountType.
	 */
	public String getAccountType() {
		return accountType;
	}
	/**
	 * @param accountType The accountType to set.
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getAccountClass() {
		return accountClass;
	}

	public void setAccountClass(String accountClass) {
		this.accountClass = accountClass;
	}
}
