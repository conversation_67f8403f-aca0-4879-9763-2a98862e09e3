/*
 * @(#)ILMTransactionSetHDR.java  03/12/2013
 * 
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.util.Hashtable;
import java.util.Set;

import org.swallow.model.BaseObject;

public class ILMTransactionSetHDR extends BaseObject implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	

	private String txnSetName	= null ;
	private Set<ILMTransactionSetDTL> ilmTransactionSetDTLs;

	public Set<ILMTransactionSetDTL> getIlmTransactionSetDTLs() {
		return ilmTransactionSetDTLs;
	}

	public void setIlmTransactionSetDTLs(
			Set<ILMTransactionSetDTL> ilmTransactionSetDTLs) {
		this.ilmTransactionSetDTLs = ilmTransactionSetDTLs;
	}


	private Id id = new Id();

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("txnSetName","Transaction Set Name");
		logTable.put("txnSetId","Transaction Set Id");
	}

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Getter method for logTable
	 * 
	 * @return logTable as Hashtable
	 */

	public static Hashtable getLogTable() {
		return logTable;
	}

	/**
	 * Setter method for logTable
	 * 
	 * @param logTable
	 */

	public static void setLogTable(Hashtable logTable) {
		ILMTransactionSetHDR.logTable = logTable;
	}

	public String getTxnSetName() {
		return txnSetName;
	}

	public void setTxnSetName(String txnSetName) {
		this.txnSetName = txnSetName;
	}


	public static class Id extends BaseObject{

		private String hostId = null;
		private String entityId = null;
		private String currencyCode = null;
		private String txnSetId = null;

		/**
		 * @return the hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId the hostId to set
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return the currencyCode
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}

		/**
		 * @param currencyCode the currencyCode to set
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}

		/**
		 * @return the txnSetId
		 */
		public String getTxnSetId() {
			return txnSetId;
		}

		/**
		 * @param txnSetId the txnSetId to set
		 */
		public void setTxnSetId(String txnSetId) {
			this.txnSetId = txnSetId;
		}

		public Id() {
		}
		
		public Id(String hostId,String entityId,String currencyCode,String txnSetId) {
			
			this.setHostId(hostId);
			this.setEntityId(entityId);
			this.setCurrencyCode(currencyCode);
			this.setTxnSetId(txnSetId);
			
		}

	}

}
