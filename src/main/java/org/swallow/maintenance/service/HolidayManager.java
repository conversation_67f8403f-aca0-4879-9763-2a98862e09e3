/*
 * @(#)HolidayManger.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Holiday;

public interface HolidayManager {
	/**
	 * This is used to delete the holiday details from S_HOLIDAY table
	 * 
	 * @param holiday
	 * @return
	 * @throws SwtException
	 */
	public void deleteHoliday(Holiday holiday) throws SwtException;

	/**
	 * This is used save the newly added holiday details in S_HOLIDAY table
	 * 
	 * @param holiday
	 * @return
	 * @throws SwtException
	 */
	public void saveHoliday(Holiday holiday) throws SwtException;

	/**
	 * This is used to fetches the holiday's list from S_HOLIDAY table
	 * 
	 * @param entity
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection getHolidayList(String entity, String hostId)
			throws SwtException;

	/**
	 * This is used to get Country details from S_COUNTRY table
	 * 
	 * @param
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection getCountry() throws SwtException;
}
