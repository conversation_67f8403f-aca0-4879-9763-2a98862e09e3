package org.swallow.maintenance.service;

import java.util.ArrayList;
import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.InterfaceRule;

public interface InterfaceRulesManager {

	/**
	 * Queries hibernate for a collection of Interface rules objects.
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<InterfaceRule> getList() throws SwtException;

	/**
	 * Queries hibernate for a collection of Interface rules objects matching
	 * the supplied message type parameter
	 * 
	 * @param messageType
	 * @return Collection
	 * @throws SwtException
	 */
	/*
	 * Code Added for Mantis 1365:"Allow Special characters in Interface Rules
	 * Screen." by venkatesan on 24-feb-2011.
	 */
	public Collection<InterfaceRule> getInterfaceRulesList(String messageType,
			String ruleId, String partialRuleId) throws SwtException;

	/**
	 * Deletes the unique interface rule matching the data in the given
	 * InterfaceRule object
	 * 
	 * @param interfaceRule
	 * @throws SwtException
	 */
	public void deleteInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException;

	/**
	 * Queries hibernate for a unique record matching the messageType, ruleId
	 * and ruleKey contained in the given InterfaceRule object
	 * 
	 * @param interfaceRule
	 * @return InterfaceRule
	 * @throws SwtException
	 */
	public InterfaceRule getInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException;

	/**
	 * Updates the persistent storage with the existing InterfaceRule object
	 * given. The PK data contained therein is used to locate the record to
	 * update.
	 * 
	 * @param interfaceRule
	 * @throws SwtException
	 */
	public void updateInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException;

	/**
	 * This is used to save the interface rule details in I_INTERFACE_RULES
	 * table.
	 * 
	 * @param interfaceRule
	 * @throws SwtException
	 */
	public void saveInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException;

	/**
	 * This is used to populate the interface rules message types drop down
	 * values.
	 * 
	 * @return list
	 * @throws SwtException
	 */
	// public Collection<String> getMessageTypesDropdownValues() throws
	// SwtException;
	/*
	 * Code modified by Venkat on 24-jan-2011 for mantis 1365:"Allow Special
	 * characters in Interface Rules Screen."
	 */
	public int getInterfaceRulesListUsingStoredProc(String messageType,
			String ruleId, int currentPage, int maxPage,
			ArrayList<InterfaceRule> interfaceRulesList,
			String filterSortStatus, String partialRuleId) throws SwtException;
}
