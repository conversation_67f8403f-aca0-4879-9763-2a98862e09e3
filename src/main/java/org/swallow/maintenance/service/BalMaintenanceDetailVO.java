/*
 * Created on Dec 15, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.service;

import java.util.Collection;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class BalMaintenanceDetailVO {
	
	private Collection balmaintenanceList;
	private Collection balmaintenanceListDetails;

	
	public BalMaintenanceDetailVO(){
	}
	public BalMaintenanceDetailVO(Collection balmaintenanceList, Collection balmaintenanceListDetails){
		this.balmaintenanceList = balmaintenanceList;
		this.balmaintenanceListDetails = balmaintenanceListDetails;
	}

	/**
	 * @return Returns the balmaintenanceList.
	 */
	public Collection getBalmaintenanceList() {
		return balmaintenanceList;
	}
	/**
	 * @param balmaintenanceList The balmaintenanceList to set.
	 */
	public void setBalmaintenanceList(Collection balmaintenanceList) {
		this.balmaintenanceList = balmaintenanceList;
	}
	/**
	 * @return Returns the balmaintenanceListDetails.
	 */
	public Collection getBalmaintenanceListDetails() {
		return balmaintenanceListDetails;
	}
	/**
	 * @param balmaintenanceListDetails The balmaintenanceListDetails to set.
	 */
	public void setBalmaintenanceListDetails(
			Collection balmaintenanceListDetails) {
		this.balmaintenanceListDetails = balmaintenanceListDetails;
	}
}
