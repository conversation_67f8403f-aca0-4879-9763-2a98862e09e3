
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.ILMTransScenarioMaintenanceDAO;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.maintenance.model.ILMTransactionSetDTL;
import org.swallow.maintenance.model.ILMTransactionSetHDR;
import org.swallow.maintenance.service.ILMTransScenarioMaintenanceManager;
import org.swallow.util.SwtUtil;
@Component("ilmTransScenarioMaintenanceManager")
public class ILMTransScenarioMaintenanceManagerImpl implements
		ILMTransScenarioMaintenanceManager {
	@Autowired
	private ILMTransScenarioMaintenanceDAO ilmTransScenarioMaintenanceDAO;

	public void setIlmTransScenarioMaintenanceDAO(
			ILMTransScenarioMaintenanceDAO ilmTransScenarioMaintenanceDAO) {
		this.ilmTransScenarioMaintenanceDAO = ilmTransScenarioMaintenanceDAO;
	}

	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory
			.getLog(ILMTransScenarioMaintenanceManagerImpl.class);

	
	public ArrayList getILMScenarioList(String hostId, String entityId,
			String currencyCode, HttpServletRequest request)
			throws SwtException {
		
		ArrayList accountGroupsListDetails;
		Collection collScen;
		ILMScenario ilmScenario;
		Iterator itr;
		boolean insertItem = true;
		
		try {
			log.debug(this.getClass().getName()
					+ " - [getILMScenarioList] - " + "Entry");
			accountGroupsListDetails = new ArrayList();
			// Collect the Scenario Detail list from
			// ilmTransScenarioMaintenanceDAO
			collScen = ilmTransScenarioMaintenanceDAO.getILMScenarioList(hostId,
					entityId, currencyCode);
			// Iterate the collection
			itr = collScen.iterator();
			ilmScenario = new ILMScenario();
			// Loop till the last value of the iterator.
			while (itr.hasNext()) {
				// Get the value in Scenario bean
				ilmScenario = (ILMScenario) (itr.next());
//				if (ilmScenario.getPublicPrivate() != null)
//					ilmScenario.setPublicPrivateAsString(ilmScenario
//							.getPublicPrivate().equals("P") ? "Public"
//							: "Private");

				if (entityId.equals("All")) {
					if ("*".equalsIgnoreCase(ilmScenario.getEntityId())) {
						insertItem = true;
					} else {
						if ("*".equalsIgnoreCase(ilmScenario.getCurrencyCode())) {
							Collection collEntity = SwtUtil.getUserEntityAccessList(request
									.getSession());
							int entityAccess = SwtUtil.getUserEntityAccess(collEntity, ilmScenario.getEntityId());
							insertItem = (entityAccess == 2) ? false : true;
						} else {
							insertItem = (SwtUtil.getCcyGrpAccessType(request, hostId, ilmScenario.getEntityId(), ilmScenario.getCurrencyCode()) == 2) ? false : true;
						}
					}
				} else {
					if ("*".equalsIgnoreCase(ilmScenario.getCurrencyCode())) {
						insertItem = true;
					} else {
						insertItem = (SwtUtil.getCcyGrpAccessType(request, hostId, entityId, ilmScenario.getCurrencyCode()) == 2) ? false : true;
					}
				}
				// Add transaction bean to the transactionSetsDetails
				if (insertItem)
					accountGroupsListDetails.add(ilmScenario);
			}

			log.debug(this.getClass().getName()
					+ " - [getILMScenarioList] - " + "Exit");
		} catch (Exception e) {
			e.printStackTrace();
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getILMScenarioList] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getILMScenarioList] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getILMScenarioList",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
		return accountGroupsListDetails;
	}

	public ArrayList getTransactionList(String hostId, String entityId,
			String currencyCode, HttpServletRequest request)
			throws SwtException {
		ArrayList transactionSetsDetails;
		Collection collScen;
		ILMTransactionSetHDR ilmTranSet;
		Iterator itr;
		boolean insertItem = true;
		try {
			log.debug(this.getClass().getName()
					+ " - [getScenariosDetailList] - " + "Entry");
			transactionSetsDetails = new ArrayList();
			// Collect the Scenario Detail list from
			// ilmTransScenarioMaintenanceDAO
			collScen = ilmTransScenarioMaintenanceDAO.getTransactionList(
					hostId, entityId, currencyCode);

			// Iterate the collection
			itr = collScen.iterator();
			ilmTranSet = new ILMTransactionSetHDR();

			// Loop till the last value of the iterator.
			while (itr.hasNext()) {
				// Get the value in ILMTransactionSetHDR bean
				ilmTranSet = (ILMTransactionSetHDR) (itr.next());

				insertItem = SwtUtil.getCcyGrpAccessType(request, hostId,
						ilmTranSet.getId().getEntityId(), ilmTranSet.getId()
								.getCurrencyCode()) == 2 ? false : true;
				// add transaction bean to the transactionSetsDetails
				if (insertItem)
					transactionSetsDetails.add(ilmTranSet);
			}

			log.debug(this.getClass().getName()
					+ " - [getScenariosDetailList] - " + "Exit");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getScenariosDetailList] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenariosDetailList] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getScenariosDetailList",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
		return transactionSetsDetails;
	}


	public void saveILMScenarioDetails(ILMScenario ilmScenario)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [saveILMScenarioDetails] - Entry ");

			ilmTransScenarioMaintenanceDAO.saveILMScenarioDetails(ilmScenario);
			log.debug(this.getClass().getName()
					+ "- [saveILMScenarioDetails] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveILMScenarioDetails] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveILMScenarioDetails",
					ILMGeneralMaintenanceManagerImpl.class);
		}

	}

	public void updateILMScenarioDetails(ILMScenario ilmScenario)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [updateILMScenarioDetails] - "
					+ "Entry");
			
			ilmTransScenarioMaintenanceDAO.updateILMScenario(ilmScenario);
			
			log.debug(this.getClass().getName() + " - [updateILMScenarioDetails] - "
					+ "Exit");
		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'updateILMScenarioDetails' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateILMScenarioDetails",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}

	public ILMScenario getEditableScenarioData(String scenarioId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [getEditableScenarioData] - "
				+ "Entry");

		ILMScenario ilmScenario = null;
		ilmScenario = ilmTransScenarioMaintenanceDAO
				.getEditableScenarioData(scenarioId);

		log.debug(this.getClass().getName() + " - [getEditableScenarioData] - "
				+ "Exit");

		return ilmScenario;
	}

	public void deleteILMScenario(ILMScenario ilmScenario) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [deleteILMScenario] - "
					+ "Entry");
			ilmTransScenarioMaintenanceDAO.deleteILMScenario(ilmScenario);

			log.debug(this.getClass().getName() + " - [deleteILMScenario] - "
					+ "Exit");

		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'deleteILMScenario' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"deleteILMScenario",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}

	}

	public Collection<ILMTransactionSetDTL> getTransactionDetailsList(
			String hostId, String entityId, String currencyCode, String txnSetId)
			throws SwtException {
		Collection<ILMTransactionSetDTL> list = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [getTransactionDetailsList] - " + "Entry");
			list = ilmTransScenarioMaintenanceDAO.getTransactionDetailsList(
					hostId, entityId, currencyCode, txnSetId);

			log.debug(this.getClass().getName()
					+ " - [getTransactionDetailsList] - " + "Exit");
			return list;
		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'getTransactionDetailsList' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getTransactionDetailsList",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}

	public void saveILMTransaction(ILMTransactionSetHDR ilmTransactionSetHDR,
			Set<ILMTransactionSetDTL> ilmTransactionSetDTLs)
			throws SwtException {

		try {
			log.debug(this.getClass().getName()
					+ " - [saveILMTransaction] - Entry");
		
			ilmTransScenarioMaintenanceDAO
					.saveILMTransaction(ilmTransactionSetHDR);
			if(ilmTransactionSetDTLs!=null)
			saveILMTransactionDetail(ilmTransactionSetHDR, ilmTransactionSetDTLs);
			
			log.debug(this.getClass().getName()
					+ " - [saveILMTransaction] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveILMTransaction] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveILMTransaction",
					ILMGeneralMaintenanceManagerImpl.class);
		}

	}

	public ILMTransactionSetDTL getEditableTransactionDTLData(String hostId,
			String entityId, String currencyCode, String txnSetId,
			String accountId, String time) throws SwtException {
		ILMTransactionSetDTL ilmTransactionSetDTL;
		try {
			log.debug(this.getClass().getName()
					+ " - [getEditableTransactionDTLData] - Entry");
			
			ilmTransactionSetDTL = ilmTransScenarioMaintenanceDAO
					.getEditableTransactionDTLData(hostId, entityId,
							currencyCode, txnSetId, accountId, time);
			
			log.debug(this.getClass().getName()
					+ " - [getEditableTransactionDTLData] - Exit");
			
			return ilmTransactionSetDTL;
			
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getEditableTransactionDTLData] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEditableTransactionDTLData] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getEditableTransactionDTLData",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}

	
	public ILMTransactionSetHDR getEditableTransactionHDRData(String hostId,
			String entityId, String currencyCode, String txnSetId)
			throws SwtException {
		ILMTransactionSetHDR ilmTransactionSetHDR;
		try {
			log.debug(this.getClass().getName()
					+ " - [getEditableTransactionHDRData] - Entry");
			ilmTransactionSetHDR = ilmTransScenarioMaintenanceDAO
					.getEditableTransactionHDRData(hostId, entityId,
							currencyCode, txnSetId);
			log.debug(this.getClass().getName()
					+ " - [getEditableTransactionHDRData] - Exit");
			return ilmTransactionSetHDR;
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getEditableTransactionHDRData] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEditableTransactionHDRData] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getEditableTransactionHDRData",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}


	public void updateILMTransaction(ILMTransactionSetHDR ilmTransactionSetHDR, boolean updatedRelatedScenarios)
			throws SwtException {
		try {
			ilmTransScenarioMaintenanceDAO.updateILMTransaction(ilmTransactionSetHDR, updatedRelatedScenarios);
		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'updateILMTransaction' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateILMTransaction",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}
	public void updateILMTransactionDetail( Set<ILMTransactionSetDTL>  setILMTransactionSetDTL)
			throws SwtException {
		Iterator itr;
		ILMTransactionSetDTL ilmTransactionSetDTL ;
		try {
			itr = setILMTransactionSetDTL.iterator();
			ilmTransactionSetDTL = new ILMTransactionSetDTL();
			// Loop till the last value of the iterator.
			while (itr.hasNext()) {
				// Get the value in ILMTransactionSetDTL bean
				ilmTransactionSetDTL = (ILMTransactionSetDTL) (itr.next());
			ilmTransScenarioMaintenanceDAO.updateILMTransactionDetail(ilmTransactionSetDTL);
			}
			
		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'updateILMTransactionDetail' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateILMTransactionDetail",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}


	public void saveILMTransactionDetail(ILMTransactionSetHDR ilmTransactionSetHDR ,
			Set<ILMTransactionSetDTL>  setILMTransactionSetDTL) throws SwtException {
		Iterator itr;
		ILMTransactionSetDTL ilmTransactionSetDTL ;
		try {
			log.debug(this.getClass().getName()
					+ "- [saveILMTransactionDetail] - Entry ");
			// Iterate the collection
			itr = setILMTransactionSetDTL.iterator();
			ilmTransactionSetDTL = new ILMTransactionSetDTL();
			// Loop till the last value of the iterator.
			while (itr.hasNext()) {
				// Get the value in ILMTransactionSetDTL bean
				ilmTransactionSetDTL = (ILMTransactionSetDTL) (itr.next());
				ilmTransactionSetDTL.setIlmTransactionSetHDR(ilmTransactionSetHDR);
			ilmTransScenarioMaintenanceDAO.saveILMTransactionDetail(ilmTransactionSetDTL);
			}
			log.debug(this.getClass().getName()
					+ "- [saveILMTransactionDetail] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveILMTransactionDetail] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveILMTransactionDetail",
					ILMGeneralMaintenanceManagerImpl.class);
		}

	}


	public void deleteILMTransactionDetail( Set<ILMTransactionSetDTL>  setILMTransactionSetDTL)
			throws SwtException {
		Iterator itr;
		ILMTransactionSetDTL ilmTransactionSetDTL ;
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteILMTransactionDetail] - Entry ");
			// Iterate the collection
			itr = setILMTransactionSetDTL.iterator();
			ilmTransactionSetDTL = new ILMTransactionSetDTL();

			// Loop until the last value of the iterator.
			while (itr.hasNext()) {
				// Get the value in ILMTransactionSetDTL bean
				ilmTransactionSetDTL = (ILMTransactionSetDTL) (itr.next());
			ilmTransScenarioMaintenanceDAO.deleteILMTransactionDetail(ilmTransactionSetDTL);
			}
			log.debug(this.getClass().getName()
					+ "- [deleteILMTransactionDetail] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteILMTransactionDetail] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteILMTransactionDetail",
					ILMGeneralMaintenanceManagerImpl.class);
		}

	}


	public void deleteILMTransaction(ILMTransactionSetHDR ilmTransactionSetHDR)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteILMTransaction] - Entry ");
			deleteILMTransactionDetail( ilmTransactionSetHDR.getIlmTransactionSetDTLs());
			ilmTransScenarioMaintenanceDAO.deleteILMTransaction(ilmTransactionSetHDR);
			
			log.debug(this.getClass().getName()
					+ "- [deleteILMTransaction] - Exit ");
		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'deleteILMTransaction' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"deleteILMTransaction",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}

	public String getFilterConditionResult(String filterCondition)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getFilterConditionResult] - Entry ");
		
		return ilmTransScenarioMaintenanceDAO.getFilterConditionResult(filterCondition);
		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'getFilterConditionResult' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getFilterConditionResult",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}

	
	public boolean isTransactionSetAlreadyUsed(String transactionSetId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [isTransactionSetAlreadyUsed] - Entry ");
		
		return ilmTransScenarioMaintenanceDAO.isTransactionSetAlreadyUsed(transactionSetId);
		} catch (SwtException ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'isTransactionSetAlreadyUsed' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"isTransactionSetAlreadyUsed",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
	}


	public Collection getILMScenarioForReporting(String hostId,
			ArrayList<String> entities , ArrayList<String> currencies ) throws SwtException {
		Collection collScen = null;
	
		try {
			collScen= ilmTransScenarioMaintenanceDAO.getILMScenarioForReporting(hostId, entities, currencies);
		} catch (Exception ex) {
			log.error("Exception Catch in ILMTransScenarioMaintenanceManagerImpl.'getILMScenarioForReporting' method : "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getILMScenarioForReporting",
					ILMTransScenarioMaintenanceManagerImpl.class);
		}
		return collScen;
	}
}