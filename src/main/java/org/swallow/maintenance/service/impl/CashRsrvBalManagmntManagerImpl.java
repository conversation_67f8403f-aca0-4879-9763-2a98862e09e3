
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CashRsrvBalManagmntDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.service.CashRsrvBalManagmntManager;
import org.swallow.util.LabelValueBean;
import org.swallow.work.model.CashManagementVO;

@Component("cashRsrvBalManagmntManager")
public class CashRsrvBalManagmntManagerImpl implements CashRsrvBalManagmntManager {

	/**
	 * To create a Log factory reference variable.
	 */
	private static final Log log = LogFactory.getLog(CashRsrvBalManagmntManagerImpl.class);

	/**
	 * Default DAO object associated with this managerimpl class
	 */
	@Autowired
	private  CashRsrvBalManagmntDAO cashRsrvBalManagmntDAO;

	/**
	 * @param CashRsrvBalManagmntDAO
	 *            The CashRsrvBalManagmntDAO to set.
	 */
	public void setCashRsrvBalManagmntDAO(CashRsrvBalManagmntDAO cashRsrvBalManagmntDAO) {
		this.cashRsrvBalManagmntDAO = cashRsrvBalManagmntDAO;
	}
	
	public CashManagementVO getCashRsvBalanceGridData(CashManagementVO cashManagementVO) throws SwtException{
		try {
			cashManagementVO =  this.cashRsrvBalManagmntDAO.getCashRsvBalanceGridData(cashManagementVO);
			return cashManagementVO;

		} catch (Exception e) {
			e.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [processSweep] method : - " + e.getMessage());
			throw new SwtException(e.getMessage());
		} 
	}
	

	public CashManagementVO getCashRsvBalancePeriod(CashManagementVO cashManagementVO) throws SwtException{
		try {
			cashManagementVO =  this.cashRsrvBalManagmntDAO.getCashRsvBalancePeriod(cashManagementVO);
			return cashManagementVO;

		} catch (Exception e) {
			e.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [processSweep] method : - " + e.getMessage());
			throw new SwtException(e.getMessage());
		} 
	}
	
	/**
	 * 
	 * This method is used to display the account list based on the Currency id
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getAccountIDDropDown(
			String hostId, String entityId, String currencyCode, String accountType)
			throws SwtException {
		// Variable to hold the accountTypeList object
		ArrayList<LabelValueBean> accountTypeList = null;
		// Variable to hold the itrAcctMaintenance object
		Iterator<AcctMaintenance> itrAccountMaintenance = null;
		// Variable to hold the acctMaintenance object
		AcctMaintenance acctMaintenance = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getAccountIDDropDown] - Entering ");
			accountTypeList = new ArrayList<LabelValueBean>();
			// Get the collection of account id
			itrAccountMaintenance = (cashRsrvBalManagmntDAO.getAccountIDDropDown(
					hostId, entityId, currencyCode, accountType)).iterator();

			accountTypeList.add(new LabelValueBean("", ""));
			// iterate the account list
			while (itrAccountMaintenance.hasNext()) {
				acctMaintenance = itrAccountMaintenance.next();
				// Check the currency code and Set the selected currency account
				// list in accountTypeList
						accountTypeList.add(new LabelValueBean(acctMaintenance
								.getAcctname(), acctMaintenance.getId()
								.getAccountId()));
					}
				
			
			log.debug(this.getClass().getName()
					+ "- [getAccountIDDropDown] - Exit ");
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAccountIDDropDown] - SwtException - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountIDDropDown",
					CashRsrvBalManagmntManagerImpl.class);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAccountIDDropDown] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountIDDropDown",
					CashRsrvBalManagmntManagerImpl.class);
		} finally {
			itrAccountMaintenance = null;
			acctMaintenance = null;
		}
		return accountTypeList;
	}


	

	
	
	


}