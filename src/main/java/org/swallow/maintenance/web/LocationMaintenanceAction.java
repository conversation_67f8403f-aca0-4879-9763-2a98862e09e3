/*
 * @(#)LocationMaintenanceAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.springframework.web.bind.annotation.ModelAttribute;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Location;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.maintenance.service.LocationMaintenanceManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 *
 * This is action class for Location screen.
 *
 */




import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/location", "/location.do"})
public class LocationMaintenanceAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/locationdefinition");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/maintenance/locationmaintenance");
		viewMap.put("change", "jsp/maintenance/locationdefinition");
	}

	private String getView(String resultName) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		if(request.getAttribute("location") == null) {
			request.setAttribute("location", new Location());
		}
		return viewMap.getOrDefault(resultName, "error");
	}




	private Location location;
	public Location getLocation() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		location = RequestObjectMapper.getObjectFromRequest(Location.class, request);

		return location;
	}
	public void setLocation(Location location) {
		this.location = location;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("location", location);
	}

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(LocationMaintenanceAction.class);
	/**
	 * Initializing LocationMaintenanceManager object for this class
	 */
	@Autowired
	private LocationMaintenanceManager locationManager = null;

	/**
	 * Method to set Location maintenanceManager
	 *
	 * @param locationManager
	 * @return
	 */
	public void setLocationMaintenanceManager(
			LocationMaintenanceManager locationManager) {
		this.locationManager = locationManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						   HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "displayList":
				return displayList();
			case "delete":
				return delete();
			case "add":
				return add();
			case "change":
				return change();
			case "saveLocDefinition":
				return saveLocDefinition();
			case "updateLocDefinition":
				return updateLocDefinition();
		}


		return unspecified();
	}
	/**
	 * This is the default method for this class returns displayList method
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		log.debug(this.getClass().getName() + " - [unspecified] - "
				+ "returns displayList method");

		return displayList();
	}

	/**
	 * Method to load the location details for the default and other selected
	 * entities
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {

			log.debug(this.getClass().getName() + " - [displayList] - "
					+ "Entry");


			/* Methods local variable and class instance declaration */
			String hostId;
			String entityId;
// To remove: 			DynaValidatorForm dyForm;
			Location location;
			Collection collLocation;

// To remove: 			dyForm = (DynaValidatorForm) form;
			location = (Location) getLocation();

			/* Read and store the host id from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Read entity id from form bean */
			entityId = location.getId().getEntityId();

			/* Condition to check entity id is null or empty */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/*
				 * If it is empty the default entity is get from swt util
				 * method.
				 */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				location.getId().setEntityId(entityId);
			}

			/*
			 * The collection of location details is collected from manager
			 * files
			 */
			collLocation = locationManager.getLocationDetails(hostId, entityId);

			request.setAttribute("LocationDetailsList", collLocation);

			/*
			 * Pass the request parameter to set entity list into the request
			 * attribute
			 */
			putEntityListInReq(request);

			/*
			 * Access Rights of menu entity and currency group of the user is
			 * given based on their roles
			 */
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
			request.setAttribute("locationzz", location);
			request.setAttribute("location", location);
			setLocation(location);
			log.debug(this.getClass().getName() + " - [displayList] - "
					+ "Exit");

			return getView("success");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [displayList] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [displayList] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayList", LocationMaintenanceAction.class),
					request, "");

			return getView("fail");
		}
	}

	/**
	 * This method collects the entity id and entity name which has proper
	 * access for the user based on their role and set them in request attribute
	 *
	 * @param request
	 * @throws SwtException
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");

		/* Method's local variable and class instance declaration */
		HttpSession session;
		Collection entities;

		/* Read the session from request */
		session = request.getSession();
		entities = new ArrayList();
		/* Collects the user access entities */
		entities = SwtUtil.getUserEntityAccessList(session);
		/* Collects the entity id and entity name */
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		request.setAttribute("entities", entities);

		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Exit");

	}

	/**
	 * Method to delete the Existing location details from data base and to
	 * display the error message if the records is not exist
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		try {

			log.debug(this.getClass().getName() + " - [delete] - " + "Entry");

			/* Method's local variable and class instance declaration */
			String selectedLocationId;
// To remove: 			DynaValidatorForm dyForm;

// To remove: 			dyForm = (DynaValidatorForm) form;
			System.err.println("aaaaas ");
			/* Get the form bean of location through dyna validator form */
			Location location = (Location) getLocation();


			/* Read the selected location id from the request */
			selectedLocationId = request.getParameter("selectedLocationId")
					.trim();

			/* The current host id is set to the form bean */
			location.getId().setHostId(SwtUtil.getCurrentHostId());

			/* Set the location id to the form bean */
			location.getId().setLocationId(selectedLocationId);
			location.setLocationName("");

			/* to avoid integrity errors, it is necessary to look if there are books related to the location before delete */
			BookCodeManager bookCodeManager = (BookCodeManager) SwtUtil.getBean("bookCodeManager");
			Collection books = bookCodeManager.getBooksLocationId(location.getId().getLocationId());
			if(books.size()== 0) {
				/* The manager method to perform deletion */
				Collection locationDetail = locationManager.getLocationIdDetails(location.getId().getLocationId());
				if(locationDetail.size()> 0) {
					locationManager.deleteLocationDetail(location);
				}else {
					throw new SwtException("errors.SwtRecordNotExist");
				}
			}else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninDelete");
			}

			log.debug(this.getClass().getName() + " - [delete] - " + "Exit ");

			return displayList();
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return displayList();
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "delete", LocationMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * Action method to load add location screen loads with parent screen's
	 * entity and allows the user to add the location details
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [add] - " + "Entry");

		/* Method's local variable and class instance declaration */
		String entityId;
// To remove: 		DynaValidatorForm dyForm;
		Location locationdefinition;

		try {

			/* Read entity id from the request */
			entityId = request.getParameter("selectedEntityId");

			/*
			 * Pass the request parameter to set the entity list into the
			 * request attribute
			 */
			putEntityListInReq(request);

// To remove: 			dyForm = (DynaValidatorForm) form;

			locationdefinition = (Location) getLocation();

			/* Set entity id in location bean */
			locationdefinition.getId().setEntityId(entityId);
			setLocation(locationdefinition);

			request.setAttribute("entityName", (request
					.getParameter("selectedEntityCode")));
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("method", "add");
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			request.setAttribute("entityName", (request
					.getParameter("selectedEntityCode")));
			request.setAttribute("selectedEntityId", request
					.getParameter("selectedEntityId"));
			request.setAttribute("method", "add");

			putEntityListInReq(request);
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", LocationMaintenanceAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * Action method to load the change screen with entity id from parent screen
	 * and location id from the selected location, it allows the user to change
	 * the existing location name
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {

			log.debug(this.getClass().getName() + " - [change] - " + "Entry");

			/* Method's local variable and class instance declaration */
			String entityId;
// To remove: 			DynaValidatorForm dyForm;
			Location locationdefinition;

			/* Read the entity id from the request */
			entityId = request.getParameter("selectedEntityId");
// To remove: 			dyForm = (DynaValidatorForm) form;

			locationdefinition = (Location) getLocation();

			/* Set entity id to the location form bean */
			locationdefinition.getId().setEntityId(entityId);

			/* Set location name to the form bean */
			locationdefinition.setLocationName(request
					.getParameter("selectedLocationName"));

			/* Set location id to the form bean */
			locationdefinition.getId().setLocationId(
					request.getParameter("selectedLocationId"));

			setLocation(locationdefinition);

			request.setAttribute("entityName", (request
					.getParameter("selectedEntityCode")));
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("method", "change");

			log.debug(this.getClass().getName() + " - [change] - " + "Exit");

			return getView("add");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "change", LocationMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * Action method to validate the location details and returns an error
	 * message if the validation fails or save in to the database
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String saveLocDefinition() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [saveLocDefinition] - "
				+ "Entry");

		/* Method's local variable and class instance declaration */
		String entityId=null;
		String user=null;
// To remove: 		DynaValidatorForm dyForm=null;
		Location locationdefinition=null;
		ActionMessages errors=null;
		try {
// To remove: 			dyForm = (DynaValidatorForm) form;
			locationdefinition = (Location) getLocation();
			errors = new ActionMessages();
			/* Read the selected entity id from the request to store entity id */
			entityId = request.getParameter("selectedEntityId");

			/* Collects the user name form the User thread local holder */
			user = UserThreadLocalHolder.getUser();

			/* Set the entity id to location form bean */
			locationdefinition.getId().setEntityId(entityId);

			/* Set the update user to location form bean */
			locationdefinition.setUpdateUser(user);

			/* Set the update date to the Location form bean */
			locationdefinition
					.setUpdateDate(SwtUtil.getSystemDatewithoutTime());

			/* Set host id to location form bean collected from SwtUtil */
			locationdefinition.getId().setHostId(SwtUtil.getCurrentHostId());

			/*
			 * Set the location id to the form bean collected from the same
			 * location form bean
			 */
			locationdefinition.getId().setLocationId(
					locationdefinition.getId().getLocationId().trim());

			/* Bean is forward to location manager to save the location details */
			locationManager.saveLocationDetail(locationdefinition);

			/*
			 * Access Rights of menu entity and currency group of the user is
			 * given based on their roles
			 */
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("method", "add");
			setLocation(locationdefinition);

			log.debug(this.getClass().getName() + " - [saveLocDefinition] - "
					+ "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveLocDefinition] method : - "
					+ swtexp.getMessage());
			request.setAttribute("entityName", (request
					.getParameter("selectedEntityCode")));
			request.setAttribute("selectedEntityId", request
					.getParameter("selectedEntityId"));
			request.setAttribute("method", "add");

			request.setAttribute("method", "add");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */

			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveLocDefinition] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", LocationMaintenanceAction.class), request, "");

			return getView("fail");
		}finally
		{
			errors=null;
		}
	}

	/**
	 * Method to validate the existing location details and return the error
	 * message if validation fails or update the location detail for the
	 * location name.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String updateLocDefinition() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		log.debug(this.getClass().getName() + " - [updateLocDefinition] - "
				+ "Entry");

		try {
			/* Method's local variable declaration */
			String entityId;
			String user;

			/* Method's class instance declaration */
// To remove: 			DynaValidatorForm dyForm;
			Location locationdefinition;

// To remove: 			dyForm = (DynaValidatorForm) form;
			locationdefinition = (Location) getLocation();

			/* Read the selected entity from request */
			entityId = request.getParameter("selectedEntityId");

			/* Read the user id from the user thread local holder */
			user = UserThreadLocalHolder.getUser();

			/* Set entity id to the Location form bean */
			locationdefinition.getId().setEntityId(entityId);

			/* Set update user to the Location form bean */
			locationdefinition.setUpdateUser(user);

			/* Set update date to the Location form bean */
			locationdefinition
					.setUpdateDate(SwtUtil.getSystemDatewithoutTime());

			/* Set host id to the Location form bean collected from SwtUtil */
			locationdefinition.getId().setHostId(SwtUtil.getCurrentHostId());

			/*
			 * Set location id to the form bean collected from the same location
			 * form bean
			 */
			locationdefinition.getId().setLocationId(
					locationdefinition.getId().getLocationId().trim());

			/*
			 * Pass the location bean to update method of location manager to
			 * update the location details in database
			 */
			locationManager.updateLocationDetail(locationdefinition);

			/*
			 * Access Rights of menu entity and currency group of the user is
			 * given based on their roles
			 */
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("method", "change");

			log.debug(this.getClass().getName() + " - [updateLocDefinition] - "
					+ "Exit");
			return getView("add");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [updateLocDefinition] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateLocDefinition] method : - "
					+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("add");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [updateLocDefinition] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateLocDefinition] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "", LocationMaintenanceAction.class), request, "");

			return getView("fail");
		}
	}
}