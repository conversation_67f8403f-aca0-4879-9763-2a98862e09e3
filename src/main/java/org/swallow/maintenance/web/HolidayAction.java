/*
 * @(#)HolidayAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.Shortcut;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.model.HolidayColumnData;
import org.swallow.export.service.impl.Obj2CsvImpl;
import org.swallow.export.service.impl.Obj2PdfImpl;
import org.swallow.export.service.impl.Obj2XlsImpl;
import org.swallow.export.service.impl.Obj2XmlHoliday;
import org.swallow.export.service.impl.Xml2CsvImpl;
import org.swallow.export.service.impl.Xml2PdfImpl;
import org.swallow.export.service.impl.Xml2XlsImpl;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.Holiday;
import org.swallow.maintenance.service.HolidayManager;
import org.swallow.model.ExportObject;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;


/**
 *
 * This is action class for Holiday screen.
 *
 */





import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/holiday", "/holiday.do"})
public class HolidayAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/holidaysinputadd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/maintenance/holidaysinput");
		viewMap.put("save", "jsp/maintenance/holidaysinput");
		viewMap.put("delete", "jsp/maintenance/holidaysinput");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(HolidayAction.class);
	/**
	 * Initializing HolidayManager object for this class
	 */
	@Autowired
	private HolidayManager holidayManager;

	/**
	 * This is used to set the holiday manager
	 *
	 * @param holidayManager
	 * @return
	 */
	public void setHolidayManager(HolidayManager holidayManager) {
		this.holidayManager = holidayManager;
	}


	Holiday holiday;
	public Holiday getHoliday() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		holiday = RequestObjectMapper.getObjectFromRequest(Holiday.class, request);
		return holiday;
	}

	public void setHoliday(Holiday holiday) {
		this.holiday = holiday;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("holiday", holiday);
	}



	String menuAccessId;
	public String getMenuAccessId() {
		return menuAccessId;
	}
	public void setMenuAccessId(String menuAccessId) {
		this.menuAccessId = menuAccessId;
	}

	String ismenuItem;
	public String getIsmenuItem() {
		return ismenuItem;
	}
	public void setIsmenuItem(String ismenuItem) {
		this.ismenuItem = ismenuItem;
	}

	String menuItemId;
	public String getMenuItemId() {
		return menuItemId;
	}
	public void setMenuItemId(String menuItemId) {
		this.menuItemId= menuItemId;
	}

	String selectedHolidayDate;
	public String getSelectedHolidayDate() {
		return selectedHolidayDate;
	}
	public void setSelectedHolidayDate(String selectedHolidayDate) {
		this.selectedHolidayDate = selectedHolidayDate;

	}

	String selectedCountryCode;
	public String getSelectedCountryCode() {
		return selectedCountryCode;
	}
	public void setSelectedCountryCode(String selectedCountryCode) {
		this.selectedCountryCode = selectedCountryCode;
	}

	String totalcount;
	public String getTotalcount() {
		return totalcount;
	}
	public void setTotalcount(String totalcount) {
		this.totalcount = totalcount;
	}

	String screen;

	public String getScreen() {
		return screen;
	}
	public void setScreen(String screen) {
		this.screen = screen;
	}

	String entityId;

	public String getEntityId() {
		return entityId;
	}
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}


	String exportType;


	public String getExportType() {
		return exportType;
	}
	public void setExportType(String exportType) {
		this.exportType = exportType;
	}

	String holidayDate_Date;

	public String getHolidayDate_Date() {
		return holidayDate_Date;
	}
	public void setHolidayDate_Date(String holidayDate_Date) {
		this.holidayDate_Date = holidayDate_Date;
	}

	String holidayDay;

	public String getHolidayDay() {
		return holidayDay;
	}
	public void setHolidayDay(String holidayDay) {
		this.holidayDay = holidayDay;
	}

	String EntityName;
	String HolidayDate;
	String entityCode;

	public String getEntityName() {
		return EntityName;
	}
	public void setEntityName(String entityName) {
		EntityName = entityName;
	}
	public String getHolidayDate() {
		return HolidayDate;
	}
	public void setHolidayDate(String holidayDate) {
		HolidayDate = holidayDate;
	}
	public String getEntityCode() {
		return entityCode;
	}
	public void setEntityCode(String entityCode) {
		this.entityCode = entityCode;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "add":
				return add();
			case "delete":
				return delete();
			case "save":
				return save();
			case "exportHoliday":
				return exportHoliday();
			case "showDetails":
				return showDetails();
		}

		return showDetails();
	}


	/**
	 * This method is default method in struts.This method is called only once
	 * at the time of loading.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [unspecified] - return displayList method ");
		return getView("showDetails");
	}

	/**
	 * This method is used to add the new Holidays details. It loaded entity and
	 * country details from parent screen.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + "- [add] - Enter");
			/* Method's local variable declaration */
			String entitycode;
			String countryCode;
			String entityName;
			Collection collCountry = null;
			Holiday holi = null;
			entitycode = request.getParameter("entityCode");
			countryCode = request.getParameter("countryCode");
			entityName = request.getParameter("EntityName");

			holi = getHoliday();




			/* Setting entity id using bean class */
			holi.getId().setEntityId(entitycode);
			/* Retrieve the country id and country code from DB */
			collCountry = holidayManager.getCountry();
			request.setAttribute("countries", collCountry);
			setHoliday(holi);
			request.setAttribute("entityName", entityName);
			request.setAttribute("screenFieldsStatus", "true");
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", HolidayAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to remove the existing holidays details from database and
	 * throws an error message if the record's not exists.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public  String delete() throws SwtException {
		log.debug(this.getClass().getName() + " - [delete] - " + "Entry");
		/* Method's local variable declaration */
		String holidayDate;
		String holidayDateAsString;
		String hostId;
		/* Class instance declaration */
		Holiday holiday;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {

			holiday = getHoliday();



			/* Getting and setting the selected holiday date */
			holidayDate =  request.getParameter("selectedHolidayDate");
			holidayDateAsString = (holidayDate.replaceAll("%2F", "/"));

			holiday.setHolidayDateAsString(holidayDateAsString);
			/* Getting and setting the selected currency code */
			holiday.getId().setCountryCode(
					request.getParameter("selectedCountryCode"));
			/* Retrieve and store hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Setting host id using bean class */
			holiday.getId().setHostId(hostId);
			/* Getting and setting the holiday date using bean class */
			holiday.getId().setHolidayDate(
					SwtUtil.parseDateGeneral(holiday.getHolidayDateAsString()));
			setHoliday(holiday);
			/* Delete the holiday details from DB based on the parameters passed */
			holidayManager.deleteHoliday(holiday);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			log.debug(this.getClass().getName() + " - [delete] - " + "Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", HolidayAction.class), request, "");
			return getView("fail");
		}
		return showDetails();
	}

	/**
	 * This action allows the user's to enter the input and then validate. If
	 * the data's are valid then stores in database.Otherwise it throws an error
	 * message.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		log.debug(this.getClass().getName() + " - [save] - " + "Entry");
		/* Method's local variable declaration */
		String hostId = null;
		String entityName = null;
		/* Class instance declaration */
		Holiday holi = null;
		ActionMessages errors = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {

			holi = getHoliday();




			holi.getId().setHolidayDate(
					SwtUtil.parseDateGeneral(holi.getHolidayDateAsString()));
			/* Retrieve and store hostId from swtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Set the host id using bean class */
			holi.getId().setHostId(hostId);
			/* Save the holiday details in DB based on the parameters passed */
			holidayManager.saveHoliday(holi);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			entityName = request.getParameter("entityName");
			request.setAttribute("entityName", entityName);
			request.setAttribute("methodName", "showDetails");
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("screenFieldsStatus", "true");
			/* This is used to put the country list in request */
			putCountryListInReqDefault(request);
			log.debug(this.getClass().getName() + " - [save] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			request.setAttribute("countries", holidayManager.getCountry());
			request.setAttribute("methodName", "showDetails");
			request.setAttribute("screenFieldsStatus", "true");

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				errors = new ActionMessages();
				errors.add("", new ActionMessage(swtexp.getErrorCode()));
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", HolidayAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to put the country id and country name in request
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putCountryListInReqDefault(HttpServletRequest request)
			throws SwtException {

		log.debug(this.getClass().getName()
				+ " - [putCountryListInReqDefault] - " + "Entry");
		Collection collCountry = CacheManager.getInstance().getCountries();
		request.setAttribute("countries", collCountry);
		log.debug(this.getClass().getName()
				+ " - [putCountryListInReqDefault] - " + "Exit");
	}

	/**
	 * This method loaded the holiday's details in a screen based on a default
	 * entity or user selected entity.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String showDetails()
			throws SwtException {
		log.debug(this.getClass().getName() + " - [showDetails] - " + "Entry");
		/* Method's local variable declaration */
		String hostId;
		String entityId;
		String countryCode;
		Collection holidaysColl = null;
		Iterator itr = null;
		int accessInd;

		Holiday holiday;
		SystemFormats sysformat = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {

			holiday = getHoliday();




			/* Retrieve and store hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Read the entity id from bean class */
			entityId = holiday.getId().getEntityId();
			/* Read the country id from bean class */
			countryCode = holiday.getId().getCountryCode();
			/* Condition to check entity is equal to null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Retrieve the user's selected entity from session */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the entity id using bean class */
				holiday.getId().setEntityId(entityId);
			}
			/*
			 * Retrieve the holidays from database based on the parameters
			 * passed
			 */
			holidaysColl = holidayManager.getHolidayList(entityId, hostId);
			/* Retrieve the current system formats from session */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			itr = holidaysColl.iterator();
			/*
			 * Retrieve the holiday date from bean class then convert it into
			 * date format and set as Holiday date
			 */
			while (itr.hasNext()) {
				holiday = (Holiday) (itr.next());
				holiday.setHolidayDate_Date(SwtUtil.formatDate(holiday.getId()
						.getHolidayDate(), sysformat.getDateFormatValue()));
			}
			request.setAttribute("holidayList", holidaysColl);
			setHoliday(holiday);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			request.setAttribute("methodName", "showDetails");
			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/* Condition to check the access rights. */
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			log.debug(this.getClass().getName() + " - [showDetails] - "
					+ "Exit");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "showDetails", HolidayAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method puts entity details in request based on user's access rights.
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");
		/* Method's local variable declaration */
		HttpSession session = null;
		/* Collection hold the entity list */
		Collection<EntityUserAccess> entityColl = null;
		/* Collection hold the entity label list */
		Collection<LabelValueBean> labelColl = null;
		try {
			session = request.getSession();
			/* Retrieve the user's entity Access List from session */
			entityColl = SwtUtil.getUserEntityAccessList(session);
			/*
			 * Retrieve the user's entity and default entity List from entity
			 * collection
			 */
			labelColl = SwtUtil
					.convertEntityAcessCollectionLVLWithDefault(entityColl);
			/* Set collection for combobox in screen */
			request.setAttribute("entities", labelColl);
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [putEntityListInReq] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putEntityListInReq", this.getClass());
		} finally {
			/* To set null value for method local variable's */
			entityColl = null;
			labelColl = null;
			session = null;
		}
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Exit");

	}

	/**
	 * This is used to set the status of button to request.
	 *
	 * @param req
	 * @param addStatus
	 * @param deleteStatus
	 * @param saveStatus
	 * @param cancelStatus
	 * @return
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String deleteStatus, String saveStatus, String cancelStatus) {
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Entry");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Exit");
	}

	/**
	 * This method is used to export the data in Holiday Screen
	 * @throws SwtException
	 */
	public String exportHoliday()
			throws SwtException {

		/* Local variable declaration and initialization */
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold exportType
		String exportType = null;
		// Variable to hold holiday
		Holiday holiday = null;
		// Variable to hold holidaysColl
		Collection holidaysColl = null;
		// Variable to hold sysformat
		SystemFormats sysFormat = null;
		// Variable to hold holidayIterate
		Iterator holidayIterate = null;
		// Variable to hold fileName
		String fileName = null;
		// Variable to hold titleSuffix
		String titleSuffix = null;
		// Variable to hold filterData
		ArrayList<FilterDTO> filterData = null;
		// Variable to hold fDTO
		FilterDTO fDTO = null;
		// Variable to hold hxHm
		Obj2XmlHoliday hxHm = null;
		// Variable to hold csvResponse
		String csvResponse = null;
		// Variable declaration for pdfGen
		Obj2PdfImpl pdfGen = null;
		// Variable declaration for csvGen
		Obj2CsvImpl csvGen = null;
		// Variable declaration for excelGen
		Obj2XlsImpl excelGen = null;
		//
		ArrayList<ArrayList<ExportObject>> data = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName()
					+ "- [exportHoliday] - Exiting ");
			/* Gets the host id from swtutil file */
			hostId = SwtUtil.getCurrentHostId();
			/* Read host id and export type from request */
			entityId = request.getParameter("entityId");
			// Getting exportType from request
			exportType = request.getParameter("exportType");

			holiday = getHoliday();




			/* Setting host is and entity id using bean class */
			holiday.getId().setHostId(hostId);
			// Setting entityId using bean class
			holiday.getId().setEntityId(entityId);
			/* Fetches holidays details from DB by calling manager class */
			holidaysColl = holidayManager.getHolidayList(entityId, hostId);
			/* Reterive the currenct system format */
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			holidayIterate = holidaysColl.iterator();
			/*
			 * Retrieve the holiday date from bean class then convert it into
			 * date format and set as Holiday date
			 */
			while (holidayIterate.hasNext()) {
				holiday = (Holiday) (holidayIterate.next());
				holiday.setHolidayDate_Date(SwtUtil.formatDate(holiday.getId()
						.getHolidayDate(), sysFormat.getDateFormatValue()));
			}

			/* Used to put the entity list in request */
			putEntityListInReq(request);
			/* Set and display the data in defined format */
			filterData = new ArrayList<FilterDTO>();
			fDTO = new FilterDTO();

			fDTO.setName("Entity");
			fDTO.setValue(entityId);
			filterData.add(fDTO);

			/* Gets the Screen name from request */
			fileName = request.getParameter("screen").replaceAll(" ", "");
			titleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");
			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			/* check whether titleSuffix is null and set empty string */
			if (titleSuffix == null) {
				titleSuffix = "";
			}
			hxHm = new Obj2XmlHoliday();

			data  = hxHm.getExportData(HolidayColumnData
					.getColumnData(), filterData, (ArrayList) holidaysColl);
			/* To export the data in PDF,Excel and CSV format */
			try {

				if (exportType.equalsIgnoreCase("excel")) {
					excelGen = new Obj2XlsImpl();
					response.setContentType("application/vnd.ms-excel");
					response
							.setHeader("Content-disposition",
									"attachment; filename=" + fileName
											+ titleSuffix + "_"
											+ SwtUtil.FormatCurrentDate()
											+ ".xls");
					excelGen.convertObject(request, response, HolidayColumnData
							.getColumnData(), filterData, data, null,null, fileName);
				} else if (exportType.equalsIgnoreCase("pdf")) {
					pdfGen = new Obj2PdfImpl();
					response.setContentType("application/pdf");
					response
							.setHeader("Content-disposition",
									"attachment; filename=" + fileName
											+ titleSuffix + "_"
											+ SwtUtil.FormatCurrentDate()
											+ ".pdf");
					pdfGen.convertObject(request, response, HolidayColumnData
							.getColumnData(), filterData, data, null,null, fileName);
				} else if (exportType.equalsIgnoreCase("csv")) {
					csvGen = new Obj2CsvImpl();
					response.setContentType("application/vnd.ms-excel");
					response
							.setHeader("Content-disposition",
									"attachment; filename=" + fileName
											+ titleSuffix + "_"
											+ SwtUtil.FormatCurrentDate()
											+ ".csv");
					csvResponse = csvGen.convertObject(request, HolidayColumnData
							.getColumnData(), filterData, data, null,null, fileName);
					/*
					 * End Code modified by Chidambaranathan for include
					 * timestamp in the export function for Mantis_1513 on
					 * 04-Aug-2011
					 */
					try {
						response.getOutputStream().print(csvResponse);
						log.debug(this.getClass().getName()
								+ "- [exportHoliday] - Exiting ");
					} catch (IOException e) {
						log.error(this.getClass().getName()
								+ "- [exportHoliday] - IOException "
								+ e.getMessage());
						throw new SwtException(e.getMessage());
					}
				}
			} catch (SwtException swtexp) {
				log.error(this.getClass().getName()
						+ "- [exportHoliday] - SwtException "
						+ swtexp.getMessage());
				SwtUtil.logException(swtexp, request, "");
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [exportHoliday] - Exception " + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "exportHoliday", HolidayAction.class), request, "");
		} finally {
			// Nullifying the already created objects
			hostId = null;
			entityId = null;
			exportType = null;
			holiday = null;
			holidaysColl = null;
			sysFormat = null;
			holidayIterate = null;
			pdfGen = null;
			csvGen = null;
			excelGen = null;
			fileName = null;
			titleSuffix = null;
			filterData = null;
			fDTO = null;
			hxHm = null;
			csvResponse = null;
		}
		return null;
	}

}
