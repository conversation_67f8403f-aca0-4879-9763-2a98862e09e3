/*
 * @(#)GroupAction.java 1.0 15/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.web;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.context.ApplicationContext;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.GroupLevel;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.maintenance.service.GroupLevelManager;
import org.swallow.maintenance.service.GroupManager;
import org.swallow.maintenance.service.impl.GroupManagerImpl;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.springframework.beans.factory.annotation.Autowired;




import org.swallow.util.struts.ActionErrors;
import org.swallow.util.struts.ActionMessage;












/**
 * GroupAction.java
 *
 * This class is used to add, delete, change, get the Group details.
 */
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/group", "/group.do"})
public class GroupAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/groupmaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/maintenance/groupmaintenance");
		viewMap.put("change", "jsp/maintenance/groupmaintenanceadd");
		viewMap.put("bookcodes", "jsp/maintenance/groupmaintenancebookcodes");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "displayList":
				return displayList();
			case "add":
				return add();
			case "change":
				return change();
			case "delete":
				return delete();
			case "bookcodes":
				return bookcodes();
			case "save":
				return save();
			case "update":
				return update();
		}


		return displayList();
	}


	private Group group;
	public Group getGroup() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		group = RequestObjectMapper.getObjectFromRequest(Group.class, request);
		return group;
	}

	public void setGroup(Group group) {
		this.group = group;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("group", group);
	}



	private String selectedgroupCode;
	public String getSelectedgroupCode() {
		return selectedgroupCode;
	}
	public void setSelectedgroupCode(String selectedgroupCode) {
		this.selectedgroupCode = selectedgroupCode;
	}


	private static String methodFlag = "D";
	@Autowired
	private GroupManager grpMgr = null;
	@Autowired
	private GroupLevelManager grpLvlMgr = null;
	@Autowired
	private BookCodeManager bookCodeManager = null;
	private final Log log = LogFactory.getLog(GroupAction.class);
	private ApplicationContext ctx = null;

	/**
	 * This is used to set the group manager
	 *
	 * @param groupManager
	 * @return
	 */
	public void setGroupManager(GroupManager groupManager) {
		this.grpMgr = groupManager;
	}

	/**
	 * This is used to set the group level manager
	 *
	 * @param groupLevelManager
	 * @return
	 */
	public void setGroupLevelManager(GroupLevelManager groupLevelManager) {
		this.grpLvlMgr = groupLevelManager;
	}

	/**
	 * This is used to set book code manager
	 *
	 * @param bookCodeManager
	 * @return
	 */

	public void setBookCodeManager(BookCodeManager bookCodeManager) {
		this.bookCodeManager = bookCodeManager;
	}

	/**
	 * This method is default method in struts This method is called if no
	 * method are specified in the request
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [unspecified] - returns displayList method ");
		log.debug("exiting 'unspecified'  method");
		return displayList();
	}

	/**
	 * This method is used to load the group details in screen
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {
			log.debug(this.getClass().getName() + "- [displayList] - Entering");
			/* Methods local variable declarations */
			String hostId = "";
			Group group;
			String entityId = "";
			Integer groupLvlCode;
			Collection grpLvlList;
			Iterator itrlevel;
			Collection collGroup;
			Iterator groupIterator;
			Collection collBookCode;
			LabelValueBean labelValueBean = new LabelValueBean();
			SystemInfo systemInfo = new SystemInfo();
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			group = getGroup();



			entityId = group.getId().getEntityId();
			// If entity id from form object is null then Entity id is fetched
			// from session
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				group.getId().setEntityId(entityId);
			}
			// Sets the buttons status whether disabled or enabled
			setButtonStatus(request, entityId);
			group.getId().setHostId(hostId);
			// Sets the Group level list for dropdown
			putGroupLevelListInReq(request, entityId);
			// Fetches the group level code from bean object
			groupLvlCode = group.getGroupLvlCode();
			// If the group level code is null then get the group level code
			// list from Request
			if (groupLvlCode == null) {
				grpLvlList = (Collection) request.getAttribute("groupLevel");
				if ((grpLvlList != null) && grpLvlList.size() > 0) {
					itrlevel = grpLvlList.iterator();
					if (itrlevel.hasNext()) {
						labelValueBean = (LabelValueBean) (itrlevel.next());
						group.setGroupLvlCode(new Integer(labelValueBean
								.getValue()));
						groupLvlCode = group.getGroupLvlCode();
					}
				}
			}

			// Fetches the group details for group level code selected
			collGroup = grpMgr.getGroupList(entityId, hostId, groupLvlCode);

			groupIterator = collGroup.iterator();
			// To fetch the no of book code for each group
			while (groupIterator.hasNext()) {
				group = (Group) (groupIterator.next());
				/* This method is used to call BookCodeManager File */
				BookCodeManager bookCodeManager = (BookCodeManager) (SwtUtil
						.getBean("bookCodeManager"));
				/*
				 * Retrieve the book code details from DB based on the
				 * parameters passed
				 */
				collBookCode = bookCodeManager.getBookCodeList(hostId,
						entityId, group.getId().getGroupId());
				/* Set book codes from bean class */
				group.setNoOfBookCode(new Integer(collBookCode.size()));
			}
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			request.setAttribute("groupColl", collGroup);
			/* Getting Menu entity Currency group access from swtutil file */
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
			grpLvlList = (Collection) request.getAttribute("groupLevel");
			if (grpLvlList == null || grpLvlList.size() == 0) {
				request.setAttribute("disableAdd", "Y");
			}
			setGroup(group);
			log.debug(this.getClass().getName() + "- [displayList] - Exiting");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [displayList] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [displayList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", GroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This method is used to add the group details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Variable Declaration for entityId */
		String entityId = null;
		/* Variable Declaration for entityName */
		String entityName = null;
		/* Variable Declaration for grpLvlId */
		String grpLvlId = null;
		/* Variable Declaration for grpLvlName */
		String grpLvlName = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		Group group= null;
		/*
		 * Code midified by venkat on 19_mar_2011 for Mantis 1385:"Support for
		 * symbolic characters in book, group and metagroup names."
		 */
		/* Variable Declaration for groupName */
		String groupName = null;
		try {
			log.debug(this.getClass().getName() + "- [add] - Entering");
			/* Class instance declaration */
			request.setAttribute("screenFieldsStatus", "true");
			/* Reading Entity id from request */
			entityId = request.getParameter("entityCode");
			/* Reading Entity name from request */
			entityName = request.getParameter("entityName");
			/* Reading Group level id from request */
			grpLvlId = request.getParameter("gLevel");
			/* Reading Group level name from request */
			grpLvlName = request.getParameter("grpLvlName");

			group = new Group();

			/* Setting group level id */
			group.setGroupLvlCode(new Integer(grpLvlId));
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Setting entity id */
			group.getId().setEntityId(entityId);
			/* Condition to check entity id is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Retrieve user's entity from session */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Setting Entity id from bean class */
				group.getId().setEntityId(entityId);
			}
			request.setAttribute("entityName", entityName);
			request.setAttribute("grpLvlName", grpLvlName);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			/* Used to put the meta group list in request */
			putMetaGroupIdListInReq(request, entityId);
			request.setAttribute("methodName", "add");
			setGroup(group);
			log.debug(this.getClass().getName() + "- [add] - Exiting");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", GroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This is used to update the records in group screen
	 * @return a ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable declaration */
		/* Variable Declaration for entityId */
		String entityId = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		/* Variable Declaration for entityName */
		String entityName = null;
		/* Variable Declaration for grpLvlId */
		String grpLvlId = null;
		/* Variable Declaration for grpLvlName */
		String grpLvlName = null;
		/* Variable Declaration for groupId */
		String groupId = null;
		/*
		 * Code modified by venkat on 19_mar_2011 for Mantis 1385:"Support for
		 * symbolic characters in book, group and metagroup names."
		 */
		/* Variable Declaration for groupName */
		String groupName = null;
		/* Class Instance declaration */
		Group group = null;
		SystemInfo systemInfo = new SystemInfo();
		try {
			log.debug(this.getClass().getName() + "- [change] - Entering");
			request.setAttribute("screenFieldsStatus", "true");
			group = getGroup();



			/* Reading entity id from request */
			entityId = request.getParameter("entityCode");
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Reading entity name from request */
			entityName = request.getParameter("entityName");
			/* Reading group level from request */
			grpLvlId = request.getParameter("gLevel");
			/* Reading group level name from request */
			grpLvlName = request.getParameter("grpLvlName");
			/* Reading group id from request */
			groupId = request.getParameter("groupId");
			/*
			 * Start:Code midified by venkat on 19_mar_2011 for Mantis
			 * 1385:"Support for symbolic characters in book, group and
			 * metagroup names."
			 */
			/* get groupName from request and decode. */
			groupName = URLDecoder.decode(request.getParameter("groupName"),
					"UTF-8");
			/* set groupName in from */
			group.setGroupName(groupName);
			/*
			 * End:Code midified by venkat on 19_mar_2011 for Mantis
			 * 1385:"Support for symbolic characters in book, group and
			 * metagroup names."
			 */
			/* Setting group level code in request */
			group.setGroupLvlCode(new Integer(grpLvlId));
			/* Setting hostid in bean class */
			group.getId().setHostId(hostId);
			/* Setting group id in bean class */
			group.getId().setGroupId(groupId);
			/* Setting metagroup id from request */
			group.setMgroupId(request.getParameter("mgroupId"));
			/*
			 * Editable fields are fetches from DB based on the parameters
			 * passed
			 */
			group = grpMgr.getEditableData(hostId, entityId, groupId);
			request.setAttribute("metaGroup", new ArrayList());
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			group.setCutoffOffset(request.getParameter("cutoffOffset"));
			putMetaGroupIdListInReq(request, entityId);
			setGroup(group);
			request.setAttribute("methodName", "change");
			request.setAttribute("entityName", entityName);
			request.setAttribute("grpLvlName", grpLvlName);
			log.debug(this.getClass().getName() + "- [change] - Exiting");
			return getView("change");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", GroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This function is used to delete the group details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable declaration */
		String hostId = "";
		String groupId;
		String entityId = "";
		Integer groupLvlCode;
		Collection collGroup;
		/* Class instance declaration */
		ActionErrors errors;
		SystemInfo systemInfo = new SystemInfo();
		Group group = null;
		group = getGroup();



		errors = new ActionErrors();
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		/* Setting the hostid using bean class */
		group.getId().setHostId(hostId);
		entityId = group.getId().getEntityId();
		groupId = (String)  request.getParameter("selectedgroupCode");
		groupLvlCode = group.getGroupLvlCode();
		/* Setting the id for group in bean class */
		group.getId().setGroupId(groupId);
		try {
			log.debug(this.getClass().getName() + "- [delete] - Entering");
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			/* Used to put the group level list in request */
			putGroupLevelListInReq(request, entityId);
			request.setAttribute("metaGroup", new ArrayList());
			grpMgr.deleteGroupDetail(group);
			setGroup(group);
			putEntityListInReq(request);
			putGroupLevelListInReq(request, entityId);
			/*
			 * This method is used to get the group details list from DB based
			 * on the parameter values passed.
			 */
			collGroup = grpMgr.getGroupList(entityId, hostId, groupLvlCode);
			request.setAttribute("groupColl", collGroup);
			request.setAttribute("methodName", "displayList");
			group = new Group();
			request.setAttribute("metaGroup", new ArrayList());
			log.debug(this.getClass().getName() + "- [delete] - Exiting");
			return displayList();
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "delete");
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			/* Group List are fetched from DB based on the parameters passed */
			collGroup = grpMgr.getGroupList(entityId, hostId, groupLvlCode);
			request.setAttribute("groupColl", collGroup);
			swtexp.printStackTrace();
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return displayList();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", GroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This function is used to get the book codes details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String bookcodes()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + "- [bookcodes] - Entering");
			/* Class instance declaration */
			Group group;
			String entityId = "";
			String hostId = "";
			String groupCode = "";
			/* Class instance declaration */
			group = new Group();
			setGroup(group);
			SystemInfo systemInfo = new SystemInfo();
			// Retrieving parameters from the JSP
			entityId = request.getParameter("entityCode");
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Setting the host id and group id */
			group.getId().setHostId(hostId);
			groupCode = request.getParameter("groupId");
			/* Used to Put the book code list in request */
			putBookCodeListInReq(request, entityId, groupCode);
			log.debug(this.getClass().getName() + "- [bookcodes] - Exiting");
			return getView("bookcodes");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [bookcodes] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [bookcodes] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "bookcodes", GroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This is used to save the newly added group details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ActionErrors errors = new ActionErrors();
		/* Method's local variable declaration */
		String entityId = "";
		String hostId = "";
		String groupName;
		Integer groupLvlCode;
		/* Class Instance declaration */
		SystemInfo systemInfo = new SystemInfo();
		Group group = null;
		group = getGroup();



		entityId = group.getId().getEntityId();
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		/* Setting the host id using bean class */
		group.getId().setHostId(hostId);
		groupLvlCode = group.getGroupLvlCode();
		try {
			log.debug(this.getClass().getName() + "- [save] - Entering");
			groupName = group.getGroupLvlName();
			/* Used to save the updated details in database */
			grpMgr.saveGroupDetail(group);
			setGroup(group);
			request.setAttribute("methodName", "displayList");
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + "- [save] - Exiting");
			return displayList();
		} catch (SwtException swtexp) {
			setGroup(group);
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());

			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			putMetaGroupIdListInReq(request, entityId);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("methodName", "add");
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				errors.add("", new ActionMessage(swtexp.getErrorCode()));
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */

			return getView("add");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", GroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This is used to save the modified group details.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable declaration */
		String hostId = "";
		String entityId;
		String userId = "";
		String groupId;
		Integer groupLvlCode;
		String oldValue;
		String groupName = "";
		String newValue;
		int grpLevelDetail;
		/* Class instance declaration */
		SystemInfo systemInfo;
		systemInfo = new SystemInfo();
		Group group = null;
		/* Retrieve the group level manager file */
		GroupLevelManager groupLevelManager = (GroupLevelManager) (SwtUtil
				.getBean("groupLevelManager"));
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		group = getGroup();



		group.getId().setHostId(hostId);
		entityId = group.getId().getEntityId();
		groupId = group.getId().getGroupId();
		groupLvlCode = group.getGroupLvlCode();
		try {
			log.debug(this.getClass().getName() + "- [update] - Entering");
			group = getGroup();
			/* Appending the new values in buffer */
			newValue = new StringBuffer("Group-Name=").append(
					group.getGroupName()).append("^Group-Level=").append(
					group.getGroupLvlCode()).append("^MetaGroup-ID=").append(
					group.getMgroupId()).append("^Cut-Off Offset=").append(
					group.getCutoffOffset()).toString();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			group.setUpdateUser(userId);
			group.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));

			systemInfo.setIpAddress(request.getRemoteAddr());
			systemInfo.setNewLogString(newValue);
			groupName = group.getGroupLvlName();
			grpLevelDetail = groupLevelManager.getGroupLevelDetailsList(hostId,
					entityId, groupName);
			/*
			 * Modified group details are save in DB based on the parameters
			 * passed
			 */
			grpMgr.updateGroupDetail(group);
			request.setAttribute("parentFormRefresh", "yes");
			group = new Group();
			setGroup(group);
			request.setAttribute("methodName", "displayList");
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + "- [update] - Exiting");
			return displayList();
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("methodName", "change");
			putEntityListInReq(request);
			putMetaGroupIdListInReq(request, entityId);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("change");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", GroupAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to put the entity list in request
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering");
		/* Method's local variable declaration */
		HttpSession session = null;
		Collection coll;
		session = request.getSession();
		/* Getting user entity access details in collection variables */
		coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting");
	}

	/**
	 * This function is used to put the group level details in request
	 *
	 * @param request
	 * @param entityId
	 * @returns
	 * @throws SwtException
	 */
	private void putGroupLevelListInReq(HttpServletRequest request,
										String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putGroupLevelListInReq] - Entering");
		/* Method's local variable declaration */
		String hostId = "";
		Collection collGroupLevel = null;
		GroupLevel grpLevel = null;
		/* Class Instance declaration */
		GroupLevelManager groupLevelManager;
		groupLevelManager = (GroupLevelManager) (SwtUtil
				.getBean("groupLevelManager"));
		/* Retrieve and store hostId from Properties file using CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		/*
		 * This method is used to get group level name from DB based on the
		 * parameter values passed.
		 */
		collGroupLevel = groupLevelManager.getGroupLevelNamesList(hostId,
				entityId);
		request.setAttribute("groupLevel", collGroupLevel);
		log.debug(this.getClass().getName()
				+ "- [putGroupLevelListInReq] - Exiting");
	}

	/**
	 * This method sets the book code details in Request
	 *
	 * @param request
	 * @param entityId
	 * @param groupId
	 * @throws SwtException
	 */
	private void putBookCodeListInReq(HttpServletRequest request,
									  String entityId, String groupId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putBookCodeListInReq] - Entering");
		/* Method Local Variable declarations */
		SystemInfo systemInfo = new SystemInfo();
		String hostId = "";
		Collection collBookCode;
		// Gets an instance for book code Manager
		BookCodeManager bookCodeManager = (BookCodeManager) (SwtUtil
				.getBean("bookCodeManager"));
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		// Fetches the book code details for the host,Entity and group id.
		collBookCode = bookCodeManager.getBookCodeList(hostId, entityId,
				groupId);
		request.setAttribute("bookCode", collBookCode);
		log.debug(this.getClass().getName()
				+ "- [putBookCodeListInReq] - Exiting");
	}

	/**
	 * This is used to put the meta group list in request
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	private void putMetaGroupIdListInReq(HttpServletRequest request,
										 String entityId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [putMetaGroupIdListInReq] - Entering");
			/* Method's local variable declaration */
			String hostId = "";
			Collection collMetaGrp;
			/* Class instance declaration */
			SystemInfo systemInfo;
			systemInfo = new SystemInfo();
			/* Retrieve the group manager file */
			GroupManager grpManager = (GroupManager) (SwtUtil
					.getBean("groupManager"));
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Retrieve the meta group list from DB based on parameter value
			 * passed
			 */
			/*
			 * Start : Empty String is added in the first index of collMetaGrp
			 * (ArrayList) so as to show the list of Meta Group Ids in that
			 * order: Code modified for the issue found on v1051 beta2 by
			 * Marshal on 12-01-2011
			 */
			LabelValueBean labelValueBeanObj = null;
			collMetaGrp = new ArrayList();
			collMetaGrp.add(new LabelValueBean(SwtConstants.EMPTY_STRING,
					SwtConstants.EMPTY_STRING));
			collMetaGrp.addAll(grpManager.getMetaGroupList(hostId, entityId));
			/*
			 * End : Empty String is added in the first index of collMetaGrp
			 * (ArrayList) so as to show the list of Meta Group Ids in that
			 * order: Code modified for the issue found on v1051 beta2 by
			 * Marshal on 12-01-2011
			 */
			request.setAttribute("metaGroup", collMetaGrp);
			log.debug(this.getClass().getName()
					+ "- [putMetaGroupIdListInReq] - Exiting");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [putMetaGroupIdListInReq] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [putMetaGroupIdListInReq] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putMetaGroupIdListInReq", GroupManagerImpl.class);
		}
	}

	/**
	 * This is used to set the status of the button
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */

	private void setButtonStatus(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + "- [setButtonStatus] - Entering");
		// Set the button status according to entity access
		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		/*
		 * Retrieve and Store the user's menu,entity and currency group from
		 * swtutil file
		 */
		int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
				null);
		if (accessInd == 0) {
			/* This is used to set the button status by enable button */
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			/* This is used to set the button status by disable button */
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}
		log.debug(this.getClass().getName() + "- [setButtonStatus] - Exiting");
	}

	/**
	 * Setting Button status for view access and full access
	 *
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @param bkcodeStatus
	 * @param cancelStatus
	 * @return
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String bkcodeStatus,
								 String cancelStatus) {
		log.debug(this.getClass().getName() + "- [setButtonStatus] - Entering");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.BKCODE_BUT_STS, bkcodeStatus);
		log.debug(this.getClass().getName() + "- [setButtonStatus] - Exiting");
	}

}
