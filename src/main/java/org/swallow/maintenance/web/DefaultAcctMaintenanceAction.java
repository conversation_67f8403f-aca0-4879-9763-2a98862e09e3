/**
 * @(#)DefaultAcctMaintenanceAction.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 *
 * Created on July 25, 2007
 */
package org.swallow.maintenance.web;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.StringTokenizer;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.web.AccountAccessAction;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.DefaultAcctColumnData;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.Obj2CsvImpl;
import org.swallow.export.service.impl.Obj2PdfImpl;
import org.swallow.export.service.impl.Obj2XlsImpl;
import org.swallow.export.service.impl.Obj2XmlDefaultAcct;
import org.swallow.maintenance.model.AccountMaster;
import org.swallow.maintenance.model.DefaultAcct;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.maintenance.service.DefaultAcctMaintenanceManager;
import org.swallow.model.ExportObject;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;




import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;


/**
 * This is action class for Default Account screen.
 *
 * <AUTHOR> Beveridge
 */
















import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/defaultacctmaintenance", "/defaultacctmaintenance.do"})
public class DefaultAcctMaintenanceAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/defaultaccountmaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("listview", "jsp/maintenance/defaultaccountmaintenance");
		viewMap.put("change", "jsp/maintenance/defaultaccountmaintenanceadd");
		viewMap.put("close", "jsp/maintenance/defaultaccountmaintenanceclose");




	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		switch (method) {
			case "delete":
				return delete();
			case "save":
				return save();
			case "update":
				return update();
			case "add":
				return add();
			case "change":
				return change();
			case "unspecified":
				return unspecified();
			case "listview":
				return listview();
			case "exportDefaultacc":
				return exportDefaultacc();
			case "currencyAccessConfirm":
				return currencyAccessConfirm();
		}


		return unspecified();
	}


	private DefaultAcct defaultAcct;
	public DefaultAcct getDefaultAcct() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		defaultAcct = RequestObjectMapper.getObjectFromRequest(DefaultAcct.class, request);

		return defaultAcct;
	}

	public void setDefaultAcct(DefaultAcct defaultAcct) {
		this.defaultAcct = defaultAcct;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("defaultAcct", defaultAcct);
	}

	@Autowired
	private DefaultAcctMaintenanceManager defaultAcctMaintenanceManager = null;
	@Autowired
	private CurrencyManager currencyManager = null;
	private final Log log = LogFactory
			.getLog(DefaultAcctMaintenanceAction.class);

	/**
	 * Default account service layer setter
	 *
	 * @param manager
	 */
	public void setDefaultAcctMaintenanceManager(
			DefaultAcctMaintenanceManager manager) {
		this.defaultAcctMaintenanceManager = manager;
	}

	/**
	 * Currency service layer setter
	 *
	 * @param manager
	 */
	public void setCurrencyManager(CurrencyManager manager) {
		this.currencyManager = manager;
	}

	/**
	 * Action method called to delete a record
	 * @return ActionForward
	 * @throws Exception
	 */
	public String delete()
			throws Exception {
		// Variable to hold dyForm object
		// DynaValidatorForm dyForm = null;
		// Variable to hold defaultAcct object
		DefaultAcct defaultAcct = null;
		// Variable to hold cacheManagerInst object
		CacheManager cacheManagerInst = null;
		// String variable to hostId
		String hostId = null;
		// String variable to xrefCode
		String xrefCode = null;
		// String variable to currencyCode
		String currencyCode = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + "- [delete] - Entering ");
			// getting defaultAcct from the dyForm
			defaultAcct = (DefaultAcct) getDefaultAcct();
			// getting cacheManagerInst from the CacheManager
			cacheManagerInst = CacheManager.getInstance();
			// Asign hostId from cacheManagerInst
			hostId = cacheManagerInst.getHostId();
			// setting hostid to defaultAcct
			defaultAcct.getId().setHostId(hostId);
			// getting xrefCode in request
			xrefCode = request.getParameter("xrefCode");
			// setting XrefCode to defaultAcct
			defaultAcct.getId().setXrefCode(xrefCode);
			// getting currencyCode in request
			currencyCode = request.getParameter("currencyCode");
			// setting CurrencyCode to defaultAcct
			defaultAcct.getId().setCurrencyCode(currencyCode);
			defaultAcctMaintenanceManager.deleteRecord(defaultAcct);
			log.debug(this.getClass().getName() + "- [delete] - Exit ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [delete] - SwtException "
					+ swtexp.getMessage());
			SwtUtil.logErrorInDatabase(swtexp);
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [delete] - Exception "
					+ e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "delete",
							DefaultAcctMaintenanceAction.class));
			return getView("fail");
		} finally {
			// Nullifying the already created objects
			defaultAcct = null;
			hostId = null;
			xrefCode = null;
			cacheManagerInst = null;

		}

		return listview();
	}

	/**
	 * Action method called to save a new record
	 * @return ActionForward
	 * @throws Exception
	 */
	public String save()
			throws Exception {
		/* Methods local variable declaration */
		// DynaValidatorForm dyForm = null;
		ActionMessages errors = null;
		String entityId = null;
		String hostId = null;
		DefaultAcct defaultAcct = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			errors = new ActionMessages();
			defaultAcct = (DefaultAcct) getDefaultAcct();
			/* Read the host id from swtutil file and set using bean class */
			hostId = SwtUtil.getCurrentHostId();
			defaultAcct.getId().setHostId(hostId);
			/* Setting entity id using bean class */
			entityId = defaultAcct.getId().getEntityId();
			/* Save the records in DB by calling manager class */
			defaultAcctMaintenanceManager.saveRecord(defaultAcct);
			request.setAttribute("parentFormRefresh", "yes");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "save");

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			return getView("add");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "save",
							DefaultAcctMaintenanceAction.class));
			return getView("fail");
		} finally {
			Collection currencyList = getCurrencyList(request, CacheManager
					.getInstance().getHostId(), entityId);
			String currencyCode = getFirstValueFromCollection(currencyList);
			if (defaultAcct.getId().getCurrencyCode() != null)
				currencyCode = defaultAcct.getId().getCurrencyCode();
			Collection collAccount = defaultAcctMaintenanceManager
					.getAccountList(CacheManager.getInstance().getHostId(),
							entityId, currencyCode);

			putAccountsInRequest(request, collAccount);

			request.setAttribute("entityId", entityId);
			request.setAttribute("entityName", getLabelFromValue(
					getEntityList(request), entityId));
			request.setAttribute("currencies", currencyList);
			request.setAttribute("methodName", "save");
		}
		return getView("add");
	}

	/**
	 * Action method called to update an existing record
	 * @return ActionForward
	 * @throws Exception
	 */
	public String update()
			throws Exception {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			DefaultAcct defaultAcct = (DefaultAcct) getDefaultAcct();

			// the entityId, xrefCode and currencyCode are already in the form
			// bean from the post so just assign missing hostId
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			defaultAcct.getId().setHostId(hostId);
			defaultAcctMaintenanceManager.updateRecord(defaultAcct);
			request.setAttribute("parentFormRefresh", "yes");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [update] - SwtException "
					+ swtexp.getMessage());
			SwtUtil.logErrorInDatabase(swtexp);
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [update] - Exception "
					+ e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "update",
							DefaultAcctMaintenanceAction.class));
			return getView("fail");
		}
		return getView("add");
	}

	/**
	 * Action method called to show the new record adder form
	 * @return ActionForward
	 * @throws Exception
	 */
	public String add()
			throws Exception {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			DefaultAcct defaultAcct = (DefaultAcct) getDefaultAcct();

			// get the host id
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			defaultAcct.getId().setHostId(hostId);

			// get the entity id
			String entityId = null;
			if (request.getParameter("entityId") != null) {
				// coming from listview, in get params
				entityId = request.getParameter("entityId");
				defaultAcct.getId().setEntityId(entityId);
			} else {
				// coming from add view currency on change, in dynaform
				entityId = defaultAcct.getId().getEntityId();
			}
			Collection currencyList = getCurrencyList(request, hostId, entityId);
			Collection collAccount = null;
			if(currencyList != null && currencyList.size() >0) {
				// get the currency code set, defaults to all
				String currencyCode = getFirstValueFromCollection(currencyList);
				if (defaultAcct.getId().getCurrencyCode() != null)
					currencyCode = defaultAcct.getId().getCurrencyCode();

				// get a list of the accounts for this currency code
				collAccount = defaultAcctMaintenanceManager
						.getAccountList(hostId, entityId, currencyCode);
			}else  {
				collAccount = new ArrayList<LabelValueBean>() ;
			}
			putAccountsInRequest(request, collAccount);

			request.setAttribute("entityId", entityId);
			request.setAttribute("entityName", getLabelFromValue(
					getEntityList(request), entityId));
			request.setAttribute("currencies", currencyList);
			request.setAttribute("methodName", "save");
			setDefaultAcct(defaultAcct);
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [add] - SwtException "
					+ swtexp.getMessage());
			SwtUtil.logErrorInDatabase(swtexp);
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [add] - Exception "
					+ e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "add",
							DefaultAcctMaintenanceAction.class));
			return getView("fail");
		}

		return getView("add");
	}

	/**
	 * Action method called to show the update existing record (change form)
	 * @return ActionForward
	 * @throws Exception
	 */
	public String change()
			throws Exception {
		// Variable to the dynamic validator form
		// DynaValidatorForm dyForm = null;
		// Variable to the default account
		DefaultAcct defaultAcct = null;
		// To get the entity id
		String entityId = null;
		// To get the currency code
		String currencyCode = null;
		// To get the host id
		String hostId = null;
		// To get the default Acct Record from manager
		DefaultAcct defaultAcctRecord = null;
		// To get the currency list from manager
		Collection<CurrencyDetailVO> currencyList = null;
		// To get the Account Master List
		Collection<AccountMaster> collAccount = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [change] - Enter");
			// Gets the dynamic form
			// Gets the default account from dynamic form
			defaultAcct = (DefaultAcct) getDefaultAcct();

			// Get the entity id and set to the default account object
			entityId = request.getParameter("entityId");
			defaultAcct.getId().setEntityId(entityId);
			request.setAttribute("entityName", getLabelFromValue(
					getEntityList(request), entityId));
			// Get the xrefcode and set to the default account object
			defaultAcct.getId().setXrefCode(
					URLDecoder
							.decode(request.getParameter("xrefCode"), "UTF-8"));

			// Get the currencycode and set to the default account object
			currencyCode = request.getParameter("currencyCode");
			defaultAcct.getId().setCurrencyCode(currencyCode);

			// Get the host id and set to the default account object
			hostId = CacheManager.getInstance().getHostId();
			defaultAcct.getId().setHostId(hostId);
			// Get the default Acct Record from manager
			defaultAcctRecord = defaultAcctMaintenanceManager
					.getRecord(defaultAcct);
			// Get the currency list
			currencyList = currencyManager.getCurrencyDetailListWithAll(
					entityId, hostId, "all").getCurrencyList();
			currencyList = pruneFirstFromCollection(currencyList);
			// Get the Account Master List
			collAccount = defaultAcctMaintenanceManager.getAccountList(hostId,
					entityId, currencyCode);
			// Set the default account record in the dynamic form
			setDefaultAcct(defaultAcctRecord);
			// Sets the account collection to the request
			putAccountsInRequest(request, collAccount);
			// Update the accountName in the request
			request.setAttribute("accountName", defaultAcctRecord
					.getAccountMaster().getAccountName());
			// Update the currencyCode in the request
			request.setAttribute("currencyName", getLabelFromValue(
					currencyList, currencyCode));
			// Update the methodName in the request
			request.setAttribute("methodName", "update");
			log.debug(this.getClass().getName() + " - [change] - Exit");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName() + "- [change] - SwtException "
					+ swtExp.getMessage());
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [change] - Exception "
					+ e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "change",
							DefaultAcctMaintenanceAction.class));
			return getView("fail");
		} finally {
			// nullify the objects
			defaultAcct = null;
			entityId = null;
			currencyCode = null;
			hostId = null;
			defaultAcctRecord = null;
			currencyList = null;
			collAccount = null;
		}

		return getView("change");
	}

	/**
	 * Action method called when no 'method' paramater specified, ie when menu
	 * item is clicked
	 * @return ActionForward
	 * @throws Exception
	 */
	public String unspecified()
			throws Exception {
		return listview();
	}

	/**
	 * Action method called to show the tabular list of existing records
	 * @return ActionForward
	 * @throws Exception
	 */
	public String listview()
			throws Exception {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			DefaultAcct defaultAcct = (DefaultAcct) getDefaultAcct();

			// get the host id
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			defaultAcct.getId().setHostId(hostId);

			// get the entity id
			String entityId = defaultAcct.getId().getEntityId();

			// if the entity id is not in the form bean then use the user's
			// default
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				defaultAcct.getId().setEntityId(entityId);
			}

			// setup button access permissions
			if (SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null) == 0) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS);
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS);
			}

			// put entity list for drop-down in the request (constrained by
			// permissions)
			Collection entities = getEntityList(request);
			request.setAttribute("entities", entities);
			request.setAttribute("entityName", getLabelFromValue(entities,
					entityId));

			// put the list view data in the request
			request.setAttribute("listViewData", defaultAcctMaintenanceManager
					.getDefaultAccountList(entityId, hostId));

			setFilterSortInReq(request);

			// put the updated model bean back in the form bean
			setDefaultAcct(defaultAcct);
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ "- [listview] - SwtException " + swtexp.getMessage());
			SwtUtil.logErrorInDatabase(swtexp);
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [listview] - SwtException " + e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "listview",
							DefaultAcctMaintenanceAction.class));
			return getView("fail");
		}

		return getView("listview");
	}

	/**
	 * Utility method to put table filter state in request
	 *
	 * @param request
	 */
	private void setFilterSortInReq(HttpServletRequest request) {
		String selectedFilterStatus = request
				.getParameter("selectedFilterStatus");

		if (selectedFilterStatus == null) {
			selectedFilterStatus = SwtConstants.EMPTY_STRING;
		}

		String selectedSortStatus = request.getParameter("selectedSortStatus");

		if (selectedSortStatus == null) {
			selectedSortStatus = SwtConstants.EMPTY_STRING;
		}

		String selectedSortDescending = request
				.getParameter("selectedSortDescending");

		if (selectedSortDescending == null) {
			selectedSortDescending = SwtConstants.EMPTY_STRING;
		}

		StringTokenizer strtokens = new StringTokenizer(selectedFilterStatus,
				",");

		boolean filterStatusFlag = false;

		while (strtokens.hasMoreTokens()) {
			String nextToken = strtokens.nextToken();

			if (!nextToken.equals("special_all")
					&& !nextToken.equals("undefined")) {
				filterStatusFlag = true;
				break;
			}
		}

		if (!filterStatusFlag) {
			selectedFilterStatus = SwtConstants.EMPTY_STRING;
		}

		request.setAttribute("filterStatus", selectedFilterStatus);
		request.setAttribute("sortStatus", selectedSortStatus);
		request.setAttribute("sortDescending", selectedSortDescending);
	} // End of setFilterSortInReq

	/*
	 * Start: Code added by RK on 08-Mar-2012 for Mantis 1645
	 */
	/**
	 * Return a collection of entity label-value beans this user may see
	 *
	 * @param request
	 * @return Collection
	 */
	private Collection getEntityList(HttpServletRequest request)
			throws SwtException {
		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		return coll;
	}

	/*
	 * End: Code added by RK on 08-Mar-2012 for Mantis 1645
	 */

	/**
	 * Sets flags in request scope for the buttons that this user may access
	 *
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @param saveStatus
	 * @param cancelStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String saveStatus,
								 String cancelStatus) {
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
	}

	/**
	 * Given a collection of label-value beans, returns the 'label' from the
	 * first occurrence with a 'value' matching the given 'value' parameter
	 *
	 * @param coll
	 * @param value
	 * @return String
	 */
	private String getLabelFromValue(Collection coll, String value) {
		if (coll != null) {
			Iterator itr = coll.iterator();

			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				if (lvb.getValue().equals(value))
					return lvb.getLabel();
			}
		}
		return null;
	}

	/**
	 * Adds an empty label-value bean to the given collection and then puts it
	 * in request scope
	 *
	 * @param request
	 * @param accounts
	 */
	private void putAccountsInRequest(HttpServletRequest request,
									  Collection accounts) {
		Collection accountDropDown = new ArrayList();
		accountDropDown.add(new LabelValueBean("", ""));
		Iterator itr = accounts.iterator();
		while (itr.hasNext()) {
			LabelValueBean lvb = (LabelValueBean) (itr.next());
			accountDropDown.add(lvb);
		}

		request.setAttribute("accounts", accountDropDown);
	}

	private Collection pruneFirstFromCollection(Collection coll) {
		Iterator itr = coll.iterator();
		if (itr.hasNext())
			coll.remove(itr.next());
		return coll;
	}

	private String getFirstValueFromCollection(Collection coll)
			throws Exception {
		Iterator itr = coll.iterator();

		if (itr.hasNext())
			return ((LabelValueBean) itr.next()).getValue();

		SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
		SwtException swtexp = swtErrorHandler.handleException(new Exception(
						"Empty collection, expected at least one LabelValueBean"),
				"getFirstValueFromCollection",
				DefaultAcctMaintenanceAction.class);
		throw swtexp;
	}

	/**
	 *
	 * This function returns collection of currencies
	 *            entityId
	 * @return - collection of currencies
	 * @throws SwtException -
	 *             SwtException object
	 */
	private Collection getCurrencyList(HttpServletRequest request,
									   String hostId, String entityId) throws SwtException {
		// collection of currency drop down
		Collection<LabelValueBean> currencyDropDown = null;
		// Map for currency id , currency name
		Map<String, String> currencyMap = null;
		// To iterate currency key
		Iterator<String> itrCurrencyKey = null;
		// hold the role id
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [putCurrencyFullAccessListInReq] - Entering");
			// Instantiate the currency drop down
			currencyDropDown = new ArrayList<LabelValueBean>();
			// get the role id for current user
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the map for currency with full access
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);
			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					String currencyId = itrCurrencyKey.next();
					// add labelvaluebean for currency id
					currencyDropDown.add(new LabelValueBean(currencyId,
							currencyId));
				}
			}
			// set the currency drop down collection in request
			log.debug(this.getClass().getName()
					+ " - [putCurrencyFullAccessListInReq] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [putCurrencyFullAccessListInReq] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			currencyMap = null;
			itrCurrencyKey = null;
			roleId = null;
		}

		return currencyDropDown;
	}

	/**
	 * This is used to export Defalult Account details.
	 * @return
	 * @throws SwtException
	 */
	public String exportDefaultacc()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Local variable declaration and initialization */
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold exportType
		String exportType = null;
		// Variable to hold defaultAcct
		DefaultAcct defaultAcct = null;
		// Variable to hold dyForm
		// DynaValidatorForm dyForm = null;
		// Variable to hold entities
		Collection entities = null;
		// Variable to hold fileName
		String fileName = null;
		// Variable to hold titleSuffix
		String titleSuffix = null;
		// Variable to hold cacheManagerInst
		CacheManager cacheManagerInst = null;
		// Variable to hold filterData
		ArrayList<FilterDTO> filterData = null;
		// Variable to hold fDTO
		FilterDTO fDTO = null;
		// Variable to hold hxDa
		Obj2XmlDefaultAcct hxDa = null;
		// Variable to hold csvResponse
		String csvResponse = null;
		// Variable declaration for pdfGen
		Obj2XlsImpl excelObjGen = null;
		Obj2PdfImpl objPdfGen = null;
		Obj2CsvImpl csvObjGen = null;
		ArrayList<ArrayList<ExportObject>> data = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [exportDefaultacc] - Entering ");
			/* Gets the hostid from cachemanger class */
			cacheManagerInst = CacheManager.getInstance();
			hostId = cacheManagerInst.getHostId();
			/* Read the entity id and entityCode from request */
			entityId = request.getParameter("entityCode");
			/* Read the exportType and export type from request */
			exportType = request.getParameter("exportType");
			/* Converts the Action Form to DynaValidatorForm */
			/* Used to set the value in bean object */
			defaultAcct = (DefaultAcct) getDefaultAcct();
			/* Setting host id and entity id using bean class */
			defaultAcct.getId().setHostId(hostId);
			defaultAcct.getId().setEntityId(entityId);
			/* Fetches account details from DB by calling manager class */
			entities = defaultAcctMaintenanceManager.getDefaultAccountList(
					entityId, hostId);

			/* Set and display the data in defined format */
			filterData = new ArrayList<FilterDTO>();
			fDTO = new FilterDTO();

			fDTO.setName("Entity");
			fDTO.setValue(entityId);
			filterData.add(fDTO);

			/* Gets the Screen name from request */
			fileName = request.getParameter("screen").replaceAll(" ", "");
			titleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");
			/* check whether titleSuffix is null and set empty string */
			if (titleSuffix == null) {
				titleSuffix = "";
			}
			hxDa = new Obj2XmlDefaultAcct();

			data  = 	hxDa.getExportData(DefaultAcctColumnData
					.getColumnData(), filterData, (ArrayList) entities)	;

			/* To export the data in PDF,Excel and CSV format */
			try {
				if (exportType.equalsIgnoreCase("excel")) {
					response.setContentType("application/vnd.ms-excel");
					response
							.setHeader("Content-disposition",
									"attachment; filename=" + fileName
											+ titleSuffix + "_"
											+ SwtUtil.FormatCurrentDate()
											+ ".xls");

					excelObjGen = new Obj2XlsImpl();
					excelObjGen.convertObject(request, response,DefaultAcctColumnData
							.getColumnData(), filterData, data, null,null, fileName);
				} else if (exportType.equalsIgnoreCase("pdf")) {
					response.setContentType("application/pdf");
					response
							.setHeader("Content-disposition",
									"attachment; filename=" + fileName
											+ titleSuffix + "_"
											+ SwtUtil.FormatCurrentDate()
											+ ".pdf");


					objPdfGen = new Obj2PdfImpl();
					objPdfGen.convertObject(request, response,DefaultAcctColumnData
							.getColumnData(), filterData, data, null,null, fileName);


				} else if (exportType.equalsIgnoreCase("csv")) {
					response.setContentType("application/vnd.ms-excel");
					response
							.setHeader("Content-disposition",
									"attachment; filename=" + fileName
											+ titleSuffix + "_"
											+ SwtUtil.FormatCurrentDate()
											+ ".csv");

					csvObjGen = new Obj2CsvImpl();

					csvResponse = 	csvObjGen.convertObject(request, DefaultAcctColumnData
							.getColumnData(), filterData, data, null, null, fileName);
					try {
						response.getOutputStream().print(csvResponse);
						log.debug(this.getClass().getName()
								+ "- [exportDefaultacc] - Exiting ");
					} catch (IOException e) {
						log.error(this.getClass().getName()
								+ "- [exportDefaultacc] - IOException "
								+ e.getMessage());
						throw new SwtException(e.getMessage());
					}
				}
			} catch (SwtException swtexp) {
				log.error(this.getClass().getName()
						+ "- [exportDefaultacc] - SwtException "
						+ swtexp.getMessage());

				SwtUtil.logException(swtexp, request, "");
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [exportDefaultacc] - Exception " + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "exportDefaultacc", DefaultAcctMaintenanceAction.class),
					request, "");
		} finally {
			// Nullifying the already created objects
			hostId = null;
			entityId = null;
			exportType = null;
			defaultAcct = null;
			entities = null;
			fileName = null;
			titleSuffix = null;
			cacheManagerInst = null;
			filterData = null;
			fDTO = null;
			hxDa = null;
			csvResponse = null;
		}
		return null;
	}

	/**
	 * This is used to get the currency access confirmation
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String currencyAccessConfirm() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Local variable declaration and initialization */
		String hostId=null;
		String entityId=null;
		String currency=null;
		boolean flag = false;

		try {
			log.debug(this.getClass().getName() + " - [currencyAccessConfirm] - " + "Entry");
			/*Getting host id from cache manager file*/
			hostId = SwtUtil.getCurrentHostId();
			/*Getting account id,entity id,status from request*/
			entityId = request.getParameter("entityId");
			currency = request.getParameter("currency");
			/*Checking account access by calling manager file*/
			flag = SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId, entityId, currency);
			response.getWriter().print(flag);
			log.debug(this.getClass().getName() + " - [currencyAccessConfirm] - " + "Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [currencyAccessConfirm] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp,request,"");
			return getView("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "currencyAccessConfirm", AccountAccessAction.class), request, "");
		}

		return null;
	}
}
