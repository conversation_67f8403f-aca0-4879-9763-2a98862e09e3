package org.swallow.repository.impl;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.swallow.model.User;
import org.swallow.repository.UserDao;

import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Created by allan on 23/08/16.
 */
@Repository
public class UserDaoImpl implements UserDao {

    @PersistenceContext
    @Qualifier(value = "dbaEntityManager")
    private EntityManager entityManager;

    public User persist(User user) {
        entityManager.persist(user);
        return user;
    }
    
    public User authenticate(User user) {
        return (User) entityManager.createQuery("SELECT u FROM User u WHERE u.name = :username")
                .setParameter("username", user.getId().getUserId()).getSingleResult();
    }

	@Override
	public List<User> findAll() {
		return (List<User>) entityManager.createQuery("SELECT u FROM User u").getResultList();
	}

    
}
