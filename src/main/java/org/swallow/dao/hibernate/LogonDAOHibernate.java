/*
 * @(#)LogonDAOHibernate.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.FlushMode;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.LockMode;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.control.model.*;
import org.swallow.maintenance.model.Entity;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import org.swallow.control.dao.ChangePasswordDAO;
import org.swallow.control.dao.hibernate.UserMaintenanceDAOHibernate;
import org.swallow.control.service.ConnectionPoolControlManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.GroupLevel;
import org.swallow.model.MenuAccess;
import org.swallow.model.MenuItem;
import org.swallow.model.Profile;
import org.swallow.model.ScreenInfo;
import org.swallow.model.User;
import org.swallow.model.UserProfile;
import org.swallow.model.UserProfileDetail;
import org.swallow.util.JDBCCloser;
import org.swallow.util.StlHash;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.web.LogonAction;
import org.swallow.web.LogoutAction;
import org.swallow.util.SwtDataSource;

/**
 * <AUTHOR>
 *
 * This Class that implements the LogonDAO and acts as DAO layer for all
 * database operations
 *
 */
@Repository ("logonDAO")
@Transactional
public class LogonDAOHibernate extends CustomHibernateDaoSupport implements LogonDAO {

	private final Log log = LogFactory.getLog(LogonDAOHibernate.class);

	public LogonDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
		super(sessionfactory, entityManager);
	}

	/**
	 * This method resets the field <code>INV_PASS_ATTEMPT</code> in <code>S_USERS</code>
	 * when the user is logged-in successfully
	 * @param userId
	 * @throws SwtException
	 */
	public void resetInvalidAttempts(String userId) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + "- [resetInvalidAttempts] - Entering ");

			String hql = "from User c where c.id.userId = :userId and c.id.hostId = :hostId";
			TypedQuery<User> query = session.createQuery(hql, User.class);
			query.setParameter("userId", userId);
			query.setParameter("hostId", getHostIdFromDB());

			List<User> records = query.getResultList();

			if (!records.isEmpty()) {
				// Extracting User information from Database
				User usrFromDB = records.get(0);
				usrFromDB.setInvPassAttempt(0);

				Transaction tx = session.beginTransaction();
				session.update(usrFromDB);
				tx.commit();
			}

		} catch (Exception e) {
			log.error(this.getClass().getName() + "[resetInvalidAttempts] - Exception " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "resetInvalidAttempts", LogonDAOHibernate.class);
		}
	}


	/**
	 * This method verifies user's details in the database.
	 *
	 * @param user       User object
	 * @param ipAddress  IP address of the user
	 * @param processInt Optional process integer
	 * @return ArrayList<Object> containing user details and password rules
	 * @throws SwtException If an error occurs
	 */
	public ArrayList<Object> verifyUser(User user, String ipAddress, int... processInt) throws SwtException {
		log.debug(this.getClass().getName() + "- [verifyUser] - Entering ");

		ArrayList<Object> userAndPasswordRules = new ArrayList<>();
		User usrFromDB;
		Role roleObj = null;
		SystemLog systemLogObject = null;
		Password pwdRules;
		Date testDate;
		boolean isOldPasswordEncryption = false;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			Transaction tx = session.beginTransaction();

			// Retrieve user record with locking mechanism
			usrFromDB = session.get(User.class, new User.Id(user.getId().getHostId(), user.getId().getUserId()));

			if (usrFromDB != null) {
				userAndPasswordRules.add(usrFromDB);

				// Fetch user's role
				String roleQuery = "from Role r where r.hostId = :hostId and r.roleId = :roleId";
				TypedQuery<Role> roleQueryTyped = session.createQuery(roleQuery, Role.class);
				roleQueryTyped.setParameter("hostId", usrFromDB.getId().getHostId());
				roleQueryTyped.setParameter("roleId", usrFromDB.getRoleId());
				List<Role> roleList = roleQueryTyped.getResultList();

				if (!roleList.isEmpty()) {
					roleObj = roleList.get(0);
					usrFromDB.setAlertType(roleObj.getAlertType());
				}

				testDate = getTestDate(usrFromDB.getId().getHostId());

				// Validate if user is using an old encryption method
				isOldPasswordEncryption = (!SwtUtil.isEmptyOrNull(user.getPassword1()))
						&& SwtUtil.encodePassword(user.getPassword1(), user.getId().getUserId()).equals(usrFromDB.getPassword());

				// Check user status
				if (!SwtUtil.isEmptyOrNull(usrFromDB.getStatus()) && Integer.parseInt(usrFromDB.getStatus()) != 1) {
					systemLogObject = createSystemLogObject(usrFromDB, user.getPassword(), isOldPasswordEncryption, 4, 7);
					systemLogObject.setLogDate(testDate);
					systemLogObject.setIpAddress(ipAddress);
					session.save(systemLogObject);
				} else if (user.getPassword().equals(usrFromDB.getPassword()) || isOldPasswordEncryption) {
					// Fetch password rules
					pwdRules = getPasswordrule(user);
					userAndPasswordRules.add(pwdRules);

					// Check inactivity period
					long inActiveDisable = Long.parseLong(SwtUtil.getInactiveDisable());
					Date lastLogin = usrFromDB.getLastLogin();

					if (lastLogin != null && (((testDate.getTime() - lastLogin.getTime()) / (1000 * 60 * 60 * 24)) > inActiveDisable) && !user.isMfaUser()) {
						usrFromDB.setLastLogin(lastLogin);
						if (!user.getId().getUserId().equals(SwtConstants.ADMIN)) {
							usrFromDB.setStatus(SwtConstants.USER_DISABLE_STATUS);
							usrFromDB.setInactiveDisable(true);
						}
						systemLogObject = createSystemLogObject(usrFromDB, null, false, 3);
					} else {
						usrFromDB.setInactiveDisable(false);
						usrFromDB.setLastLogin(testDate);
					}

					if (processInt.length > 0) {
						systemLogObject = createSystemLogObject(usrFromDB, null, false, processInt[0]);
					}

					if (systemLogObject != null) {
						systemLogObject.setLogDate(testDate);
						systemLogObject.setIpAddress(ipAddress);
						session.save(systemLogObject);
					}

					if (user.getSecureId() == null) {
						usrFromDB.setInvPassAttempt(0);
					}

					if (isOldPasswordEncryption) {
						usrFromDB.setPassword(StlHash.getStlHash(user.getId().getHostId(), user.getId().getUserId(), user.getPassword1()));
					}

					usrFromDB.setLastLoginIp(ipAddress);
					session.update(usrFromDB);
				} else {
					pwdRules = getPasswordrule(user);
					userAndPasswordRules.add(pwdRules);

					int unSuccLoginAttempt = pwdRules.getUnsuccLoginAttempt();
					int toSetUnSuccLoginAttempt = processInt.length > 0 ? usrFromDB.getInvPassAttempt() : usrFromDB.getInvPassAttempt() + 1;

					if (unSuccLoginAttempt <= toSetUnSuccLoginAttempt && !user.isMfaUser()) {
						usrFromDB.setStatus(SwtConstants.USER_DISABLE_STATUS);
						usrFromDB.setInvPassAttempt(toSetUnSuccLoginAttempt);
					} else {
						usrFromDB.setInvPassAttempt(toSetUnSuccLoginAttempt);
					}

					usrFromDB.setLastLoginFailed(testDate);
					usrFromDB.setLastLoginFailedIp(ipAddress);

					systemLogObject = createSystemLogObject(usrFromDB, null, false, (toSetUnSuccLoginAttempt >= unSuccLoginAttempt) ? 8 : 2);
					systemLogObject.setLogDate(testDate);
					systemLogObject.setIpAddress(ipAddress);
					session.save(systemLogObject);

					session.update(usrFromDB);
				}
				tx.commit();
			} else {
				systemLogObject = createSystemLogObject(user, null, false, 2);
				systemLogObject.setLogDate(getTestDate(user.getId().getHostId()));
				session.save(systemLogObject);
			}
		} catch (Exception e) {
			log.error(this.getClass().getName() + "[verifyUser] - Exception " + e.getMessage(), e);
			logAndCleanLocks();
			throw SwtErrorHandler.getInstance().handleException(e, "verifyUser", LogonDAOHibernate.class);
		}

		log.debug(this.getClass().getName() + "- [verifyUser] - Exiting ");
		return userAndPasswordRules;
	}

	/**
	 * Helper method to create a system log object.
	 */
	private SystemLog createSystemLogObject(User user, String password, boolean isOldPasswordEncryption, int statusCode, int... alternativeCode) throws SwtException {
		int code = (password != null && (password.equals(user.getPassword()) || isOldPasswordEncryption)) ? statusCode : alternativeCode.length > 0 ? alternativeCode[0] : statusCode;
		return getSystemLogObject(user.getId().getUserId(), code);
	}


	private Password getPasswordrule(User user) throws SwtException {
		ChangePasswordDAO changePasswordDAO = (ChangePasswordDAO) SwtUtil
				.getBean("changePasswordDAO");
		Collection collPwdRules = changePasswordDAO.getPasswordRules(user);
		Password pwdRules = (Password) collPwdRules.iterator().next();
		return pwdRules;
	}

	public void saveUserStatus(UserStatus userStatus) throws SwtException {
		log.debug("Entering saveUserStatus");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		SystemLog systemLogObj = getSystemLogObject(userStatus.getUsers()
				.getId().getUserId(), 0);
		systemLogObj.setIpAddress(userStatus.getIpAddress());
		Date systemDate = getTestDate(userStatus.getId().getHostId());
		systemLogObj.setLogDate(systemDate);

		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();


			session.save(userStatus);
			session.save(systemLogObj);
			tx.commit();
		} catch (HibernateException e) {
			try {tx.rollback();} catch (Exception e2) { }
			log.error(this.getClass().getName() + "- [saveUserStatus] - Exception " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "saveUserStatus", LogonDAOHibernate.class);
		}finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}

		log.debug("Exiting saveUserStatus");
	}

	/**
	 * This method used to save the Password change and invPassAttempt events in
	 * system log.
	 *
	 * @param user
	 * @return
	 * @throws SwtException
	 */
	public void saveChangePassword(User user) throws SwtException {
		// variable to hold the systemLogObject
		SystemLog systemLogObject = null;
		// variable to hold the systemDate
		Date systemDate = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [saveChangePassword] - Entering ");
			// for setting status in system log
			systemLogObject = getSystemLogObject(user.getId().getUserId(), 6);
			// getting test date from SwtUtil class
			systemDate = getTestDate(user.getId().getHostId());
			// set the systemdate to model class
			systemLogObject.setLogDate(systemDate);
			// set the UserId to model class
			systemLogObject.setUserId(user.getUpdateUser());
			// save the values in model

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			session.save(systemLogObject);
			tx.commit();

			log.debug(this.getClass().getName()
					+ "- [saveChangePassword] - Exit ");
		} catch (Exception e) {
			try {tx.rollback();} catch (Exception e2) { }
			log.error(this.getClass().getName()
					+ "[saveChangePassword] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveChangePassword", LogonDAOHibernate.class);
		} finally {
			systemLogObject = null;
			systemDate = null;
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	/**
	 * This method used to update the process in the system log.
	 *
	 * @param userId
	 * @param process
	 * @return systemLogObj
	 */

	private SystemLog getSystemLogObject(String userId, int process)
			throws SwtException {
		// variable to hold the systemLogObj
		SystemLog systemLogObj = null;
		// String variable to hold passwordChange
		String passwordChange = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getSystemLogObject] - Entering ");
			// instantiates systemLogObj
			systemLogObj = new SystemLog();
			// setting host id to systemLogObj
			systemLogObj.setHostId(SwtUtil.getCurrentHostId());
			// setting UserId to systemLogObj
			systemLogObj.setUserId(userId);
			// setting IpAddress to systemLogObj
			systemLogObj.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
			// setting Action to systemLogObj
			systemLogObj.setAction("");

			if (process == 0) {
				// setting Process is Log on to systemLogObj
				systemLogObj.setProcess("Log on");
				resetInvalidAttempts(userId);
			} else if (process == 1) {
				// setting Process is Log off to systemLogObj
				systemLogObj.setProcess("Log off");
				resetInvalidAttempts(userId);
			} else if (process == 2) {
				// setting Process is Invalid Logon Attempt to systemLogObj
				systemLogObj.setProcess("Invalid Login Attempt");
			} else if (process == 3) {
				// setting Process is Disabled User Login to systemLogObj
				systemLogObj.setProcess("Disabled User Login");
			} else if (process == 4) {
				// setting Process is Locked User Login to systemLogObj
				systemLogObj.setProcess("Login attempted by locked user");
			} else if (process == 5) {
				// setting Process is User Already Logged to systemLogObj
				systemLogObj.setProcess("User Already Logged");
			} else if (process == 6) {
				passwordChange = "Password Change(" + userId + ")";
				// setting Process is passwordChange to systemLogObj
				systemLogObj.setProcess(passwordChange);
			} else if (process == 7) {
				// setting Process is Locked User Login to systemLogObj
				systemLogObj.setProcess("Invalid login attempt by locked user");
			} else if (process == 8) {
				// setting Process is Locked User Login to systemLogObj
				systemLogObj.setProcess("Invalid login attempt (User locked)");
			} else if (process == 9) {
				systemLogObj.setProcess("Invalid login attempt (RSA server timed out)");
			} else if (process == 10) {
				systemLogObj.setProcess("Invalid login attempt (DFA rejected)");
			}
			log.debug(this.getClass().getName()
					+ "- [getSystemLogObject] - Exit ");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ "[getSystemLogObject] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getSystemLogObject", LogonDAOHibernate.class);
		} finally {

			passwordChange = null;
		}
		return systemLogObj;
	}

	public void updateUserStatus(UserStatus userStatus) throws SwtException {
		log.debug("Entering updateUserStatus");
		SystemLog systemLogObj = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		boolean updateUserLogoutTime = false;
		systemLogObj = getSystemLogObject(userStatus.getUsers().getId()
				.getUserId(), 1);
		/* action entry added in case session times out due to inactivity. */
		if (userStatus.isSessionTimeOutInactivity() == true) {
			systemLogObj.setAction("Session Timeout");
			updateUserLogoutTime = true;
		}else if(!SwtUtil.isEmptyOrNull(userStatus.getKilledBy())) {
			systemLogObj.setAction("Session Killed");
			systemLogObj.setUpdateUser(userStatus.getKilledBy());
			updateUserLogoutTime = true;
		}
		systemLogObj.setIpAddress(userStatus.getIpAddress());
		Date systemDate = getTestDate(userStatus.getId().getHostId());
		systemLogObj.setLogDate(systemDate);
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Try to lock table s_user_status FOR UPDATE NO WAIT, ignore TransientObjectException as object might not be in DB
			UserStatus userStatusDb = (UserStatus)session.get(UserStatus.class, userStatus.getId(), LockMode.UPGRADE_NOWAIT);
			if(userStatusDb != null) {
				userStatus = userStatusDb.updateFrom(userStatus);
			}
			session.update(userStatus);
			session.save(systemLogObj);
			if(updateUserLogoutTime) {
				// Try to lock table s_users FOR UPDATE NO WAIT
				User usrFromDB = (User) session.get(User.class, new User.Id( userStatus.getId().getHostId(), userStatus.getId().getUserId()), LockMode.UPGRADE_NOWAIT);
				if (usrFromDB != null) {
					usrFromDB.setLastLogout(SwtUtil.getSystemDatewithTime());
					session.update(usrFromDB);
				}
			}
			tx.commit();
		}  catch (HibernateException e) {
			// Try rolling back when possible
			try {tx.rollback();} catch (Exception e2) { }
			log.error(this.getClass().getName() + "- [updateUserStatus] - Exception " + e.getMessage(), e);
			logAndCleanLocks();
			throw SwtErrorHandler.getInstance().handleException(e, "updateUserStatus", LogonDAOHibernate.class);
		}finally {
			JDBCCloser.close(session);

		}

		log.debug("Exiting updateUserStatus");
	}


	private void logAndCleanLocks(){
		// Any lock being detected from internal sources, will be logged
		try {
			long MAX_OPENED_THRESHOLD = 30*1000;
			// PCM do not handle user tables
			List<ConnectionPool> openConnections = ((SwtDataSource) SwtDataSource.getInstance()).getLastActiveConnections();

			// It may be monitoring not enabled
			final boolean disabledPoolMonitor;
			if(openConnections == null || openConnections.size() == 0) {
				disabledPoolMonitor = true;
				openConnections = ((SwtDataSource) SwtDataSource.getInstance()).getActiveConnections();
			}
			else {
				disabledPoolMonitor = false;
			}

			for(ConnectionPool cnxP:openConnections) {
				try {
					if(cnxP.getId() != null && cnxP.getId().getConnectionId() != null) {
						Connection conn = ((SwtDataSource) SwtDataSource.getInstance()).getStoredConnections().get(cnxP.getId().getConnectionId());
						cnxP.setConnection(conn);
						CompletableFuture.supplyAsync(() -> {
							try {
								return !conn.isClosed();
							} catch (Exception e) {
								return false;
							}
						}).get(15, TimeUnit.SECONDS);
					}
				} catch (Exception e) {
					cnxP.setStatus("LOCKED");
					log.error(" => Failed to test if connection is Closed after 15 seconds, probably a lock is associated with this connection ?: "+cnxP);
				}
			}

			// Filter listed connections
			openConnections = openConnections != null ? openConnections : Arrays.asList();
			openConnections = openConnections.stream().filter(cnxPool->{
				try {
					// When using the ConnectionPoolMonitor
					if(!disabledPoolMonitor) {
						return (cnxPool.getIsClosed() != null && !cnxPool.getIsClosed() );
					}
					else {
						// Return if connexion was marked as LOCKED
						return  "LOCKED".equalsIgnoreCase(cnxPool.getStatus()) || (cnxPool.getConnection() != null && !cnxPool.getConnection().isClosed());
					}
				} catch (Exception e2) {
					return false;
				}
			}).collect(Collectors.toList());

			log.error("==> Next are logged all opened JDBC connections (isClosed = false) : Total size = " +(openConnections != null ? openConnections.size() : 0));
			for (ConnectionPool cnxPool : openConnections){
				log.error(cnxPool);
				// Available connections that are opened since x seconds from org.swallow.dao.hibernate.LogonDAOHibernate should be closed !
				try {
					long openedSince = cnxPool.getStartTime() != null ? (new Date().getTime() - cnxPool.getStartTime().getTime()) : 0;
					if(!SwtUtil.isEmptyOrNull(cnxPool.getStackTrace()) &&
							(cnxPool.getStackTrace().contains(LogonDAOHibernate.class.getCanonicalName()) ||
									cnxPool.getStackTrace().contains(UserMaintenanceDAOHibernate.class.getCanonicalName()) ||
									cnxPool.getStackTrace().contains(LogoutAction.class.getCanonicalName()) ||
									cnxPool.getStackTrace().contains(LogonAction.class.getCanonicalName())) &&
							openedSince > MAX_OPENED_THRESHOLD ) {
						log.error(" => Found unclosed JDBJ connection, opened for more than " + MAX_OPENED_THRESHOLD + " millis (" + openedSince + " millis). Predict will proceed on closing it !: "+cnxPool);
						// Asynchronous rollback and close
						CompletableFuture.supplyAsync(() -> {
							if(!disabledPoolMonitor) {
								ConnectionPoolControlManager connectionPoolControlManager = (ConnectionPoolControlManager) (SwtUtil
										.getBean("connectionPoolControlManager"));
								try {
									connectionPoolControlManager.killDBSessionConnections("PREDICT", cnxPool.getId().getConnectionId());
								} catch (SwtException e) {
								}
							}
							JDBCCloser.close(cnxPool.getConnection());
							return true;
						}).get(20, TimeUnit.SECONDS);
					}
				} catch (Exception e1) {
					log.error("Error on closing "+cnxPool+", cause: "+e1.getMessage());
				}
			};
		} catch (Exception e2) {
			log.error("Error on method [logAndCleanLocks], cause: "+e2.getMessage());
		}
	}


	public Collection getProfileList(User user) throws SwtException {
		log.debug(this.getClass().getName() + "- [getProfileList] - Entering");

		List<UserProfile> userList;
		List<Profile> profileList;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hqlUserProfile = "Select u from UserProfile u Where u.id.hostId = :hostId AND u.id.userId = :userId Order By u.id.profileId";
			String hqlProfile = "Select p from Profile p Where p.id.hostId = :hostId Order By p.id.profileId";

			TypedQuery<UserProfile> userProfileQuery = session.createQuery(hqlUserProfile, UserProfile.class);
			userProfileQuery.setParameter("hostId", user.getId().getHostId());
			userProfileQuery.setParameter("userId", user.getId().getUserId());
			userList = userProfileQuery.getResultList();

			TypedQuery<Profile> profileQuery = session.createQuery(hqlProfile, Profile.class);
			profileQuery.setParameter("hostId", user.getId().getHostId());
			profileList = profileQuery.getResultList();
		}

		for (Profile profile : profileList) {
			boolean exists = userList.stream().anyMatch(up -> up.getId().getProfileId().equals(profile.getId().getProfileId()));
			if (!exists) {
				UserProfile userProfile = new UserProfile();
				UserProfile.Id uId = new UserProfile.Id(user.getId().getHostId(), user.getId().getUserId(), profile.getId().getProfileId());
				userProfile.setUserProfileName(profile.getProfileName());
				userProfile.setId(uId);
				userList.add(userProfile);
			}
		}

		userList.sort(Comparator.comparing(UserProfile::getUserProfileName));
		log.debug(this.getClass().getName() + "- [getProfileList] - Exiting");
		return userList;
	}

	public Collection getUserProfileDetails(User user) throws SwtException {
		log.debug(this.getClass().getName() + "- [getUserProfileDetails] - Entering");

		List<UserProfileDetail> userList;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select u from UserProfileDetail u Where u.id.hostId = :hostId AND u.id.userId = :userId "
					+ "AND u.userProfile.currentProfile = 'Y' Order By u.id.profileId";
			TypedQuery<UserProfileDetail> query = session.createQuery(hql, UserProfileDetail.class);
			query.setParameter("hostId", user.getId().getHostId());
			query.setParameter("userId", user.getId().getUserId());
			userList = query.getResultList();
		}

		Collection<MenuItem> menuitemids = getMenuList(user);
		Collection<UserProfileDetail> userProfileList = new ArrayList<>();

		for (UserProfileDetail userProfile : userList) {
			boolean idFlag = false;
			for (MenuItem menuAccess : menuitemids) {
				if (menuAccess.getItemId().equals(userProfile.getId().getMenuItemId())) {
					userProfile.getMenuItem().setAccessId(menuAccess.getAccessId());
					idFlag = true;
					break;
				}
			}
			if (idFlag) {
				userProfileList.add(userProfile);
			}
		}

		log.debug(this.getClass().getName() + "- [getUserProfileDetails] - Exiting");
		return userProfileList;
	}

	public Collection getShortcutList(User user) throws SwtException {
		log.debug(this.getClass().getName() + "- [getShortcutList] - Entering");

		List<Shortcut> list;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select s from Shortcut s where s.id.hostId = :hostId AND s.id.userId = :userId ORDER BY s.id.shortcutId";
			TypedQuery<Shortcut> query = session.createQuery(hql, Shortcut.class);
			query.setParameter("hostId", user.getId().getHostId());
			query.setParameter("userId", user.getId().getUserId());
			list = query.getResultList();
		}

		log.debug(this.getClass().getName() + "- [getShortcutList] - Exiting");
		return list;
	}

	public Collection getMenuList(User user) throws SwtException {
		log.debug(this.getClass().getName() + "- [getMenuList] - Entering");

		List<MenuItem> lstMenu;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "SELECT ma.menuItem.itemId, ma.menuItem.description, ma.menuItem.parentId, "
					+ "ma.menuItem.menuOrder, ma.menuItem.menuGroupOrder, ma.menuItem.imageName, "
					+ "ma.menuItem.width, ma.menuItem.height, ma.accessId, ma.menuItem.program.programName "
					+ "FROM MenuAccess ma INNER JOIN ma.menuItem ON (ma.menuItem.itemId = ma.id.itemId) "
					+ "LEFT OUTER JOIN Program p ON (ma.menuItem.program = p) "
					+ "WHERE ma.id.roleId = :roleId "
					+ "ORDER BY TO_NUMBER(ma.menuItem.parentId), ma.menuItem.menuGroupOrder, ma.menuItem.menuOrder";

			TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
			query.setParameter("roleId", user.getRoleId());
			List<Object[]> results = query.getResultList();

			lstMenu = new ArrayList<>();
			for (Object[] record : results) {
				int paramIndex = 0;
				MenuItem menuItem = new MenuItem();
				menuItem.setItemId((String) record[paramIndex++]);
				menuItem.setDescription((String) record[paramIndex++]);
				menuItem.setParentId((String) record[paramIndex++]);
				menuItem.setMenuOrder((Integer) record[paramIndex++]);
				menuItem.setMenuGroupOrder((Integer) record[paramIndex++]);
				menuItem.setImageName((String) record[paramIndex++]);
				menuItem.setWidth((Integer) record[paramIndex++]);
				menuItem.setHeight((Integer) record[paramIndex++]);
				menuItem.setAccessId((String) record[paramIndex++]);
				menuItem.setProgramName((String) record[paramIndex++]);
				lstMenu.add(menuItem);
			}
		}

		log.debug(this.getClass().getName() + "- [getMenuList] - Exiting");
		return lstMenu;
	}
	public Collection getScreenList(User user) throws SwtException {
		log.debug(this.getClass().getName() + "- [getScreenList] - Entering ");

		List<MenuAccess> list;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "SELECT m FROM MenuAccess m "
					+ "WHERE m.id.roleId = :roleId "
					+ "AND m.menuItem.programId IS NOT NULL "
					+ "ORDER BY m.menuItem.parentId, m.menuItem.menuGroupOrder, m.menuItem.menuOrder";

			TypedQuery<MenuAccess> query = session.createQuery(hql, MenuAccess.class);
			query.setParameter("roleId", user.getRoleId());
			list = query.getResultList();

			// To solve LazyInitializationException
			list.forEach(item -> Hibernate.initialize(item.getMenuItem()));
		}

		log.debug(this.getClass().getName() + "- [getScreenList] - Exiting ");
		return list;
	}

	public Collection getScreenList() throws SwtException {
		log.debug(this.getClass().getName() + "- [getScreenList] - Entering ");

		List<MenuItem> list;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "SELECT m FROM MenuItem m "
					+ "WHERE m.programId IS NOT NULL "
					+ "ORDER BY m.description";

			TypedQuery<MenuItem> query = session.createQuery(hql, MenuItem.class);
			list = query.getResultList();
		}

		log.debug(this.getClass().getName() + "- [getScreenList] - Exiting ");
		return list;
	}

	/*
	 * Start:code modified by sudhakar for Mantis 1969 on 29-Jun-2012:Emptying
	 * Group level names and save the entity causes problem
	 */
	/**
	 * This method used to get current user entity access list from database and
	 * setting the group level and group level name in the entity access list
	 *
	 * @param roleId
	 * @param hostId
	 * @return entityUserAccessList
	 */
	public Collection<EntityUserAccess> getEntityAccessList(String roleId,
															String hostId) throws SwtException {
		// Variable to hold the entityUserAccessList object
		ArrayList<EntityUserAccess> entityUserAccessList = null;
		// Variable to hold the entityAccessRecords object
		List entityAccessRecords = null;
		// Variable to hold the itrEntityAccessRecords object
		Iterator itrEntityAccessRecords = null;
		// Variable to hold the rowEntityAccessRecords object
		Object[] rowEntityAccessRecords = null;
		// Variable to hold the entityUserAccess object
		EntityUserAccess entityUserAccess = null;
		// Variable to hold the groupLevelColl object
		Collection<GroupLevel> groupLevelColl = null;
		// Variable to hold the groupLevelArr object
		Object[] groupLevelArr = null;
		// String Variable to hold the groupLevelName
		String groupLevelName = null;
		// Variable to hold the grouplevel object
		GroupLevel groupLevel = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getEntityAccessList] - Entering ");
			// instantiate the entityUserAccessList
			entityUserAccessList = new ArrayList<EntityUserAccess>();

			String hql = "select s.id.entityId, s.entityName, s.domesticCurrency, e.accessId " +
					"from Entity s, EntityAccess e " +
					"where e.id.roleId = :roleId " +
					"and s.id.hostId = :hostId " +
					"and e.id.hostId = :hostId2 " +
					"and s.id.entityId = e.id.entityId " +
					"order by s.id.entityId";
			session = getHibernateTemplate().getSessionFactory().openSession();
			TypedQuery<?> query = session.createQuery(hql);
			query.setParameter("roleId", roleId);
			query.setParameter("hostId", hostId);
			query.setParameter("hostId2", hostId);

			entityAccessRecords = query.getResultList();
			itrEntityAccessRecords = entityAccessRecords.iterator();
			// iterate the Entity Access Records
			while (itrEntityAccessRecords.hasNext()) {
				rowEntityAccessRecords = (Object[]) itrEntityAccessRecords
						.next();
				entityUserAccess = new EntityUserAccess(
						(String) rowEntityAccessRecords[0],
						(String) rowEntityAccessRecords[1], (String) rowEntityAccessRecords[2], Integer
						.parseInt((String) rowEntityAccessRecords[3]));
				// get the group level name
				groupLevelColl = getGroupLevelNames(hostId,
						(String) rowEntityAccessRecords[0]);
				// Setting the GrpLevelName1,2,3 in the entityUserAccess object
				if (groupLevelColl != null && groupLevelColl.size() == 3) {
					groupLevelArr = groupLevelColl.toArray();
					groupLevel = ((GroupLevel) groupLevelArr[0]);
					groupLevelName = groupLevel.getGroupLevelName();
					if (groupLevelName != null && groupLevelName.length() > 15)
						groupLevelName = groupLevelName.substring(0, 15);
					// Set the GrpLevelName1 in entityUserAccess object
					entityUserAccess.setGrpLevelName1(groupLevelName);

					groupLevel = ((GroupLevel) groupLevelArr[1]);
					groupLevelName = groupLevel.getGroupLevelName();
					if (groupLevelName != null && groupLevelName.length() > 15)
						groupLevelName = groupLevelName.substring(0, 15);
					// Set the GrpLevelName2 in entityUserAccess object
					entityUserAccess.setGrpLevelName2(groupLevelName);

					groupLevel = ((GroupLevel) groupLevelArr[2]);
					groupLevelName = groupLevel.getGroupLevelName();
					if (groupLevelName != null && groupLevelName.length() > 15)
						groupLevelName = groupLevelName.substring(0, 15);
					// Set the GrpLevelName3 in entityUserAccess object
					entityUserAccess.setGrpLevelName3(groupLevelName);
				}
				// set the entityUserAccess in list
				entityUserAccessList.add(entityUserAccess);
			}
			log.debug(this.getClass().getName()
					+ "- [getEntityAccessList] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "[getEntityAccessList] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getEntityAccessList", LogonDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());

			// nullify the object
			entityAccessRecords = null;
			itrEntityAccessRecords = null;
			rowEntityAccessRecords = null;
			entityUserAccess = null;
			groupLevelColl = null;
			groupLevelArr = null;
			groupLevelName = null;
			groupLevel = null;
		}
		return entityUserAccessList;
	}

	/*
	 * End:code modified by sudhakar for Mantis 1969 on 29-Jun-2012:Emptying
	 * Group level names and save the entity causes problem
	 */
	private Collection<GroupLevel> getGroupLevelNames(String hostId, String entityId) throws SwtException {
		Session session = null;
		try {
			// Open a session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query
			String hql = "from GroupLevel c " +
					"where c.id.hostId = :hostId " +
					"and c.id.entityId = :entityId " +
					"order by c.id.groupLevelId asc";

			// Create a TypedQuery
			TypedQuery<GroupLevel> query = session.createQuery(hql, GroupLevel.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			// Execute the query and return results
			return query.getResultList();
		} catch (HibernateException e) {
			// Handle any Hibernate exception
			throw new SwtException("Error fetching group level names: " + e.getMessage(), e);
		} finally {
			// Ensure session is closed
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null) {
				throw new SwtException("Error closing session: " + hThrownException.getMessage(), hThrownException);
			}
		}
	}

	public Collection getDomesticCurrency(String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getDomesticCurrency] - Entry");

		List<Entity> noofRecords;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from Entity c where c.id.entityId = :entityId and c.id.hostId = :hostId";
			TypedQuery<Entity> query = session.createQuery(hql, Entity.class);
			query.setParameter("entityId", entityId);
			query.setParameter("hostId", hostId);
			noofRecords = query.getResultList();
		}

		log.debug(this.getClass().getName() + " - [getDomesticCurrency] - Exit");
		return noofRecords;
	}

	/**
	 * This method updates the system log when logged in user attempts to log-in
	 * again
	 *
	 * @param user
	 * @param ipAddress
	 * @throws SwtException
	 */
	public void enterLoggedInUserStatus(User user, String ipAddress)
			throws SwtException

	{
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			User usrFromDB = (User) session.get(User.class, new User.Id( user.getId().getHostId(), user.getId().getUserId()), LockMode.UPGRADE_NOWAIT);

			if (usrFromDB != null) {
				SystemLog systemLogObject = getSystemLogObject(usrFromDB.getId()
						.getUserId(), 5);

				Date systemDate = getTestDate(usrFromDB.getId().getHostId());
				systemLogObject.setLogDate(systemDate);

				session.update(usrFromDB);
				systemLogObject.setIpAddress(ipAddress);

				session.save(systemLogObject);
			}
			tx.commit();
		} catch (HibernateException e) {
			try {tx.rollback();} catch (Exception e2) { }
			log.error(this.getClass().getName() + "- [enterLoggedInUserStatus] - Exception " + e.getMessage());
			logAndCleanLocks();
			throw SwtErrorHandler.getInstance().handleException(e, "enterLoggedInUserStatus", LogonDAOHibernate.class);
		}finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see org.swallow.dao.LogonDAO#saveUserStatusRow(org.swallow.control.model.UserStatus)
	 */
	public void saveUserStatusRow(UserStatus userStatus) throws SwtException {
		log.debug("Entering saveUserStatusRow");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			session.save(userStatus);
			tx.commit();
		} catch (HibernateException e) {
			try {tx.rollback();} catch (Exception e2) { }
			log.error(this.getClass().getName() + "- [saveUserStatusRow] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "saveUserStatusRow", LogonDAOHibernate.class);
		}finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}

		log.debug("Exiting saveUserStatusRow");

	}

	/**
	 * This method update the user details to the database.
	 *
	 * @param user
	 * @throws SwtException
	 */
	public void updateUserDetail(User user) throws SwtException {
		log.debug(this.getClass().getName() + "- [updateUserDetail] - Entering ");

		Transaction tx = null;
		SwtInterceptor interceptor;
		User oldDetails = null;

		try (Session session = getHibernateTemplate().getSessionFactory()
				.withOptions().interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {

			tx = session.beginTransaction();

			oldDetails = getUserDetail(SwtUtil.getCurrentHostId(), user.getId().getUserId());
			if (oldDetails != null) {
				user.setLastLoginFailedIp(oldDetails.getLastLoginFailedIp());
				user.setLastLoginIp(oldDetails.getLastLoginIp());
				user.setLastLoginFailed(oldDetails.getLastLoginFailed());
			}

			session.update(user);

			if (user.getUserAuthDetails() != null) {
				String hql = "from UserAuthDetails c where c.id.userId = :userId and c.id.hostId = :hostId";
				TypedQuery<UserAuthDetails> query = session.createQuery(hql, UserAuthDetails.class);
				query.setParameter("userId", user.getId().getUserId());
				query.setParameter("hostId", user.getId().getHostId());

				List<UserAuthDetails> records = query.getResultList();

				if (!records.isEmpty()) {
					UserAuthDetails existingUserAuthDetails = records.get(0);
					user.getUserAuthDetails().setId(existingUserAuthDetails.getId()); // Ensure the ID is set
					session.merge(user.getUserAuthDetails()); // Use merge instead of update
				} else {
					session.save(user.getUserAuthDetails());
				}
			}

			tx.commit();
		} catch (HibernateException e) {
			if (tx != null) {
				try {
					tx.rollback();
				} catch (Exception e2) {
					log.error("Rollback failed", e2);
				}
			}
			log.error(this.getClass().getName() + "- [updateUserDetail] - Exception " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "updateUserDetail", LogonDAOHibernate.class);
		}

		log.debug(this.getClass().getName() + "- [updateUserDetail] - Exiting ");
	}



	/**
	 * This method returns the test date used by the system. If it is null it
	 * returns system date with time.
	 *
	 * @return testDate Date
	 * @throws SwtException
	 */
	private Date getTestDate(String hostId) throws SwtException {
		Date testDate = SwtUtil.getTestDateFromParams(hostId);
		return testDate;
	}

	public Collection getAccessIdOfShortcut(String roleId, String menuItemId) throws SwtException {
		log.debug("entering getAccessIdOfShortcut roleId ");

		List<MenuAccess> list;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select m from MenuAccess m WHERE m.id.roleId = :roleId AND m.id.itemId = :menuItemId";
			TypedQuery<MenuAccess> query = session.createQuery(hql, MenuAccess.class);
			query.setParameter("roleId", roleId);
			query.setParameter("menuItemId", menuItemId);
			list = query.getResultList();
		}

		log.debug(this.getClass().getName() + "- [getAccessIdOfShortcut] - Exiting ");
		return list;
	}

	public User getUserDetail(String hostId, String userId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getUserDetail] - Entering ");

		User user = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from User u where u.id.hostId = :hostId and u.id.userId = :userId";
			TypedQuery<User> query = session.createQuery(hql, User.class);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			List<User> userList = query.getResultList();

			if (!userList.isEmpty()) {
				user = userList.get(0);
			}
		}

		log.debug(this.getClass().getName() + "- [getUserDetail] - Exiting");
		return user;
	}

	public Collection getLocationAccessList(String roleId, String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getLocationAccessList] - Entering");

		List<Object[]> noofRecords;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String accessQuery = "select r.restrictLocations from Role r where r.roleId = :roleId and r.hostId = :hostId";
			TypedQuery<String> accessQueryTyped = session.createQuery(accessQuery, String.class);
			accessQueryTyped.setParameter("roleId", roleId);
			accessQueryTyped.setParameter("hostId", hostId);
			List<String> checkAccess = accessQueryTyped.getResultList();

			if (!checkAccess.isEmpty() && "Y".equals(checkAccess.get(0))) {
				String hql = "select l.id.locationId, l.locationName from Location l, LocationAccess a "
						+ "where a.id.roleId = :roleId "
						+ "and l.id.hostId = :hostId and a.id.hostId = :hostId "
						+ "and a.id.entityId = :entityId "
						+ "and l.id.locationId = a.id.locationId "
						+ "and l.id.entityId = a.id.entityId "
						+ "order by l.id.locationId asc";

				TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
				query.setParameter("roleId", roleId);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				noofRecords = query.getResultList();
			} else {
				String hql = "select l.id.locationId, l.locationName from Location l "
						+ "where l.id.hostId = :hostId "
						+ "and l.id.entityId = :entityId "
						+ "order by l.id.locationId asc";

				TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				noofRecords = query.getResultList();
			}
		}

		log.debug(this.getClass().getName() + "- [getLocationAccessList] - Exiting");
		return noofRecords;
	}

	public Collection getMetagroupDetails(String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getMetagroupDetails] - Entering");

		List<Object[]> noofRecords;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select m.id.mgroupId, m.mgroupName from MetaGroup m "
					+ "where m.id.hostId = :hostId "
					+ "and m.id.entityId = :entityId "
					+ "order by m.id.mgroupId asc";

			TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			noofRecords = query.getResultList();
		}

		log.debug(this.getClass().getName() + "- [getMetagroupDetails] - Exiting");
		return noofRecords;
	}

	public Collection getGroupDetails(String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getGroupDetails] - Entering");

		List<Object[]> noofRecords;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select g.id.groupId, g.groupName from Group g "
					+ "where g.id.hostId = :hostId "
					+ "and g.id.entityId = :entityId "
					+ "order by g.id.groupId asc";

			TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			noofRecords = query.getResultList();
		}

		log.debug(this.getClass().getName() + "- [getGroupDetails] - Exiting");
		return noofRecords;
	}


	/**
	 * To get the user preferences From the database
	 *
	 * @param hostId
	 * @param entityId
	 * @param userId
	 * @return userPreferenceList
	 */
	public List getUserPreference(String hostId, String entityId, String userId) {
		log.debug(this.getClass().getName() + " - [getUserPreference] - Entering");

		List<ScreenInfo> userPreferenceList;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from ScreenInfo c where c.id.hostId = :hostId and c.id.userId = :userId "
					+ "order by c.id.entityId, c.id.screenId, c.id.clsId, c.id.propertyName";

			TypedQuery<ScreenInfo> query = session.createQuery(hql, ScreenInfo.class);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			userPreferenceList = query.getResultList();
		}

		log.debug(this.getClass().getName() + " - [getUserPreference] - Exiting");
		return userPreferenceList;
	}


	/**
	 * To set the user preferences into the database
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param userId
	 * @return
	 */
	public void setUserPreference(List userPreferenceList, String hostId, String entityId, String userId)
			throws SwtException {
		ScreenInfo screenInfo;
		Session sesion = null;
		Transaction tx = null;
		try {
			sesion = getHibernateTemplate().getSessionFactory().openSession();
			tx = sesion.beginTransaction();
			for (int userCounter = 0; userCounter < userPreferenceList.size(); userCounter++) {
				screenInfo = (ScreenInfo) userPreferenceList.get(userCounter);

				ScreenInfo scrInfo = (ScreenInfo) sesion.get(ScreenInfo.class, screenInfo.getId());
				if (scrInfo != null) {
					scrInfo.setPropertyValue(screenInfo.getPropertyValue());
					sesion.update(scrInfo);
				} else
					sesion.save(screenInfo);


			}

			tx.commit();

		} catch (Exception e) {
			try {tx.rollback();} catch (Exception e2) { }
			log.error(this.getClass().getName() + " - Exception Catched in [setUserPreference] method : - "
					+ e.getMessage());
		} finally {
			HibernateException hThrownException = JDBCCloser.close(sesion);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	/**
	 * This is used to retrieve data base Host Id
	 *
	 * @param none
	 * @return Host Id
	 * @throws SwtException
	 */
	public String getHostIdFromDB() {
		log.debug(this.getClass().getName() + " - [getHostIdFromDB] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		String sysdate = new String();
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			stmt.execute("select GLOBAL_VAR.FN_GET_HOST from dual");
			rs = stmt.getResultSet();
			rs.next();
			sysdate = (String) rs.getObject(1);
			log.debug(this.getClass().getName() + " - [getHostIdFromDB] - "
					+ "Exit");
			return sysdate;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getHostIdFromDB] method : - "
					+ exp.getMessage());
		} finally {
			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return sysdate;
	}

	/**
	 * get menu item for Role
	 */
	public MenuItem getMenuItemForRole(String menuItemId, String roleId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getMenuItemForRole] - Entering ");

		MenuItem menuItem = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "SELECT ma.menuItem.itemId, ma.menuItem.description, ma.menuItem.parentId, "
					+ "ma.menuItem.menuOrder, ma.menuItem.menuGroupOrder, ma.menuItem.imageName, "
					+ "ma.menuItem.width, ma.menuItem.height, ma.accessId, ma.menuItem.program.programName "
					+ "FROM MenuAccess ma "
					+ "WHERE ma.id.roleId = :roleId AND ma.menuItem.itemId = :menuItemId";

			TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
			query.setParameter("roleId", roleId);
			query.setParameter("menuItemId", menuItemId);

			List<Object[]> results = query.getResultList();

			if (!results.isEmpty()) {
				Object[] record = results.get(0);
				int paramIndex = 0;

				menuItem = new MenuItem();
				menuItem.setItemId((String) record[paramIndex++]);
				menuItem.setDescription((String) record[paramIndex++]);
				menuItem.setParentId((String) record[paramIndex++]);
				menuItem.setMenuOrder((Integer) record[paramIndex++]);
				menuItem.setMenuGroupOrder((Integer) record[paramIndex++]);
				menuItem.setImageName((String) record[paramIndex++]);
				menuItem.setWidth((Integer) record[paramIndex++]);
				menuItem.setHeight((Integer) record[paramIndex++]);
				menuItem.setAccessId((String) record[paramIndex++]);
				menuItem.setProgramName((String) record[paramIndex++]);
			}
		} catch (Exception ex) {
			log.error(this.getClass().getName() + " - [getMenuItemForRole] - Exception: " + ex.getMessage(), ex);
		}

		log.debug(this.getClass().getName() + "- [getMenuItemForRole] - Exiting ");
		return menuItem;
	}

	/**
	 * this method is used to get menu item details
	 * @param menuItemId
	 * @param user
	 * @return
	 * @throws SwtException
	 */
	public MenuItem getMenuItem(String menuItemId, User user) throws SwtException {
		log.debug(this.getClass().getName() + "- [getMenuItem] - Entering ");

		MenuItem menuItem = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "SELECT ma.menuItem.itemId, ma.menuItem.description, "
					+ "ma.menuItem.parentId, ma.menuItem.menuOrder, ma.menuItem.menuGroupOrder, "
					+ "ma.menuItem.imageName, ma.menuItem.width, ma.menuItem.height, ma.accessId, "
					+ "ma.menuItem.program.programName "
					+ "FROM MenuAccess ma "
					+ "INNER JOIN ma.menuItem "
					+ "LEFT OUTER JOIN ma.menuItem.program "
					+ "WHERE ma.id.roleId = :roleId AND ma.menuItem.itemId = :menuItemId";

			TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
			query.setParameter("roleId", user.getRoleId());
			query.setParameter("menuItemId", menuItemId);

			List<Object[]> results = query.getResultList();

			if (!results.isEmpty()) {
				Object[] record = results.get(0);
				int paramIndex = 0;

				menuItem = new MenuItem();
				menuItem.setItemId((String) record[paramIndex++]);
				menuItem.setDescription((String) record[paramIndex++]);
				menuItem.setParentId((String) record[paramIndex++]);
				menuItem.setMenuOrder((Integer) record[paramIndex++]);
				menuItem.setMenuGroupOrder((Integer) record[paramIndex++]);
				menuItem.setImageName((String) record[paramIndex++]);
				menuItem.setWidth((Integer) record[paramIndex++]);
				menuItem.setHeight((Integer) record[paramIndex++]);
				menuItem.setAccessId((String) record[paramIndex++]);
				menuItem.setProgramName((String) record[paramIndex++]);
			}
		} catch (Exception ex) {
			log.error("Exception in getMenuItem - " + ex.getMessage(), ex);
		}

		log.debug(this.getClass().getName() + "- [getMenuItem] - Exiting ");
		return menuItem;
	}

	public MenuItem getMenuItem(String menuItemId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getMenuItem] - Entering ");

		MenuItem menuItem = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from MenuItem ma where ma.itemId = :menuItemId";
			TypedQuery<MenuItem> query = session.createQuery(hql, MenuItem.class);
			query.setParameter("menuItemId", menuItemId);

			List<MenuItem> lstMenu = query.getResultList();
			if (!lstMenu.isEmpty()) {
				menuItem = lstMenu.get(0);
			}
		} catch (Exception ex) {
			log.error("Exception in getMenuItem - " + ex.getMessage(), ex);
		}

		log.debug(this.getClass().getName() + "- [getMenuItem] - Exiting ");
		return menuItem;
	}

	@Override
	public User getUserDetailByExtAuthId(String hostId, String extAuthId) throws SwtException {
		log.debug(this.getClass().getName() + "- [getUserDetailByExtAuthId] - Entering ");

		User user = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from User u where u.id.hostId = :hostId and u.extAuthId = :extAuthId";
			TypedQuery<User> query = session.createQuery(hql, User.class);
			query.setParameter("hostId", hostId);
			query.setParameter("extAuthId", extAuthId);

			List<User> userList = query.getResultList();
			if (!userList.isEmpty()) {
				user = userList.get(0);
			}
		} catch (Exception ex) {
			log.error(this.getClass().getName() + "- [getUserDetailByExtAuthId] - Exception: " + ex.getMessage(), ex);
		}

		log.debug(this.getClass().getName() + "- [getUserDetailByExtAuthId] - Exiting");
		return user;
	}



	/**
	 * This method gets the valid menu items from database for the given user
	 * and returns the same
	 *
	 * @param user
	 *            the user object
	 * @throws SwtException
	 */
	//start mantis 5785
	public Collection getMenuListUpdated(User user) throws SwtException {
		// Variable to hold query
		StringBuffer sbQuery = null;
		// Parameter index
		int paramIndex;
		// Variable to hold list of menu
		List lstMenu = null;
		// Variable to get menu
		Iterator iterMenuAccess = null;
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String matchHash = null;
		MenuItem menuItem = null;
		try {
			// log debug message
			log
					.debug(this.getClass().getName()
							+ "- [getMenuListUpdated] - Entering ");
			// Initialize list to hold menu item
			lstMenu = new ArrayList();
			// Query to fetch menu
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);

			String query="WITH menu_access AS( " +
					" SELECT mi.menu_item_id, mi.menu_item_desc, mi.parent_menu_item_id, mi.menu_order, " +
					"       mi.menu_group_order, mi.image_name, mi.width, mi.height, " +
					"       NVL(ma.access_id, 2) AS access_id, p.program_name " +
					"  FROM P_MENU_ITEM mi " +
					"  LEFT JOIN S_PROGRAM p ON (p.program_id = mi.program_id) " +
					"  LEFT JOIN P_MENU_ACCESS ma ON (mi.menu_item_id = ma.menu_item_id AND ma.role_id=?) " +
					" ORDER BY TO_NUMBER(parent_menu_item_id),menu_group_order, menu_order " +
					") " +
					"SELECT c.menu_item_id, c.menu_item_desc, c.parent_menu_item_id, c.menu_order, " +
					"       c.menu_group_order, c.image_name, c.width, c.height, " +
					"       GREATEST(c.access_id, p.access_id) AS access_id, c.program_name FROM menu_access c " +
					"LEFT JOIN menu_access p ON (c.parent_menu_item_id=p.menu_item_id) " ;

			statement = conn.prepareStatement(query);
			statement.setString(1, user.getRoleId());
			statement.execute();
			resultSet = statement.getResultSet();


			if (resultSet != null) {
				while (resultSet.next()) {

					// Initialize new menu item object
					menuItem = new MenuItem();
					menuItem.setItemId(resultSet.getString("MENU_ITEM_ID"));
					menuItem.setDescription(resultSet.getString("MENU_ITEM_DESC"));
					menuItem.setParentId(resultSet.getString("PARENT_MENU_ITEM_ID"));
					menuItem.setMenuOrder(resultSet.getInt("MENU_ORDER"));
					menuItem.setMenuGroupOrder(resultSet.getInt("MENU_GROUP_ORDER"));
					menuItem.setImageName(resultSet.getString("IMAGE_NAME"));
					menuItem.setWidth(resultSet.getInt("WIDTH"));
					menuItem.setHeight(resultSet.getInt("HEIGHT"));
					menuItem.setAccessId(resultSet.getString("ACCESS_ID"));
					menuItem.setProgramName(resultSet.getString("PROGRAM_NAME"));

					// Add menu item object
					lstMenu.add(menuItem);
				}
			}

			// log debug message
			log.debug(this.getClass().getName() + "- [getMenuListUpdated] - Exiting ");
			return lstMenu;
		} catch (Exception ex) {
			log.error("Exception in getMenuListUpdated - " + ex.getMessage());
			return null;
		} finally {
			sbQuery = null;
			lstMenu = null;
			iterMenuAccess = null;

			JDBCCloser.close(resultSet, statement, conn, session);
			// log debug message
			log.debug("getMenuListUpdated exit");
		}
	}
	//end mantis 5785

}